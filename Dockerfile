# # Use node 20
FROM node:20.18.2-bookworm-slim AS builder
WORKDIR /app
# Define build-time arguments with default values
ARG NODE_ENV=staging
ARG PORT=3000

# Set environment variables using the build-time arguments
ENV NODE_ENV=$NODE_ENV
ENV PORT=$PORT

COPY . ./
COPY package-lock-stable.json package-lock.json
COPY .env.${NODE_ENV} ./.env
COPY .env.${NODE_ENV} ./.env.${NODE_ENV}

RUN --mount=type=cache,target=/root/.npm npm i --legacy-peer-deps
RUN sh -c "npm run build:${NODE_ENV}"

# # Run
# FROM node:20.18.2-bookworm-slim
# WORKDIR /app
ENV NODE_ENV=$NODE_ENV
ENV PORT=$PORT

# COPY --chown=node:node --from=builder /app /app
# RUN touch /var/log/service.log
# RUN chown node:node /var/log/service.log

# EXPOSE $PORT
USER node
# CMD ["bash", "-c", "node dist/main.js 2>&1 | tee /var/log/service.log"]
CMD [ "node", "dist/main.js" ]
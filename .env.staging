APP_ENV=staging
APP_PORT=80

DATABASE_TYPE=postgres
DATABASE_HOST=zileo-db-staging-v3.ci86gncdfulv.eu-west-2.rds.amazonaws.com
DATABASE_HOST_READONLY=zileo-backend-staging-rep-cluster.cluster-ro-ci86gncdfulv.eu-west-2.rds.amazonaws.com
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=abcABC123!
DATABASE_DATABASE=postgres
DATABASE_SYNCHRONIZE=0



#JWT
COMMON_API_JWT_SECRET=randomtext1
COMMON_API_JWT_EXPIRES_IN=60m

# JWT REFESH TOKEN
COMMON_API_JWT_REFRESH_TOKEN_SECRET=randomtext3
COMMON_API_JWT_REFRESH_TOKEN_EXPIRES_IN=120m

# Rate limit
COMMON_API_TTL=60
COMMON_API_LIMIT=1


##BullHorn
BULLHORN_API_GET_ACCESS_CODE=""
BULLHORN_CLIENT_ID=1bd49cd7-bc5e-4e3c-934a-b491c5a26993
BULLHORN_USERNAME=pcapi.api
BULLHORN_PASSWORD=Pearsonapi1
BULLHORN_CLIENT_SECRET=bViIFWVWyh7gfIEgt7Of3qEa

SENDGRID_API_KEY=*********************************************************************
SENDGRID_API_KEY_EMAIL_EXPLORER_CANDIDATE=*********************************************************************
SENDGRID_API_KEY_EMAIL_VALID=*********************************************************************
URL_API=http://zileo.io

CORS_WHITE_LIST=http://localhost:3000,http://localhost,http://**********:3001,http://**********:3000,http://**********,http://zileo.io,http://**********,http://test.zileo.io,http://staging.zileo.io
CRAWLING_API=http://zileo-elb-crawler-staging-1256927277.eu-west-2.elb.amazonaws.com

REGION_AWS=eu-west-2
ACCESS_KEY_ID_AWS=********************
SECRET_ACCESS_KEY_AWS=bcPKHuRtKw3gnLWf4hRmnGk+U0b8LAo3z3VpDxbl
BUCKET_NAME_S3=zileo-dev-bucket

APOLLO_API_KEY=r4mkHRxuEcYq5UUTS37SXg
APOLLO_API_URL=https://app.apollo.io/api/v1

SNS_TOPIC_ARN=arn:aws:sns:eu-west-2:559880962148:zileo-sns-staging


#elasticsearch
ELASTICSEARCH_HOST=https://search-zileo-staging-os-tn7ngx7vskonafuuhbyp7pn3qy.eu-west-2.es.amazonaws.com
# ELASTICSEARCH_HOST=https://**************:9200
ELASTICSEARCH_USER=zileoaws
ELASTICSEARCH_PASSWORD=Zileo@2024


OPENAI_API_KEY=***************************************************

NYLAS_CLIENT_ID=5c4ca094-2de1-4eef-a2e0-5df604aec277
NYLAS_API_KEY=nyk_v0_kMed1nH4P6c8SNDcfSprlpgP5Q7eNYFLTD1vLsVo5DLe2YnWiHdlpSgV5Tgm4RjZ
NYLAS_API_URI=https://api.eu.nylas.com

REDIS_CONNECTION=redis://:zileo@*************:6379/0
# REDIS_CONNECTION=rediss://zileo-staging-redis-ztkc6c.serverless.euw2.cache.amazonaws.com:6379/0
IS_USE_ELASTICCACHE="ON" # ON or OFF


API_URL=https://api-staging.zileo.io
#API_URL=http://localhost
CLIENT_URL=https://staging.zileo.io

#unipile
UNIPILE_KEY=xn3DuTx5.H6nzvXw3iY8VfE2GLoXfFyAbLno28KrAxTyHQ7YVJwY=
UNIPILE_SUB_DOMAIN=api4
UNIPILE_PORT=13435

# Proxy
PROXY_HOST=residential-proxy.scrapeops.io
PROXY_PORT=8181
PROXY_USERNAME=scrapeops
PROXY_PASSWORD=1ed85493-46c9-4a64-a542-6ad4c1faaa94

LAMBDA_OPEN_STREET_SEARCH_URL=https://gn4wkinj2blptyzzy7h64ghyiu0mbbts.lambda-url.eu-west-2.on.aws/

CLAY_PULL_DATA_WEBHOOK_URL=https://api.clay.com/v3/sources/webhook/pull-in-data-from-a-webhook-fb6d097a-1ec5-4736-b801-915f1bd474ac

# Savvycal - Timezones
SAVVYCAL_PRIVATE_KEY=pt_secret_b2b5365d9a4a990bcc8f804095965a44
SAVVYCAL_BASE_URL=https://api.savvycal.com

EMAIL_VALIDATION_API_URL=https://emailvalidation.abstractapi.com/v1/
EMAIL_VALIDATION_API_KEY=8d7f8147d7a942ffab35b6eb9d80e05c

GEMINI_API_KEY=AIzaSyCwYseiXUYMMNE1qpCBkvqlvKT5EIgH0TQ
REFINE_API=http://************/search

CRON_DASHBOARD_USERNAME=ZileoAdmin
CRON_DASHBOARD_PASSWORD=Zileopw@12345
require('dotenv').config({
  path: `.env.${process.env.NODE_ENV || 'dev'}`
});


module.exports = {
  type: process.env.DATABASE_TYPE || 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: process.env.DATABASE_PORT || '5432',
  username: process.env.DATABASE_USERNAME || 'postgres',
  password: process.env.DATABASE_PASSWORD || '12345',
  database: process.env.DATABASE_DATABASE || 'job-finder-dev',
  synchronize: +process.env.DATABASE_SYNCHRONIZE || 0,
  seeds: ['src/seeds/*.seeder{.ts,.js}'],
  entities: ['src/modules/**/**/*.entity{.ts,.js}'],
  ssl: {
    rejectUnauthorized: false,
  },
  extra: {
    max: 20,
    statementTimeout: 600000,
    idle_in_transaction_session_timeout: '3000s',
  },
};

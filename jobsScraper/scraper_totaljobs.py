import logging
import random
import re
import json
import uuid
from datetime import datetime, timedelta
import re

import html2text
import scrapy
from urllib.parse import urlencode

from dateutil import parser

from models.job_boards import insertJobBoardObject

html2textObject = html2text.HTML2Text()

# to extract number of date from "Posted x days ago"
def extract_days_posted(text):
    if "today" in text.lower() or "just" in text.lower():
        return 0
    match = re.search(r'\b(\d+)\b', text)
    if match:
        days = int(match.group())
        return days
    return None

def translate_days_ago(days_ago):
    # Calculate the date x days ago
    date_ago = datetime.now() - timedelta(days=days_ago)
    return date_ago


def translate_salary_string(salary_string):
    if salary_string is None:
        return [None, None, '']
    [number_part, unit_part] = salary_string.split(' per ') if len(salary_string.split(' per ')) == 2 else ['','']
    unit = unit_part.strip().split(' ')[0] if len(unit_part.split(' ')) >= 1 else None
    number_part = number_part.strip().replace(',','').replace('£','').replace('k','000').replace('K','000').replace('m','000000').replace('M','000000')

    def extract_numbers(input_string):
        numbers = re.findall(r'\d+', input_string)
        return [float(num) for num in numbers]
    numbers = extract_numbers(number_part)
    if len(numbers) == 0:
        return [None, None, '']
    elif len(numbers) == 1:
        return [numbers[0], numbers[0], unit]
    return numbers[-2:] + [unit]


class JobSpiderTotalJobs(scrapy.Spider):
    name = "total_jobs"

    def __init__(self, *args, **kwargs):
        super(JobSpiderTotalJobs, self).__init__(*args, **kwargs)

    def start_requests(self):
        jobTitleQuery = getattr(self, 'jobTitleQuery')
        location = getattr(self, 'location')
        parameters = {"q": jobTitleQuery, "l": location}
        url = f'https://www.totaljobs.com/jobs/{jobTitleQuery.replace(" ", "-")}/{"in-"}{"united-kingdom" if not location else location.replace(" ", "-")}?sort=2'
        yield scrapy.Request(self.get_totaljobs_search_url(url), callback=self.parse)

    def parse(self, response):
        jobs = response.css('div[data-genesis-element=CARD_GROUP_CONTAINER] > article')
        for job in jobs:
            job_id = job.css('article::attr(id)').get()
            if job_id is None:
                continue
            title = job.css('a[data-at="job-item-title"] > div > div > div::text').get()
            location = job.css('span[data-at="job-item-location"] > span[data-genesis-element="TEXT"]::text').get() or job.css('span[data-at="job-item-location"]::text').get()
            salary = job.css('span[data-at="job-item-salary-info"]::text').get()

            job_item_title_url = job.css('a[data-at="job-item-title"]::attr(href)').get()
            if 'https' in job_item_title_url:
                link = job_item_title_url
            else:
                link = 'https://www.totaljobs.com' + job_item_title_url
            posted = job.css('span[data-at="job-item-timeago"] > time::attr(datetime)').get()

            company = job.css('span[data-at="job-item-company-name"] > span > span[data-genesis-element="TEXT"]::text').get()
            # Extract min and max salary values if available

            salary_range_and_unit = translate_salary_string(salary)
            salary_unit = salary_range_and_unit[2]
            salary_multiplication = 1
            if 'hour' in salary_unit:
                salary_multiplication = 2080
            if 'day' in salary_unit:
                salary_multiplication = 260
            if 'year' or 'annum' in salary_unit:
                salary_multiplication = 1
            if 'month' in salary_unit:
                salary_multiplication = 12
            min_salary = salary_range_and_unit[0] * salary_multiplication if salary_range_and_unit[0] is not None else None
            max_salary = salary_range_and_unit[1] * salary_multiplication if salary_range_and_unit[1] is not None else None
            job_data = {
                'job_id': 'totaljobs' + job_id,
                'source': 'totaljobs',
                'jobtitle': title,
                'jobtype': None,
                'joblocationcity': location,
                'company': company,
                'companyrating': None,
                'salary': salary,
                'link': link,
                'posted': parser.parse(posted),
                'extracteddate': datetime.now(),
                'description': None,
                'min_salary': min_salary,
                'max_salary': max_salary,
                'joblocationinput': self.location
            }
            yield response.follow(self.get_totaljobs_search_url(link), callback=self.parse_job, meta=job_data)

        next_page_url = response.css('a[aria-label="Next"]::attr(href)').get()
        if next_page_url is not None:
            yield response.follow(self.get_totaljobs_search_url(next_page_url), callback=self.parse)


    def parse_job(self, response):
        current_job_data = response.meta
        job_type = response.css('li.job-type >div::text').get()
        # Replace <p> tags with a new line
        description = '' if not response.css('div.job-description').get() else html2textObject.handle(response.css('div.job-description').extract_first())
        company = current_job_data['company'] if current_job_data['company'] else response.css('li.company >div > a#companyJobsLink::text').get()
        amended_job_data = current_job_data
        amended_job_data['description'] = description
        amended_job_data['jobtype'] = job_type
        amended_job_data['company'] = company
        insertJobBoardObject(amended_job_data, getattr(self, 'jobTitleQuery'))
        yield amended_job_data

    def closed(self, reason):
        logging.info("Scraping completed. Restarting!")

    # def get_headers(self):
    #   user_agents = [
    #     'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    #     'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    #     'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    #     'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    #     'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    #     'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15'
    #     'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15'
    #   ]
    #   headers = {
    #     "user-agent": random.choice(user_agents)
    #   }
    #   return headers

    def get_totaljobs_search_url(self, url):
        payload = {
            'api_key': '1910af6c-f11b-48b5-9217-b79a9efbf8d9',
            'url': url
        }
        proxy_url = 'https://proxy.scrapeops.io/v1?' + urlencode(payload)
        return proxy_url
        return url


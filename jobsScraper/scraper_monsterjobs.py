import random
from datetime import datetime
import logging
from dateutil import parser

import html2text
import scrapy
import json
import uuid

from models.job_boards import insertJobBoardObject
html2textObject = html2text.HTML2Text()

class JobSpiderMonsterJobs(scrapy.Spider):
  name = "monster_jobs"
  start_url = "https://appsapi.monster.io/jobs-svx-service/v2/monster/search-jobs/samsearch/en-GB?apikey=AE50QWejwK4J73X1y1uNqpWRr2PmKB3S"
  page_size = 50  # Number of job postings per page

  def start_requests(self):
    yield scrapy.Request(self.start_url, method="POST", headers=self.get_headers(), body=self.get_payload(0))

  def parse(self, response):
    data = json.loads(response.text)
    search_id = data.get('searchId','')
    job_postings = data.get('jobResults', [])
    page_offset = data.get('jobRequest',{}).get('offset',0)
    page_size = data.get('jobRequest',{}).get('pageSize',0)
    total_size =  data.get('totalSize',0)

    if job_postings:
      for job in job_postings:
        # Extract job information and yield the job object
        job_id = job.get('jobId')
        source = 'monster'

        jobPosting = job.get('jobPosting', {})
        job_title = jobPosting.get('title')
        job_type = ",".join(jobPosting.get('employmentType',[]))
        job_location_object = jobPosting.get('jobLocation', [])[0] if len(jobPosting.get('jobLocation', [])) > 0 else {}
        job_location_city = job_location_object.get('address', {}).get('addressLocality')
        company = jobPosting.get('hiringOrganization', {}).get('name')
        company_rating = None
        link = jobPosting.get('url')
        posted = jobPosting.get('datePosted')
        extracted_date = datetime.now().date()
        description = html2textObject.handle(jobPosting.get('description'))
        base_salary = jobPosting.get('baseSalary', {}).get('value', {})
        job_currency = '£'
        min_salary = base_salary.get('minValue') if base_salary else None
        max_salary = base_salary.get('maxValue') if base_salary else None
        salary_unit = base_salary.get('unitText') if base_salary else ''

        if 'year' in salary_unit.lower():
            salary_multiplier = 1
        elif 'day' in salary_unit.lower():
            salary_multiplier = 2080
        salary = f"{job_currency}{min_salary} - {job_currency}{max_salary} {salary_unit}" if job_currency is not None and salary_unit is not None and min_salary is not None and max_salary is not None else None
        min_salary = min_salary * salary_multiplier if min_salary is not None else None
        max_salary = max_salary * salary_multiplier if max_salary is not None else None
        # Create a dictionary representing the job object
        job_object = {
          "job_id": f"{source}{job_id}",
          "source": source,
          "jobtitle": job_title,
          "jobtype": job_type,
          "joblocationcity": job_location_city,
          "company": company,
          "companyrating": company_rating,
          "salary": salary,
          "link": link,
          "posted": parser.parse(posted),
          "extracteddate": extracted_date,
          "description": description,
          "min_salary": min_salary,
          "max_salary": max_salary,
          'joblocationinput': self.location
        }
        insertJobBoardObject(job_object, getattr(self, 'jobTitleQuery'))

      current_offset = data.get('jobRequest',{}).get('offset', 0)
      next_offset = current_offset + self.page_size

      if page_size == total_size:
        yield scrapy.Request(self.start_url, method="POST", headers=self.get_headers(),
                             body=self.get_payload(next_offset, search_id), callback=self.parse)

  def get_headers(self):
    user_agents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15'
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15'
    ]
    headers = {
      "user-agent": random.choice(user_agents),
      "Content-Type": "application/json"

    }
    return headers

  def closed(self, reason):
    logging.info("Scraping completed. Restarting!")

  def get_payload(self, offset, search_id = None):
    payload = json.dumps({
      "jobQuery": {
        "query": self.jobTitleQuery,
        "locations": [
          {
            "country": "gb",
            "address": self.location
          }
        ]
      },
      "jobAdsRequest": {
        "position": [
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8,
          9
        ],
        "placement": {
          "channel": "MOBILE",
          "location": "JobSearchPage",
          "property": "monster.co.uk",
          "type": "JOB_SEARCH",
          "view": "CARD"
        }
      },
      "fingerprintId": str(uuid.uuid4()),  # Generate fingerprint using UUID
      "searchId": search_id,
      "offset": offset,
      "pageSize": self.page_size,
      "histogramQueries": [
        "count(company_display_name)",
        "count(employment_type)"
      ]
    })
    return payload



import random
from datetime import datetime
import logging

import html2text
import scrapy
import json
import uuid

from dateutil import parser

from models.job_boards import insertJobBoardObject
html2textObject = html2text.HTML2Text()


jobTypeDict = {1: 'Permanent', 2: 'Contract', 4: 'Temporary'}
class JobSpiderReed(scrapy.Spider):
  name = "reed_jobs"
  start_url = f"https://api.reed.co.uk/api-bff-jobseeker-jobs/search/anonymous/"

  def start_requests(self):
    yield scrapy.Request(self.start_url + '?page=1', method="POST", headers=self.get_headers(), body=self.get_payload())

  def parse(self, response):
    data = json.loads(response.text)
    job_postings = data.get('result', {}).get('response',{}).get('jobSearchResult', {}).get('searchResults',{}).get('results', [])

    if job_postings:
      for job in job_postings:
        # Extract job information and yield the job object
        jobPosting = job.get('jobDetail', {})
        job_id = str(jobPosting.get('jobId'))
        source = 'reed'
        job_title = jobPosting.get('jobTitle')
        job_type = jobTypeDict[jobPosting.get('jobType', '')]
        job_location_city = jobPosting.get('displayLocationName')
        company = jobPosting.get('ouName')
        company_rating = None
        link = f"https://www.reed.co.uk/jobs/{job_title.replace(' ', '-')}/{jobPosting.get('jobId')}"
        posted = jobPosting.get('dateCreated')
        extracted_date = datetime.now().date()
        description = jobPosting.get('jobDescription')
        job_currency = '£'
        min_salary = jobPosting.get('salaryFrom')
        max_salary = jobPosting.get('salaryTo')
        #yearly
        if jobPosting.get('salaryType') == 5:
            salary_multiplier = 1
            salary_unit = 'per year'
        #hour
        elif jobPosting.get('salaryType') == 1:
            salary_multiplier = 2080*8
            salary_unit = 'per hour'
        #daily
        elif jobPosting.get('salaryType') == 2:
            salary_multiplier = 2080
            salary_unit = 'per day'
        salary = f"{job_currency}{min_salary} - {job_currency}{max_salary} {salary_unit}" if job_currency is not None and salary_unit is not None and min_salary is not None and max_salary is not None else None
        min_salary = min_salary * salary_multiplier if min_salary is not None else None
        max_salary = max_salary * salary_multiplier if max_salary is not None else None
        # Create a dictionary representing the job object
        job_object = {
          "job_id": f"{source}{job_id}",
          "source": source,
          "jobtitle": job_title,
          "jobtype": job_type,
          "joblocationcity": job_location_city,
          "company": company,
          "companyrating": company_rating,
          "salary": salary,
          "link": link,
          "posted": parser.parse(posted),
          "extracteddate": extracted_date,
          "description": description,
          "min_salary": min_salary,
          "max_salary": max_salary,
          'joblocationinput': self.location
        }
        insertJobBoardObject(job_object, getattr(self, 'jobTitleQuery'))

      totalItemCount = data.get('result',{}).get('totalItemCount', 0)
      pageItemCount = data.get('result',{}).get('pageItemCount', 0)
      currentPage = data.get('result',{}).get('currentPage', 0)

      if len(job_postings) > 0:
        yield scrapy.Request(self.start_url + f"?page={currentPage + 1}", method="POST", headers=self.get_headers(),
                             body=self.get_payload(), callback=self.parse)

  def get_headers(self):
    user_agents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36'
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36'
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36'
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15'
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15'
    ]
    headers = {
      "user-agent": random.choice(user_agents),
      "Content-Type": "application/json",
      "x-correlation-id": str(uuid.uuid4()),
      "x-correlation-id": "D8046A9F-3F59-4C80-A0B4-A1342B424597",
      "x-correlation-source-id": "WEB-JOBSEEKER-JOBS",
    }
    return headers
  def get_payload(self):
    payload = f'{{"sortBy":"displayDate","keywords":"{self.jobTitleQuery.lower().strip()}","location":{{"locationId":0,"locationName": "{self.location}"}},"parentSectorIds":[],"proximity":10,"salaryFrom":0,"salaryTo":0,"perHour":false,"perm":false,"temp":false,"partTime":false,"fullTime":false,"ouId":0,"recruiterName":"","clientProfileId":0,"isReed":false,"agency":false,"direct":false,"graduate":false,"contract":false,"hideTrainingJobs":false,"remoteWorkingOption":"notSpecified","pageno":1,"take":25,"dateCreatedOffSet":"anytime","seoDirectory":"{self.jobTitleQuery.lower().strip()}","misspeltKeywords":null,"skipKeywordSpellcheck":false,"visaSponsorship":false,"isEasyApply":false,"excludeSalaryDescriptions":[],"minApplicants":0,"maxApplicants":0}}'
    return payload
  def closed(self, reason):
    logging.info("Scraping completed. Restarting!")

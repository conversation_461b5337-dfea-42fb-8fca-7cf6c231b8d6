from scrapy import signals


class ProxyMiddleware:
    def __init__(self, proxy_list):
        self.proxy_list = proxy_list

    @classmethod
    def from_crawler(cls, crawler):
        # Read proxy list from settings
        proxy_list = crawler.settings.getlist('PROXY_LIST')
        middleware = cls(proxy_list)
        middleware.username = crawler.settings.get('PROXY_USERNAME')
        middleware.password = crawler.settings.get('PROXY_PASSWORD')
        crawler.signals.connect(middleware.spider_opened, signal=signals.spider_opened)
        return middleware

    def spider_opened(self, spider):
        spider.logger.info('ProxyMiddleware: Spider opened: %s' % spider.name)

    def process_request(self, request, spider):
        # Set proxy with username and password for each request
        if self.proxy_list:
            proxy = self.proxy_list.pop(0)
            request.meta['proxy'] = self.add_credentials_to_proxy(proxy)
            spider.logger.debug('Using proxy: %s' % request.meta['proxy'])

    def add_credentials_to_proxy(self, proxy):
        # Add username and password to the proxy URL
        if self.username and self.password:
            parts = proxy.split('://')
            if len(parts) > 1:
                protocol = parts[0]
                url = parts[1]
                if '@' in url:
                    return proxy  # Proxy already contains credentials
                else:
                    proxy_with_credentials = f'{protocol}://{self.username}:{self.password}@{url}'
                    return proxy_with_credentials
        return proxy

    def process_exception(self, request, exception, spider):
        # Handle proxy errors
        if 'proxy' in request.meta:
            proxy = request.meta['proxy']
            spider.logger.debug('Proxy error: %s, %s' % (proxy, exception))
            # Retry request with a different proxy or handle the exception as needed

    def process_response(self, request, response, spider):
        # Process the response if needed
        return response

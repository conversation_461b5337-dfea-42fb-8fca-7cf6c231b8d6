import logging
from datetime import datetime, timedelta
import re

import html2text
import scrapy
from urllib.parse import urlencode

from models.job_boards import insertJobBoardObject

html2textObject = html2text.HTML2Text()

# to extract number of date from "Posted x days ago"
def extract_days_posted(text):
    if "today" in text.lower() or "just" in text.lower():
        return 0
    match = re.search(r'\b(\d+)\b', text)
    if match:
        days = int(match.group())
        return days
    return None

def translate_days_ago(days_ago):
    # Calculate the date x days ago
    date_ago = datetime.now() - timedelta(days=days_ago)
    return date_ago


def translate_salary_string(salary_string):
    if salary_string is None:
        return [None, None, '']

    # Define a mapping for the abbreviations
    def extract_number(string):
        pattern = r"\£([\d.,]+)([KM]?)"
        translation = {'K': 1000, 'M': 1000000}

        match = re.search(pattern, string)
        if match:
            number_str = match.group(1)
            suffix = match.group(2)

            # Remove commas from the number string and convert it to a float
            number = float(number_str.replace(',', ''))

            # Apply the translation if a suffix is present
            if suffix in translation:
                number *= translation[suffix]

            return number
    result = []
    words = salary_string.split(" ")
    containsDigitPattern = r"\d"
    for word in words:
        if bool(re.search(containsDigitPattern, word)):
            result.append(extract_number(word))
    if len(result) == 1:
        result.append(result[0])
    result.append(words[len(words) - 1])
    return result


class JobSpiderIndeed(scrapy.Spider):
    name = "indeed_jobs"

    def __init__(self, *args, **kwargs):
        super(JobSpiderIndeed, self).__init__(*args, **kwargs)

    def start_requests(self):
        jobTitleQuery = getattr(self, 'jobTitleQuery')
        location = getattr(self, 'location')
        parameters = {"q": jobTitleQuery, "l": location, "sort": "date"}
        url = f"{self.get_indeed_search_url('https://uk.indeed.com/jobs?' + urlencode(parameters))}"
        yield scrapy.Request(url, callback=self.parse)

    def closed(self, reason):
      logging.info("Scraping completed. Restarting!")
      self.start_requests()

    def parse(self, response):
        jobs = response.css('div.mosaic-provider-jobcards > ul > li')
        for job in jobs:
            job_id = job.css('h2.jobTitle > a').css('a::attr(data-jk)').get()
            if job_id is None:
                continue
            title = job.css('h2.jobTitle > a').css('a > span::text').get()
            location = job.css('div[data-testid="text-location"]::text').get()
            salary = job.css('span.estimated-salary > span::text').get() or job.css('div.salary-snippet-container > div.attribute_snippet::text').get()
            link = 'https://uk.indeed.com' + job.css('h2.jobTitle > a::attr(href)').get()
            postedDateString = job.css('div.result-footer > span.date::text').get()
            posted = translate_days_ago(extract_days_posted(postedDateString)) if postedDateString is not None else None
            company = job.css('span[data-testid="company-name"]::text').get()
            # Extract min and max salary values if available

            salary_range_and_unit = translate_salary_string(salary)
            salary_unit = salary_range_and_unit[2]
            salary_multiplication = 1
            if 'hour' in salary_unit:
                salary_multiplication = 2080
            if 'day' in salary_unit:
                salary_multiplication = 260
            if 'year' in salary_unit:
                salary_multiplication = 1
            if 'month' in salary_unit:
                salary_multiplication = 12
            min_salary = salary_range_and_unit[0] * salary_multiplication if salary_range_and_unit[0] is not None else None
            max_salary = salary_range_and_unit[1] * salary_multiplication if salary_range_and_unit[1] is not None else None
            job_data = {
                'job_id': 'indeed' + job_id,
                'source': 'indeed',
                'jobtitle': title,
                'jobtype': None,
                'joblocationcity': location,
                'company': company,
                'companyrating': None,
                'salary': salary,
                'link': link,
                'posted': posted,
                'extracteddate': datetime.now(),
                'description': None,
                'min_salary': min_salary,
                'max_salary': max_salary,
                'joblocationinput': self.location
            }
            yield response.follow(self.get_indeed_search_url(link), callback=self.parse_job, meta=job_data)
        next_page_postfix_url = response.css('a[data-testid="pagination-page-next"]::attr(href)').get()
        if next_page_postfix_url is not None:
          next_page_url = 'https://uk.indeed.com' + next_page_postfix_url
          yield response.follow(self.get_indeed_search_url(next_page_url), callback=self.parse)


    def parse_job(self, response):
        current_job_data = response.meta
        job_type = response.css('div#salaryInfoAndJobType > span::text').get()
        # Replace <p> tags with a new line
        description = html2textObject.handle(response.css('div#jobDescriptionText').extract_first())
        amended_job_data = current_job_data
        amended_job_data['description'] = description
        amended_job_data['jobtype'] = job_type
        insertJobBoardObject(amended_job_data, getattr(self, 'jobTitleQuery'))
        yield amended_job_data

    def closed(self, reason):
        logging.info("Scraping completed. Restarting!")

    def get_indeed_search_url(self, url):
        payload = {
            'api_key': '1910af6c-f11b-48b5-9217-b79a9efbf8d9',
            'url': url
        }
        proxy_url = 'https://proxy.scrapeops.io/v1?' + urlencode(payload)
        return proxy_url

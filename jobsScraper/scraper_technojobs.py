import logging
import re
from urllib.parse import urlencode

from dateutil import parser
import scrapy
import sys
from datetime import datetime, timedelta
import html2text

from models.job_boards import insertJobBoardObject
from utils import string_contains_all_words

html2textObject = html2text.HTML2Text()

class JobSpiderTechnojobs(scrapy.Spider):
    name = "job_spider"

    def __init__(self, *args, **kwargs):
        super(JobSpiderTechnojobs, self).__init__(*args, **kwargs)
        self.base_url = 'https://www.technojobs.co.uk/search.phtml'

    def start_requests(self):
        page = getattr(self, 'page', 1)
        row_offset = getattr(self, 'row_offset', 10)
        sortby = getattr(self, 'sortby', 'newest')  # Set default value to "newest"
        jobTitleQuery = getattr(self, 'jobTitleQuery')
        location = getattr(self, 'location')
        keywords = getattr(self, 'keywords')
        salary = getattr(self, 'salary', 0)
        jobtype = getattr(self, 'jobtype', 'all')
        radius = getattr(self, 'radius', 25)
        remote = getattr(self, 'remote', 'all')
        query_params = {
            'page': str(page),
            'row_offset': str(row_offset),
            'sortby': sortby,
        #search by job title and keywords
            'keywords': jobTitleQuery,
            'salary': str(salary),
            'jobtype': jobtype,
            'location': 'United Kingdom' if not location else location,
            'radius': str(radius),
            'remote': remote
        }

        url = f"{self.base_url}?{urlencode(query_params)}"
        yield scrapy.Request(url, callback=self.parse)

    def parse(self, response):
        jobs = response.css('ul.listings > li')
        for job in jobs:
            job_id = job.css('a::attr(href)').re_first(r'\/job\/(\d+)\/')
            title = job.css('h2.listing__title_jobcard > a::text').get() or job.css('h2.listing__title_jobcard > a > b::text').get()

            location = job.css('.listing__items > .listing__item:nth-child(2) > div > div::text').get()
            salary = job.css('.listing__items > .listing__item:nth-child(3) > div > div::text').get() or ''
            link = 'https://www.technojobs.co.uk' + job.css('.listing__title_jobcard > a::attr(href)').get()
            posted = job.css('.listing__items > .listing__item:nth-child(1) > div > time::attr(datetime)').get()
            description = job.css('.listing__text').xpath('string()').get().strip()

            job_type = ''
            company = ''
            # Extract min and max salary values if available
            min_salary = None
            max_salary = None
            salary_range = salary.split('-')
            if len(salary_range) == 2:
                min_salary = salary_range[0].strip()
                max_salary = salary_range[1].strip()
                # convert to numeric
                min_salary = re.sub(r'[^\d.]', '', min_salary)
                # get 15 first characters so it won't include other number
                # this is to handle a case when salary is
                # "£40,400 - £49,991 plus an allowance of up to £5,000"
                max_salary = re.sub(r'[^\d.]', '', max_salary[0:15])

            job_data = {
                'job_id': 'technojobs' + job_id,
                'source': 'technojobs',
                'jobtitle': title,
                'jobtype': job_type,
                'joblocationcity': location,
                'company': company,
                'companyrating': None,
                'salary': salary,
                'link': link,
                'posted': parser.parse(posted),
                'extracteddate': datetime.now(),
                'description': description,
                'min_salary': min_salary,
                'max_salary': max_salary,
                'joblocationinput': self.location
            }
            yield response.follow(link, callback=self.parse_job, meta=job_data)


        next_page = response.\
            css(".pagination > .pagination__control")\
            .xpath("//*[contains(text(), 'Next')]").css('::attr(href)').get()
        if next_page is not None:
            yield response.follow('https://www.technojobs.co.uk' + next_page, callback=self.parse)


    def parse_job(self, response):
        current_job_data = response.meta
        job_type = response.css('.listing__items > .listing__item:nth-child(5) > div > div::text').get()
        company = response.css('.listing__items > .listing__item:nth-child(1) > div > div::text').get()
        # Replace <p> tags with a new line
        description = html2textObject.handle(response.css('article > article.description').extract_first())

        amended_job_data = current_job_data
        amended_job_data['company'] = company
        amended_job_data['description'] = description
        amended_job_data['jobtype'] = job_type
        del amended_job_data['depth']
        del amended_job_data['download_timeout']
        del amended_job_data['download_slot']
        del amended_job_data['download_latency']
        insertJobBoardObject(amended_job_data, getattr(self, 'jobTitleQuery'))
        yield amended_job_data

    def closed(self, reason):
        logging.info("Scraping completed. Restarting!")



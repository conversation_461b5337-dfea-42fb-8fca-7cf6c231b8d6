import re
from datetime import datetime, timedelta
from urllib.parse import urlencode

import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.wait import WebDriverWait
from webdriver_manager.chrome import ChromeDriverManager
from time import sleep
from selenium.webdriver.common.keys import Keys
import html2text
from selenium.webdriver.support import expected_conditions as EC

from models.job_boards import insertJobBoardObject

html2textObject = html2text.HTML2Text()


def extract_day_posted(text):
    try:
        if "h" in text.lower():
            return int(text.split('h')[0]) / 24.0
        if "d" in text.lower():
            days = int(text.split('d')[0])
            return days
        return None
    except Exception as e:
        print(e)
        return None


def translate_days_ago(days_ago):
    # Calculate the date x days ago
    date_ago = datetime.now() - timedelta(days=days_ago)
    return date_ago


def translate_salary_string(salary_string):
    if salary_string is None:
        return [None, None, '']

    # Define a mapping for the abbreviations
    def extract_number(string):
        pattern = r"\£([\d.,]+)([KM]?)"
        translation = {'K': 1000, 'M': 1000000}

        match = re.search(pattern, string)
        if match:
            number_str = match.group(1)
            suffix = match.group(2)

            # Remove commas from the number string and convert it to a float
            number = float(number_str.replace(',', ''))

            # Apply the translation if a suffix is present
            if suffix in translation:
                number *= translation[suffix]

            return number

    result = []
    words = salary_string.split(" ")
    containsDigitPattern = r"\d"
    for word in words:
        if bool(re.search(containsDigitPattern, word)):
            result.append(extract_number(word))
    if len(result) == 1:
        result.append(result[0])
    result.append(words[len(words) - 1])
    return result

def execute247glassdoorScraper(jobTitleQuery, location):
    while True:
        jobScraperGlassdoor(jobTitleQuery, location)

def get_glassdoor_search_url(url):
    payload = {
        'api_key': '1910af6c-f11b-48b5-9217-b79a9efbf8d9',
        'url': url
    }
    proxy_url = 'https://proxy.scrapeops.io/v1?' + urlencode(payload)
    return proxy_url

def jobScraperGlassdoor(jobTitleQuery, location):
    locationWithUK = (location or '') + ' UK'
    getLocationResponse = requests.get(get_glassdoor_search_url(f"https://www.glassdoor.co.uk/util/ajax/findLocationsByFullText.htm?{urlencode({'locationSearchString': locationWithUK})}&allowPostalCodes=true"))
    jsonLocations = getLocationResponse.json()
    glassdoorClosestLocationId = jsonLocations['locations'][0]["id"]
    jobTitleQueryWithHyphen = jobTitleQuery.strip().replace(' ', '-')
    locationQueryWithHyphen = re.sub(r'[^a-zA-Z0-9\s]+', '', jsonLocations['locations'][0]['name']).strip().replace(' ', '-')
    options = Options()
    options.add_experimental_option("detach", True)
    options.add_argument('intl.accept_languages')
    options.add_argument("start-maximized")
    options.add_argument("disable-infobars")
    options.add_argument('--lang=en-GB')
    options.add_argument('--no-sandbox')
    options.add_argument("--disable-extensions")
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--headless')
    options.binary_location = '/usr/bin/google-chrome-stable'
    driver = webdriver.Chrome('/bin/chromedriver', options=options)
    # driver = webdriver.Chrome(ChromeDriverManager().install(), options=options)
    driver.get(get_glassdoor_search_url(f"https://www.glassdoor.co.uk/Job/{locationQueryWithHyphen}-{jobTitleQueryWithHyphen}-jobs-SRCH_IL.0,{len(locationQueryWithHyphen)}_IC{glassdoorClosestLocationId}_KO{len(locationQueryWithHyphen)+1},{len(locationQueryWithHyphen)+1 + len(jobTitleQueryWithHyphen)}.htm?sortBy=date_desc"))
    currentStartingLi = 0
    WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR,'li.JobsList_jobListItem__JBBUV')))
    while True:
        li_elements = driver.find_elements(By.CSS_SELECTOR, 'li.JobsList_jobListItem__JBBUV')
        for li_element in li_elements[currentStartingLi:]:
            driver.execute_script("document.styleSheets[0].insertRule('#LoginModal {visibility: hidden;}', 0 )")
            driver.execute_script("arguments[0].scrollIntoView();", li_element)
            try:
                job_id = li_element.find_element(By.CSS_SELECTOR, 'a[id*="job-title-"]').get_attribute("id").replace(
                    'job-title-', '')
            except Exception as e:
                print(e)
                job_id = ''
            try:
                job_employer_element = li_element.find_elements(By.CSS_SELECTOR, 'div[id*="job-employer-"]')[-1]
                for span_element in job_employer_element.find_elements(By.CSS_SELECTOR, "span"):
                    driver.execute_script("arguments[0].parentNode.removeChild(arguments[0]);", span_element)
                job_employer = job_employer_element.text
            except Exception as e:
                print(e)
                job_employer = ''
            try:
                job_title = li_element.find_element(By.CSS_SELECTOR, 'a[id*="job-title-"]').text
            except Exception as e:
                print(e)
                job_title = ''
            try:
                job_location = li_element.find_element(By.CSS_SELECTOR, 'div[id*="job-location-"]').text
            except Exception as e:
                print(e)
                job_location = ''
            try:
                job_salary = li_element.find_element(By.CSS_SELECTOR, 'div.salary-estimate').text
            except Exception as e:
                print(e)
                job_salary = ''
            try:
                job_link = li_element.find_element(By.CSS_SELECTOR, 'a[data-test="job-link"]').get_attribute('href')
            except Exception as e:
                print(e)
                job_link = ''
            try:
                posted = li_element.find_element(By.CSS_SELECTOR,
                                                 'div.d-flex.align-items-end.ml-xsm.listing-age').text
            except Exception as e:
                print(e)
                posted = ''
            try:
                # # li_element.click()
                # sleep(1)
                # description = driver.find_element(By.CSS_SELECTOR,
                #                                   'section[class^="JobDetails_jobDetailsSection"]').find_element(
                #     By.CSS_SELECTOR, "div").find_element(By.CSS_SELECTOR, "div").get_attribute('innerHTML')
                description = ''
                # description = html2textObject.handle(description)
            except Exception as e:
                print(e)
                description = ''
            posted = translate_days_ago(extract_day_posted(posted)) if posted else None
            salary_range_and_unit = translate_salary_string(job_salary) if job_salary else [None, None, '']
            salary_unit = salary_range_and_unit[2]
            salary_multiplication = 1
            if 'hour' in salary_unit:
                salary_multiplication = 2080
            if 'day' in salary_unit:
                salary_multiplication = 260
            if 'year' in salary_unit:
                salary_multiplication = 1
            if 'month' in salary_unit:
                salary_multiplication = 12
            min_salary = salary_range_and_unit[0] * salary_multiplication if salary_range_and_unit[
                                                                                 0] is not None else None
            max_salary = salary_range_and_unit[1] * salary_multiplication if salary_range_and_unit[
                                                                                 1] is not None else None
            extractedDate = datetime.now()
            job_data = {
                'job_id': 'glassdoor' + job_id,
                'source': 'glassdoor',
                'jobtitle': job_title,
                'jobtype': None,
                'joblocationcity': job_location,
                'company': job_employer,
                'companyrating': None,
                'salary': job_salary,
                'link': job_link,
                'posted': posted,
                'extracteddate': extractedDate,
                'description': description,
                'min_salary': min_salary,
                'max_salary': max_salary,
                'joblocationinput': location
            }
            insertJobBoardObject(job_data, getattr(self, 'jobTitleQuery'))
        currentStartingLi = currentStartingLi + 20
        try:
            driver.stop_client()
            break
            # element = WebDriverWait(driver, 3).until(
            #     EC.element_to_be_clickable((By.CSS_SELECTOR, 'div[class*="JobsList_buttonWrapper__"] > button[class*="button_Button__meEg5 button-base_Button__"]')))
            # element.click()
            # sleep(3)
        except Exception as e:
            print(e)
            #stop the while true loop
            driver.stop_client()
            sleep(5)
            break

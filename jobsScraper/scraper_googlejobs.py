import datetime
import re
import logging
from urllib.parse import urlencode
import html2text
import scrapy
from models.job_boards import insertJobBoardObject


def translate_salary_string(salary_string):
  if salary_string is None:
    return [None, None, '']

  # Define a mapping for the abbreviations
  def extract_number(string):
    pattern = r"\£([\d.,]+)([KM]?)"
    translation = {'K': 1000, 'M': 1000000}

    match = re.search(pattern, string)
    if match:
      number_str = match.group(1)
      suffix = match.group(2)

      # Remove commas from the number string and convert it to a float
      number = float(number_str.replace(',', ''))

      # Apply the translation if a suffix is present
      if suffix in translation:
        number *= translation[suffix]

      return number

  result = []
  words = salary_string.split(" ")
  containsDigitPattern = r"\d"
  for word in words:
    if bool(re.search(containsDigitPattern, word)):
      result.append(extract_number(word))
  if len(result) == 1:
    result.append(result[0])
  result.append(words[len(words) - 1])
  return result

def extract_days_posted(text):
  if "today" in text.lower() or "just" in text.lower():
    return 0
  match = re.search(r'\b(\d+)\b', text)
  if match:
    days = int(match.group())
    return days
  return None

def parsedatetime(str_val):
    parts = str_val.split(' ')
    if len(parts) != 3 and parts[2] != 'ago':
       raise Exception("can't parse %s" % str_val)
    try:
       interval = int(parts[0])
    except ValueError :
       raise Exception("can't parse %s" % str_val)

    desc = parts[1]
    if 'second' in desc:
       td = datetime.timedelta(seconds=interval)
    elif 'minute' in desc:
       td = datetime.timedelta(minutes=interval)
    elif 'hour' in desc:
       td = datetime.timedelta(minutes=interval*60)
    elif 'day' in desc:
       td = datetime.timedelta(days=interval)
    else:
      return None
    answer = datetime.datetime.now() - td
    return answer

def translate_days_ago(days_ago):
  # Calculate the date x days ago
  date_ago = datetime.datetime.now() - datetime.timedelta(days=days_ago)
  return date_ago

html2textObject = html2text.HTML2Text()

class JobSpiderGoogleJobs(scrapy.Spider):
  name = "google_jobs"

  def start_requests(self):
    payload = {
      'q': f"{self.jobTitleQuery} jobs in {self.location} UK"
    }
    encodedQuery = urlencode(payload)
    self.start_url = f"https://www.google.com/search?vet=10ahUKEwju06X1h-WBAxWqVKQEHXhxCXIQ06ACCN4M..i&ei=7uUhZYzBF7-okdUPpMSbyAo&opi=89978449&yv=3&rciv=jb&nfpr=0&{encodedQuery}&asearch=jb_list&cs=1&async=_id:VoQFxe,_pms:hts,_fmt:pc"
    self.start = 0
    yield scrapy.Request(self.get_google_url(self.start_url + f'&start={self.start}'), method="GET", headers=self.get_headers())

  def parse(self, response):
    job_postings = response.css('li')
    if job_postings:
      for job_li_element in job_postings:
        job_detail = job_li_element.css('div.KKh3md > div')
        salary = None
        job_type = None
        posted = None
        for det in job_detail:
          extracted_text = det.css("::text").get()
          if any(chr.isdigit() for chr in extracted_text) and "£" not in extracted_text:
            extract_days = extract_days_posted(extracted_text)
            posted = translate_days_ago(extract_days) if extracted_text is not None and extract_days is not None else None
          if "£" in extracted_text:
            salary = extracted_text
          elif "Full-time" in extracted_text or "Part-time" in extracted_text \
                 or "Contractor" in extracted_text\
                  or "Internship" in extracted_text:
            job_type = extracted_text

        for span in job_detail.css('span'):
          if span.css("::attr(aria-label)").get() and 'Posted' in span.css("::attr(aria-label)").get():
            posted = parsedatetime(span.css("::text").get())
        # Extract job information and yield the job object
        job_title = job_li_element.css("div.BjJfJf.PUpOsf::text").get()
        link = job_li_element.css("g-scrolling-carousel > div > div > span > div > span > a::attr(href)").extract_first()
        job_id = f"{link}"
        source = 'googleJobs'
        # job_type = jobTypeDict[jobPosting.get('jobType', '')]
        job_location_city = job_li_element.css("div.oNwCmf > div.Qk80Jf::text").get()
        company = job_li_element.css("span.sssrNe::text").get()
        company_rating = None
        extracted_date = datetime.datetime.now().date()
        description = html2textObject.handle(job_li_element.css('span.HBvzbc').get()) if job_li_element.css('span.HBvzbc').get() else ""
        salary_range_and_unit = translate_salary_string(salary)
        salary_unit = salary_range_and_unit[2]
        salary_multiplication = 1
        if 'hour' in salary_unit:
          salary_multiplication = 2080
        if 'day' in salary_unit:
          salary_multiplication = 260
        if 'year' in salary_unit:
          salary_multiplication = 1
        if 'month' in salary_unit:
          salary_multiplication = 12
        min_salary = salary_range_and_unit[0] * salary_multiplication if salary_range_and_unit[0] is not None else None
        max_salary = salary_range_and_unit[1] * salary_multiplication if salary_range_and_unit[1] is not None else None
        # Create a dictionary representing the job object
        job_object = {
          "job_id": f"{source}{job_id}",
          "source": source,
          "jobtitle": job_title,
          "jobtype": job_type,
          "joblocationcity": job_location_city,
          "company": company,
          "companyrating": company_rating,
          "salary": salary,
          "link": link,
          "posted": posted,
          "extracteddate": extracted_date,
          "description": description,
          "min_salary": min_salary,
          "max_salary": max_salary,
          'joblocationinput': self.location
        }
        if posted is not None:
          insertJobBoardObject(job_object, getattr(self, 'jobTitleQuery'))

      if len(job_postings) > 0:
        self.start = self.start + 20
        yield scrapy.Request(self.start_url + f"&start={self.start}", method="GET", headers=self.get_headers(), callback=self.parse)


  def get_headers(self):
    headers = {
      'authority': 'www.google.com',
      'accept': '*/*',
      'accept-language': 'en-GB,en;q=0.9',
      'cookie': 'AEC=Ackid1Rr_hG8AwGT5hHHrubxwIySIbUX22MLc_eqjj59IgZFkfxNLox5SfQ; SEARCH_SAMESITE=CgQItZkB; 1P_JAR=2023-10-07-19; NID=511=iewTZmadOm9miV24iNI_O9GuEmYkZkTJ8YKMSUYDMD0_NkmyC4j8vqskbO5QZJMEyMubX3xF5YFDIkODEL0kLXP2VhIM5_1SheC4IxZl8dlCmBno63Ep9A-3HI_A9en5kReQSp14RmA45gxCsAwrTwEIkSF6rwLRYxrGbFaReJ-x_6frgyYR86sVe-QXxiQo08Vt-1zRIilzEF7qpw5SBj6MbHBlydQeamMfn2f5LOuDVxvXzJqg0RPHvnYts5CAWA; DV=s5aQa73W8EY6IEANI_VkzuAr48C6sNiTQjqbJ61vTdgAAFDfwJQCpgYXmDYAAAA; 1P_JAR=2023-10-07-19',
      'referer': 'https://www.google.com/',
      'sec-ch-ua': '"Google Chrome";v="117", "Not;A=Brand";v="8", "Chromium";v="117"',
      'sec-ch-ua-arch': '"x86"',
      'sec-ch-ua-bitness': '"64"',
      'sec-ch-ua-full-version': '"117.0.5938.149"',
      'sec-ch-ua-full-version-list': '"Google Chrome";v="117.0.5938.149", "Not;A=Brand";v="*******", "Chromium";v="117.0.5938.149"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-model': '""',
      'sec-ch-ua-platform': '"Windows"',
      'sec-ch-ua-platform-version': '"10.0.0"',
      'sec-ch-ua-wow64': '?0',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'x-client-data': 'CJW2yQEIprbJAQipncoBCPrrygEIlKHLAQib/swBCIWgzQEI3L3NAQiSys0BCLnKzQEIvNDNAQjF0c0BCPfTzQEI0tbNAQio2M0BCPnA1BUY9cnNARi60s0B'
    }
    return headers

  def get_google_url(self, url):
    payload = {
      'api_key': '1910af6c-f11b-48b5-9217-b79a9efbf8d9',
      'url': url
    }
    proxy_url = 'https://proxy.scrapeops.io/v1?' + urlencode(payload)
    return proxy_url
  def closed(self, reason):
    logging.info("Scraping completed. Restarting!")

import logging
import random
import re
from urllib.parse import urlencode
import scrapy
import sys
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from models.job_boards import insertJobBoardObject
from utils import string_contains_all_words
import html2text

html2textObject = html2text.HTML2Text()


def calculate_date_from_string(date_string):
  current_date = datetime.now()

  # Remove spaces around "x y ago"
  date_string = date_string.strip()

  # Check if the input string matches the "x y ago" format
  parts = date_string.split(" ago")
  if len(parts) == 2:
    x, y = parts[0].split()
    x = int(x) if x.isdigit() else 1
    y = y.lower().rstrip("s")

    if y == "hour":
      time_difference = relativedelta(hours=x)
      calculated_date = current_date - time_difference
      return calculated_date

    if y in ["day", "week", "month"]:
      y += "s"
      time_difference = relativedelta(**{y: x})
      calculated_date = current_date - time_difference
      return calculated_date

  # Check if the input string is "yesterday"
  if date_string.lower() == "yesterday":
    yesterday = current_date - timedelta(days=1)
    return yesterday
  if date_string.lower() == "today" or date_string.lower() == "just now":
    today = current_date
    return today

  try:
    parsed_date = datetime.strptime(date_string, "%d/%m/%Y")
    return parsed_date
  except ValueError:
    pass

  return None
def generateHeader():
  user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36'
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36'
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36'
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15'
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15'
  ]
  headers = {
    "user-agent": random.choice(user_agents)
  }
  return headers

class JobSpiderCVLibrary(scrapy.Spider):
    name = "job_spider"

    def __init__(self, *args, **kwargs):
        super(JobSpiderCVLibrary, self).__init__(*args, **kwargs)
        self.base_url = 'https://www.cv-library.co.uk'


    def start_requests(self):
        page = getattr(self, 'page', 1)
        perpage = getattr(self, 'perpage', 25)
        us = getattr(self, 'us', '1')  # Set default value to "newest"
        jobTitleQuery = getattr(self, 'jobTitleQuery')
        location = getattr(self, 'location')
        query_params = {
            'page': str(page),
            'perpage': str(perpage),
            'us': str(us),
            'order': 'date'
        }
        cvLibraryQuery = jobTitleQuery.strip().replace(' ', '-') + '-jobs'
        location = location.strip().replace(' ', '-')
        if location:
            cvLibraryQuery = cvLibraryQuery + f'-in-{location}'

        url = f"{self.base_url}/{cvLibraryQuery}?{urlencode(query_params)}"
        yield scrapy.Request(url, headers=generateHeader(), callback=self.parse)

    def closed(self, reason):
      logging.info("Scraping completed. Restarting!")
      self.start_requests()

    def parse(self, response):
        jobs = response.css('ol#searchResults > li')
        for job in jobs:
            job_id = job.css('article::attr(data-job-id)').get()
            if job_id is None:
                continue
            link = self.base_url + job.css('.job__title > a::attr(href)').get()

            job_data = {
                'job_id': 'cv-library' + job_id,
                'source': 'cv-library',
                'jobtitle': None,
                'jobtype': None,
                'joblocationcity': None,
                'company': None,
                'companyrating': None,
                'salary': None,
                'link': link,
                'posted': None,
                'extracteddate': datetime.now(),
                'description': None,
                'min_salary': None,
                'max_salary': None,
                'joblocationinput': self.location
            }
            yield response.follow(link, headers=generateHeader(), callback=self.parse_job, meta=job_data)

        next_page = response.css('a[aria-label="Next page"]::attr(href)').get()
        if next_page is not None:
            yield response.follow(self.base_url + next_page, headers=generateHeader(), callback=self.parse)

    def parse_job(self, response):
        posted = (response.css('span[data-jd-posted]::text').get() or response.css('dd[data-jd-posted] > span::text').get()).replace("\n", "").strip()
        posted_date = calculate_date_from_string(posted)
        link = response.meta['link']
        current_job_data = response.meta
        jobtitle = html2textObject.handle(response.css('h1.job__title').extract_first())
        jobtype = response.css('div.job__detail')[0].css('dd.job__details-value::text').get()
        joblocationcity = response.css("dd[data-jd-location]").css("dd::text").get() or ''
        company = response.css("span[data-jd-company]").css("a::text").get()
        # Replace <p> tags with a new line
        description = html2textObject.handle(response.css('div.job__description').extract_first())
        salary = response.css("dd[data-jd-salary]").css("dd::text").get() or ''

        if "/annum" in salary:
            salary_multiplier = 1
            salary_range = salary.split('/annum')[0].split('-')
        elif "/day" in salary:
            salary_multiplier = 260
            salary_range = salary.split('/day')[0].split('-')
        elif "/month" in salary:
            salary_multiplier = 12
            salary_range = salary.split('/month')[0].split('-')
        elif "/hour" in salary:
            salary_multiplier = 2080
            salary_range = salary.split('/hour')[0].split('-')
        elif "/week" in salary:
            salary_multiplier = 52
            salary_range = salary.split('/week')[0].split('-')
        else:
            salary_multiplier = 1
            salary_range = [salary, salary]

        if len(salary_range) < 2:
            salary_range = [salary_range[0], salary_range[0]]
        min_salary = salary_range[0].strip()
        max_salary = salary_range[1].strip()
        # convert to numeric
        min_salary = re.sub(r'[^\d.]', '', min_salary) or None
        # get 15 first characters so it won't include other number
        # this is to handle a case when salary is
        # "£40,400 - £49,991 plus an allowance of up to £5,000"
        max_salary = re.sub(r'[^\d.]', '', max_salary) or None

        if min_salary:
          min_salary = float(min_salary)*salary_multiplier
        if max_salary:
          max_salary = float(max_salary)*salary_multiplier
        amended_job_data = current_job_data
        amended_job_data['company'] = company
        amended_job_data['description'] = description
        amended_job_data['joblocationcity'] = joblocationcity
        amended_job_data['jobtitle'] = jobtitle
        amended_job_data['jobtype'] = jobtype
        amended_job_data['posted'] = posted_date
        amended_job_data['salary'] = salary
        amended_job_data['min_salary'] = min_salary
        amended_job_data['max_salary'] = max_salary
        del amended_job_data['depth']
        del amended_job_data['download_timeout']
        del amended_job_data['download_slot']
        del amended_job_data['download_latency']
        insertJobBoardObject(amended_job_data, getattr(self, 'jobTitleQuery'))
        yield amended_job_data

    def closed(self, reason):
        logging.info("Scraping completed!")



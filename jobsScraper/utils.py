import re

def string_contains_all_words(string, words):
    # Remove punctuation from the string

    string = re.sub(r'[^\w\s]', '', string)

    # Remove punctuation from the words
    words = re.sub(r'[^\w\s]', '', words)

    # Convert strings to lowercase
    string = string.lower()
    words = words.lower()

    # Split the second string into individual words
    word_list = words.split()

    # Check if each word is present in the first string
    for word in word_list:
        if word not in string:
            return False

    return True

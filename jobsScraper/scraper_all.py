import itertools
import sys
import time

from scrapy.crawler import <PERSON><PERSON>lerPro<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ner
from scrapy.utils.project import get_project_settings
import logging

from twisted.internet import task, reactor

from scraper_cvlibrary import JobSpiderCVLibrary
from scraper_glassdoor import jobScraperGlassdoor, execute247glassdoorScraper
from scraper_googlejobs import JobSpiderGoogleJobs
from scraper_indeed import JobSpiderIndeed
from scraper_linkedin import JobSpiderLinkedIn
from scraper_monsterjobs import JobSpiderMonsterJobs
from scraper_reed import JobSpider<PERSON><PERSON>
from scraper_technojobs import JobSpiderTechnojobs
from scraper_totaljobs import JobSpiderTotalJobs

logging.basicConfig(level=logging.ERROR)

jobBoardsDict = {
  "linkedin": JobSpiderLinkedIn,
  "indeed": JobSpiderIndeed,
  "monster": JobSpiderMonsterJobs,
  "reed": JobSpiderReed,
  "technojobs": JobSpiderTechnojobs,
  "totaljobs": <PERSON>SpiderTotalJobs,
  "cv-library": <PERSON>SpiderCV<PERSON>ib<PERSON>,
  "googleJobs": JobSpiderGoogleJobs
}
jobBoardsNonScrapy = {
  "glassdoor": execute247glassdoorScraper
}
if __name__ == "__main__":
    # Get the command-line arguments
    keywords = None
    location = 'Tyne And Wear'
    jobTitle = None
    jobBoards = None
    # Parse the command-line arguments
    for arg in sys.argv[1:]:
        if arg.startswith("keywords="):
            keywords = arg.split("=")[1]
        if arg.startswith("location="):
            location = arg.split("=")[1]
        if arg.startswith("jobTitleQuery="):
            jobTitle = arg.split("=")[1]
        if arg.startswith("jobBoards="):
            jobBoards = arg.split("=")[1]

    # Check if both keywords and location are provided
    if jobTitle is None:
        logging.info("Error: Please provide 'jobTitle' arguments.", file=sys.stderr)
        sys.exit(1)

    timeout = 60
    def run_spider():
      l.stop()
      runner = CrawlerRunner({
        'PROXY_USERNAME': 'gwfhqhvi-GB-rotate',
        'PROXY_PASSWORD': 'pkq3qvhg2g6b',
        'DOWNLOADER_MIDDLEWARES': {
          'middlewares.ProxyMiddleware.ProxyMiddleware': 1,
        },
        'DOWNLOAD_DELAY': 5,
        'PROXY_LIST': [
          'http://p.webshare.io:80',
        ],
        'RETRY_TIMES': 10,
        'RETRY_HTTP_CODES': [500, 502, 503, 504, 522, 524, 408, 429, 403]
      })
      spider_args = {
        'jobTitleQuery': jobTitle,
        'keywords': keywords or '',
        'location': location or ''
      }
      # Start the JobSpider with the provided arguments
      jobBoardListToScrape = []
      if jobBoards:
        jobBoardListToScrape = jobBoards.split(",")
      if len(jobBoardListToScrape) == 0:
        jobBoardListToScrape = jobBoardsDict.keys()

      for jobBoard in jobBoardListToScrape:
        if jobBoardsDict.get(jobBoard):
          d = runner.crawl(jobBoardsDict[jobBoard], **spider_args)
        else:
          jobBoardsNonScrapy[jobBoard](jobTitle, location)
      d.addBoth(lambda _: l.start(timeout, False))
    l = task.LoopingCall(run_spider)
    l.start(timeout)
    reactor.run()


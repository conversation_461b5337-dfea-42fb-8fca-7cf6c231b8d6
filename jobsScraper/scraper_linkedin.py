import logging
import random
import re
import json
import uuid
from datetime import datetime, timedelta
import re

import html2text
import scrapy
from urllib.parse import urlencode

from models.job_boards import insertJobBoardObject

html2textObject = html2text.HTML2Text()

# to extract number of date from "Posted x days ago"
def extract_days_posted(text):
    if "today" in text.lower() or "just" in text.lower():
        return 0
    match = re.search(r'\b(\d+)\b', text)
    if match:
        days = int(match.group())
        return days
    return None

def translate_days_ago(days_ago):
    # Calculate the date x days ago
    date_ago = datetime.now() - timedelta(days=days_ago)
    return date_ago


def translate_salary_string(salary_string):
    if salary_string is None:
        return [None, None, '']

    # Define a mapping for the abbreviations
    def extract_number(string):
        pattern = r"\£([\d.,]+)([KM]?)"
        translation = {'K': 1000, 'M': 1000000}

        match = re.search(pattern, string)
        if match:
            number_str = match.group(1)
            suffix = match.group(2)

            # Remove commas from the number string and convert it to a float
            number = float(number_str.replace(',', ''))

            # Apply the translation if a suffix is present
            if suffix in translation:
                number *= translation[suffix]

            return number
    result = []
    words = salary_string.split(" ")
    containsDigitPattern = r"\d"
    for word in words:
        if bool(re.search(containsDigitPattern, word)):
            result.append(extract_number(word))
    if len(result) == 1:
        result.append(result[0])
    result.append(words[len(words) - 1])
    return result


class JobSpiderLinkedIn(scrapy.Spider):
    name = "linkedin_jobs"

    def __init__(self, *args, **kwargs):
        super(JobSpiderLinkedIn, self).__init__(*args, **kwargs)
        self.start = 0

    def start_requests(self):
        jobTitleQuery = getattr(self, 'jobTitleQuery')
        location = getattr(self, 'location')
        parameters = {"keywords": jobTitleQuery, "location": 'United Kingdom' if not location else location + ', United Kingdom', 'start': self.start, 'f_TPR': 'r2592000'}
        url = f"{self.get_linkedin_search_url('https://uk.linkedin.com/jobs-guest/jobs/api/seeMoreJobPostings/search?' + urlencode(parameters))}"
        yield scrapy.Request(url, callback=self.parse)

    def closed(self, reason):
      logging.info("Scraping completed. Restarting!")
      self.start_requests()

    def parse(self, response):
        jobs = response.css('li')
        for job in jobs:
            job_id = job.css('div::attr(data-entity-urn)').get()
            if job_id is None:
                continue
            title = job.css('h3.base-search-card__title::text').get()
            location = job.css('span.job-search-card__location::text').get()
            salary = job.css('span.job-search-card__salary-info::text').get()
            link = job.css('div > a::attr(href)').get()
            posted = job.css('div.base-search-card__metadata > time::attr(datetime)').get()
            company = job.css('h4.base-search-card__subtitle > a::text').get()
            # Extract min and max salary values if available

            min_salary = None
            max_salary = None
            if salary:
                min_max = salary.replace('£', '').replace('\n','').replace(',','').strip().split("-")
                if len(min_max) == 2:
                    min_salary = float(min_max[0])
                    max_salary = float(min_max[1])

            job_data = {
                'job_id': 'linkedin' + job_id,
                'source': 'linkedin',
                'jobtitle': title,
                'jobtype': None,
                'joblocationcity': location,
                'company': company,
                'companyrating': None,
                'salary': salary,
                'link': link,
                'posted': datetime.strptime(posted, "%Y-%m-%d"),
                'extracteddate': datetime.now(),
                'description': None,
                'min_salary': min_salary,
                'max_salary': max_salary,
                'joblocationinput': self.location
            }
            yield response.follow(self.get_linkedin_search_url(link), callback=self.parse_job, meta=job_data)

        self.start = self.start + 25
        parameters = {"keywords": self.jobTitleQuery, "location": self.location + ', United Kingdom', 'start': self.start}
        next_page_url = f"{self.get_linkedin_search_url('https://uk.linkedin.com/jobs-guest/jobs/api/seeMoreJobPostings/search?' + urlencode(parameters))}"
        if next_page_url is not None:
            yield response.follow(self.get_linkedin_search_url(next_page_url), callback=self.parse)


    def parse_job(self, response):
        current_job_data = response.meta
        try:
            job_description_criteria_list_elements = response.css("ul.description__job-criteria-list > li")
            job_type = job_description_criteria_list_elements[1].css("span::text").get() if len(job_description_criteria_list_elements) > 1 else ''
        except Exception:
            job_type = None
        # Replace <p> tags with a new line
        description = html2textObject.handle(response.css('div.description__text').extract_first())
        amended_job_data = current_job_data
        amended_job_data['description'] = description
        amended_job_data['jobtype'] = job_type
        insertJobBoardObject(amended_job_data, getattr(self, 'jobTitleQuery'))
        yield amended_job_data

    def closed(self, reason):
        logging.info("Scraping completed!")

    def get_linkedin_search_url(self, url):
        return url

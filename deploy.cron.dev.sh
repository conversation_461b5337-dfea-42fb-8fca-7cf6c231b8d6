#!/bin/bash

git checkout .
git add .
git stash
git reset --hard HEAD~1
git pull origin

# Get current git commit hash
REVISION=$(git rev-parse HEAD)
echo "Git commit hash: $REVISION"

# Build with revision
docker build --build-arg NODE_ENV=dev --build-arg BUILD_REVISION=$REVISION -t zileo-dev-cron . -f Dockerfile.cron
docker rm -f zileo-dev-cron
docker run -d -it --log-driver=awslogs --log-opt awslogs-region=eu-west-2 --log-opt awslogs-group=zileo-dev-cron --log-opt awslogs-create-group=true --restart=always --name zileo-dev-cron zileo-dev-cron
docker logs zileo-dev-cron --tail 100

#!/bin/bash

git config --global --add safe.directory '*'

cd job-finder-backend
git checkout dev
git -c credential.helper='!f() { echo "username=eddieGong2604"; echo "password=**************************"; }; f' pull
npm install --legacy-peer-deps
npm run build:dev

pm2 delete app-be-dev
NODE_OPTIONS="--max-old-space-size=8192" APP_PORT=80 NODE_ENV=dev pm2 start dist/main.js --name "app-be-dev" -- --port 80

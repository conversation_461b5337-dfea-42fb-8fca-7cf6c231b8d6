# Use node 20
FROM node:20.18.2-bookworm-slim AS builder
WORKDIR /app
# Define build-time arguments with default values
ARG NODE_ENV=staging
ARG PORT=3000
ARG BUILD_REVISION=unknown

# Set environment variables using the build-time arguments
ENV NODE_ENV=$NODE_ENV
ENV PORT=$PORT
ENV BUILD_REVISION=$BUILD_REVISION

COPY . ./
COPY package-lock-stable.json package-lock.json
COPY .env.${NODE_ENV} ./.env
COPY .env.${NODE_ENV} ./.env.${NODE_ENV}

# Save build revision to a file
RUN echo $BUILD_REVISION > src/revision.txt

RUN --mount=type=cache,target=/root/.npm npm i --legacy-peer-deps
RUN sh -c "npm run build:${NODE_ENV}"

# Run
ENV NODE_ENV=$NODE_ENV
ENV PORT=$PORT
ENV BUILD_REVISION=$BUILD_REVISION

USER node
CMD [ "node", "dist/cron.js" ]
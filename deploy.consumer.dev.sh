#!/bin/bash

git checkout .
git add .
git stash
git reset --hard HEAD~1
git pull origin
docker build --build-arg NODE_ENV=dev -t zileo-dev-consumer . -f Dockerfile.consumer
docker rm -f zileo-dev-consumer
docker run -d -it --log-driver=awslogs --log-opt awslogs-region=eu-west-2 --log-opt awslogs-group=zileo-dev-consumer --log-opt awslogs-create-group=true --restart=always --name zileo-dev-consumer zileo-dev-consumer
docker logs zileo-dev-consumer --tail 100

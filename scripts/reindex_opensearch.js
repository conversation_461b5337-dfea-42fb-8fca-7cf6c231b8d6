#!/usr/bin/env node

/**
 * Script to reindex data from one OpenSearch cluster to another.
 * This script supports reindexing specific indices or all indices.
 *
 * Usage:
 *   node reindex_opensearch.js --source-host SOURCE_HOST --source-user SOURCE_USER --source-password SOURCE_PASSWORD
 *                             --target-host TARGET_HOST --target-user TARGET_USER --target-password TARGET_PASSWORD
 *                             [--indices INDEX1,INDEX2,...] [--batch-size BATCH_SIZE] [--scroll-time SCROLL_TIME]
 *                             [--concurrency CONCURRENCY] [--dry-run]
 *
 * Example:
 *   node reindex_opensearch.js --source-host http://source-opensearch:9200 --source-user admin --source-password password
 *                             --target-host http://target-opensearch:9200 --target-user admin --target-password password
 *                             --indices job-leads,crm-contacts --batch-size 1000 --concurrency 4
 */

const { Client } = require('@opensearch-project/opensearch');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const fs = require('fs');
const path = require('path');
const pLimit = require('p-limit');
const ora = require('ora');
const chalk = require('chalk');
const Table = require('cli-table3');

// Define indices configuration
const INDICES_CONFIG = {
  // Location related indices
  'location_mappings': {
    description: 'Location mappings data',
    priority: 1
  },
  'cached_locations': {
    description: 'Cached locations data',
    priority: 2
  },
  'unknown_location_jobs': {
    description: 'Unknown location jobs data',
    priority: 3
  },

  // Agency related indices
  'reported_agency_country': {
    description: 'Reported agency country data',
    priority: 1
  },

  // Jobs indices
  'jobs': {
    description: 'Jobs data',
    priority: 1
  }
};

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('source-host', {
    description: 'Source OpenSearch host URL',
    type: 'string',
    demandOption: true
  })
  .option('source-user', {
    description: 'Source OpenSearch username',
    type: 'string',
    demandOption: true
  })
  .option('source-password', {
    description: 'Source OpenSearch password',
    type: 'string',
    demandOption: true
  })
  .option('target-host', {
    description: 'Target OpenSearch host URL',
    type: 'string',
    demandOption: true
  })
  .option('target-user', {
    description: 'Target OpenSearch username',
    type: 'string',
    demandOption: true
  })
  .option('target-password', {
    description: 'Target OpenSearch password',
    type: 'string',
    demandOption: true
  })
  .option('indices', {
    description: 'Comma-separated list of indices to reindex',
    type: 'string'
  })
  .option('batch-size', {
    description: 'Batch size for reindexing (number of documents per batch)',
    type: 'number',
    default: 250
  })
  .option('max-batch-mb', {
    description: 'Maximum batch size in MB (to prevent "Request size exceeded" errors)',
    type: 'number',
    default: 10
  })
  .option('skip-large-docs', {
    description: 'Skip documents larger than max-batch-mb instead of failing',
    type: 'boolean',
    default: false
  })
  .option('increase-http-max-size', {
    description: 'Instructions to increase HTTP max size on target OpenSearch',
    type: 'boolean',
    default: false
  })
  .option('scroll-time', {
    description: 'Scroll time for reindexing',
    type: 'string',
    default: '5m'
  })
  .option('concurrency', {
    description: 'Number of concurrent reindex operations',
    type: 'number',
    default: 4
  })
  .option('dry-run', {
    description: 'Dry run mode (no actual reindexing)',
    type: 'boolean',
    default: false
  })
  .option('list-indices', {
    description: 'List available indices and exit',
    type: 'boolean',
    default: false
  })
  .option('inspect-document', {
    description: 'Inspect a document from the specified index before reindexing',
    type: 'boolean',
    default: false
  })
  .option('document-id', {
    description: 'Document ID to inspect (used with --inspect-document)',
    type: 'string'
  })
  .help()
  .alias('help', 'h')
  .argv;

// Create OpenSearch clients
function createOpenSearchClient(host, username, password) {
  const node = host;

  return new Client({
    node,
    auth: {
      username,
      password
    },
    ssl: {
      rejectUnauthorized: false
    },
    requestTimeout: 60000
  });
}

// Get list of indices
async function getIndices(client, specifiedIndices = null) {
  try {
    if (specifiedIndices) {
      const existingIndices = [];
      for (const index of specifiedIndices) {
        const exists = await client.indices.exists({ index });
        if (exists.body) {
          existingIndices.push(index);
        } else {
          console.warn(chalk.yellow(`Index ${index} does not exist in source cluster`));
        }
      }
      return existingIndices;
    } else {
      const response = await client.cat.indices({ format: 'json' });
      return response.body
        .map(index => index.index)
        .filter(index => !index.startsWith('.'));
    }
  } catch (error) {
    console.error(chalk.red(`Error getting indices: ${error.message}`));
    return [];
  }
}

// Get index settings and mappings
async function getIndexSettingsAndMappings(client, index) {
  try {
    const response = await client.indices.get({ index });
    const settings = response.body[index].settings;
    const mappings = response.body[index].mappings;

    // Remove settings that can't be applied when creating an index
    if (settings.index) {
      ['uuid', 'creation_date', 'version', 'provided_name'].forEach(key => {
        if (settings.index[key]) {
          delete settings.index[key];
        }
      });
    }

    return {
      settings,
      mappings
    };
  } catch (error) {
    console.error(chalk.red(`Error getting settings and mappings for index ${index}: ${error.message}`));
    return null;
  }
}

// Create target index
async function createTargetIndex(client, index, indexConfig) {
  try {
    const exists = await client.indices.exists({ index });
    if (exists.body) {
      console.warn(chalk.yellow(`Index ${index} already exists in target cluster. Skipping creation.`));
      return true;
    }

    await client.indices.create({
      index,
      body: indexConfig
    });

    console.log(chalk.green(`Created index ${index} in target cluster`));
    return true;
  } catch (error) {
    console.error(chalk.red(`Error creating index ${index} in target cluster: ${error.message}`));
    return false;
  }
}

// Ensure logs directory exists
function ensureLogsDirectory() {
  const logsDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  return logsDir;
}

// Get log file path
function getLogFilePath(filename) {
  const logsDir = ensureLogsDirectory();
  return path.join(logsDir, filename);
}

// Estimate size of a document in bytes
function estimateDocSize(doc) {
  return Buffer.byteLength(JSON.stringify(doc));
}

// Reindex data
async function reindexData(sourceClient, targetClient, index, batchSize, maxBatchMB, scrollTime, skipLargeDocs) {
  try {
    // Get document count
    const countResponse = await sourceClient.count({ index });
    const count = countResponse.body.count;
    console.log(chalk.blue(`Reindexing ${count} documents from index ${index}`));

    // Initialize counters
    let processed = 0;
    let failed = 0;
    const startTime = Date.now();

    // Use the scroll API to efficiently transfer data
    let response = await sourceClient.search({
      index,
      scroll: scrollTime,
      size: batchSize,
      body: {
        query: {
          match_all: {}
        }
      }
    });

    let scrollId = response.body._scroll_id;
    let hits = response.body.hits.hits;

    const spinner = ora(`Reindexing ${index}: 0/${count} documents`).start();

    while (hits && hits.length > 0) {
      // Prepare bulk request
      const operations = [];
      let currentBatchSize = 0;
      const maxBatchSizeBytes = maxBatchMB * 1024 * 1024; // Convert MB to bytes

      for (const hit of hits) {
        // Estimate the size of this document
        const docOperation = {
          index: {
            _index: index,
            _id: hit._id
          }
        };
        const docSize = estimateDocSize(docOperation) + estimateDocSize(hit._source);

        // Check if this single document is larger than the max batch size
        if (docSize > maxBatchSizeBytes) {
          if (skipLargeDocs) {
            // Skip this document and log it
            console.warn(chalk.yellow(`Skipping large document with ID ${hit._id} (size: ${(docSize / (1024 * 1024)).toFixed(2)}MB)`));

            // Create a detailed error log file for this index if it doesn't exist
            const errorLogFile = getLogFilePath(`${index}_large_docs_log.json`);
            let errorLog = [];
            if (fs.existsSync(errorLogFile)) {
              try {
                errorLog = JSON.parse(fs.readFileSync(errorLogFile, 'utf8'));
              } catch (e) {
                errorLog = [];
              }
            }

            // Add to error log file
            errorLog.push({
              document_id: hit._id,
              size_mb: (docSize / (1024 * 1024)).toFixed(2),
              timestamp: new Date().toISOString()
            });

            // Write error log to file
            fs.writeFileSync(errorLogFile, JSON.stringify(errorLog, null, 2));

            continue; // Skip to next document
          } else {
            // Warn about large document
            console.warn(chalk.yellow(`Document with ID ${hit._id} is too large (${(docSize / (1024 * 1024)).toFixed(2)}MB). Consider using --skip-large-docs or --increase-http-max-size.`));
          }
        }

        // Check if adding this document would exceed the max batch size
        if (currentBatchSize + docSize > maxBatchSizeBytes && operations.length > 0) {
          // Process the current batch before adding this document
          break;
        }

        // Add document to batch
        operations.push(docOperation);
        operations.push(hit._source);
        currentBatchSize += docSize;
      }

      if (operations.length > 0) {
        try {
          const bulkResponse = await targetClient.bulk({ body: operations });

          // Count successful and failed operations
          if (bulkResponse.body.errors) {
            // Create a detailed error log file for this index if it doesn't exist
            const errorLogFile = getLogFilePath(`${index}_error_log.json`);
            let errorLog = [];
            if (fs.existsSync(errorLogFile)) {
              try {
                errorLog = JSON.parse(fs.readFileSync(errorLogFile, 'utf8'));
              } catch (e) {
                errorLog = [];
              }
            }

            for (const item of bulkResponse.body.items) {
              if (item.index && item.index.status >= 200 && item.index.status < 300) {
                processed++;
              } else {
                // Log detailed error information
                const errorDetail = item.index?.error || { reason: 'Unknown error' };
                const docId = item.index?._id || 'unknown';

                // Add to error log file
                errorLog.push({
                  document_id: docId,
                  error: errorDetail,
                  timestamp: new Date().toISOString()
                });

                // Print error to console
                console.error(chalk.red(`Document ID ${docId} error: ${errorDetail.type || 'Unknown'} - ${errorDetail.reason || 'No reason provided'}`));
                if (errorDetail.caused_by) {
                  console.error(chalk.red(`  Caused by: ${errorDetail.caused_by.type} - ${errorDetail.caused_by.reason}`));
                }

                failed++;
              }
            }

            // Write error log to file
            fs.writeFileSync(errorLogFile, JSON.stringify(errorLog, null, 2));
          } else {
            processed += operations.length / 2;
          }

          // Update spinner
          const elapsed = (Date.now() - startTime) / 1000;
          const docsPerSecond = processed / elapsed;
          spinner.text = `Reindexing ${index}: ${processed}/${count} documents (${docsPerSecond.toFixed(2)} docs/sec)`;
        } catch (error) {
          console.error(chalk.red(`Error in bulk operation for index ${index}: ${error.message}`));

          // Log detailed error information if available
          if (error.meta && error.meta.body) {
            console.error(chalk.red(`Error details: ${JSON.stringify(error.meta.body, null, 2)}`));

            // Create error log file
            const errorLogFile = getLogFilePath(`${index}_error_log.json`);
            let errorLog = [];
            if (fs.existsSync(errorLogFile)) {
              try {
                errorLog = JSON.parse(fs.readFileSync(errorLogFile, 'utf8'));
              } catch (e) {
                errorLog = [];
              }
            }

            errorLog.push({
              error_type: 'bulk_operation_error',
              error: error.message,
              details: error.meta.body,
              timestamp: new Date().toISOString()
            });

            fs.writeFileSync(errorLogFile, JSON.stringify(errorLog, null, 2));
          } else if (error.stack) {
            console.error(chalk.red(`Error stack: ${error.stack}`));
          }

          failed += operations.length / 2;
        }
      }

      // Get next batch
      response = await sourceClient.scroll({
        scroll_id: scrollId,
        scroll: scrollTime
      });

      scrollId = response.body._scroll_id;
      hits = response.body.hits.hits;
    }

    // Clear scroll
    await sourceClient.clearScroll({
      scroll_id: scrollId
    });

    // Log final stats
    const elapsed = (Date.now() - startTime) / 1000;
    const docsPerSecond = processed / elapsed;

    spinner.succeed(`Index ${index}: Completed. Processed ${processed}/${count} documents in ${elapsed.toFixed(2)}s (${docsPerSecond.toFixed(2)} docs/sec)`);

    if (failed > 0) {
      console.warn(chalk.yellow(`Index ${index}: Failed documents: ${failed}`));
    }

    return {
      index,
      total: count,
      processed,
      failed,
      elapsed_seconds: elapsed
    };
  } catch (error) {
    console.error(chalk.red(`Error reindexing data for index ${index}: ${error.message}`));

    // Log detailed error information if available
    if (error.meta && error.meta.body) {
      console.error(chalk.red(`Error details: ${JSON.stringify(error.meta.body, null, 2)}`));
    } else if (error.stack) {
      console.error(chalk.red(`Error stack: ${error.stack}`));
    }

    // Create error log file
    const errorLogFile = getLogFilePath(`${index}_error_log.json`);
    const errorLog = [{
      error_type: 'reindex_operation_error',
      error: error.message,
      details: error.meta?.body || {},
      stack: error.stack,
      timestamp: new Date().toISOString()
    }];

    fs.writeFileSync(errorLogFile, JSON.stringify(errorLog, null, 2));

    return {
      index,
      error: error.message,
      processed: 0,
      failed: 0,
      status: 'failed',
      reason: error.message
    };
  }
}

// Worker function for reindexing
async function reindexWorker(sourceClient, targetClient, index, batchSize, maxBatchMB, scrollTime, skipLargeDocs, dryRun) {
  // Get index configuration
  const indexConfig = await getIndexSettingsAndMappings(sourceClient, index);
  if (!indexConfig) {
    return { index, status: 'failed', reason: 'Could not get index configuration' };
  }

  // Create target index
  if (!dryRun) {
    if (!await createTargetIndex(targetClient, index, indexConfig)) {
      return { index, status: 'failed', reason: 'Could not create target index' };
    }

    // Reindex data
    const result = await reindexData(sourceClient, targetClient, index, batchSize, maxBatchMB, scrollTime, skipLargeDocs);
    result.status = 'completed';
    return result;
  } else {
    console.log(chalk.blue(`[DRY RUN] Would reindex ${index}`));
    return { index, status: 'skipped', reason: 'dry run' };
  }
}

// Inspect a document from an index
async function inspectDocument(client, index, documentId) {
  try {
    let response;

    if (documentId) {
      // Get specific document by ID
      response = await client.get({
        index,
        id: documentId
      });

      console.log(chalk.bold(`\nDocument ID: ${documentId} from index: ${index}`));
      console.log(JSON.stringify(response.body, null, 2));
    } else {
      // Get a random document from the index
      response = await client.search({
        index,
        size: 1
      });

      if (response.body.hits.hits.length > 0) {
        const doc = response.body.hits.hits[0];
        console.log(chalk.bold(`\nRandom document from index: ${index}`));
        console.log(chalk.bold(`Document ID: ${doc._id}`));
        console.log(JSON.stringify(doc, null, 2));
      } else {
        console.log(chalk.yellow(`No documents found in index: ${index}`));
      }
    }

    // Get index mapping
    const mappingResponse = await client.indices.getMapping({
      index
    });

    console.log(chalk.bold(`\nIndex mapping for: ${index}`));
    console.log(JSON.stringify(mappingResponse.body[index].mappings, null, 2));

  } catch (error) {
    console.error(chalk.red(`Error inspecting document: ${error.message}`));
    if (error.meta && error.meta.body) {
      console.error(chalk.red(`Error details: ${JSON.stringify(error.meta.body, null, 2)}`));
    }
  }
}

// Display instructions to increase HTTP max size
function displayHttpMaxSizeInstructions() {
  console.log(chalk.bold('\nInstructions to increase HTTP max size on OpenSearch:'));
  console.log(chalk.cyan('\n1. Edit opensearch.yml on all nodes in the target cluster:'));
  console.log('```');
  console.log('# Increase HTTP max content length (default is 100mb)');
  console.log('http.max_content_length: 500mb');
  console.log('');
  console.log('# Increase max request size (default is 10mb)');
  console.log('http.max_initial_line_length: 50mb');
  console.log('http.max_header_size: 50mb');
  console.log('```');

  console.log(chalk.cyan('\n2. Restart OpenSearch on all nodes:'));
  console.log('```');
  console.log('sudo systemctl restart opensearch');
  console.log('# or');
  console.log('sudo service opensearch restart');
  console.log('```');

  console.log(chalk.cyan('\n3. For AWS OpenSearch Service:'));
  console.log('- Go to the AWS OpenSearch Service console');
  console.log('- Select your domain');
  console.log('- Go to "Actions" > "Edit cluster configuration"');
  console.log('- Under "Advanced cluster settings", add the following JSON:');
  console.log('```');
  console.log('{');
  console.log('  "http.max_content_length": "500mb",');
  console.log('  "http.max_initial_line_length": "50mb",');
  console.log('  "http.max_header_size": "50mb"');
  console.log('}');
  console.log('```');

  console.log(chalk.yellow('\nNote: After increasing these limits, you may need to restart your reindex operation.'));
}

// Display available indices
function displayAvailableIndices() {
  const table = new Table({
    head: [chalk.cyan('Index Name'), chalk.cyan('Description'), chalk.cyan('Priority')],
    colWidths: [30, 50, 10]
  });

  Object.entries(INDICES_CONFIG)
    .sort((a, b) => a[1].priority - b[1].priority)
    .forEach(([index, config]) => {
      table.push([index, config.description, config.priority]);
    });

  console.log(chalk.bold('\nAvailable Indices:'));
  console.log(table.toString());
  console.log('\nUse --indices option to specify which indices to reindex (comma-separated)');
}

// Main function
async function main() {
  // If list-indices flag is set, display available indices and exit
  if (argv.listIndices) {
    displayAvailableIndices();
    return;
  }

  // If increase-http-max-size flag is set, display instructions and exit
  if (argv.increaseHttpMaxSize) {
    displayHttpMaxSizeInstructions();
    return;
  }

  // Create source client for inspection or reindexing
  const sourceClient = createOpenSearchClient(argv.sourceHost, argv.sourceUser, argv.sourcePassword);

  // If inspect-document flag is set, inspect a document and exit
  if (argv.inspectDocument) {
    if (!argv.indices) {
      console.error(chalk.red('Error: --indices option is required with --inspect-document'));
      process.exit(1);
    }

    const indexToInspect = argv.indices.split(',')[0]; // Use the first index if multiple are specified
    await inspectDocument(sourceClient, indexToInspect, argv.documentId);
    return;
  }

  // Create target client for reindexing
  const targetClient = createOpenSearchClient(argv.targetHost, argv.targetUser, argv.targetPassword);

  // Check connections
  try {
    const sourceInfo = await sourceClient.info();
    console.log(chalk.green(`Connected to source OpenSearch cluster: ${sourceInfo.body.version.distribution} ${sourceInfo.body.version.number}`));
  } catch (error) {
    console.error(chalk.red(`Could not connect to source OpenSearch cluster: ${error.message}`));
    process.exit(1);
  }

  try {
    const targetInfo = await targetClient.info();
    console.log(chalk.green(`Connected to target OpenSearch cluster: ${targetInfo.body.version.distribution} ${targetInfo.body.version.number}`));
  } catch (error) {
    console.error(chalk.red(`Could not connect to target OpenSearch cluster: ${error.message}`));
    process.exit(1);
  }

  // Get indices to reindex
  const indicesToReindex = argv.indices ? argv.indices.split(',') : null;
  const indices = await getIndices(sourceClient, indicesToReindex);

  if (!indices.length) {
    console.error(chalk.red('No indices to reindex'));
    process.exit(1);
  }

  console.log(chalk.green(`Found ${indices.length} indices to reindex: ${indices.join(', ')}`));

  // Sort indices by priority if defined in INDICES_CONFIG
  const sortedIndices = indices.sort((a, b) => {
    const priorityA = INDICES_CONFIG[a]?.priority || 999;
    const priorityB = INDICES_CONFIG[b]?.priority || 999;
    return priorityA - priorityB;
  });

  // Reindex data
  const startTime = Date.now();
  const results = [];

  // Use p-limit to limit concurrency
  const limit = pLimit(argv.concurrency);

  const promises = sortedIndices.map(index =>
    limit(() => reindexWorker(sourceClient, targetClient, index, argv.batchSize, argv.maxBatchMb, argv.scrollTime, argv.skipLargeDocs, argv.dryRun))
  );

  for (const result of await Promise.all(promises)) {
    results.push(result);
  }

  // Print summary
  const totalElapsed = (Date.now() - startTime) / 1000;
  console.log(chalk.green(`\nReindexing completed in ${totalElapsed.toFixed(2)} seconds`));

  const completed = results.filter(r => r.status === 'completed');
  const failed = results.filter(r => r.status === 'failed');
  const skipped = results.filter(r => r.status === 'skipped');

  console.log(chalk.bold(`Summary: ${completed.length} indices completed, ${failed.length} failed, ${skipped.length} skipped`));

  if (failed.length) {
    console.warn(chalk.yellow('Failed indices:'));
    for (const result of failed) {
      console.warn(chalk.yellow(`  ${result.index}: ${result.reason || 'Unknown error'}`));
    }
  }

  // Create summary table
  const table = new Table({
    head: [chalk.cyan('Index'), chalk.cyan('Status'), chalk.cyan('Documents'), chalk.cyan('Processed'), chalk.cyan('Failed'), chalk.cyan('Time (s)')],
    colWidths: [30, 15, 15, 15, 15, 15]
  });

  for (const result of results) {
    table.push([
      result.index,
      result.status === 'completed' ? chalk.green(result.status) :
        result.status === 'failed' ? chalk.red(result.status) : chalk.yellow(result.status),
      result.total || '-',
      result.processed || '-',
      result.failed || '-',
      result.elapsed_seconds ? result.elapsed_seconds.toFixed(2) : '-'
    ]);
  }

  console.log(table.toString());

  // Write detailed report to file
  const report = {
    summary: {
      total_indices: indices.length,
      completed: completed.length,
      failed: failed.length,
      skipped: skipped.length,
      total_elapsed_seconds: totalElapsed
    },
    indices: results
  };

  const reportFilePath = getLogFilePath('reindex_report.json');
  fs.writeFileSync(reportFilePath, JSON.stringify(report, null, 2));
  console.log(chalk.green(`Detailed report written to ${reportFilePath}`));
}

// Run main function
main().catch(error => {
  console.error(chalk.red(`Unhandled error: ${error.message}`));
  process.exit(1);
});

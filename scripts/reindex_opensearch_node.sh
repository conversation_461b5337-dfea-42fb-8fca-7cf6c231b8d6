#!/bin/bash

# Script to reindex data from one OpenSearch cluster to another using Node.js
# This script is a wrapper around the reindex_opensearch.js script

# Default values
SOURCE_HOST=${SOURCE_HOST:-"http://**************:9200"}
SOURCE_USER=${SOURCE_USER:-"admin"}
SOURCE_PASSWORD=${SOURCE_PASSWORD:-"Zileo@2024"}
TARGET_HOST=${TARGET_HOST:-""}
TARGET_USER=${TARGET_USER:-"admin"}
TARGET_PASSWORD=${TARGET_PASSWORD:-""}
INDICES=${INDICES:-""}
BATCH_SIZE=${BATCH_SIZE:-100}
MAX_BATCH_MB=${MAX_BATCH_MB:-5}
SKIP_LARGE_DOCS=${SKIP_LARGE_DOCS:-false}
INCREASE_HTTP_MAX_SIZE=${INCREASE_HTTP_MAX_SIZE:-false}
SCROLL_TIME=${SCROLL_TIME:-"5m"}
CONCURRENCY=${CONCURRENCY:-4}
DRY_RUN=${DRY_RUN:-false}
LIST_INDICES=${LIST_INDICES:-false}
INSPECT_DOCUMENT=${INSPECT_DOCUMENT:-false}
DOCUMENT_ID=${DOCUMENT_ID:-""}

# Display help message
function show_help {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --source-host HOST       Source OpenSearch host URL (default: $SOURCE_HOST)"
    echo "  --source-user USER       Source OpenSearch username (default: $SOURCE_USER)"
    echo "  --source-password PASS   Source OpenSearch password"
    echo "  --target-host HOST       Target OpenSearch host URL (required)"
    echo "  --target-user USER       Target OpenSearch username (default: $TARGET_USER)"
    echo "  --target-password PASS   Target OpenSearch password (required)"
    echo "  --indices INDICES        Comma-separated list of indices to reindex (default: all indices)"
    echo "  --batch-size SIZE        Batch size for reindexing (default: $BATCH_SIZE)"
    echo "  --max-batch-mb SIZE      Maximum batch size in MB (default: $MAX_BATCH_MB)"
    echo "  --skip-large-docs        Skip documents larger than max-batch-mb instead of failing (default: $SKIP_LARGE_DOCS)"
    echo "  --increase-http-max-size Show instructions to increase HTTP max size on target OpenSearch"
    echo "  --scroll-time TIME       Scroll time for reindexing (default: $SCROLL_TIME)"
    echo "  --concurrency NUM        Number of concurrent reindex operations (default: $CONCURRENCY)"
    echo "  --dry-run                Dry run mode (no actual reindexing)"
    echo "  --list-indices           List available indices and exit"
    echo "  --inspect-document       Inspect a document from the specified index before reindexing"
    echo "  --document-id ID         Document ID to inspect (used with --inspect-document)"
    echo "  --help                   Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --target-host http://new-opensearch:9200 --target-password newpassword --indices job-leads,crm-contacts"
    echo ""
    echo "Environment variables:"
    echo "  You can also set the options using environment variables with the same names (uppercase with underscores)."
    echo "  For example: SOURCE_HOST, TARGET_PASSWORD, etc."
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --source-host)
            SOURCE_HOST="$2"
            shift 2
            ;;
        --source-user)
            SOURCE_USER="$2"
            shift 2
            ;;
        --source-password)
            SOURCE_PASSWORD="$2"
            shift 2
            ;;
        --target-host)
            TARGET_HOST="$2"
            shift 2
            ;;
        --target-user)
            TARGET_USER="$2"
            shift 2
            ;;
        --target-password)
            TARGET_PASSWORD="$2"
            shift 2
            ;;
        --indices)
            INDICES="$2"
            shift 2
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --max-batch-mb)
            MAX_BATCH_MB="$2"
            shift 2
            ;;
        --skip-large-docs)
            SKIP_LARGE_DOCS=true
            shift
            ;;
        --increase-http-max-size)
            INCREASE_HTTP_MAX_SIZE=true
            shift
            ;;
        --scroll-time)
            SCROLL_TIME="$2"
            shift 2
            ;;
        --concurrency)
            CONCURRENCY="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --list-indices)
            LIST_INDICES=true
            shift
            ;;
        --inspect-document)
            INSPECT_DOCUMENT=true
            shift
            ;;
        --document-id)
            DOCUMENT_ID="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Check required parameters based on operation mode
if [ "$LIST_INDICES" = true ]; then
    # No additional parameters needed for listing indices
    :  # No-op
elif [ "$INSPECT_DOCUMENT" = true ]; then
    # For document inspection, we need indices
    if [ -z "$INDICES" ]; then
        echo "Error: --indices option is required with --inspect-document"
        show_help
        exit 1
    fi
else
    # For reindexing, we need target host and password
    if [ -z "$TARGET_HOST" ]; then
        echo "Error: Target host is required"
        show_help
        exit 1
    fi

    if [ -z "$TARGET_PASSWORD" ]; then
        echo "Error: Target password is required"
        show_help
        exit 1
    fi
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed"
    exit 1
fi

# Change to script directory
cd "$(dirname "$0")"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# Create logs directory if it doesn't exist
if [ ! -d "logs" ]; then
    echo "Creating logs directory..."
    mkdir -p logs
fi

# Build command
CMD="node reindex_opensearch.js"

if [ "$LIST_INDICES" = true ]; then
    CMD+=" --list-indices"
elif [ "$INSPECT_DOCUMENT" = true ]; then
    CMD+=" --inspect-document"

    if [ -n "$INDICES" ]; then
        CMD+=" --indices \"$INDICES\""
    fi

    if [ -n "$DOCUMENT_ID" ]; then
        CMD+=" --document-id \"$DOCUMENT_ID\""
    fi
else
    CMD+=" --source-host \"$SOURCE_HOST\""
    CMD+=" --source-user \"$SOURCE_USER\""
    CMD+=" --source-password \"$SOURCE_PASSWORD\""
    CMD+=" --target-host \"$TARGET_HOST\""
    CMD+=" --target-user \"$TARGET_USER\""
    CMD+=" --target-password \"$TARGET_PASSWORD\""
    CMD+=" --batch-size $BATCH_SIZE"
    CMD+=" --max-batch-mb $MAX_BATCH_MB"

    if [ "$SKIP_LARGE_DOCS" = true ]; then
        CMD+=" --skip-large-docs"
    fi
    CMD+=" --scroll-time \"$SCROLL_TIME\""
    CMD+=" --concurrency $CONCURRENCY"

    if [ -n "$INDICES" ]; then
        CMD+=" --indices \"$INDICES\""
    fi

    if [ "$DRY_RUN" = true ]; then
        CMD+=" --dry-run"
    fi
fi

# Print command (with password masked)
if [ "$LIST_INDICES" = true ] || [ "$INSPECT_DOCUMENT" = true ] || [ "$INCREASE_HTTP_MAX_SIZE" = true ]; then
    PRINT_CMD="$CMD"
else
    PRINT_CMD=$(echo "$CMD" | sed "s/--source-password \"[^\"]*\"/--source-password \"****\"/g" | sed "s/--target-password \"[^\"]*\"/--target-password \"****\"/g")
fi
echo "Running command: $PRINT_CMD"

# Execute command
eval "$CMD"

# Check exit status
if [ $? -eq 0 ]; then
    if [ "$LIST_INDICES" = true ]; then
        # No additional message needed for listing indices
        :  # No-op
    elif [ "$INSPECT_DOCUMENT" = true ]; then
        echo "Document inspection completed successfully"
    elif [ "$INCREASE_HTTP_MAX_SIZE" = true ]; then
        echo "HTTP max size instructions displayed successfully"
    else
        echo "Reindexing completed successfully"
    fi
else
    if [ "$LIST_INDICES" = true ]; then
        echo "Listing indices failed"
    elif [ "$INSPECT_DOCUMENT" = true ]; then
        echo "Document inspection failed"
    elif [ "$INCREASE_HTTP_MAX_SIZE" = true ]; then
        echo "Failed to display HTTP max size instructions"
    else
        echo "Reindexing failed"
    fi
    exit 1
fi

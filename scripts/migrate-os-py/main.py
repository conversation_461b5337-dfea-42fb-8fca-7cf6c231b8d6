from opensearchpy import OpenSearch, helpers
import json
from datetime import datetime, timedelta
import os
import re

# Function to clean invalid Unicode characters
def clean_source(source):
    """Clean invalid Unicode characters from the source document."""
    if isinstance(source, dict):
        return {k: clean_source(v) for k, v in source.items()}
    elif isinstance(source, list):
        return [clean_source(item) for item in source]
    elif isinstance(source, str):
        # Remove or replace problematic Unicode characters
        # This regex pattern matches surrogate pairs and other invalid Unicode
        return re.sub(r'[\ud800-\udfff]', '', source)
    else:
        return source

# Source and destination OpenSearch configurations
SOURCE_OPENSEARCH = {
    "host": "**************",
    "port": 9200,
    "username": "admin",
    "password": "<PERSON>ileo@2024",
}

DESTINATION_OPENSEARCH = {
    "host": "search-zileo-staging-os-tn7ngx7vskonafuuhbyp7pn3qy.eu-west-2.es.amazonaws.com",
    "port": 443,
    "username": "zileoaws",
    "password": "<PERSON><PERSON><PERSON>@2024",
}

INDEX_NAME = "jobs"  # Replace with the actual index name
STATUS_FILE = "migration_status.json"  # File to save the current state
DATE_FORMAT = "%Y-%m-%d"
BATCH_SIZE = 500  # Increased batch size for faster processing
SCROLL_TIME = "1m"

def connect_to_opensearch(config):
    """Connect to an OpenSearch instance."""
    return OpenSearch(
        hosts=[{"host": config["host"], "port": config["port"]}],
        http_auth=(config["username"], config["password"]),
        use_ssl=True,
        verify_certs=False,
        ssl_assert_hostname=False,
        ssl_show_warn=False,
    )

def get_migration_status():
    """Retrieve the migration status from the status file."""
    if os.path.exists(STATUS_FILE):
        with open(STATUS_FILE, "r") as file:
            status = json.load(file)
            # Ensure all required keys exist
            if "last_date" not in status:
                status["last_date"] = (datetime.now() - timedelta(days=60)).strftime(DATE_FORMAT)
            if "scroll_id" not in status:
                status["scroll_id"] = None
            if "current_date_completed" not in status:
                status["current_date_completed"] = False
            return status
    # Default status
    return {
        "last_date": (datetime.now() - timedelta(days=60)).strftime(DATE_FORMAT),
        "scroll_id": None,
        "current_date_completed": False
    }

def save_migration_status(last_date, scroll_id=None, current_date_completed=False):
    """Save the migration status to the status file."""
    status = {
        "last_date": last_date.strftime(DATE_FORMAT),
        "scroll_id": scroll_id,
        "current_date_completed": current_date_completed
    }
    with open(STATUS_FILE, "w") as file:
        json.dump(status, file)

def fetch_index_config(client, index_name):
    """Fetch the settings and mappings of an index."""
    if not client.indices.exists(index=index_name):
        raise ValueError(f"Index '{index_name}' does not exist in the source OpenSearch.")
    settings = client.indices.get_settings(index=index_name)
    mappings = client.indices.get_mapping(index=index_name)
    return settings[index_name]["settings"], mappings[index_name]["mappings"]

def create_index_with_config(client, index_name, settings, mappings):
    """Create an index with the given settings and mappings."""
    index_config = {
        "settings": {
            "number_of_shards": 2,
            "number_of_replicas": 1,
            "analysis": settings['index'].get("analysis", {})
        },
        "mappings": mappings,
    }
    if client.indices.exists(index=index_name):
        print(f"Index '{index_name}' already exists on the destination cluster!")
    else:
        response = client.indices.create(index=index_name, body=index_config)
        print(f"Index '{index_name}' created successfully on the destination cluster!")
        print(response)

def migrate_day_data(source_client, destination_client, index, date):
    """Migrate data from source to destination OpenSearch for a specific day."""
    status = get_migration_status()
    current_date_str = date.strftime(DATE_FORMAT)
    
    # If we already completed this date, skip it
    if status.get("current_date_completed", False) and status["last_date"] == current_date_str:
        print(f"Date {current_date_str} already completed, skipping...")
        return
    
    # Query to fetch documents for the day
    query = {
        "query": {
            "range": {
                "created_at": {
                    "gte": date.strftime(f"{DATE_FORMAT}T00:00:00"),
                    "lt": (date + timedelta(days=1)).strftime(f"{DATE_FORMAT}T00:00:00"),
                }
            }
        }
    }

    # Check if we have a saved scroll_id to resume from
    scroll_id = status.get("scroll_id") if status["last_date"] == current_date_str else None
    
    try:
        if not scroll_id:
            # Start a new scroll
            docs = source_client.search(index=index, body=query, scroll=SCROLL_TIME, size=BATCH_SIZE)
            scroll_id = docs["_scroll_id"]
        else:
            # Resume from saved scroll
            print(f"Resuming migration for date {current_date_str} with existing scroll_id")
            try:
                docs = source_client.scroll(scroll_id=scroll_id, scroll=SCROLL_TIME)
            except Exception as e:
                print(f"Error resuming scroll: {str(e)}. Starting a new scroll.")
                # If scroll fails, start a new one
                docs = source_client.search(index=index, body=query, scroll=SCROLL_TIME, size=BATCH_SIZE)
                scroll_id = docs["_scroll_id"]
    except Exception as e:
        print(f"Error starting scroll: {str(e)}. Skipping date {current_date_str}.")
        return
    
    total = 0
    skipped = 0
    
    while docs["hits"]["hits"]:
        # Prepare bulk operation
        bulk_actions = []
        for doc in docs["hits"]["hits"]:
            try:
                # Clean the source document to remove invalid Unicode
                cleaned_source = clean_source(doc["_source"])
                bulk_actions.append({
                    "_index": index,
                    "_id": doc["_id"],
                    "_source": cleaned_source
                })
            except Exception as e:
                print(f"Error processing document {doc['_id']}: {str(e)}")
                skipped += 1
        
        # Execute bulk operation
        if bulk_actions:
            try:
                helpers.bulk(destination_client, bulk_actions)
            except Exception as e:
                print(f"Bulk operation error: {str(e)}")
                # Process documents one by one to identify problematic ones
                for action in bulk_actions:
                    try:
                        destination_client.index(
                            index=action["_index"],
                            id=action["_id"],
                            body=action["_source"]
                        )
                    except Exception as doc_e:
                        print(f"Error indexing document {action['_id']}: {str(doc_e)}")
                        skipped += 1
            
        total += len(docs["hits"]["hits"]) - skipped
        print(f"Processed {total} documents for date {current_date_str} (skipped: {skipped})")
        
        # Save current state to resume if interrupted
        save_migration_status(date, scroll_id)
        
        # Get next batch
        try:
            docs = source_client.scroll(scroll_id=scroll_id, scroll=SCROLL_TIME)
            scroll_id = docs["_scroll_id"]
        except Exception as e:
            print(f"Error scrolling: {str(e)}. Ending scroll.")
            break

    # Try to clear scroll context, but don't fail if it's already gone
    try:
        if scroll_id:
            source_client.clear_scroll(scroll_id=scroll_id)
    except Exception as e:
        print(f"Error clearing scroll: {str(e)}")
    
    # Mark this date as completed
    save_migration_status(date, None, True)
    print(f"Data migration completed for date: {current_date_str}, total: {total} documents, skipped: {skipped}")

def main():
    # Connect to source and destination OpenSearch instances
    source_client = connect_to_opensearch(SOURCE_OPENSEARCH)
    destination_client = connect_to_opensearch(DESTINATION_OPENSEARCH)
    
    create_index = False
    if create_index:
        setting, mapping = fetch_index_config(source_client, INDEX_NAME)
        create_index_with_config(destination_client, INDEX_NAME, setting, mapping)
        print(f"Success creating index {INDEX_NAME}")
    
    # Get the starting date from status file
    status = get_migration_status()
    start_date = datetime.strptime(status["last_date"], DATE_FORMAT)
    
    # If the current date was not completed, don't advance
    if not status.get("current_date_completed", False):
        pass
    else:
        # Move to the next day if the current day was completed
        start_date += timedelta(days=1)
    
    end_date = datetime.now()

    # Process day by day
    current_date = start_date
    while current_date < end_date:
        print(f"Processing date: {current_date.strftime(DATE_FORMAT)}")

        # Migrate data for the current day
        migrate_day_data(source_client, destination_client, INDEX_NAME, current_date)

        # Move to the next day
        current_date += timedelta(days=1)

    print("Migration completed!")

if __name__ == "__main__":
    main()

{"name": "opensearch-reindex", "version": "1.0.0", "description": "Tool to reindex data from one OpenSearch cluster to another", "main": "reindex_opensearch.js", "scripts": {"reindex": "node reindex_opensearch.js", "list-indices": "node reindex_opensearch.js --list-indices"}, "dependencies": {"@opensearch-project/opensearch": "^2.2.0", "chalk": "^4.1.2", "cli-table3": "^0.6.3", "ora": "^5.4.1", "p-limit": "^3.1.0", "yargs": "^17.7.1"}, "engines": {"node": ">=14.0.0"}}
# OpenSearch Reindexing Tool (Node.js)

Script này giúp bạn reindex dữ liệu từ một OpenSearch cluster sang một OpenSearch cluster khác sử dụng Node.js. Nó hỗ trợ reindex tất cả các indices hoặc chỉ một số indices cụ thể đã được định nghĩa trước.

## Tính năng

- Reindex dữ liệu từ một OpenSearch cluster sang một OpenSearch cluster khác
- Hỗ trợ reindex tất cả các indices hoặc chỉ một số indices cụ thể
- Sao chép cấu hình và mapping của indices
- Hỗ trợ xử lý song song để tăng tốc độ reindex
- Chế độ dry-run để kiểm tra trước khi thực hiện reindex
- Báo cáo chi tiết về quá trình reindex
- <PERSON>iao diện dòng lệnh thân thiện với người dùng
- <PERSON>h sách các indices đã được định nghĩa trước với mô tả và ưu tiên

## Yêu cầu

- Node.js 14+
- npm

## Cài đặt

Script sẽ tự động cài đặt các dependencies cần thiết khi chạy.

## Sử dụng

### Sử dụng script shell

```bash
./reindex_opensearch_node.sh --target-host http://new-opensearch:9200 --target-password newpassword
```

### Liệt kê các indices đã được định nghĩa

```bash
./reindex_opensearch_node.sh --list-indices
```

### Kiểm tra document từ một index

```bash
# Kiểm tra một document ngẫu nhiên từ index
./reindex_opensearch_node.sh --inspect-document --indices reported_agency_country

# Kiểm tra một document cụ thể theo ID
./reindex_opensearch_node.sh --inspect-document --indices reported_agency_country --document-id your-document-id
```

### Tham số

- `--source-host HOST`: URL của OpenSearch nguồn (mặc định: http://**************:9200)
- `--source-user USER`: Tên đăng nhập OpenSearch nguồn (mặc định: admin)
- `--source-password PASS`: Mật khẩu OpenSearch nguồn (mặc định: Zileo@2024)
- `--target-host HOST`: URL của OpenSearch đích (bắt buộc)
- `--target-user USER`: Tên đăng nhập OpenSearch đích (mặc định: admin)
- `--target-password PASS`: Mật khẩu OpenSearch đích (bắt buộc)
- `--indices INDICES`: Danh sách các indices cần reindex, phân cách bằng dấu phẩy (mặc định: tất cả indices)
- `--batch-size SIZE`: Kích thước batch cho reindexing (số lượng document mỗi batch, mặc định: 100)
- `--max-batch-mb SIZE`: Kích thước tối đa của mỗi batch tính bằng MB (để tránh lỗi "Request size exceeded", mặc định: 5)
- `--skip-large-docs`: Bỏ qua các document lớn hơn max-batch-mb thay vì gặp lỗi (mặc định: false)
- `--increase-http-max-size`: Hiển thị hướng dẫn để tăng kích thước tối đa của HTTP request trên OpenSearch đích
- `--scroll-time TIME`: Thời gian scroll cho reindexing (mặc định: 5m)
- `--concurrency NUM`: Số lượng reindex song song (mặc định: 4)
- `--dry-run`: Chế độ dry run (không thực hiện reindex thực tế)
- `--list-indices`: Liệt kê các indices đã được định nghĩa và thoát
- `--inspect-document`: Kiểm tra một document từ index được chỉ định trước khi reindex
- `--document-id ID`: ID của document cần kiểm tra (sử dụng với --inspect-document)
- `--help`: Hiển thị thông tin trợ giúp

### Ví dụ

Reindex tất cả các indices:

```bash
./reindex_opensearch_node.sh --target-host http://new-opensearch:9200 --target-password newpassword
```

Reindex chỉ một số indices cụ thể:

```bash
./reindex_opensearch_node.sh --target-host http://new-opensearch:9200 --target-password newpassword --indices job-leads,crm-contacts
```

Chạy ở chế độ dry-run để kiểm tra trước:

```bash
./reindex_opensearch_node.sh --target-host http://new-opensearch:9200 --target-password newpassword --dry-run
```

Tăng số lượng reindex song song:

```bash
./reindex_opensearch_node.sh --target-host http://new-opensearch:9200 --target-password newpassword --concurrency 8
```

### Sử dụng biến môi trường

Bạn cũng có thể đặt các tham số bằng cách sử dụng biến môi trường:

```bash
export TARGET_HOST=http://new-opensearch:9200
export TARGET_PASSWORD=newpassword
export INDICES=job-leads,crm-contacts
./reindex_opensearch_node.sh
```

## Indices đã được định nghĩa

Script này đã định nghĩa sẵn các indices sau với mô tả và ưu tiên:

### Location related indices
- `location_mappings`: Location mappings data (priority: 1)
- `cached_locations`: Cached locations data (priority: 2)
- `unknown_location_jobs`: Unknown location jobs data (priority: 3)

### Agency related indices
- `reported_agency_country`: Reported agency country data (priority: 1)

## Báo cáo và Log

Sau khi reindex hoàn tất, script sẽ tạo các file báo cáo và log trong thư mục `scripts/logs/`:

### File báo cáo

1. **Báo cáo tổng quan**: `logs/reindex_report.json` chứa thông tin về quá trình reindex, bao gồm:
   - Tổng số indices đã reindex
   - Số indices đã hoàn thành, thất bại, bỏ qua
   - Thời gian thực hiện
   - Chi tiết về từng index

### File log

1. **Log lỗi**: `logs/{index}_error_log.json` chứa thông tin chi tiết về các lỗi xảy ra khi reindex index cụ thể.

2. **Log document lớn**: `logs/{index}_large_docs_log.json` chứa thông tin về các document có kích thước lớn hơn giới hạn được bỏ qua (khi sử dụng tùy chọn `--skip-large-docs`).

### Quản lý file

Tất cả các file báo cáo và log đều được lưu trong thư mục `scripts/logs/` và được ignore khỏi git. Thư mục này sẽ được tạo tự động nếu chưa tồn tại.

## Sử dụng trực tiếp script Node.js

Nếu bạn muốn sử dụng trực tiếp script Node.js:

```bash
cd scripts
npm install
node reindex_opensearch.js --source-host http://source-opensearch:9200 --source-user admin --source-password password \
                          --target-host http://target-opensearch:9200 --target-user admin --target-password password \
                          --indices job-leads,crm-contacts --batch-size 1000 --concurrency 4
```

## Xử lý lỗi

Nếu quá trình reindex gặp lỗi, script sẽ ghi lại thông tin lỗi trong log và file báo cáo. Bạn có thể kiểm tra file báo cáo để biết chi tiết về các lỗi đã xảy ra.

### Lỗi "Request size exceeded"

Nếu bạn gặp lỗi `Request size exceeded 10485760 bytes` (Kích thước request vượt quá 10MB), bạn có một số lựa chọn:

#### 1. Giảm kích thước batch

```bash
# Giảm kích thước batch xuống 2MB
./reindex_opensearch_node.sh --target-host http://new-opensearch:9200 --target-password newpassword --indices reported_agency_country --max-batch-mb 2
```

Script sẽ tự động điều chỉnh số lượng document trong mỗi batch để đảm bảo kích thước batch không vượt quá giới hạn được chỉ định.

#### 2. Bỏ qua các document lớn

Nếu có một số document có kích thước lớn hơn giới hạn, bạn có thể chọn bỏ qua chúng:

```bash
./reindex_opensearch_node.sh --target-host http://new-opensearch:9200 --target-password newpassword --indices reported_agency_country --max-batch-mb 5 --skip-large-docs
```

Script sẽ bỏ qua các document lớn hơn giới hạn và ghi lại thông tin về chúng trong file `logs/reported_agency_country_large_docs_log.json`. Bạn có thể kiểm tra file này để xem danh sách các document đã bỏ qua.

#### 3. Tăng giới hạn kích thước request trên OpenSearch

```bash
./reindex_opensearch_node.sh --increase-http-max-size
```

Lệnh này sẽ hiển thị hướng dẫn để tăng giới hạn kích thước request trên OpenSearch. Sau khi thực hiện các thay đổi này, bạn có thể chạy lại script reindex với giá trị `--max-batch-mb` cao hơn.

## Lưu ý

- Quá trình reindex có thể mất nhiều thời gian tùy thuộc vào kích thước dữ liệu.
- Đảm bảo rằng OpenSearch đích có đủ dung lượng để lưu trữ dữ liệu.
- Nên chạy script trong một terminal session riêng biệt hoặc sử dụng `nohup` để tránh bị ngắt kết nối.
- Nên thực hiện backup dữ liệu trước khi reindex.
- Nên chạy ở chế độ dry-run trước để kiểm tra trước khi thực hiện reindex thực tế.
- Các indices sẽ được reindex theo thứ tự ưu tiên đã định nghĩa.

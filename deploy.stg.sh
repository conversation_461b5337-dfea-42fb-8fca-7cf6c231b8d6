#!/bin/bash

git config --global --add safe.directory '*'

cd job-finder-backend
git checkout staging
git -c credential.helper='!f() { echo "username=eddieGong2604"; echo "password=**************************"; }; f' pull
npm install --legacy-peer-deps
npm run build:staging

pm2 delete app-be-staging
NODE_OPTIONS="--max-old-space-size=8192" APP_PORT=80 NODE_ENV=staging pm2 start dist/main.js --name "app-be-staging" -- --port 80

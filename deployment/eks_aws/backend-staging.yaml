apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-pod-stagging
  labels:
    app: backend-pod-stagging
spec:
  selector:
    matchLabels:
      app: backend-pod-stagging
  replicas: 2
  template:
    metadata:
      labels:
        app: backend-pod-stagging
    spec:
      imagePullSecrets:
        - name: aws-ecr

      containers:
      - name: backend-pod-stagging
        image: $BUILD_ID
        resources:
          requests:
            memory: "1000Mi"
            cpu: "1000m"
          limits:
            memory: "2500Mi"
            cpu: "2500m"
        ports:
        - containerPort: 80

        volumeMounts:
        - name: zileo-manager-cfg
          mountPath: /workspace/.env
          subPath: .devstaging

      volumes:
      - name: zileo-manager-cfg
        configMap:
          name: zileo-manager-cfg

---
apiVersion: v1
kind: Service
metadata:
  name: backend-pod-service
spec:
  type: ClusterIP
  selector:
    app: backend-pod-stagging
  
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80


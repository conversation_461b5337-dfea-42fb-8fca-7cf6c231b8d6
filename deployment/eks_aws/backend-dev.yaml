apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-pod-deployment
  labels:
    app: backend-pod-deployment
spec:
  selector:
    matchLabels:
      app: backend-pod-deployment
  replicas: 1
  template:
    metadata:
      labels:
        app: backend-pod-deployment
    spec:
      imagePullSecrets:
        - name: aws-ecr

      containers:
      - name: backend-pod-deployment
        image: $BUILD_ID
        resources:
          requests:
            memory: "1000Mi"
            cpu: "1000m"
          limits:
            memory: "2500Mi"
            cpu: "2500m"
        ports:
        - containerPort: 80

        volumeMounts:
        - name: zileo-manager-cfg
          mountPath: /workspace/.env
          subPath: .envdev

      volumes:
      - name: zileo-manager-cfg
        configMap:
          name: zileo-manager-cfg

---
apiVersion: v1
kind: Service
metadata:
  name: backend-pod-service
spec:
  type: ClusterIP
  selector:
    app: backend-pod-deployment
  
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80


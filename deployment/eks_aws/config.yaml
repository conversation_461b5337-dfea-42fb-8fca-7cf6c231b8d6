apiVersion: v1
kind: ConfigMap
metadata:
  name: zileo-manager-cfg
data:
  .envdev: |
    APP_ENV=dev
    APP_PORT=80

    DATABASE_TYPE=postgres
    DATABASE_HOST=zileo-db-dev-v2.ci86gncdfulv.eu-west-2.rds.amazonaws.com
    DATABASE_PORT=5432
    DATABASE_USERNAME=postgres
    DATABASE_PASSWORD=zileotest
    DATABASE_DATABASE=postgres
    DATABASE_SYNCHRONIZE=0

    #elasticsearch
    ELASTICSEARCH_HOST=https://search-zileo-opensearch-dev-v2-t572oewtfmdwfxafyrlr5fas4y.aos.eu-west-2.on.aws
    ELASTICSEARCH_USER=opensearch
    ELASTICSEARCH_PASSWORD=abcABC123!

    #JWT
    COMMON_API_JWT_SECRET=randomtext1
    COMMON_API_JWT_EXPIRES_IN=30m

    # JWT REFESH TOKEN
    COMMON_API_JWT_REFRESH_TOKEN_SECRET=randomtext3
    COMMON_API_JWT_REFRESH_TOKEN_EXPIRES_IN=60m

    # Rate limit
    COMMON_API_TTL=60
    COMMON_API_LIMIT=1


    ##BullHorn
    BULLHORN_API_GET_ACCESS_CODE=""
    BULLHORN_CLIENT_ID=1bd49cd7-bc5e-4e3c-934a-b491c5a26993
    BULLHORN_USERNAME=pcapi.api
    BULLHORN_PASSWORD=Pearsonapi1
    BULLHORN_CLIENT_SECRET=bViIFWVWyh7gfIEgt7Of3qEa

    SENDGRID_API_KEY=*********************************************************************
    SENDGRID_API_KEY_EMAIL_EXPLORER_CANDIDATE=*********************************************************************
    SENDGRID_API_KEY_EMAIL_VALID=*********************************************************************
    URL_API=http://zileo.io

    CORS_WHITE_LIST=http://localhost:3000,http://localhost,http://**********:3001,http://**********:3000,http://**********,http://zileo.io,http://**********,http://dev.zileo.io,https://dev.zileo.io
    CRAWLING_API=http://zileo-elb-crawler-dev-1485188518.eu-west-2.elb.amazonaws.com

    REGION_AWS=eu-west-2
    ACCESS_KEY_ID_AWS=********************
    SECRET_ACCESS_KEY_AWS=bcPKHuRtKw3gnLWf4hRmnGk+U0b8LAo3z3VpDxbl
    BUCKET_NAME_S3=zileo-dev-bucket

    APOLLO_API_KEY=r4mkHRxuEcYq5UUTS37SXg
    APOLLO_API_URL=https://app.apollo.io/api/v1

    SNS_TOPIC_ARN='arn:aws:sns:eu-west-2:559880962148:zileosnsdev'

    # OPENAI_API_KEY=***************************************************
    OPENAI_API_KEY=********************************************************

    NYLAS_CLIENT_ID=5c4ca094-2de1-4eef-a2e0-5df604aec277
    NYLAS_API_KEY=nyk_v0_kMed1nH4P6c8SNDcfSprlpgP5Q7eNYFLTD1vLsVo5DLe2YnWiHdlpSgV5Tgm4RjZ
    NYLAS_API_URI=https://api.eu.nylas.com

    REDIS_CONNECTION=redis://:zileo@**************:6379/0

    API_URL=https://api-dev.zileo.io
    #API_URL=http://localhost
    CLIENT_URL=https://dev.zileo.io

  .devstaging: |
    APP_ENV=staging
    APP_PORT=80

    DATABASE_TYPE=postgres
    DATABASE_HOST=zileo-db-staging.ci86gncdfulv.eu-west-2.rds.amazonaws.com
    DATABASE_PORT=5432
    DATABASE_USERNAME=postgres
    DATABASE_PASSWORD=abcABC123!
    DATABASE_DATABASE=postgres
    DATABASE_SYNCHRONIZE=0

    COMMON_API_JWT_SECRET=randomtext1
    COMMON_API_JWT_EXPIRES_IN=30m

    COMMON_API_JWT_REFRESH_TOKEN_SECRET=randomtext3
    COMMON_API_JWT_REFRESH_TOKEN_EXPIRES_IN=60m

    COMMON_API_TTL=60
    COMMON_API_LIMIT=1


    BULLHORN_API_GET_ACCESS_CODE=""
    BULLHORN_CLIENT_ID=1bd49cd7-bc5e-4e3c-934a-b491c5a26993
    BULLHORN_USERNAME=pcapi.api
    BULLHORN_PASSWORD=Pearsonapi1
    BULLHORN_CLIENT_SECRET=bViIFWVWyh7gfIEgt7Of3qEa

    SENDGRID_API_KEY=*********************************************************************
    SENDGRID_API_KEY_EMAIL_EXPLORER_CANDIDATE=*********************************************************************
    SENDGRID_API_KEY_EMAIL_VALID=*********************************************************************
    URL_API=http://zileo.io

    CORS_WHITE_LIST=http://localhost:3000,http://localhost,http://**********:3001,http://**********:3000,http://**********,http://zileo.io,http://**********,http://test.zileo.io,http://staging.zileo.io
    CRAWLING_API=http://zileo-elb-crawler-staging-1256927277.eu-west-2.elb.amazonaws.com

    REGION_AWS=eu-west-2
    ACCESS_KEY_ID_AWS=********************
    SECRET_ACCESS_KEY_AWS=bcPKHuRtKw3gnLWf4hRmnGk+U0b8LAo3z3VpDxbl
    BUCKET_NAME_S3=zileo-dev-bucket

    APOLLO_API_KEY=r4mkHRxuEcYq5UUTS37SXg
    APOLLO_API_URL=https://app.apollo.io/api/v1

    SNS_TOPIC_ARN=arn:aws:sns:eu-west-2:559880962148:zileo-sns-staging


    ELASTICSEARCH_HOST=https://search-zileo-opensearch-staging-thtjeayvhgccgybgkjtl2byoia.eu-west-2.es.amazonaws.com
    ELASTICSEARCH_USER=opensearch
    ELASTICSEARCH_PASSWORD=abcABC123!

    OPENAI_API_KEY=********************************************************

    NYLAS_CLIENT_ID=5c4ca094-2de1-4eef-a2e0-5df604aec277
    NYLAS_API_KEY=nyk_v0_kMed1nH4P6c8SNDcfSprlpgP5Q7eNYFLTD1vLsVo5DLe2YnWiHdlpSgV5Tgm4RjZ
    NYLAS_API_URI=https://api.eu.nylas.com

    REDIS_CONNECTION=redis://:zileo@**************:6379/0

    API_URL=https://api-staging.zileo.io
    CLIENT_URL=https://staging.zileo.io



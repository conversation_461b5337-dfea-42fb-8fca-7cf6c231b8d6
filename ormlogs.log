[2023-05-07T18:51:02.100Z][QUERY]: SELECT * FROM current_schema()
[2023-05-07T18:51:03.026Z][QUERY]: CREATE EXTENSION IF NOT EXISTS "uuid-ossp"
[2023-05-07T18:51:03.030Z][QUERY]: SELECT version();
[2023-05-07T18:51:03.033Z][QUERY]: START TRANSACTION
[2023-05-07T18:51:03.035Z][QUERY]: SELECT * FROM current_schema()
[2023-05-07T18:51:03.036Z][QUERY]: SELECT * FROM current_database()
[2023-05-07T18:51:03.038Z][QUERY]: SELECT "table_schema", "table_name" FROM "information_schema"."tables" WHERE ("table_schema" = 'public' AND "table_name" = 'formats') OR ("table_schema" = 'public' AND "table_name" = 'campaign_formats') OR ("table_schema" = 'public' AND "table_name" = 'user_categories') OR ("table_schema" = 'public' AND "table_name" = 'categories') OR ("table_schema" = 'public' AND "table_name" = 'campaign_categories') OR ("table_schema" = 'public' AND "table_name" = 'transaction_history') OR ("table_schema" = 'public' AND "table_name" = 'user_notifications') OR ("table_schema" = 'public' AND "table_name" = 'notifications') OR ("table_schema" = 'public' AND "table_name" = 'campaigns') OR ("table_schema" = 'public' AND "table_name" = 'evidence_task_scans') OR ("table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks') OR ("table_schema" = 'public' AND "table_name" = 'campaign_influencers') OR ("table_schema" = 'public' AND "table_name" = 'user_instagram_informations') OR ("table_schema" = 'public' AND "table_name" = 'user_favorites') OR ("table_schema" = 'public' AND "table_name" = 'favorites') OR ("table_schema" = 'public' AND "table_name" = 'languages') OR ("table_schema" = 'public' AND "table_name" = 'user_brand_profiles') OR ("table_schema" = 'public' AND "table_name" = 'balance') OR ("table_schema" = 'public' AND "table_name" = 'users') OR ("table_schema" = 'public' AND "table_name" = 'admin-roles') OR ("table_schema" = 'public' AND "table_name" = 'admins') OR ("table_schema" = 'public' AND "table_name" = 'admin-permissions') OR ("table_schema" = 'public' AND "table_name" = 'admin-prices') OR ("table_schema" = 'public' AND "table_name" = 'admin-feedbacks') OR ("table_schema" = 'public' AND "table_name" = 'admin-configurations') OR ("table_schema" = 'public' AND "table_name" = 'permissions') OR ("table_schema" = 'public' AND "table_name" = 'roles') OR ("table_schema" = 'public' AND "table_name" = 'user_role')
[2023-05-07T18:51:03.057Z][QUERY]: SELECT TRUE FROM information_schema.columns WHERE table_name = 'pg_class' and column_name = 'relispartition'
[2023-05-07T18:51:03.077Z][QUERY]: SELECT columns.*, pg_catalog.col_description(('"' || table_catalog || '"."' || table_schema || '"."' || table_name || '"')::regclass::oid, ordinal_position) AS description, ('"' || "udt_schema" || '"."' || "udt_name" || '"')::"regtype" AS "regtype", pg_catalog.format_type("col_attr"."atttypid", "col_attr"."atttypmod") AS "format_type" FROM "information_schema"."columns" LEFT JOIN "pg_catalog"."pg_attribute" AS "col_attr" ON "col_attr"."attname" = "columns"."column_name" AND "col_attr"."attrelid" = ( SELECT "cls"."oid" FROM "pg_catalog"."pg_class" AS "cls" LEFT JOIN "pg_catalog"."pg_namespace" AS "ns" ON "ns"."oid" = "cls"."relnamespace" WHERE "cls"."relname" = "columns"."table_name" AND "ns"."nspname" = "columns"."table_schema" ) WHERE ("table_schema" = 'public' AND "table_name" = 'roles') OR ("table_schema" = 'public' AND "table_name" = 'user_role') OR ("table_schema" = 'public' AND "table_name" = 'permissions') OR ("table_schema" = 'public' AND "table_name" = 'campaign_formats') OR ("table_schema" = 'public' AND "table_name" = 'formats') OR ("table_schema" = 'public' AND "table_name" = 'user_categories') OR ("table_schema" = 'public' AND "table_name" = 'categories') OR ("table_schema" = 'public' AND "table_name" = 'campaign_categories') OR ("table_schema" = 'public' AND "table_name" = 'languages') OR ("table_schema" = 'public' AND "table_name" = 'transaction_history') OR ("table_schema" = 'public' AND "table_name" = 'campaigns') OR ("table_schema" = 'public' AND "table_name" = 'user_instagram_informations') OR ("table_schema" = 'public' AND "table_name" = 'user_favorites') OR ("table_schema" = 'public' AND "table_name" = 'favorites') OR ("table_schema" = 'public' AND "table_name" = 'admin-permissions') OR ("table_schema" = 'public' AND "table_name" = 'user_brand_profiles') OR ("table_schema" = 'public' AND "table_name" = 'balance') OR ("table_schema" = 'public' AND "table_name" = 'users') OR ("table_schema" = 'public' AND "table_name" = 'campaign_influencers') OR ("table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks') OR ("table_schema" = 'public' AND "table_name" = 'evidence_task_scans') OR ("table_schema" = 'public' AND "table_name" = 'admin-roles') OR ("table_schema" = 'public' AND "table_name" = 'admins') OR ("table_schema" = 'public' AND "table_name" = 'admin-feedbacks') OR ("table_schema" = 'public' AND "table_name" = 'admin-prices') OR ("table_schema" = 'public' AND "table_name" = 'admin-configurations') OR ("table_schema" = 'public' AND "table_name" = 'user_notifications') OR ("table_schema" = 'public' AND "table_name" = 'notifications')
[2023-05-07T18:51:03.078Z][QUERY]: SELECT "ns"."nspname" AS "table_schema", "t"."relname" AS "table_name", "cnst"."conname" AS "constraint_name", pg_get_constraintdef("cnst"."oid") AS "expression", CASE "cnst"."contype" WHEN 'p' THEN 'PRIMARY' WHEN 'u' THEN 'UNIQUE' WHEN 'c' THEN 'CHECK' WHEN 'x' THEN 'EXCLUDE' END AS "constraint_type", "a"."attname" AS "column_name" FROM "pg_constraint" "cnst" INNER JOIN "pg_class" "t" ON "t"."oid" = "cnst"."conrelid" INNER JOIN "pg_namespace" "ns" ON "ns"."oid" = "cnst"."connamespace" LEFT JOIN "pg_attribute" "a" ON "a"."attrelid" = "cnst"."conrelid" AND "a"."attnum" = ANY ("cnst"."conkey") WHERE "t"."relkind" IN ('r', 'p') AND (("ns"."nspname" = 'public' AND "t"."relname" = 'roles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_role') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'permissions') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_formats') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'formats') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'languages') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'transaction_history') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaigns') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_instagram_informations') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_favorites') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'favorites') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-permissions') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_brand_profiles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'balance') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'users') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_influencers') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_influencer_tasks') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'evidence_task_scans') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-roles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admins') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-feedbacks') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-prices') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-configurations') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_notifications') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'notifications'))
[2023-05-07T18:51:03.078Z][QUERY]: SELECT "ns"."nspname" AS "table_schema", "t"."relname" AS "table_name", "i"."relname" AS "constraint_name", "a"."attname" AS "column_name", CASE "ix"."indisunique" WHEN 't' THEN 'TRUE' ELSE'FALSE' END AS "is_unique", pg_get_expr("ix"."indpred", "ix"."indrelid") AS "condition", "types"."typname" AS "type_name" FROM "pg_class" "t" INNER JOIN "pg_index" "ix" ON "ix"."indrelid" = "t"."oid" INNER JOIN "pg_attribute" "a" ON "a"."attrelid" = "t"."oid"  AND "a"."attnum" = ANY ("ix"."indkey") INNER JOIN "pg_namespace" "ns" ON "ns"."oid" = "t"."relnamespace" INNER JOIN "pg_class" "i" ON "i"."oid" = "ix"."indexrelid" INNER JOIN "pg_type" "types" ON "types"."oid" = "a"."atttypid" LEFT JOIN "pg_constraint" "cnst" ON "cnst"."conname" = "i"."relname" WHERE "t"."relkind" IN ('r', 'p') AND "cnst"."contype" IS NULL AND (("ns"."nspname" = 'public' AND "t"."relname" = 'roles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_role') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'permissions') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_formats') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'formats') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'languages') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'transaction_history') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaigns') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_instagram_informations') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_favorites') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'favorites') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-permissions') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_brand_profiles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'balance') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'users') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_influencers') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_influencer_tasks') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'evidence_task_scans') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-roles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admins') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-feedbacks') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-prices') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-configurations') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_notifications') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'notifications'))
[2023-05-07T18:51:03.079Z][QUERY]: SELECT "con"."conname" AS "constraint_name", "con"."nspname" AS "table_schema", "con"."relname" AS "table_name", "att2"."attname" AS "column_name", "ns"."nspname" AS "referenced_table_schema", "cl"."relname" AS "referenced_table_name", "att"."attname" AS "referenced_column_name", "con"."confdeltype" AS "on_delete", "con"."confupdtype" AS "on_update", "con"."condeferrable" AS "deferrable", "con"."condeferred" AS "deferred" FROM ( SELECT UNNEST ("con1"."conkey") AS "parent", UNNEST ("con1"."confkey") AS "child", "con1"."confrelid", "con1"."conrelid", "con1"."conname", "con1"."contype", "ns"."nspname", "cl"."relname", "con1"."condeferrable", CASE WHEN "con1"."condeferred" THEN 'INITIALLY DEFERRED' ELSE 'INITIALLY IMMEDIATE' END as condeferred, CASE "con1"."confdeltype" WHEN 'a' THEN 'NO ACTION' WHEN 'r' THEN 'RESTRICT' WHEN 'c' THEN 'CASCADE' WHEN 'n' THEN 'SET NULL' WHEN 'd' THEN 'SET DEFAULT' END as "confdeltype", CASE "con1"."confupdtype" WHEN 'a' THEN 'NO ACTION' WHEN 'r' THEN 'RESTRICT' WHEN 'c' THEN 'CASCADE' WHEN 'n' THEN 'SET NULL' WHEN 'd' THEN 'SET DEFAULT' END as "confupdtype" FROM "pg_class" "cl" INNER JOIN "pg_namespace" "ns" ON "cl"."relnamespace" = "ns"."oid" INNER JOIN "pg_constraint" "con1" ON "con1"."conrelid" = "cl"."oid" WHERE "con1"."contype" = 'f' AND (("ns"."nspname" = 'public' AND "cl"."relname" = 'roles') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_role') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'permissions') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaign_formats') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'formats') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_categories') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'categories') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaign_categories') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'languages') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'transaction_history') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaigns') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_instagram_informations') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_favorites') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'favorites') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-permissions') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_brand_profiles') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'balance') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'users') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaign_influencers') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaign_influencer_tasks') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'evidence_task_scans') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-roles') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admins') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-feedbacks') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-prices') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-configurations') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_notifications') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'notifications')) ) "con" INNER JOIN "pg_attribute" "att" ON "att"."attrelid" = "con"."confrelid" AND "att"."attnum" = "con"."child" INNER JOIN "pg_class" "cl" ON "cl"."oid" = "con"."confrelid"  AND "cl"."relispartition" = 'f'INNER JOIN "pg_namespace" "ns" ON "cl"."relnamespace" = "ns"."oid" INNER JOIN "pg_attribute" "att2" ON "att2"."attrelid" = "con"."conrelid" AND "att2"."attnum" = "con"."parent"
[2023-05-07T18:51:03.299Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'roles' AND "column_name"='permissions'
[2023-05-07T18:51:03.299Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'transaction_history' AND "column_name"='direction_transaction'
[2023-05-07T18:51:03.299Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='walk_in_type'
[2023-05-07T18:51:03.299Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='platform_type'
[2023-05-07T18:51:03.300Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='campaign_type'
[2023-05-07T18:51:03.300Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='gender'
[2023-05-07T18:51:03.300Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='promote_type'
[2023-05-07T18:51:03.300Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='mentions'
[2023-05-07T18:51:03.300Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='hash_tags'
[2023-05-07T18:51:03.300Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='additional_pictures'
[2023-05-07T18:51:03.300Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='languages'
[2023-05-07T18:51:03.300Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'user_instagram_informations' AND "column_name"='account_type'
[2023-05-07T18:51:03.300Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'favorites' AND "column_name"='platform_type'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'user_brand_profiles' AND "column_name"='brand_picture'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'users' AND "column_name"='type_user'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'users' AND "column_name"='platform_type'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'users' AND "column_name"='language_array'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencers' AND "column_name"='status'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencers' AND "column_name"='pay_status'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencers' AND "column_name"='shipment_status'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks' AND "column_name"='campaign_type'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks' AND "column_name"='format_type'
[2023-05-07T18:51:03.301Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks' AND "column_name"='status'
[2023-05-07T18:51:03.302Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'evidence_task_scans' AND "column_name"='status'
[2023-05-07T18:51:03.302Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'evidence_task_scans' AND "column_name"='caption'
[2023-05-07T18:51:03.302Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'evidence_task_scans' AND "column_name"='tagged_users'
[2023-05-07T18:51:03.302Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'evidence_task_scans' AND "column_name"='hash_tags'
[2023-05-07T18:51:03.302Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'admin-roles' AND "column_name"='permissions'
[2023-05-07T18:51:03.305Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.307Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'transaction_history_direction_transaction_enum'
[2023-05-07T18:51:03.311Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_walk_in_type_enum'
[2023-05-07T18:51:03.314Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_platform_type_enum'
[2023-05-07T18:51:03.316Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_campaign_type_enum'
[2023-05-07T18:51:03.319Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_gender_enum'
[2023-05-07T18:51:03.322Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_promote_type_enum'
[2023-05-07T18:51:03.327Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.332Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.335Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.338Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.341Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'user_instagram_informations_account_type_enum'
[2023-05-07T18:51:03.344Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'favorites_platform_type_enum'
[2023-05-07T18:51:03.347Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.350Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'users_type_user_enum'
[2023-05-07T18:51:03.353Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'users_platform_type_enum'
[2023-05-07T18:51:03.355Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.358Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencers_status_enum'
[2023-05-07T18:51:03.361Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencers_pay_status_enum'
[2023-05-07T18:51:03.364Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencers_shipment_status_enum'
[2023-05-07T18:51:03.366Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencer_tasks_campaign_type_enum'
[2023-05-07T18:51:03.369Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencer_tasks_format_type_enum'
[2023-05-07T18:51:03.372Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencer_tasks_status_enum'
[2023-05-07T18:51:03.375Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'evidence_task_scans_status_enum'
[2023-05-07T18:51:03.377Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.381Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.386Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.389Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:51:03.411Z][QUERY]: SELECT * FROM "information_schema"."tables" WHERE "table_schema" = 'public' AND "table_name" = 'typeorm_metadata'
[2023-05-07T18:51:03.448Z][QUERY]: ALTER TABLE "transaction_history" ALTER COLUMN "amount" TYPE double precision
[2023-05-07T18:51:03.450Z][QUERY]: ALTER TABLE "transaction_history" ALTER COLUMN "fee" TYPE double precision
[2023-05-07T18:51:03.452Z][QUERY]: ALTER TABLE "transaction_history" ALTER COLUMN "net" TYPE double precision
[2023-05-07T18:51:03.472Z][QUERY]: COMMIT
[2023-05-07T18:59:07.063Z][QUERY]: SELECT * FROM current_schema()
[2023-05-07T18:59:07.579Z][QUERY]: CREATE EXTENSION IF NOT EXISTS "uuid-ossp"
[2023-05-07T18:59:07.632Z][QUERY]: SELECT version();
[2023-05-07T18:59:07.637Z][QUERY]: START TRANSACTION
[2023-05-07T18:59:07.658Z][QUERY]: SELECT * FROM current_schema()
[2023-05-07T18:59:07.670Z][QUERY]: SELECT * FROM current_database()
[2023-05-07T18:59:07.681Z][QUERY]: SELECT "table_schema", "table_name" FROM "information_schema"."tables" WHERE ("table_schema" = 'public' AND "table_name" = 'formats') OR ("table_schema" = 'public' AND "table_name" = 'campaign_formats') OR ("table_schema" = 'public' AND "table_name" = 'user_categories') OR ("table_schema" = 'public' AND "table_name" = 'categories') OR ("table_schema" = 'public' AND "table_name" = 'campaign_categories') OR ("table_schema" = 'public' AND "table_name" = 'transaction_history') OR ("table_schema" = 'public' AND "table_name" = 'user_notifications') OR ("table_schema" = 'public' AND "table_name" = 'notifications') OR ("table_schema" = 'public' AND "table_name" = 'campaigns') OR ("table_schema" = 'public' AND "table_name" = 'evidence_task_scans') OR ("table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks') OR ("table_schema" = 'public' AND "table_name" = 'campaign_influencers') OR ("table_schema" = 'public' AND "table_name" = 'user_instagram_informations') OR ("table_schema" = 'public' AND "table_name" = 'user_favorites') OR ("table_schema" = 'public' AND "table_name" = 'favorites') OR ("table_schema" = 'public' AND "table_name" = 'languages') OR ("table_schema" = 'public' AND "table_name" = 'user_brand_profiles') OR ("table_schema" = 'public' AND "table_name" = 'balance') OR ("table_schema" = 'public' AND "table_name" = 'users') OR ("table_schema" = 'public' AND "table_name" = 'admin-roles') OR ("table_schema" = 'public' AND "table_name" = 'admins') OR ("table_schema" = 'public' AND "table_name" = 'admin-permissions') OR ("table_schema" = 'public' AND "table_name" = 'admin-prices') OR ("table_schema" = 'public' AND "table_name" = 'admin-feedbacks') OR ("table_schema" = 'public' AND "table_name" = 'admin-configurations') OR ("table_schema" = 'public' AND "table_name" = 'permissions') OR ("table_schema" = 'public' AND "table_name" = 'roles') OR ("table_schema" = 'public' AND "table_name" = 'user_role')
[2023-05-07T18:59:07.698Z][QUERY]: SELECT TRUE FROM information_schema.columns WHERE table_name = 'pg_class' and column_name = 'relispartition'
[2023-05-07T18:59:07.708Z][QUERY]: SELECT columns.*, pg_catalog.col_description(('"' || table_catalog || '"."' || table_schema || '"."' || table_name || '"')::regclass::oid, ordinal_position) AS description, ('"' || "udt_schema" || '"."' || "udt_name" || '"')::"regtype" AS "regtype", pg_catalog.format_type("col_attr"."atttypid", "col_attr"."atttypmod") AS "format_type" FROM "information_schema"."columns" LEFT JOIN "pg_catalog"."pg_attribute" AS "col_attr" ON "col_attr"."attname" = "columns"."column_name" AND "col_attr"."attrelid" = ( SELECT "cls"."oid" FROM "pg_catalog"."pg_class" AS "cls" LEFT JOIN "pg_catalog"."pg_namespace" AS "ns" ON "ns"."oid" = "cls"."relnamespace" WHERE "cls"."relname" = "columns"."table_name" AND "ns"."nspname" = "columns"."table_schema" ) WHERE ("table_schema" = 'public' AND "table_name" = 'roles') OR ("table_schema" = 'public' AND "table_name" = 'user_role') OR ("table_schema" = 'public' AND "table_name" = 'permissions') OR ("table_schema" = 'public' AND "table_name" = 'campaign_formats') OR ("table_schema" = 'public' AND "table_name" = 'formats') OR ("table_schema" = 'public' AND "table_name" = 'user_categories') OR ("table_schema" = 'public' AND "table_name" = 'categories') OR ("table_schema" = 'public' AND "table_name" = 'campaign_categories') OR ("table_schema" = 'public' AND "table_name" = 'languages') OR ("table_schema" = 'public' AND "table_name" = 'transaction_history') OR ("table_schema" = 'public' AND "table_name" = 'campaigns') OR ("table_schema" = 'public' AND "table_name" = 'user_instagram_informations') OR ("table_schema" = 'public' AND "table_name" = 'user_favorites') OR ("table_schema" = 'public' AND "table_name" = 'favorites') OR ("table_schema" = 'public' AND "table_name" = 'admin-permissions') OR ("table_schema" = 'public' AND "table_name" = 'user_brand_profiles') OR ("table_schema" = 'public' AND "table_name" = 'balance') OR ("table_schema" = 'public' AND "table_name" = 'users') OR ("table_schema" = 'public' AND "table_name" = 'campaign_influencers') OR ("table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks') OR ("table_schema" = 'public' AND "table_name" = 'evidence_task_scans') OR ("table_schema" = 'public' AND "table_name" = 'admin-roles') OR ("table_schema" = 'public' AND "table_name" = 'admins') OR ("table_schema" = 'public' AND "table_name" = 'admin-feedbacks') OR ("table_schema" = 'public' AND "table_name" = 'admin-prices') OR ("table_schema" = 'public' AND "table_name" = 'admin-configurations') OR ("table_schema" = 'public' AND "table_name" = 'user_notifications') OR ("table_schema" = 'public' AND "table_name" = 'notifications')
[2023-05-07T18:59:07.709Z][QUERY]: SELECT "ns"."nspname" AS "table_schema", "t"."relname" AS "table_name", "cnst"."conname" AS "constraint_name", pg_get_constraintdef("cnst"."oid") AS "expression", CASE "cnst"."contype" WHEN 'p' THEN 'PRIMARY' WHEN 'u' THEN 'UNIQUE' WHEN 'c' THEN 'CHECK' WHEN 'x' THEN 'EXCLUDE' END AS "constraint_type", "a"."attname" AS "column_name" FROM "pg_constraint" "cnst" INNER JOIN "pg_class" "t" ON "t"."oid" = "cnst"."conrelid" INNER JOIN "pg_namespace" "ns" ON "ns"."oid" = "cnst"."connamespace" LEFT JOIN "pg_attribute" "a" ON "a"."attrelid" = "cnst"."conrelid" AND "a"."attnum" = ANY ("cnst"."conkey") WHERE "t"."relkind" IN ('r', 'p') AND (("ns"."nspname" = 'public' AND "t"."relname" = 'roles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_role') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'permissions') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_formats') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'formats') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'languages') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'transaction_history') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaigns') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_instagram_informations') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_favorites') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'favorites') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-permissions') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_brand_profiles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'balance') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'users') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_influencers') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_influencer_tasks') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'evidence_task_scans') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-roles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admins') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-feedbacks') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-prices') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-configurations') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_notifications') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'notifications'))
[2023-05-07T18:59:07.709Z][QUERY]: SELECT "ns"."nspname" AS "table_schema", "t"."relname" AS "table_name", "i"."relname" AS "constraint_name", "a"."attname" AS "column_name", CASE "ix"."indisunique" WHEN 't' THEN 'TRUE' ELSE'FALSE' END AS "is_unique", pg_get_expr("ix"."indpred", "ix"."indrelid") AS "condition", "types"."typname" AS "type_name" FROM "pg_class" "t" INNER JOIN "pg_index" "ix" ON "ix"."indrelid" = "t"."oid" INNER JOIN "pg_attribute" "a" ON "a"."attrelid" = "t"."oid"  AND "a"."attnum" = ANY ("ix"."indkey") INNER JOIN "pg_namespace" "ns" ON "ns"."oid" = "t"."relnamespace" INNER JOIN "pg_class" "i" ON "i"."oid" = "ix"."indexrelid" INNER JOIN "pg_type" "types" ON "types"."oid" = "a"."atttypid" LEFT JOIN "pg_constraint" "cnst" ON "cnst"."conname" = "i"."relname" WHERE "t"."relkind" IN ('r', 'p') AND "cnst"."contype" IS NULL AND (("ns"."nspname" = 'public' AND "t"."relname" = 'roles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_role') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'permissions') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_formats') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'formats') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_categories') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'languages') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'transaction_history') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaigns') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_instagram_informations') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_favorites') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'favorites') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-permissions') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_brand_profiles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'balance') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'users') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_influencers') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'campaign_influencer_tasks') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'evidence_task_scans') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-roles') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admins') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-feedbacks') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-prices') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'admin-configurations') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'user_notifications') OR ("ns"."nspname" = 'public' AND "t"."relname" = 'notifications'))
[2023-05-07T18:59:07.710Z][QUERY]: SELECT "con"."conname" AS "constraint_name", "con"."nspname" AS "table_schema", "con"."relname" AS "table_name", "att2"."attname" AS "column_name", "ns"."nspname" AS "referenced_table_schema", "cl"."relname" AS "referenced_table_name", "att"."attname" AS "referenced_column_name", "con"."confdeltype" AS "on_delete", "con"."confupdtype" AS "on_update", "con"."condeferrable" AS "deferrable", "con"."condeferred" AS "deferred" FROM ( SELECT UNNEST ("con1"."conkey") AS "parent", UNNEST ("con1"."confkey") AS "child", "con1"."confrelid", "con1"."conrelid", "con1"."conname", "con1"."contype", "ns"."nspname", "cl"."relname", "con1"."condeferrable", CASE WHEN "con1"."condeferred" THEN 'INITIALLY DEFERRED' ELSE 'INITIALLY IMMEDIATE' END as condeferred, CASE "con1"."confdeltype" WHEN 'a' THEN 'NO ACTION' WHEN 'r' THEN 'RESTRICT' WHEN 'c' THEN 'CASCADE' WHEN 'n' THEN 'SET NULL' WHEN 'd' THEN 'SET DEFAULT' END as "confdeltype", CASE "con1"."confupdtype" WHEN 'a' THEN 'NO ACTION' WHEN 'r' THEN 'RESTRICT' WHEN 'c' THEN 'CASCADE' WHEN 'n' THEN 'SET NULL' WHEN 'd' THEN 'SET DEFAULT' END as "confupdtype" FROM "pg_class" "cl" INNER JOIN "pg_namespace" "ns" ON "cl"."relnamespace" = "ns"."oid" INNER JOIN "pg_constraint" "con1" ON "con1"."conrelid" = "cl"."oid" WHERE "con1"."contype" = 'f' AND (("ns"."nspname" = 'public' AND "cl"."relname" = 'roles') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_role') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'permissions') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaign_formats') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'formats') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_categories') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'categories') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaign_categories') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'languages') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'transaction_history') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaigns') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_instagram_informations') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_favorites') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'favorites') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-permissions') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_brand_profiles') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'balance') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'users') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaign_influencers') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'campaign_influencer_tasks') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'evidence_task_scans') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-roles') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admins') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-feedbacks') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-prices') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'admin-configurations') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'user_notifications') OR ("ns"."nspname" = 'public' AND "cl"."relname" = 'notifications')) ) "con" INNER JOIN "pg_attribute" "att" ON "att"."attrelid" = "con"."confrelid" AND "att"."attnum" = "con"."child" INNER JOIN "pg_class" "cl" ON "cl"."oid" = "con"."confrelid"  AND "cl"."relispartition" = 'f'INNER JOIN "pg_namespace" "ns" ON "cl"."relnamespace" = "ns"."oid" INNER JOIN "pg_attribute" "att2" ON "att2"."attrelid" = "con"."conrelid" AND "att2"."attnum" = "con"."parent"
[2023-05-07T18:59:07.859Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'roles' AND "column_name"='permissions'
[2023-05-07T18:59:07.875Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'transaction_history' AND "column_name"='direction_transaction'
[2023-05-07T18:59:07.875Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='walk_in_type'
[2023-05-07T18:59:07.876Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='platform_type'
[2023-05-07T18:59:07.876Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='campaign_type'
[2023-05-07T18:59:07.877Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='gender'
[2023-05-07T18:59:07.877Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='promote_type'
[2023-05-07T18:59:07.877Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='mentions'
[2023-05-07T18:59:07.877Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='hash_tags'
[2023-05-07T18:59:07.877Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='additional_pictures'
[2023-05-07T18:59:07.877Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaigns' AND "column_name"='languages'
[2023-05-07T18:59:07.877Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'user_instagram_informations' AND "column_name"='account_type'
[2023-05-07T18:59:07.878Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'favorites' AND "column_name"='platform_type'
[2023-05-07T18:59:07.878Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'user_brand_profiles' AND "column_name"='brand_picture'
[2023-05-07T18:59:07.878Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'users' AND "column_name"='type_user'
[2023-05-07T18:59:07.878Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'users' AND "column_name"='platform_type'
[2023-05-07T18:59:07.878Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'users' AND "column_name"='language_array'
[2023-05-07T18:59:07.879Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencers' AND "column_name"='status'
[2023-05-07T18:59:07.879Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencers' AND "column_name"='pay_status'
[2023-05-07T18:59:07.879Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencers' AND "column_name"='shipment_status'
[2023-05-07T18:59:07.879Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks' AND "column_name"='campaign_type'
[2023-05-07T18:59:07.879Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks' AND "column_name"='format_type'
[2023-05-07T18:59:07.879Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'campaign_influencer_tasks' AND "column_name"='status'
[2023-05-07T18:59:07.881Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'evidence_task_scans' AND "column_name"='status'
[2023-05-07T18:59:07.881Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'evidence_task_scans' AND "column_name"='caption'
[2023-05-07T18:59:07.881Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'evidence_task_scans' AND "column_name"='tagged_users'
[2023-05-07T18:59:07.881Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'evidence_task_scans' AND "column_name"='hash_tags'
[2023-05-07T18:59:07.881Z][QUERY]: SELECT "udt_schema", "udt_name" FROM "information_schema"."columns" WHERE "table_schema" = 'public' AND "table_name" = 'admin-roles' AND "column_name"='permissions'
[2023-05-07T18:59:07.884Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.888Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'transaction_history_direction_transaction_enum'
[2023-05-07T18:59:07.891Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_walk_in_type_enum'
[2023-05-07T18:59:07.893Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_platform_type_enum'
[2023-05-07T18:59:07.896Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_campaign_type_enum'
[2023-05-07T18:59:07.899Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_gender_enum'
[2023-05-07T18:59:07.902Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaigns_promote_type_enum'
[2023-05-07T18:59:07.904Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.907Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.912Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.917Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.924Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'user_instagram_informations_account_type_enum'
[2023-05-07T18:59:07.927Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'favorites_platform_type_enum'
[2023-05-07T18:59:07.930Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.933Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'users_type_user_enum'
[2023-05-07T18:59:07.936Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'users_platform_type_enum'
[2023-05-07T18:59:07.940Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.944Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencers_status_enum'
[2023-05-07T18:59:07.948Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencers_pay_status_enum'
[2023-05-07T18:59:07.951Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencers_shipment_status_enum'
[2023-05-07T18:59:07.953Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencer_tasks_campaign_type_enum'
[2023-05-07T18:59:07.957Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencer_tasks_format_type_enum'
[2023-05-07T18:59:07.961Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'campaign_influencer_tasks_status_enum'
[2023-05-07T18:59:07.964Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'evidence_task_scans_status_enum'
[2023-05-07T18:59:07.966Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.969Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.972Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:07.975Z][QUERY]: SELECT "e"."enumlabel" AS "value" FROM "pg_enum" "e" INNER JOIN "pg_type" "t" ON "t"."oid" = "e"."enumtypid" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = 'public' AND "t"."typname" = 'text'
[2023-05-07T18:59:08.007Z][QUERY]: SELECT * FROM "information_schema"."tables" WHERE "table_schema" = 'public' AND "table_name" = 'typeorm_metadata'
[2023-05-07T18:59:08.049Z][QUERY]: ALTER TABLE "transaction_history" ALTER COLUMN "amount" TYPE double precision
[2023-05-07T18:59:08.052Z][QUERY]: ALTER TABLE "transaction_history" ALTER COLUMN "fee" TYPE double precision
[2023-05-07T18:59:08.053Z][QUERY]: ALTER TABLE "transaction_history" ALTER COLUMN "net" TYPE double precision
[2023-05-07T18:59:08.099Z][QUERY]: COMMIT

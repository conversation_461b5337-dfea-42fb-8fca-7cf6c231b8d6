import * as os from 'node:os';
import * as moment from 'moment';
import { Promise as BBPromise } from 'bluebird';
import { Inject, Injectable } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { RedisClientType } from 'redis';
import { B<PERSON>ets, DataSource, In, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Not } from 'typeorm';
import { BULL_JOB_NAMES, BullHornConfig, DefaultRoleIds, RECIPIENT_PER_TASK, REDIS_KEYS } from 'src/configs/configs.constants';
import { BullHornService } from 'src/middlewares/bullhorn/bullhorn.service';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';
import { SequenceRepository } from 'src/modules/mail/repositories/sequence.repostiory';
import { SequenceStepRepository } from 'src/modules/mail/repositories/sequence-step.repostiory';
import { SequenceInstanceRepository } from 'src/modules/mail/repositories/sequence-instance.repository';
import { SequenceStepTaskRepository } from 'src/modules/mail/repositories/sequence-step-task.repository';
import { SequenceActivityLogRepository } from 'src/modules/mail/repositories/sequence-activity-log.repository';
// import { SequenceStepTaskWorker } from 'src/cron/sequence-step-task.worker';
import { SequenceStatus } from 'src/modules/mail/entities/sequence.entity';
import { SequenceActivityType } from 'src/modules/mail/entities/sequence-activity-log.entity';
import { SequenceStepStatus, SequenceStepType } from 'src/modules/mail/entities/sequence-step.entity';
import { SharedUserService } from 'src/modules/shared/shared-user.service';
import { SharedMailService } from 'src/modules/shared/shared-mail.service';
import { chunkArray, filterOutByKey, getValueByPath, queryAllByChunk, removeDuplicatesByKey, safeParseJSON } from 'src/common/utils/helpers.util';
import { SequenceInstanceEntity, SequenceStepFlag } from 'src/modules/mail/entities/sequence-instance.entity';
import { RecipientInfo } from 'src/modules/mail/dto/send-email.dto';
import { CoreJobLead } from 'src/modules/jobs/dto/job-lead/job-lead.dto';
import { MERGE_TAGS_PLACE_HOLDERS, MergeTagMappingEnum } from 'src/modules/mail/dto/send-email-process.dto';
import { sendNoteToBullhorn } from 'src/modules/jobs/utils/bullhorn-service.util';
import * as unipileClient from 'src/modules/jobs/utils/unipile-service.utils';
import { JobLeadsRepository } from 'src/modules/jobs/repository/job-leads.repository';
import { LicenseType as UserLicenseType, UserEntity } from 'src/modules/user/entities/user.entity';
import { SequenceStepTaskWorker } from 'src/cron/sequence-step-task.worker';
import { Queue } from 'bullmq';
import { InjectBullhornEmailNoteEventQueue, InjectInternalQueueEventQueue } from 'src/modules/queue/queue.decorator';
import { CrmBullhornService } from './modules/crm/services/crm-bullhorn.service';
import { CrmContactSequenceStepRepository } from './modules/crm/repositories/crm-contact-sequence-step.repository';
import { CrmContactSequenceRepository } from './modules/crm/repositories/crm-contact-sequence.repository';
import { CrmContactRepository } from './modules/crm/repositories/crm-contact.repository';
import { CrmNoteResourceName } from './modules/crm/entities/crm-file.entity';
import { CrmNoteEntity } from './modules/crm/entities/crm-note.entity';
import { CreditService } from './modules/subscription/services/credit.service';
import { FEATURES } from './configs/features.config';
import { JobSearchEntity } from './modules/jobs/entities/job-search.entity';
import { JobSearchService } from './modules/jobs/service/job-search.service';
import { CronJobStatusService } from './modules/redis/cronjob-status.service';

@Injectable()
export class CronService {
  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: RedisClientType,
    @InjectBullhornEmailNoteEventQueue() private bullhornEmailNoteEvent: Queue,
    @InjectInternalQueueEventQueue() private internalQueueEvent: Queue,
    private readonly dataSource: DataSource,
    private readonly sharedUserService: SharedUserService,
    private readonly sharedMailService: SharedMailService,
    private readonly creditService: CreditService,
    private readonly jobLeadRepository: JobLeadsRepository,
    private readonly sequenceRepository: SequenceRepository,
    private readonly sequenceStepRepository: SequenceStepRepository,
    private readonly sequenceInstanceRepository: SequenceInstanceRepository,
    private readonly sequenceStepTaskRepository: SequenceStepTaskRepository,
    private readonly sequenceActivityLogRepository: SequenceActivityLogRepository,
    private readonly sequenceStepTaskWorker: SequenceStepTaskWorker,
    private readonly crmBullhornService: CrmBullhornService,
    private readonly crmContactSequenceStepRepository: CrmContactSequenceStepRepository,
    private readonly crmContactSequenceRepository: CrmContactSequenceRepository,
    private readonly crmContactRepository: CrmContactRepository,
    private readonly jobSearchService: JobSearchService,
    private readonly cronJobStatusService: CronJobStatusService,
  ) {}

  private async acquireLock(jobName: string, ttl = 3 * 60) {
    const lockKey = `cronjob-lock:${jobName}`;
    const lockAcquired = await this.redisClient.set(lockKey, 'locked', {
      NX: true,
      EX: ttl,
    });

    return lockAcquired;
  }

  private async releaseLock(jobName: string) {
    const lockKey = `cronjob-lock:${jobName}`;
    await this.redisClient.del(lockKey);
  }

  /**
   * Wrapper method to execute cronjob with status tracking
   */
  private async executeCronJobWithStatus<T>(
    jobName: string,
    schedule: string,
    executionFunction: (executionId: string) => Promise<T>,
    ttl: number = 3 * 60,
  ): Promise<T | null> {
    const executionId = Math.random().toString(20).substring(2, 12);
    const start = Date.now();

    // Initialize cronjob info
    await this.cronJobStatusService.initializeCronJob(jobName, schedule);

    // Try to acquire lock
    const lock = await this.acquireLock(jobName, ttl);
    if (!lock) {
      console.log(`[CRON JOB] ${jobName} is already running on another instance.`);
      await this.cronJobStatusService.lockCronJob(jobName);
      return null;
    }

    // Start execution tracking
    await this.cronJobStatusService.startCronJobExecution(jobName, executionId);

    try {
      console.log(`[CRON JOB] 🔜 Executing ${jobName} ${executionId}.`);

      // Execute the actual cronjob function
      const result = await executionFunction(executionId);

      // Mark as completed
      const duration = Date.now() - start;
      await this.cronJobStatusService.completeCronJobExecution(jobName, executionId, duration);
      console.log(`[CRON JOB] 🔚 Complete ${jobName} ${executionId} after ${duration}ms.`);

      return result;
    } catch (error) {
      // Mark as failed
      const duration = Date.now() - start;
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.cronJobStatusService.failCronJobExecution(
        jobName,
        executionId,
        errorMessage,
        duration,
      );
      console.error(`[CRON JOB] ❌ Failed ${jobName} ${executionId} after ${duration}ms:`, error);
      throw error;
    } finally {
      await this.releaseLock(jobName);
    }
  }

  private async sendNote(instance: SequenceInstanceEntity) {
    const { recipients, hotlistIds, contactListIds } = instance;
    const clientContacts: RecipientInfo[] = recipients.filter(({ id }) => id);
    if (hotlistIds && hotlistIds.length) {
      const hotlistRecipients = await this.sharedMailService.findHotListContact(
        hotlistIds,
        instance.user,
      );
      clientContacts.push(...hotlistRecipients);
    }

    if (contactListIds && contactListIds.length) {
      const contactListRecipients =
        await this.sharedMailService.findContactFromContactList(contactListIds);
      clientContacts.push(
        ...contactListRecipients.map((item) => ({
          ...item,
          clientCorporation: { name: item.companyName },
          occupation: item.contactTitle,
          linkedinProfileUrl: item.linkedInProfileUrl,
          address: { address1: item.contactLocation },
        })),
      );
    }

    if (!clientContacts.length) {
      return {
        id: instance.id,
        emailSeq: instance?.sequence,
        seqStep: instance?.sequenceStep,
        reason: "Don't have any client contact to send note",
        type: SequenceStepType.NOTE,
      };
    }

    if (!instance.user.organizationId && instance.user.roleId !== DefaultRoleIds.SUPER_ADMIN) {
      console.log(`User ${instance.user.id} does not belong to an organization.`);

      return {
        id: instance.id,
        emailSeq: instance?.sequence,
        seqStep: instance?.sequenceStep,
        reason: `User ${instance.user.id} does not belong to an organization`,
        type: SequenceStepType.NOTE,
      };
    }

    // get access token which use to call to bullhorn
    const bhToken = await this.sharedMailService.getBhToken(instance.user.organizationId);
    // if (!bhToken) {
    //   return {
    //     id: instance.id,
    //     emailSeq: instance?.sequence,
    //     seqStep: instance?.sequenceStep,
    //     reason: 'Cannot get bhToken',
    //     type: SequenceStepType.NOTE,
    //   };
    // }

    let job: CoreJobLead;
    if (instance.sequenceStep?.externalJobId || instance.sequenceStep?.jobBoardId) {
      job = instance.sequenceStep?.externalJobId
        ? await this.jobLeadRepository.findOneBy({
            job_lead_external_id: instance.sequenceStep.externalJobId,
          })
        : await this.jobLeadRepository.findOneBy({
            job_board_id: instance.sequenceStep.jobBoardId,
          });
    }
    const isLicenseConnected = instance.user?.licenseType === UserLicenseType.CONNECTED;

    const crmContactsUnordered = await this.crmBullhornService.findAndSyncBullhornContacts(
      clientContacts,
      instance.user?.organizationId,
      instance.user?.licenseType,
    );
    const crmContactsMap = crmContactsUnordered.reduce((acc, contact) => {
      acc[contact.id] = contact;
      if (contact.bullhornId) {
        acc[contact.bullhornId] = contact;
      }

      return acc;
    }, {});

    /**
     * We can send to many recipients at the one request, but for Merge Tags
     * we have to send each one
     */
    const dataPayloads = await BBPromise.map(
      clientContacts,
      async (recipient) => {
        let crmContact = crmContactsMap[recipient.id];

        const emailInfo: {
          sender: object;
          recipient: RecipientInfo;
          job: CoreJobLead;
          currentSentDateTime: string;
        } = {
          sender: {
            name: instance.user.fullName,
            email: instance.user.email,
          },
          recipient,
          job,
          currentSentDateTime: new Intl.DateTimeFormat('en-GB', {
            dateStyle: 'full',
            timeStyle: 'long',
          }).format(),
        };

        MERGE_TAGS_PLACE_HOLDERS?.forEach((rawPlaceHolder) => {
          const purePlaceHolder = rawPlaceHolder.split('{{').join('').split('}}').join('');
          const replacedContent = getValueByPath(
            emailInfo,
            MergeTagMappingEnum[purePlaceHolder],
            '',
          );

          instance.sequenceStep.content = instance.sequenceStep.content
            ?.split(rawPlaceHolder)
            ?.join(Array.isArray(replacedContent) ? replacedContent.join(', ') : replacedContent);
        });
        const content = instance.sequenceStep.content?.replace(
          /\$(first_name|first name)\$/g,
          recipient.name?.split(' ')[0],
        );

        return {
          content,
          recipient,
          crmPayload: crmContact
            ? {
                resourceName: CrmNoteResourceName.CONTACT,
                resourceId: crmContact.id,
                action: instance.sequenceStep.subject,
                title: instance.sequenceStep.subject,
                content,
                creator: instance.user,
                contact: {
                  id: crmContact.id,
                },
                company: {
                  id: crmContact.companyId,
                },
              }
            : null,
          bullhornPayload: {
            comments: content,
            personReference: {
              id: recipient.id,
              searchEntity: 'ClientContact',
            },
            action: instance.sequenceStep.subject,
            clientContacts: [{ id: recipient.id }],
            commentingPerson: { id: instance.user?.consultantId || 1 },
          },
        };
      },
      { concurrency: 20 },
    );

    // Bulk insert CRM notes in chunks of 1000
    const crmPayloads = dataPayloads.map((info) => info.crmPayload).filter(Boolean);
    for (let i = 0; i < crmPayloads.length; i += 1000) {
      const chunk = crmPayloads.slice(i, i + 1000);
      await this.dataSource.getRepository(CrmNoteEntity).insert(chunk);
    }

    let results = [];
    if (isLicenseConnected) {
      results = await BBPromise.map(
        dataPayloads,
        async (info) => {
          try {
            const result = await sendNoteToBullhorn(
              bhToken.bhRestToken,
              bhToken.corporateRestUrl,
              info.bullhornPayload,
            );
            return result;
          } catch (error) {
            console.log('Error sending note to Bullhorn', error);

            return null;
          }
        },
        { concurrency: 20 },
      );
    }

    const sentIds = results.reduce(
      (ids, item) => (item ? [...ids, item?.changedEntityId] : ids),
      [],
    );

    return {
      sentIds,
      id: instance.id,
      emailSeq: instance?.sequence,
      seqStep: instance?.sequenceStep,
      type: SequenceStepType.NOTE,
    };
  }

  private async sendLinkedInRequest(instance: SequenceInstanceEntity, instanceInfo: any) {
    const listEmailReplied = instanceInfo?.repliers?.filter(Boolean).flat();
    const skipRecipients = instance.skipRecipients || [];

    let hotlistRecipients = [];
    let contactListRecipients = [];
    if (instance.hotlistIds?.length) {
      hotlistRecipients = await this.sharedMailService.findHotListContact(
        instance.hotlistIds,
        instance.user,
      );
    }

    if (instance.contactListIds?.length) {
      contactListRecipients = await this.sharedMailService.findContactFromContactList(
        instance.contactListIds, // contactListId
      );
    }

    const recipients = removeDuplicatesByKey(
      [...instance.recipients, ...hotlistRecipients, ...contactListRecipients],
      (item: any) => item?.email?.toLowerCase(),
    );
    const skipEmails = skipRecipients.map((item) => item.domain || item.email || item);

    const { clientContacts, noLinkedInProfiles, optOutContacts, skipContacts } = recipients.reduce(
      (acc, recipient) => {
        if (
          skipEmails.includes(recipient.email) ||
          skipEmails.includes(recipient.email?.split('@').at(1)?.toLowerCase()) ||
          listEmailReplied.includes(recipient.email)
        ) {
          acc.skipContacts.push(recipient);
          return acc;
        }
        // Opt out not for LinkedIn request
        // if (recipient.massMailOptOut) {
        //   acc.optOutContacts.push(recipient);
        //   return acc;
        // }

        // Unsubscribe not for LinkedIn request
        // if (recipient.isUnsubscribeEmail) {
        //   acc.optOutContacts.push(recipient);
        //   return acc;
        // }

        const linkedinProfileUrl =
          recipient.linkedinProfileUrl || recipient.customText1 || recipient.linkedInProfileUrl;
        if (unipileClient.getLinkedInIdentifier(linkedinProfileUrl)) {
          acc.clientContacts.push(recipient);
        } else {
          acc.noLinkedInProfiles.push(recipient);
        }
        return acc;
      },
      {
        clientContacts: [],
        noLinkedInProfiles: [],
        optOutContacts: [],
        skipContacts: [],
      },
    );

    const { content } = instance.sequenceStep;
    const actionLinkedIn = safeParseJSON(content);

    if (
      (!clientContacts.length && !skipContacts.length && !optOutContacts.length) ||
      !actionLinkedIn?.length
    ) {
      return {
        id: instance.id,
        emailSeq: instance?.sequence,
        seqStep: instance?.sequenceStep,
        reason: "Don't have any client contact to send LinkedIn request",
        activityStatus: SequenceActivityType.SKIPPED,
        displayText: 'No LinkedIn Profile URL was found on any contacts so we will skip this step.',
        type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
      };
    }

    if (!instance.user.unipileAccountId) {
      return {
        id: instance.id,
        emailSeq: instance?.sequence,
        seqStep: instance?.sequenceStep,
        reason: "Don't have unipileAccountId to send LinkedIn request",
        activityStatus: SequenceActivityType.FAILED,
        displayText:
          'Step {{step_number}} was failed because your account has disconnected with your LinkedIn Profile please visit the settings to link it back up.',
        type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
      };
    }

    if (noLinkedInProfiles.length) {
      await this.sequenceActivityLogRepository.insert({
        type: SequenceActivityType.SKIPPED,
        sequence: { id: instance.sequence.id },
        sequenceStep: { id: instance.sequenceStep.id },
        content: {
          sequenceInstance: instance.id,
          type: instance.sequenceStep.type,
          emails: noLinkedInProfiles.map((item) => item.email),
          recipients: noLinkedInProfiles.map(({ email, name }) => ({ email, name })),
          hostname: os.hostname(),
          displayText: "No valid LinkedIn URL was found so we couldn't send to {{contact_name}}.",
          reason: 'NO_LINKEDIN_PROFILE',
        },
        occurredAt: new Date().toISOString(),
      });
    }
    if (optOutContacts.length) {
      await this.sequenceActivityLogRepository.insert({
        type: SequenceActivityType.SKIPPED,
        sequence: { id: instance.sequence.id },
        sequenceStep: { id: instance.sequenceStep.id },
        content: {
          sequenceInstance: instance.id,
          type: instance.sequenceStep.type,
          emails: optOutContacts.map((item) => item.email),
          recipients: optOutContacts.map(({ email, name }) => ({ email, name })),
          hostname: os.hostname(),
          displayText: "Email wasn't sent to {{contact_name}} because they have Opted Out.",
          reason: 'OPT_OUT',
        },
        occurredAt: new Date().toISOString(),
      });
    }
    if (skipContacts.length) {
      await this.sequenceActivityLogRepository.insert({
        type: SequenceActivityType.SKIPPED,
        sequence: { id: instance.sequence.id },
        sequenceStep: { id: instance.sequenceStep.id },
        content: {
          sequenceInstance: instance.id,
          type: instance.sequenceStep.type,
          emails: skipContacts.map((item) => item.email),
          recipients: skipContacts.map(({ email, name }) => ({ email, name })),
          hostname: os.hostname(),
          reason: 'SKIPPED_CONTACT',
          displayText:
            'Step {{step_number}} was not sent to {{contact_name}} because this contact has replied previously.',
        },
        occurredAt: new Date().toISOString(),
      });
    }

    const chunkRecipients = chunkArray(clientContacts, RECIPIENT_PER_TASK);
    const stepTasks = chunkRecipients.map((chunk) =>
      this.sequenceStepTaskRepository.create({
        recipients: chunk,
        user: instance.user,
        sequence: instance.sequence,
        sequenceStep: instance.sequenceStep,
        sequenceInstance: instance,
        scheduledAt: new Date(),
        type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
        action: actionLinkedIn.map((action: any) => action.type).join(','),
        extraInformation: {
          hostname: os.hostname(),
        },
      }),
    );

    await this.sharedMailService.bulkUpsertTasks(stepTasks);
    if (!stepTasks.length) {
      return {
        id: instance.id,
        emailSeq: instance?.sequence,
        seqStep: instance?.sequenceStep,
        reason: 'No valid contacts to send LinkedIn request',
        activityStatus: SequenceActivityType.SKIPPED,
        displayText: 'No valid contacts were found to send the LinkedIn connection request to.',
        type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
      };
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async triggerSequenceEmail() {
    return this.executeCronJobWithStatus(
      'triggerSequenceEmail',
      CronExpression.EVERY_5_MINUTES,
      async (executionId: string) => {
        const currentDate = new Date();
        const inWorkingTimeUserIds =
          await this.sharedUserService.getInWorkingTimeUserIds(currentDate);

        const data = await this.sequenceInstanceRepository
          .createQueryBuilder('instance')
          .leftJoinAndSelect('instance.user', 'user')
          .leftJoinAndSelect('instance.sequence', 'sequence')
          .leftJoinAndSelect('instance.sequenceStep', 'sequenceStep')
          .where('instance.scheduledAt < :currentDate', { currentDate })
          .andWhere('instance.status = :status', { status: SequenceStepStatus.PENDING })
          .andWhere('sequence.status = :sequenceStatus', { sequenceStatus: SequenceStatus.LIVE })
          .andWhere('user.id IN (:...userIds)', { userIds: inWorkingTimeUserIds })
          .orderBy('instance.scheduledAt', 'ASC')
          .addOrderBy('sequenceStep.stepIndex', 'ASC')
          .getMany();

        const groupBySeqVersion = data.reduce((acc, item) => {
          const key = `${item.sequence.id}#${item.version}`;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(item);

          return acc;
        }, {});

        const newData = Object.values(groupBySeqVersion);

        let listIds = [];
        if (data.length !== 0) {
          const seqIds = data.map(({ sequence }) => sequence.id);
          const repliedData = await this.sequenceInstanceRepository
            .createQueryBuilder('si')
            .innerJoin('sequence_steps', 'ss', 'ss.id = si.sequence_step_id')
            .select(
              'si.sequence_id AS sequence_id, si.version AS version, ARRAY_AGG(si.repliers) AS repliers, MAX(ss.step_index) AS step_end, MAX(si.executed_at) AS last_executed_at',
            )
            .where('si.sequence_id IN (:...seqIds)', { seqIds })
            .groupBy('si.sequence_id, si.version')
            .getRawMany();

          const unacceptedLinkedInInvitations = await this.sequenceInstanceRepository
            .createQueryBuilder('si')
            .select('si.sequence_id AS sequence_id, si.version AS version')
            .where('si.sequence_id IN (:...seqIds) AND si.flag = :flag', {
              seqIds,
              flag: SequenceStepFlag.INVITATION_SENT,
            })
            .groupBy('si.sequence_id, si.version')
            .getRawMany();

          let unexecutedLinkedInInvitations = await this.sequenceInstanceRepository
            .createQueryBuilder('si')
            .innerJoin('sequence_steps', 'ss', 'ss.id = si.sequence_step_id')
            .select(
              'si.sequence_id AS sequence_id, si.version AS version, ss.step_index AS step_index, ss.content AS content',
            )
            .where(
              'si.sequence_id IN (:...seqIds) AND (si.status IN (:...status) OR si.executed_at IS NULL) AND ss.type = :ssType',
              {
                seqIds,
                // If step is pending, off, stop, error => it's not executed yet
                status: [
                  SequenceStepStatus.PENDING,
                  SequenceStepStatus.SCHEDULED,
                  SequenceStepStatus.OFF,
                  SequenceStepStatus.STOP,
                  SequenceStepStatus.ERROR,
                ],
                ssType: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
              },
            )
            .groupBy('si.sequence_id, si.version, ss.step_index, ss.content')
            .getRawMany();
          unexecutedLinkedInInvitations = unexecutedLinkedInInvitations.filter((item) => {
            const actionLinkedIn = safeParseJSON(item.content);

            return !!actionLinkedIn?.find(({ type }) => type === 'INVITATION');
          });

          const validInstanceIds = [];
          const skippedInstanceIds = [];
          const scheduledInstanceIds = [];
          const notSentInstanceIds = [];
          const sendRes = await BBPromise.allSettled(
            newData.map(async (instances: any[]) => {
              let instance = instances[0];
              const potentialInstance = instances.find(
                (item: any, i: any) =>
                  i > 0 && item.sequenceStep.type !== SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
              );
              const linkedInInvitationUnaccepted = unacceptedLinkedInInvitations.find(
                (rep) =>
                  rep.version === instance.version && rep.sequence_id === instance.sequence.id,
              );
              // Check previous Invitation steps
              const linkedInInvitationUnexecuted = unexecutedLinkedInInvitations.find(
                (rep) =>
                  rep.version === instance.version &&
                  rep.sequence_id === instance.sequence.id &&
                  rep.step_index < instance.sequenceStep.stepIndex,
              );

              if (
                // Wait for LinkedIn steps only
                (linkedInInvitationUnaccepted || linkedInInvitationUnexecuted) &&
                instance.sequenceStep.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST
              ) {
                const actionLinkedIn = safeParseJSON(instance.sequenceStep.content);
                const normalMessage = actionLinkedIn.find(({ type }) => type === 'NORMAL_MESSAGE');
                if (normalMessage) {
                  console.log(
                    '[CRON JOB] triggerSequenceEmail - Linked Invitation not accepted yet',
                    instance.sequence.id,
                    instance.version,
                  );
                  skippedInstanceIds.push(instance.id);
                  if (!potentialInstance) {
                    return false;
                  }
                  instance = potentialInstance;
                }
              }

              const instanceInfo = repliedData.find(
                (rep) =>
                  rep.version === instance.version && rep.sequence_id === instance.sequence.id,
              );
              const lastExecutedAt = new Date(instanceInfo?.last_executed_at);
              if (lastExecutedAt.getTime()) {
                const delayTime = instance.sequenceStep.delays.reduce(
                  (acc, item) =>
                    acc + item.delay * this.sharedMailService.DELAY_UNIT_MULTIPLE[item.unit],
                  0,
                );
                if (lastExecutedAt.getTime() + delayTime > currentDate.getTime()) {
                  console.log(
                    '[CRON JOB] triggerSequenceEmail - Delay time not passed yet',
                    instance.sequence.id,
                    instance.version,
                  );
                  skippedInstanceIds.push(instance.id);

                  return false;
                }
              }

              validInstanceIds.push(instance.id);
              if (instanceInfo?.step_end === instance.sequenceStep.stepIndex) {
                listIds.push(instance.sequence.id);
              }
              // Handle for case type is NOTE
              if (instance.sequenceStep.type === SequenceStepType.NOTE) {
                const result = await this.sendNote(instance);
                // Is last step
                if (instanceInfo?.step_end === instance.sequenceStep.stepIndex) {
                  const ancestorIds = await this.sharedMailService.getSequenceAncestorsIds(
                    instance.sequence.id,
                  );
                  await Promise.all([
                    this.sequenceRepository.update(
                      { id: In([...ancestorIds, instance.sequence.id]) },
                      { isMarkedAsCompleted: true, status: SequenceStatus.STOP },
                    ),
                    this.sequenceActivityLogRepository.insert({
                      type: SequenceActivityType.STOPPED,
                      sequence: { id: instance.sequence.id },
                      content: {
                        reason: 'AUTO_COMPLETED',
                        explain: 'Last step is NOTE',
                        stepIndex: instance.sequenceStep.stepIndex,
                      },
                      occurredAt: new Date(),
                    }),
                    this.internalQueueEvent.add(
                      BULL_JOB_NAMES.INTERNAL_QUEUE_EVENT,
                      {
                        type: 'SEND_EMAIL_SEQUENCE_STOP',
                        data: {
                          user: instance.user,
                          sequence: instance.sequence,
                          isCompleted: true,
                        },
                      },
                      {
                        jobId: `SEND_EMAIL_SEQUENCE_STOP#${instance.user?.id}#${instance.sequence?.id}`,
                        attempts: 1,
                        delay: 30 * 1000, // 30s
                      },
                    ),
                  ]);
                }

                return result;
              }
              if (instance.sequenceStep.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
                const result = await this.sendLinkedInRequest(instance, instanceInfo);
                if (!result) {
                  scheduledInstanceIds.push(instance.id);
                } else {
                  notSentInstanceIds.push(instance.id);
                }
                // Is last step & result - step is skipped
                if (instanceInfo?.step_end === instance.sequenceStep.stepIndex && result) {
                  const ancestorIds = await this.sharedMailService.getSequenceAncestorsIds(
                    instance.sequence.id,
                  );

                  await Promise.all([
                    this.sequenceRepository.update(
                      { id: In([...ancestorIds, instance.sequence.id]) },
                      { isMarkedAsCompleted: true, status: SequenceStatus.STOP },
                    ),
                    this.sequenceActivityLogRepository.insert({
                      type: SequenceActivityType.STOPPED,
                      sequence: { id: instance.sequence.id },
                      content: {
                        reason: 'AUTO_COMPLETED',
                        explain: 'No recipients to send LinkedIn & is last step',
                        stepIndex: instance.sequenceStep.stepIndex,
                      },
                      occurredAt: new Date(),
                    }),
                    this.internalQueueEvent.add(
                      BULL_JOB_NAMES.INTERNAL_QUEUE_EVENT,
                      {
                        type: 'SEND_EMAIL_SEQUENCE_STOP',
                        data: {
                          user: instance.user,
                          sequence: instance.sequence,
                          isCompleted: true,
                        },
                      },
                      {
                        jobId: `SEND_EMAIL_SEQUENCE_STOP#${instance.user?.id}#${instance.sequence?.id}`,
                        attempts: 1,
                        delay: 30 * 1000, // 30s
                      },
                    ),
                  ]);
                }

                return result;
              }
              // Handle for case type is TASK
              if (instance.sequenceStep.type === SequenceStepType.TASK) {
                const result = await this.sharedMailService.createSequenceTasks(instance);
                // Is last step
                if (instanceInfo?.step_end === instance.sequenceStep.stepIndex) {
                  const ancestorIds = await this.sharedMailService.getSequenceAncestorsIds(
                    instance.sequence.id,
                  );
                  await Promise.all([
                    this.sequenceRepository.update(
                      { id: In([...ancestorIds, instance.sequence.id]) },
                      { isMarkedAsCompleted: true, status: SequenceStatus.STOP },
                    ),
                    this.sequenceActivityLogRepository.insert({
                      type: SequenceActivityType.STOPPED,
                      sequence: { id: instance.sequence.id },
                      content: {
                        reason: 'AUTO_COMPLETED',
                        explain: 'Last step is TASK',
                        stepIndex: instance.sequenceStep.stepIndex,
                      },
                      occurredAt: new Date(),
                    }),
                    this.internalQueueEvent.add(
                      BULL_JOB_NAMES.INTERNAL_QUEUE_EVENT,
                      {
                        type: 'SEND_EMAIL_SEQUENCE_STOP',
                        data: {
                          user: instance.user,
                          sequence: instance.sequence,
                          isCompleted: true,
                        },
                      },
                      {
                        jobId: `SEND_EMAIL_SEQUENCE_STOP#${instance.user?.id}#${instance.sequence?.id}`,
                        attempts: 1,
                        delay: 30 * 1000, // 30s
                      },
                    ),
                  ]);
                }

                return result;
              }

              let sentCount = 0;
              const listEmailReplied = instanceInfo?.repliers?.filter(Boolean).flat();
              const skipRecipients = instance.skipRecipients || [];
              const skipEmails = skipRecipients.map((item) => item.domain || item.email || item);
              const skippedNormalRecipients = [];
              const normalRecipients = (instance.recipients || []).reduce((acc, item) => {
                if (
                  !listEmailReplied?.includes(item.email) &&
                  !skipEmails.includes(item.email) &&
                  !skipEmails.includes(item.email?.split('@').at(1)?.toLowerCase())
                ) {
                  acc.push(item);

                  return acc;
                }
                skippedNormalRecipients.push(item);

                return acc;
              }, []);
              const optOutContacts = [];
              const invalidContacts = [];
              const stepRecipients = normalRecipients.reduce(
                (acc, recipient: any) => {
                  if (!recipient?.email?.includes('@')) {
                    return acc;
                  }
                  if (recipient.massMailOptOut || recipient.isUnsubscribeEmail) {
                    acc.optOutRecipients.push(recipient);
                  } else {
                    acc.validRecipients.push(recipient);
                  }

                  return acc;
                },
                { validRecipients: [], optOutRecipients: [] },
              );
              const chunkRecipients = chunkArray(
                stepRecipients.validRecipients,
                RECIPIENT_PER_TASK,
              );
              optOutContacts.push(...stepRecipients.optOutRecipients);

              // const stepTasks = chunkRecipients.map((chunk, chunkIndex) =>
              //   this.sequenceStepTaskQueue.add(
              //     BULL_JOB_NAMES.SEQUENCE_STEP_TASK,
              //     {
              //       recipients: chunk,
              //       user: instance.user,
              //       sequence: instance.sequence,
              //       sequenceStep: instance.sequenceStep,
              //       sequenceInstance: instance,
              //       scheduledAt: new Date(),
              //       type: SequenceStepType.EMAIL,
              //     },
              //     {
              //       jobId: `${instance.id}#${chunkIndex}`,
              //       attempts: BULL_JOB_ATTEMPTS,
              //       delay: 30 * 1000 * chunkIndex,
              //     },
              //   ),
              // );

              const stepTasks = chunkRecipients.map((chunk) =>
                this.sequenceStepTaskRepository.create({
                  recipients: chunk,
                  user: instance.user,
                  sequence: instance.sequence,
                  sequenceStep: instance.sequenceStep,
                  sequenceInstance: instance,
                  scheduledAt: new Date(),
                  type: SequenceStepType.EMAIL,
                  extraInformation: {
                    hostname: os.hostname(),
                  },
                }),
              );

              const skippedRecipientDomains = [];
              let hotlistContacts = [];
              let contactListContacts = [];
              let shortListContacts = [];
              if (instance.hotlistIds?.length) {
                hotlistContacts = await this.sharedMailService.findHotListContact(
                  instance.hotlistIds,
                  instance.user,
                );
              }

              if (instance?.shortListIds?.length) {
                shortListContacts = await this.sharedMailService.findShortListCandidate(
                  instance?.shortListIds,
                );
              }

              if (instance.contactListIds?.length) {
                contactListContacts = await this.sharedMailService.findContactFromContactList(
                  instance.contactListIds,
                );
              }
              if (contactListContacts?.length) {
                let contactListRecipients = filterOutByKey(
                  removeDuplicatesByKey(contactListContacts, (item: any) =>
                    item?.email?.toLowerCase(),
                  ),
                  [
                    ...instance.recipients,
                    ...skipRecipients,
                    ...listEmailReplied.map((repliedEmail: string) => ({ email: repliedEmail })),
                    ...(hotlistContacts || []),
                  ],
                  (item: any) => item?.email?.toLowerCase(),
                );
                const skippedDomains = skipRecipients.map((item) => item.domain);
                contactListRecipients = contactListRecipients.reduce((acc, item) => {
                  if (skippedDomains.includes(item.email?.split('@').at(1)?.toLowerCase())) {
                    skippedRecipientDomains.push(item);

                    return acc;
                  }
                  acc.push(item);

                  return acc;
                }, []);

                const { validRecipients, optOutRecipients } = contactListRecipients.reduce(
                  (acc, recipient: any) => {
                    if (!recipient?.email?.includes('@')) {
                      return acc;
                    }
                    if (recipient.massMailOptOut || recipient.isUnsubscribeEmail) {
                      acc.optOutRecipients.push(recipient);
                    } else {
                      acc.validRecipients.push(recipient);
                    }

                    return acc;
                  },
                  { validRecipients: [], optOutRecipients: [] },
                );
                const chunkContactListRecipients = chunkArray(validRecipients, RECIPIENT_PER_TASK);
                optOutContacts.push(...optOutRecipients);

                chunkContactListRecipients.forEach((chunk, chunkIndex) => {
                  // stepTasks.push(
                  //   this.sequenceStepTaskQueue.add(
                  //     BULL_JOB_NAMES.SEQUENCE_STEP_TASK,
                  //     {
                  //       recipients: chunk,
                  //       user: instance.user,
                  //       sequence: instance.sequence,
                  //       sequenceStep: instance.sequenceStep,
                  //       sequenceInstance: instance,
                  //       scheduledAt: new Date(),
                  //       type: SequenceStepType.EMAIL,
                  //       sourceType: 'CONTACTLIST',
                  //     },
                  //     {
                  //       jobId: `${instance.id}#C${chunkIndex}`,
                  //       attempts: BULL_JOB_ATTEMPTS,
                  //       delay: 30 * 1000 * chunkIndex,
                  //     },
                  //   ),
                  // );
                  stepTasks.push(
                    this.sequenceStepTaskRepository.create({
                      recipients: chunk,
                      user: instance.user,
                      sequence: instance.sequence,
                      sequenceStep: instance.sequenceStep,
                      sequenceInstance: instance,
                      scheduledAt: new Date(),
                      type: SequenceStepType.EMAIL,
                      sourceType: 'CONTACTLIST',
                      extraInformation: {
                        hostname: os.hostname(),
                      },
                    }),
                  );
                });
              }
              if (hotlistContacts?.length) {
                let hotlistRecipients = filterOutByKey(
                  removeDuplicatesByKey(hotlistContacts, (item: any) => item?.email?.toLowerCase()),
                  [
                    ...instance.recipients,
                    ...skipRecipients,
                    ...listEmailReplied.map((repliedEmail: string) => ({ email: repliedEmail })),
                  ],
                  (item: any) => item?.email?.toLowerCase(),
                );
                const skippedDomains = skipRecipients.map((item) => item.domain);
                hotlistRecipients = hotlistRecipients.reduce((acc, item) => {
                  if (skippedDomains.includes(item.email?.split('@').at(1)?.toLowerCase())) {
                    skippedRecipientDomains.push(item);

                    return acc;
                  }
                  acc.push(item);

                  return acc;
                }, []);

                const { validRecipients, invalidRecipients, optOutRecipients } =
                  hotlistRecipients.reduce(
                    (acc, recipient: any) => {
                      if (
                        instance.sequence?.skippedEmails?.includes(recipient?.email?.toLowerCase())
                      ) {
                        acc.invalidRecipients.push(recipient);

                        return acc;
                      }
                      if (recipient.massMailOptOut || recipient.isUnsubscribeEmail) {
                        acc.optOutRecipients.push(recipient);
                      } else {
                        acc.validRecipients.push(recipient);
                      }

                      return acc;
                    },
                    { validRecipients: [], invalidRecipients: [], optOutRecipients: [] },
                  );
                optOutContacts.push(...optOutRecipients);
                invalidContacts.push(...invalidRecipients);
                const chunkHotlistRecipients = chunkArray(validRecipients, RECIPIENT_PER_TASK);

                chunkHotlistRecipients.forEach((chunk, chunkIndex) => {
                  // stepTasks.push(
                  //   this.sequenceStepTaskQueue.add(
                  //     BULL_JOB_NAMES.SEQUENCE_STEP_TASK,
                  //     {
                  //       recipients: chunk,
                  //       user: instance.user,
                  //       sequence: instance.sequence,
                  //       sequenceStep: instance.sequenceStep,
                  //       sequenceInstance: instance,
                  //       scheduledAt: new Date(),
                  //       type: SequenceStepType.EMAIL,
                  //       sourceType: 'HOTLIST',
                  //     },
                  //     {
                  //       jobId: `${instance.id}#H${chunkIndex}`,
                  //       attempts: BULL_JOB_ATTEMPTS,
                  //       delay: 10 * 1000 * chunkIndex,
                  //     },
                  //   ),
                  // );

                  stepTasks.push(
                    this.sequenceStepTaskRepository.create({
                      recipients: chunk,
                      user: instance.user,
                      sequence: instance.sequence,
                      sequenceStep: instance.sequenceStep,
                      sequenceInstance: instance,
                      scheduledAt: new Date(),
                      type: SequenceStepType.EMAIL,
                      sourceType: 'HOTLIST',
                      extraInformation: {
                        hostname: os.hostname(),
                      },
                    }),
                  );
                });
              }
              if (shortListContacts?.length) {
                const shortListRecipients = filterOutByKey(
                  removeDuplicatesByKey(shortListContacts.flat(), (item: any) =>
                    item?.email?.toLowerCase(),
                  ),
                  [
                    ...instance.recipients,
                    ...skipRecipients,
                    ...listEmailReplied.map((repliedEmail: string) => ({ email: repliedEmail })),
                    ...(hotlistContacts || []),
                  ],
                  'email',
                );

                const { validRecipients, optOutRecipients } = shortListRecipients.reduce(
                  (acc, recipient: any) => {
                    if (!recipient?.email?.includes('@')) {
                      return acc;
                    }
                    if (recipient.massMailOptOut || recipient.isUnsubscribeEmail) {
                      acc.optOutRecipients.push(recipient);
                    } else {
                      acc.validRecipients.push(recipient);
                    }
                    return acc;
                  },
                  { validRecipients: [], optOutRecipients: [] },
                );
                const chunkShortListRecipients = chunkArray(validRecipients, RECIPIENT_PER_TASK);
                optOutContacts.push(...optOutRecipients);
                chunkShortListRecipients.forEach((chunk, chunkIndex) => {
                  stepTasks.push(
                    this.sequenceStepTaskRepository.create({
                      recipients: chunk,
                      user: instance.user,
                      sequence: instance.sequence,
                      sequenceStep: instance.sequenceStep,
                      sequenceInstance: instance,
                      scheduledAt: new Date(),
                      type: SequenceStepType.EMAIL,
                      sourceType: 'SHORTLIST',
                      extraInformation: {
                        hostname: os.hostname(),
                      },
                    }),
                  );
                });
              }

              await this.sharedMailService.bulkUpsertTasks(stepTasks);
              // await this.dataSource
              //   .createQueryBuilder()
              //   .insert()
              //   .into(SequenceStepTaskEntity)
              //   .values(stepTasks)
              //   .orUpdate(
              //     ['updated_at', 'extra_information'],
              //     ['id'],
              //   )
              //   .execute();
              // await Promise.all(stepTasks);
              if (stepTasks.length) {
                scheduledInstanceIds.push(instance.id);
              } else {
                notSentInstanceIds.push(instance.id);
              }
              await BBPromise.all(
                removeDuplicatesByKey(
                  [...skipRecipients, ...skippedRecipientDomains, ...skippedNormalRecipients],
                  (item: any) => item?.email?.toLowerCase(),
                ).map((item) => {
                  if (item.domain) {
                    return null;
                  }

                  const skipEmail = item.email || item;
                  const skipType = item.type || SequenceActivityType.REPLIED;

                  return skipType === SequenceActivityType.BOUNCE
                    ? this.sequenceActivityLogRepository.insert({
                        type: SequenceActivityType.SKIPPED,
                        sequence: { id: instance.sequence.id },
                        sequenceStep: { id: instance.sequenceStep.id },
                        content: {
                          sequenceInstance: instance.id,
                          type: instance.sequenceStep.type,
                          email: skipEmail,
                          hostname: os.hostname(),
                          reason: skipType,
                        },
                        occurredAt: new Date().toISOString(),
                      })
                    : null;
                }),
              );
              if (optOutContacts.length) {
                await this.sequenceActivityLogRepository.insert({
                  type: SequenceActivityType.SKIPPED,
                  sequence: { id: instance.sequence.id },
                  sequenceStep: { id: instance.sequenceStep.id },
                  content: {
                    sequenceInstance: instance.id,
                    type: instance.sequenceStep.type,
                    emails: optOutContacts.map((item) => item.email),
                    recipients: optOutContacts.map(({ email, name }) => ({ email, name })),
                    reason: 'OPT_OUT',
                  },
                  occurredAt: new Date().toISOString(),
                });
              }

              if (invalidContacts.length) {
                await this.sequenceActivityLogRepository.insert({
                  type: SequenceActivityType.SKIPPED,
                  sequence: { id: instance.sequence.id },
                  sequenceStep: { id: instance.sequenceStep.id },
                  content: {
                    sequenceInstance: instance.id,
                    type: instance.sequenceStep.type,
                    emails: invalidContacts.map((item) => item.email),
                    recipients: invalidContacts.map(({ email, name }) => ({ email, name })),
                    reason: 'INVALID_EMAIL',
                  },
                  occurredAt: new Date().toISOString(),
                });
              }

              // Is last step && no recipients
              if (instanceInfo?.step_end === instance.sequenceStep.stepIndex && !stepTasks.length) {
                const ancestorIds = await this.sharedMailService.getSequenceAncestorsIds(
                  instance.sequence.id,
                );
                await BBPromise.all([
                  this.sequenceRepository.update(
                    { id: In([...ancestorIds, instance.sequence.id]) },
                    { isMarkedAsCompleted: true, status: SequenceStatus.STOP },
                  ),
                  this.sequenceActivityLogRepository.insert({
                    type: SequenceActivityType.STOPPED,
                    sequence: { id: instance.sequence.id },
                    content: {
                      reason: 'AUTO_COMPLETED',
                      explain: 'No recipients to send email & is last step',
                      stepIndex: instance.sequenceStep.stepIndex,
                    },
                    occurredAt: new Date(),
                  }),
                  this.internalQueueEvent.add(
                    BULL_JOB_NAMES.INTERNAL_QUEUE_EVENT,
                    {
                      type: 'SEND_EMAIL_SEQUENCE_STOP',
                      data: {
                        user: instance.user,
                        sequence: instance.sequence,
                        isCompleted: true,
                      },
                    },
                    {
                      jobId: `SEND_EMAIL_SEQUENCE_STOP#${instance.user?.id}#${instance.sequence?.id}`,
                      attempts: 1,
                      delay: 30 * 1000, // 30s
                    },
                  ),
                ]);
              }

              return false;
            }),
          );
          await BBPromise.all(
            data?.map((item) => {
              if (skippedInstanceIds.includes(item.id)) {
                return null;
              }

              let instanceStatus = SequenceStepStatus.SENT;
              if (scheduledInstanceIds.includes(item.id)) {
                instanceStatus = SequenceStepStatus.SCHEDULED;
              }
              if (notSentInstanceIds.includes(item.id)) {
                instanceStatus = SequenceStepStatus.NOT_SENT;
              }
              if (!validInstanceIds.includes(item.id)) {
                return null; // Skip for invalid instance
              }
              return this.sequenceInstanceRepository.update(item.id, {
                status: instanceStatus,
                sentIds: item.sentIds,
                executedAt: currentDate,
              });
            }),
          );

          const promiseUpdate = sendRes
            .map((res: any, index: number) => {
              if (!res.isFulfilled()) {
                console.error(res.reason());

                return [
                  this.sequenceActivityLogRepository.insert({
                    type: SequenceActivityType.FAILED,
                    sequence: { id: data[index]?.sequence?.id },
                    sequenceStep: { id: data[index]?.sequenceStep?.id },
                    content: {
                      sequenceInstance: data[index]?.id,
                      type: data[index]?.sequenceStep?.type,
                      count: data[index]?.recipients?.length,
                      recipients: data[index]?.recipients,
                      hostname: os.hostname(),
                      reason: res.reason(),
                    },
                    occurredAt: new Date().toISOString(),
                  }),
                ];
              }
              const item = res.value();
              if (!item) {
                return [];
              }

              const sentCount = Number(item?.sentCount) || 0;

              const processList: any[] = [
                this.sequenceActivityLogRepository.insert({
                  type: item.activityStatus || SequenceActivityType.SENT,
                  sequence: { id: item?.emailSeq?.id },
                  sequenceStep: { id: item?.seqStep?.id },
                  content: {
                    sequenceInstance: item.id,
                    type: item.type,
                    count: item.sentCount,
                    reason: item.reason,
                    hostname: os.hostname(),
                    displayText: item.displayText,
                  },
                  occurredAt: new Date().toISOString(),
                }),
              ];

              if (sentCount) {
                processList.push(
                  this.sequenceRepository.update(item?.emailSeq?.id, {
                    pendingCount: () => `pending_count - ${sentCount}`,
                    sentCount: () => `sent_count + ${sentCount}`,
                  }),
                );
              }

              return processList;
            })
            .flat();

          await BBPromise.all(promiseUpdate);
        }

        // Only stop sequence when there is someone replying or user turns it OFF by themselves
        // if (listIds.length > 0) {
        //   await this.sequenceRepository
        //     .createQueryBuilder()
        //     .update()
        //     .set({ status: SequenceStatus.STOP })
        //     .whereInIds(listIds)
        //     .execute();
        // }
      },
    );
  }

  async triggerSequenceTask(items: any[]) {
    const data = await this.sequenceStepTaskRepository.find({
      where: {
        id: In(items.map((item) => item.id)),
        executedAt: IsNull(),
      },
      relations: {
        user: true,
        sequence: true,
        sequenceStep: true,
        sequenceInstance: true,
      },
    });

    await this.sequenceStepTaskRepository
      .createQueryBuilder()
      .update()
      .set({ executedAt: new Date() })
      .where({ id: In(items.map((item) => item.id)) })
      .execute();

    const results = await BBPromise.allSettled(
      data.map(async (task) => {
        let isDoneTask = false;
        try {
          await this.sequenceStepTaskWorker.handler({
            id: task.id,
            data: task,
            isCronTask: true,
            attemptsMade: task.attemptsMade,
          } as any);
          isDoneTask = true;
        } catch (error) {
          console.error(`ERROR triggerSequenceTask ${task?.id}`, error);
          if (
            task.attemptsMade >= 3 ||
            (task.attemptsMade >= 1 && task.sourceType === 'HOTLIST') ||
            error?.code === 'UNRECOVERABLE_ERROR'
          ) {
            isDoneTask = true;

            return null;
          }

          const nextDate = new Date();
          if (error?.code === 'LINKEDIN_RETRYABLE_ERROR') {
            nextDate.setHours(nextDate.getHours() + 12);
          }

          return this.sequenceStepTaskRepository.insert({
            ...task,
            id: undefined,
            attemptsMade: (task.attemptsMade || 0) + 1,
            scheduledAt: nextDate,
          });
        }

        if (
          isDoneTask &&
          !['UNFOLLOW_LINKEDIN', 'CANCEL_LINKEDIN_INVITATION'].includes(task.type)
        ) {
          try {
            const isLastStep = await this.sharedMailService.isLastSequenceStep(
              task.sequence?.id,
              task.sequenceStep?.id,
            );
            const stillHasTask = await this.sequenceStepTaskRepository.findOneBy({
              sequence: { id: task.sequence.id },
              executedAt: IsNull(),
            });
            if (isLastStep && !stillHasTask) {
              const ancestorIds = [];
              await this.sharedMailService.getSequenceAncestorsIds(task.sequence.id);

              await BBPromise.all([
                this.sequenceRepository.update(
                  { id: In([...ancestorIds, task.sequence.id]) },
                  { isMarkedAsCompleted: true, status: SequenceStatus.STOP },
                ),
                this.sequenceActivityLogRepository.insert({
                  type: SequenceActivityType.STOPPED,
                  sequence: { id: task.sequence.id },
                  content: {
                    reason: 'AUTO_COMPLETED',
                    explain: 'The last task of the last sequence step has been executed',
                    stepIndex: task.sequenceStep?.stepIndex,
                  },
                  occurredAt: new Date(),
                }),
                this.internalQueueEvent.add(
                  BULL_JOB_NAMES.INTERNAL_QUEUE_EVENT,
                  {
                    type: 'SEND_EMAIL_SEQUENCE_STOP',
                    data: {
                      user: task.user,
                      sequence: task.sequence,
                      isCompleted: true,
                    },
                  },
                  {
                    jobId: `SEND_EMAIL_SEQUENCE_STOP#${task.user?.id}#${task.sequence?.id}`,
                    attempts: 1,
                    delay: 30 * 1000, // 30s
                  },
                ),
              ]);
            }
            if (task.type === 'EMAIL') {
              const instanceTask = await this.sequenceStepTaskRepository.findOneBy({
                sequence: { id: task.sequence.id },
                sequenceInstance: { id: task.sequenceInstance.id },
                executedAt: IsNull(),
              });
              if (!instanceTask) {
                // Done step instance
                await this.bullhornEmailNoteEvent.add(
                  BULL_JOB_NAMES.BULLHORN_EMAIL_NOTE_EVENT,
                  {
                    sequenceStepTaskId: task.id,
                    sequenceInstanceId: task.sequenceInstance?.id,
                    subject: task.sequenceStep.subject,
                    body: task.sequenceStep.content,
                    user: {
                      consultantId: task.user?.consultantId,
                      organizationId: task.user?.organizationId,
                    },
                  },
                  {
                    jobId: `${task.sequenceInstance?.id}#${task.type}`,
                    attempts: 1,
                    delay: 60 * 1000, // 1m
                  },
                );
                console.log(
                  `[CRON JOB] triggerSequenceTask isDoneTask ${task?.id} - Add note for email sent`,
                );
              }
            }
          } catch (error) {
            console.error(`ERROR triggerSequenceTask isDoneTask ${task?.id}`, error);
          }
        }
      }),
      { concurrency: 10 },
    );
  }

  @Cron('*/3 * * * *')
  async triggerSequenceStepTaskLinkedIn() {
    return this.executeCronJobWithStatus(
      'triggerSequenceStepTaskLinkedIn',
      '*/3 * * * *',
      async (executionId: string) => {
        const currentDate = new Date();
        const inWorkingTimeUserIds =
          await this.sharedUserService.getInWorkingTimeUserIds(currentDate);

        const subQuery = this.sequenceStepTaskRepository
          .createQueryBuilder('task')
          .innerJoin('sequences', 's', "s.id = task.sequence_id AND s.status = 'LIVE'")
          .select('task.user_id', 'user_id')
          .addSelect('task.id', 'id')
          .addSelect('task.created_at', 'created_at')
          .where('task.user_id IN (:...userIds)', { userIds: inWorkingTimeUserIds })
          .andWhere('task.executed_at IS NULL')
          .andWhere('task.scheduled_at < :currentDate', { currentDate: new Date() })
          .andWhere('task.type = :type AND action NOT IN (:...actions)', {
            type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
            actions: ['REACT_POST', 'FOLLOW'],
          })
          .orderBy('task.created_at', 'ASC')
          .addSelect(
            `ROW_NUMBER() OVER (PARTITION BY task.user_id ORDER BY task.created_at ASC)`,
            'row_num',
          );

        // Query chính để lấy các items theo row_num
        const query = this.sequenceStepTaskRepository
          .createQueryBuilder('t')
          .innerJoin(`(${subQuery.getQuery()})`, 'ranked', 't.id = ranked.id', {
            ...subQuery.getParameters(),
          })
          .select('t.id', 'id')
          .addSelect('t.user_id', 'user_id')
          .addSelect('t.type', 'type')
          .addSelect('t.sequence_id', 'sequence_id')
          .addSelect('t.action', 'action')
          .addSelect('t.sequence_step_id', 'sequence_step_id')
          .addSelect('t.sequence_instance_id', 'sequence_instance_id')
          .addSelect('t.created_at', 'created_at')
          .where('ranked.row_num <= :limit', { limit: 1 })
          .orderBy('t.created_at', 'ASC');

        const items = await query.getRawMany();

        const { items: newItems } = items.reduce(
          (acc, item) => {
            if (!acc.count[item.user_id]) {
              acc.count[item.user_id] = 0;
            }
            if (!acc.linkedInCount[item.sequence_id]) {
              acc.linkedInCount[item.sequence_id] = 0;
            }
            // Only handle 1 linkedin request per sequence
            if (acc.count[item.user_id] < 50) {
              if (item.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
                if (acc.linkedInCount[item.sequence_id] < 1) {
                  acc.items.push(item);
                }
                acc.linkedInCount[item.sequence_id] += 1;
              } else {
                acc.items.push(item);
              }
            }
            acc.count[item.user_id] += 1;

            return acc;
          },
          { count: {}, linkedInCount: {}, items: [] },
        );
        await this.triggerSequenceTask(newItems);
        return { processedItems: newItems.length };
      },
      120, // TTL in seconds
    );
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async triggerSequenceStepTaskLinkedInDailyVolume() {
    return this.executeCronJobWithStatus(
      'triggerSequenceStepTaskLinkedInDailyVolume',
      CronExpression.EVERY_5_MINUTES,
      async (executionId: string) => {
        const DAILY_VOLUME = 60;

        const currentDate = new Date();
        const inWorkingTimeUserIds =
          await this.sharedUserService.getInWorkingTimeUserIds(currentDate);
        // Get the start and end of the current date
        const startOfDay = new Date(currentDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(currentDate.setHours(23, 59, 59, 999));

        const results = await this.sequenceStepTaskRepository
          .createQueryBuilder('task')
          .select('task.userId', 'userId')
          .addSelect('task.action', 'action')
          .addSelect('COUNT(task.id)', 'count')
          .where('task.attemptsMade = :attemptsMade', { attemptsMade: 0 })
          .andWhere('task.executedAt BETWEEN :startOfDay AND :endOfDay', { startOfDay, endOfDay })
          .groupBy('task.userId')
          .addGroupBy('task.action')
          .getRawMany();

        const groupStats = results.reduce((acc, result) => {
          if (!acc[result.userId]) {
            acc[result.userId] = {};
          }
          acc[result.userId][result.action] = Number(result.count);

          return acc;
        }, {});

        const subQuery = this.sequenceStepTaskRepository
          .createQueryBuilder('task')
          .innerJoin('sequences', 's', "s.id = task.sequence_id AND s.status = 'LIVE'")
          .select('task.id', 'id')
          .addSelect('task.user_id', 'user_id')
          .addSelect('task.created_at', 'created_at')
          .where('task.user_id IN (:...userIds)', { userIds: inWorkingTimeUserIds })
          .andWhere('task.executed_at IS NULL')
          .andWhere('task.scheduled_at < :currentDate', { currentDate: new Date() })
          .andWhere('task.type = :type AND action IN (:...actions)', {
            type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
            actions: ['REACT_POST', 'FOLLOW'],
          })
          .orderBy('task.created_at', 'ASC')
          .addSelect(
            `ROW_NUMBER() OVER (PARTITION BY task.user_id ORDER BY task.created_at ASC)`,
            'row_num',
          );

        // Query chính để lấy các items có row_num <= 50
        const query = this.sequenceStepTaskRepository
          .createQueryBuilder('t')
          .innerJoin(`(${subQuery.getQuery()})`, 'ranked', 't.id = ranked.id', {
            ...subQuery.getParameters(),
          })
          .select('t.id', 'id')
          .addSelect('t.user_id', 'user_id')
          .addSelect('t.type', 'type')
          .addSelect('t.sequence_id', 'sequence_id')
          .addSelect('t.action', 'action')
          .addSelect('t.sequence_step_id', 'sequence_step_id')
          .addSelect('t.sequence_instance_id', 'sequence_instance_id')
          .addSelect('t.created_at', 'created_at')
          .where('ranked.row_num <= :limit', { limit: 1 })
          .orderBy('t.created_at', 'ASC');

        const items = await query.getRawMany();

        const sequenceOutOfVolumeLogs = {};

        const { items: newItems } = items.reduce(
          (acc, item) => {
            if (!acc.count[item.user_id]) {
              acc.count[item.user_id] = 0;
            }
            // Only handle 1 linkedin request per sequence
            if (
              acc.count[item.user_id] < 1 &&
              (!groupStats[item.user_id]?.[item.action] ||
                groupStats[item.user_id]?.[item.action] < DAILY_VOLUME)
            ) {
              acc.items.push(item);
              acc.count[item.user_id] += 1;
            } else if (
              acc.count[item.user_id] < 1 &&
              groupStats[item.user_id]?.[item.action] >= DAILY_VOLUME
            ) {
              //
              sequenceOutOfVolumeLogs[item.sequence_step_id] = item;
            }

            return acc;
          },
          { count: {}, items: [] },
        );

        const stepsAlreadyLogged = Object.keys(sequenceOutOfVolumeLogs).length
          ? await this.sequenceActivityLogRepository
              .createQueryBuilder('sal')
              .where('sal.sequence_step_id IN (:...sequenceStepIds)', {
                sequenceStepIds: Object.keys(sequenceOutOfVolumeLogs),
              })
              .andWhere('sal.type = :activityType', { activityType: SequenceActivityType.SKIPPED })
              .andWhere("sal.content->>'type' = :contentType", {
                contentType: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
              })
              .andWhere("sal.content->>'reason' = :contentReason", {
                contentReason: 'OUT_OF_VOLUME',
              })
              .andWhere('sal.created_at BETWEEN :startOfDay AND :endOfDay', {
                startOfDay,
                endOfDay,
              })
              .select('sal.sequence_step_id AS sequence_step_id')
              .groupBy('sal.sequence_step_id')
              .getRawMany()
          : [];

        const ignoreSequenceSteps = stepsAlreadyLogged.map(
          ({ sequence_step_id }) => sequence_step_id,
        );

        const activityLogs = Object.keys(sequenceOutOfVolumeLogs).reduce((acc, sequenceStepId) => {
          if (!ignoreSequenceSteps.includes(sequenceStepId)) {
            acc.push({
              type: SequenceActivityType.SKIPPED,
              sequence: { id: sequenceOutOfVolumeLogs[sequenceStepId].sequence_id },
              sequenceStep: { id: sequenceStepId },
              content: {
                type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
                reason: 'OUT_OF_VOLUME',
                displayText: 'Your account was reached the daily requests volume.',
              },
              occurredAt: new Date().toISOString(),
            });
          }

          return acc;
        }, []);
        if (activityLogs.length) {
          this.sequenceActivityLogRepository.insert(activityLogs);
        }

        await this.triggerSequenceTask(newItems);
        return { processedItems: newItems.length };
      },
    );
  }

  @Cron('*/60 * * * * *')
  async triggerSequenceStepTaskSendGrid() {
    return this.executeCronJobWithStatus(
      'triggerSequenceStepTaskSendGrid',
      '*/60 * * * * *',
      async (executionId: string) => {
        const currentDate = new Date();
        const inWorkingTimeUserIds =
          await this.sharedUserService.getInWorkingTimeUserIds(currentDate);
        // console.log(inWorkingTimeUserIds, "inWorkingTimeUserIds")

        const subQuery = this.sequenceStepTaskRepository
          .createQueryBuilder('task')
          .innerJoin('sequences', 's', "s.id = task.sequence_id AND s.status = 'LIVE'")
          .select('task.user_id', 'user_id')
          .addSelect('task.id', 'id')
          .addSelect('task.created_at', 'created_at')
          .where('task.user_id IN (:...userIds)', { userIds: inWorkingTimeUserIds })
          .andWhere('task.executed_at IS NULL')
          .andWhere('task.scheduled_at < :currentDate', { currentDate: new Date() })
          .andWhere('task.type = :type', { type: SequenceStepType.EMAIL })
          .orderBy('task.scheduled_at', 'ASC')
          .addSelect(
            `ROW_NUMBER() OVER (PARTITION BY task.user_id ORDER BY task.scheduled_at ASC)`,
            'row_num',
          );

        // Query chính để lấy các items có row_num <= 4
        const query = this.sequenceStepTaskRepository
          .createQueryBuilder('t')
          .innerJoin(`(${subQuery.getQuery()})`, 'ranked', 't.id = ranked.id', {
            ...subQuery.getParameters(),
          })
          .select('t.id', 'id')
          .addSelect('t.user_id', 'user_id')
          .addSelect('t.type', 'type')
          .addSelect('t.sequence_id', 'sequence_id')
          .addSelect('t.action', 'action')
          .addSelect('t.sequence_step_id', 'sequence_step_id')
          .addSelect('t.sequence_instance_id', 'sequence_instance_id')
          .addSelect('t.created_at', 'created_at')
          .where('ranked.row_num <= :limit', { limit: 4 })
          .orderBy('t.created_at', 'ASC');

        const items = await query.getRawMany();

        const { items: newItems, count } = items.reduce(
          (acc, item) => {
            if (!acc.count[item.user_id]) {
              acc.count[item.user_id] = 0;
            }
            if (!acc.linkedInCount[item.sequence_id]) {
              acc.linkedInCount[item.sequence_id] = 0;
            }
            // Only handle 1 linkedin request per sequence
            if (acc.count[item.user_id] < 50) {
              if (item.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
                if (acc.linkedInCount[item.sequence_id] < 1) {
                  acc.items.push(item);
                }
                acc.linkedInCount[item.sequence_id] += 1;
              } else {
                acc.items.push(item);
              }
            }
            acc.count[item.user_id] += 1;

            return acc;
          },
          { count: {}, linkedInCount: {}, items: [] },
        );

        const notEnoughUserIds: string[] = [];
        await BBPromise.map(Object.keys(count), async (userId) => {
          const isEnough = await this.creditService.hasEnoughCredits(
            userId,
            count[userId],
            FEATURES.email_sequence.id,
          );
          if (!isEnough) {
            console.log(
              `[CRON JOB] triggerSequenceStepTaskSendGrid - User ${userId} does not have enough credits`,
            );

            notEnoughUserIds.push(userId);
          }
        });

        const filteredItems = newItems.filter((item) => !notEnoughUserIds.includes(item.user_id));

        await this.triggerSequenceTask(filteredItems);
        return { processedItems: filteredItems.length };
      },
      900, // TTL in seconds (15 minutes)
    );
  }

  // @Cron('*/3 * * * *')
  async triggerSequenceStepTaskNylas() {
    return this.executeCronJobWithStatus(
      'triggerSequenceStepTaskNylas',
      '*/3 * * * *',
      async (executionId: string) => {
        const currentDate = new Date();
        const inWorkingTimeUserIds =
          await this.sharedUserService.getInWorkingTimeUserIds(currentDate);

        const subQuery = this.sequenceStepTaskRepository
          .createQueryBuilder('task')
          .select('task.user_id', 'user_id')
          .addSelect('MIN(task.created_at)', 'oldest_date')
          .where({
            executedAt: IsNull(),
            scheduledAt: LessThan(new Date()),
            type: SequenceStepType.EMAIL,
            sourceType: IsNull(),
          })
          .groupBy('task.user_id');

        const items = await this.sequenceStepTaskRepository
          .createQueryBuilder('t')
          .innerJoin(
            `(${subQuery.getQuery()})`,
            'st',
            't.user_id = st.user_id AND t.created_at = st.oldest_date',
          )
          .select('MIN(CAST(t.id AS TEXT)) AS id')
          .where({
            executedAt: IsNull(),
            scheduledAt: LessThan(new Date()),
            type: SequenceStepType.EMAIL,
            sourceType: IsNull(),
            userId: In(inWorkingTimeUserIds),
          })
          .groupBy('t.user_id')
          .orderBy('t.user_id')
          .getRawMany();

        await this.triggerSequenceTask(items);
        return { processedItems: items.length };
      },
    );
  }

  @Cron('*/2 * * * *')
  async triggerSequenceStepTaskAfterLinkedIn() {
    return this.executeCronJobWithStatus(
      'triggerSequenceStepTaskAfterLinkedIn',
      '*/2 * * * *',
      async (executionId: string) => {
        // Query get users (with oldest created at) have cancel tasks
        const subQuery = this.sequenceStepTaskRepository
          .createQueryBuilder('task')
          .select('task.user_id', 'user_id')
          .addSelect('MIN(task.created_at)', 'oldest_date')
          .where({
            executedAt: IsNull(),
            scheduledAt: LessThan(new Date()),
            type: In(['UNFOLLOW_LINKEDIN', 'CANCEL_LINKEDIN_INVITATION']),
          })
          .groupBy('task.user_id');

        // Query tasks match with user + created_at
        const items = await this.sequenceStepTaskRepository
          .createQueryBuilder('t')
          .innerJoin(
            `(${subQuery.getQuery()})`,
            'st',
            't.user_id = st.user_id AND t.created_at = st.oldest_date',
          )
          .select('t.id AS id, t.user_id AS user_id, t.type AS type, t.sequence_id AS sequence_id')
          .where({
            executedAt: IsNull(),
            scheduledAt: LessThan(new Date()),
            type: In(['UNFOLLOW_LINKEDIN', 'CANCEL_LINKEDIN_INVITATION']),
          })
          .orderBy('t.user_id')
          .limit(1000)
          .getRawMany();

        const { items: newItems } = items.reduce(
          (acc, item) => {
            if (!acc.count[item.user_id]) {
              acc.count[item.user_id] = 0;
            }
            if (!acc.linkedInCount[item.sequence_id]) {
              acc.linkedInCount[item.sequence_id] = 0;
            }
            // Only handle 1 linkedin request per sequence
            if (acc.count[item.user_id] < 1) {
              acc.items.push(item);
            }
            acc.count[item.user_id] += 1;

            return acc;
          },
          { count: {}, linkedInCount: {}, items: [] },
        );

        await this.triggerSequenceTask(newItems);
        return { processedItems: newItems.length };
      },
    );
  }

  getAcceptedInvitations(
    recipientIds: string[],
    sentInvitations: string[],
    latestRelations: string[],
  ) {
    return recipientIds.filter(
      (id) => !sentInvitations.includes(id) && latestRelations.includes(id),
    );
  }

  getNewStartSchedule(acceptedIds: string[], relationItems: any[]) {
    const acceptedRelations = relationItems.filter((relationItem) =>
      acceptedIds.includes(relationItem.member_id),
    );

    const sortResults = acceptedRelations.sort(
      (a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    );

    const result =
      sortResults.length > 0 ? new Date(sortResults[0].created_at).getTime() : new Date().getTime();
    const oneHourAgo = new Date().getTime() - 60 * 60 * 1000; // 1 hour ago in milliseconds

    return Math.max(result, oneHourAgo);
  }

  @Cron('0 */1 * * *')
  async checkLinkedInInvitation() {
    return this.executeCronJobWithStatus(
      'checkLinkedInInvitation',
      '0 */1 * * *',
      async (executionId: string) => {
        const data = await this.sequenceInstanceRepository
          .createQueryBuilder('si')
          .select('si.user_id', 'user_id')
          .addSelect('si.sequence_id', 'sequence_id')
          .addSelect('si.version', 'version')
          .addSelect('ARRAY_AGG(si.sent_ids)', 'sent_ids')
          .addSelect('ARRAY_AGG(si.recipients)', 'recipients')
          .addSelect('MIN(si.executed_at)', 'executed_at')
          .where({ flag: SequenceStepFlag.INVITATION_SENT })
          .groupBy('si.user_id, si.sequence_id, si.version')
          .getRawMany();

        const groupByUser = data.reduce((acc, item) => {
          if (!acc[item.user_id]) {
            acc[item.user_id] = [];
          }
          acc[item.user_id].push(item);

          return acc;
        }, {});

        const users = await this.dataSource
          .createQueryBuilder(UserEntity, 'u')
          .where({ id: In(Object.keys(groupByUser)) })
          .select('id')
          .addSelect('unipile_account_id')
          .getRawMany();

        await BBPromise.all(
          users.map(async (user) => {
            // Get all recipient ids
            const recipientIds = groupByUser[user.id]
              .map((item) => item.sent_ids)
              .flat()
              .map((sentId) => `${sentId}`.split('INVITATION#')[1]?.split('#')[0])
              .filter(Boolean);

            if (user.unipile_account_id && recipientIds.length) {
              const { items: sentInvitationItems = [] } = await unipileClient
                .getSentInvitations(user.unipile_account_id)
                .catch(() => ({}));

              const { items: relationItems = [] } = await unipileClient
                .getRelations(user.unipile_account_id)
                .catch(() => ({}));

              const sentInvitations = sentInvitationItems.map((item) => item.invited_user_id);
              const latestRelations = relationItems.map((item) => item.member_id);

              await BBPromise.all(
                groupByUser[user.id].map(async (insItem) => {
                  const sentRecipients = insItem.sent_ids
                    .flat()
                    .map((sentId) => {
                      const [cId, cEmail] = `${sentId}`.split('INVITATION#')[1]?.split('#') || [];

                      return cId
                        ? {
                            id: cId,
                            email: cEmail?.toLowerCase(),
                          }
                        : null;
                    })
                    .filter(Boolean);
                  const acceptedInvitations = this.getAcceptedInvitations(
                    sentRecipients.map((item) => item.id),
                    sentInvitations,
                    latestRelations,
                  );
                  const invitationExecutedAt = new Date(insItem.executed_at);
                  if (acceptedInvitations.length) {
                    console.log(
                      '[CRON JOB] SEQUENCE INSTANCE - LINKEDIN ACCEPTED',
                      insItem.sequence_id,
                      insItem.version,
                    );
                    const seqInstances = await this.sequenceInstanceRepository.find({
                      where: {
                        sequence: { id: insItem.sequence_id },
                        version: insItem.version,
                      },
                      relations: ['sequence', 'sequenceStep'],
                      order: { sequenceStep: { stepIndex: 'ASC' } },
                    });
                    const rescheduleInstances = seqInstances.filter(
                      (seqInstance) => seqInstance.status === SequenceStepStatus.PENDING,
                    );

                    let nextScheduled = this.getNewStartSchedule(
                      acceptedInvitations,
                      relationItems,
                    );
                    const newScheduleInstances = rescheduleInstances.map((rescheduleInstance) => {
                      nextScheduled += rescheduleInstance.sequenceStep.delays.reduce(
                        (acc, item) =>
                          acc + item.delay * this.sharedMailService.DELAY_UNIT_MULTIPLE[item.unit],
                        0,
                      );

                      // Only reschedule for LinkedIn Connection Request
                      return rescheduleInstance.sequenceStep.type ===
                        SequenceStepType.LINKEDIN_CONNECTION_REQUEST
                        ? {
                            id: rescheduleInstance.id,
                            scheduledAt: new Date(nextScheduled),
                          }
                        : null;
                    });

                    await BBPromise.all(
                      newScheduleInstances.filter(Boolean).map((newScheduleInstance) =>
                        this.sequenceInstanceRepository.update(newScheduleInstance.id, {
                          scheduledAt: newScheduleInstance.scheduledAt,
                        }),
                      ),
                    );

                    await this.sequenceInstanceRepository.update(
                      {
                        sequence: { id: insItem.sequence_id },
                        version: insItem.version,
                        flag: SequenceStepFlag.INVITATION_SENT,
                      },
                      { flag: SequenceStepFlag.INVITATION_ACCEPTED },
                    );

                    const seqInstance = seqInstances.find(
                      (item) => item.flag === SequenceStepFlag.INVITATION_SENT,
                    );

                    await this.sequenceActivityLogRepository.insert(
                      acceptedInvitations.map((acceptedInvitation) => {
                        const recipientEmail = sentRecipients.find(
                          (item) => item.id === acceptedInvitation,
                        )?.email;
                        const relationItem = relationItems.find(
                          (item) => item.member_id === acceptedInvitation,
                        );
                        const recipientItem = insItem.recipients
                          .flat()
                          ?.find(
                            (siRecipient) => siRecipient?.email?.toLowerCase() === recipientEmail,
                          );
                        const acceptedAt = new Date(relationItem?.created_at);

                        return this.sequenceActivityLogRepository.create({
                          type: SequenceActivityType.LINKEDIN_INVITATION_ACCEPTED,
                          sequence: { id: insItem.sequence_id },
                          sequenceStep: { id: seqInstance?.sequenceStep?.id },
                          content: {
                            type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
                            linkedInType: 'INVITATION',
                            sequenceInstance: seqInstance?.id,
                            acceptedInvitation,
                            hostname: os.hostname(),
                            recipient:
                              recipientItem?.name ||
                              `${relationItem?.first_name || ''} ${relationItem?.last_name || ''}`,
                            contact: {
                              ...recipientItem,
                            },
                          },
                          occurredAt: acceptedAt.getTime()
                            ? acceptedAt.toISOString()
                            : new Date().toISOString(),
                        });
                      }),
                    );
                  } else if (invitationExecutedAt.getTime()) {
                    console.log(
                      '[CRON JOB] SEQUENCE INSTANCE - LINKEDIN NOT ACCEPTED YET',
                      insItem.sequence_id,
                      insItem.version,
                    );
                    const seqInstances = await this.sequenceInstanceRepository.find({
                      where: {
                        sequence: { id: insItem.sequence_id },
                        version: insItem.version,
                        sequenceStep: { type: Not(SequenceStepType.LINKEDIN_CONNECTION_REQUEST) },
                        scheduledAt: IsNull(),
                      },
                      relations: ['sequence', 'sequenceStep'],
                      order: { sequenceStep: { stepIndex: 'ASC' } },
                    });

                    if (!seqInstances.length) {
                      return;
                    }

                    const rescheduleInstances = seqInstances.filter(
                      (seqInstance) => seqInstance.status === SequenceStepStatus.PENDING,
                    );

                    let nextScheduled = new Date().getTime();
                    const newScheduleInstances = rescheduleInstances.map((rescheduleInstance) => {
                      nextScheduled += rescheduleInstance.sequenceStep.delays.reduce(
                        (acc, item) =>
                          acc + item.delay * this.sharedMailService.DELAY_UNIT_MULTIPLE[item.unit],
                        0,
                      );

                      return {
                        id: rescheduleInstance.id,
                        scheduledAt: new Date(nextScheduled),
                      };
                    });

                    await BBPromise.all(
                      newScheduleInstances.map((newScheduleInstance) =>
                        this.sequenceInstanceRepository.update(newScheduleInstance.id, {
                          scheduledAt: newScheduleInstance.scheduledAt,
                        }),
                      ),
                    );

                    await this.sequenceInstanceRepository.update(
                      {
                        sequence: { id: insItem.sequence_id },
                        version: insItem.version,
                        flag: SequenceStepFlag.INVITATION_SENT,
                      },
                      { flag: SequenceStepFlag.INVITATION_OVERTIME },
                    );
                  }
                }),
              );
            }
          }),
        );
        return { processedUsers: users.length };
      },
    );
  }

  //Every 30 secs
  @Cron('*/30 * * * * *')
  async refineJobByAIToTheOldest() {
    return this.executeCronJobWithStatus(
      'refineJobByAIToTheOldest',
      '*/30 * * * * *',
      async (executionId: string) => {
        // STEP 1: Find suitable searches (enable searches OR recently disabled searches < 12h)
        const twelveHoursAgo = new Date();
        twelveHoursAgo.setHours(twelveHoursAgo.getHours() - 12);

        const searches = await this.dataSource
          .getRepository(JobSearchEntity)
          .createQueryBuilder('search')
          .where('search.isRefineBySearch = :isRefineBySearch', { isRefineBySearch: true })
          .andWhere('search.isScanOldest = :isScanOldest', { isScanOldest: false })
          .andWhere('search.isDeleted = :isDeleted', { isDeleted: false })
          .andWhere('(search.stopScapingAt IS NULL OR search.stopScapingAt > :twelveHoursAgo)', {
            twelveHoursAgo,
          })
          .addOrderBy('search.updatedAt', 'ASC') // Process searches that haven't been processed recently first
          .addOrderBy('search.id', 'ASC') // Secondary ordering for deterministic results
          .limit(20)
          .getMany();

        if (searches.length === 0) {
          return { processedSearches: 0 };
        }

        // STEP 2: Refine jobs by selected searches with controlled concurrency
        const JOBS_SIZE_PER_EACH = 50; // Reduced from 100 for better performance
        const CONCURRENCY_LIMIT = 3; // Process max 3 searches concurrently

        const results = await BBPromise.map(
          searches,
          async (search) => {
            try {
              const result = await this.jobSearchService.refinedJobsInBackground(
                search,
                JOBS_SIZE_PER_EACH,
                'OLDEST',
              );
              return { searchId: search.id, success: true, result };
            } catch (error) {
              console.error(
                `[CRON JOB] refineJobByAIToTheOldest: Failed for search ${search.id}:`,
                error.message,
              );
              return { searchId: search.id, success: false, error: error.message };
            }
          },
          { concurrency: CONCURRENCY_LIMIT },
        );

        return { processedSearches: searches.length, results };
      },
    );
  }

  //Every 5 mins
  @Cron('*/30 * * * * *')
  async refineJobByAIToTheLatest() {
    return this.executeCronJobWithStatus(
      'refineJobByAIToTheLatest',
      '*/30 * * * * *',
      async (executionId: string) => {
        // STEP 1: Find suitable searches (enable searches OR recently disabled searches < 12h)
        const twelveHoursAgo = new Date();
        twelveHoursAgo.setHours(twelveHoursAgo.getHours() - 12);

        const searches = await this.dataSource
          .getRepository(JobSearchEntity)
          .createQueryBuilder('search')
          .where('search.isRefineBySearch = :isRefineBySearch', { isRefineBySearch: true })
          .andWhere('search.isDeleted = :isDeleted', { isDeleted: false })
          .andWhere('(search.stopScapingAt IS NULL OR search.stopScapingAt > :twelveHoursAgo)', {
            twelveHoursAgo,
          })
          .addOrderBy('search.updatedAt', 'ASC') // Process searches that haven't been processed recently first
          .addOrderBy('search.id', 'ASC') // Secondary ordering for deterministic results
          .limit(20)
          .getMany();

        if (searches.length === 0) {
          return { processedSearches: 0 };
        }

        // STEP 2: Refine jobs by selected searches with controlled concurrency
        const JOBS_SIZE_PER_EACH = 50; // Reduced from 100 for better performance
        const CONCURRENCY_LIMIT = 3; // Process max 3 searches concurrently

        const results = await BBPromise.map(
          searches,
          async (search) => {
            try {
              const result = await this.jobSearchService.refinedJobsInBackground(
                search,
                JOBS_SIZE_PER_EACH,
                'LATEST',
              );
              return { searchId: search.id, success: true, result };
            } catch (error) {
              console.error(
                `[CRON JOB] refineJobByAIToTheLatest: Failed for search ${search.id}:`,
                error.message,
              );
              return { searchId: search.id, success: false, error: error.message };
            }
          },
          { concurrency: CONCURRENCY_LIMIT },
        );

        return { processedSearches: searches.length, results };
      },
    );
  }

  /**
   * Cleanup old cronjob execution records - runs daily at 2 AM
   */
  @Cron('0 2 * * *')
  async cleanupCronJobExecutions() {
    return this.executeCronJobWithStatus(
      'cleanupCronJobExecutions',
      '0 2 * * *',
      async (executionId: string) => {
        const deletedCount = await this.cronJobStatusService.cleanupOldExecutions(7);
        return { deletedCount };
      },
    );
  }
}

import { Injectable, Logger } from "@nestjs/common";
import { UserEntity } from "../modules/user/entities/user.entity";
import { DataSource } from "typeorm";
import * as bcrypt from 'bcryptjs'
import {LeadStatus} from "../modules/jobs/entities/lead-statuses.entity";

const defaultStatus = {
  name: 'New',
  order_of_display: 0
};

@Injectable()
export class LeadStatusSeeder {
  private readonly logger = new Logger(LeadStatusSeeder.name,)
  public async run(connection: DataSource): Promise<void> {
    const foundStatuses = await connection.createQueryBuilder().from(LeadStatus, 'ls').where({ }).execute();
    if (foundStatuses.length) {
      return;
    }
    await connection.createQueryBuilder().insert().into(LeadStatus).values({ ...defaultStatus }).execute();
    return;
  }
}

import { v5 as uuidv5 } from 'uuid';
import { Injectable, Logger } from "@nestjs/common";
import { DataSource, In } from "typeorm";
import { RoleEntity, RoleEnum } from "../modules/user/entities/role.entity";
import { PermissionEntity, ResourceEnum } from "../modules/user/entities/permission.entity";
import { RoleDefaultPermissionEntity } from "../modules/user/entities/role-default-permission.entity";
import { DefaultRoleIds } from "../configs/configs.constants";

const roles = [
    {
        id: DefaultRoleIds[RoleEnum.SUPER_ADMIN],
        name: 'Super Admin',
        keyCode: RoleEnum.SUPER_ADMIN
    },
    {
        id: DefaultRoleIds[RoleEnum.SALES],
        name: 'Sales',
        keyCode: RoleEnum.SALES
    },
    {
        id: DefaultRoleIds[RoleEnum.ADMIN],
        name: 'Admin',
        keyCode: RoleEnum.ADMIN
    },
    {
        id: DefaultRoleIds[RoleEnum.MANAGEMENT],
        name: 'Management',
        keyCode: RoleEnum.MANAGEMENT
    },
    {
        id: DefaultRoleIds[RoleEnum.BASIC_USER],
        name: 'Basic User',
        keyCode: RoleEnum.BASIC_USER
    }
];

const permissions = [
    {
        id: "718a25fd-d984-434f-bc58-d1d694cff2d1",
        name: 'Report Agency',
        keyCode: ResourceEnum.REPORTED_AGENCIES
    },
    {
        id: "74171658-6261-44f7-9493-e2a26d78d79d",
        name: 'Job Search',
        keyCode: ResourceEnum.JOB_SEARCH
    },
    {
        id: "1c981eec-d7d1-4e00-87fc-265a1b7e200f",
        name: 'Job Lead',
        keyCode: ResourceEnum.JOB_LEAD
    },
    {
        id: "773fd5e4-eab4-4598-8087-a8385641e863",
        name: 'Job Sync',
        keyCode: ResourceEnum.JOB_SYNC
    },
    {
        id: "664be5d4-285a-4f95-be02-a1ddb528579c",
        name: 'Email Finder',
        keyCode: ResourceEnum.EMAIL_FINDER
    },
    {
        id: "84f1cce2-fc8d-478b-b732-4dbba2d6df71",
        name: 'Email verification',
        keyCode: ResourceEnum.EMAIL_VERIFICATION
    },
    {
        id: "a9490501-81d8-438e-b215-995146a630eb",
        name: 'Dashboard',
        keyCode: ResourceEnum.DASHBOARD
    },
    {
        id: "393da2f3-7994-4a3b-b664-a93a7ecd05a3",
        name: 'User Management',
        keyCode: ResourceEnum.USER_MANAGEMENT
    },
    {
        id: "6ef68454-dd75-4edb-bead-14096a55ec27",
        name: 'Staff Performance',
        keyCode: ResourceEnum.STAFF_PERFORMANCE
    },
    {
        id: "f2a7c8d9-e3b4-5a6c-7d8e-9f0a1b2c3d4e",
        name: 'Duplicated Jobs',
        keyCode: ResourceEnum.DUPLICATED_JOBS
    },
    {
        id: "a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d",
        name: 'Team Management',
        keyCode: ResourceEnum.TEAM_MANAGEMENT
    },
    {
        id: "b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e",
        name: 'Accounts',
        keyCode: ResourceEnum.ACCOUNTS
    },
    {
        id: "c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f",
        name: 'Contract',
        keyCode: ResourceEnum.CONTRACT
    },
    {
        id: "d4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a",
        name: 'CRM',
        keyCode: ResourceEnum.CRM
    },
    {
        id: "e5f6a7b8-c9d0-e1f2-3a4b-5c6d7e8f9a0b",
        name: 'Task',
        keyCode: ResourceEnum.TASK
    },
    {
        id: "f6a7b8c9-d0e1-f23a-4b5c-6d7e8f9a0b1c",
        name: 'Search',
        keyCode: ResourceEnum.SEARCH
    },
    {
        id: "a7b8c9d0-e1f2-3a4b-5c6d-7e8f9a0b1c2d",
        name: 'Sync',
        keyCode: ResourceEnum.SYNC
    },
    {
        id: "b8c9d0e1-f23a-4b5c-6d7e-8f9a0b1c2d3e",
        name: 'My Leads',
        keyCode: ResourceEnum.MY_LEADS
    },
    {
        id: "c9d0e1f2-3a4b-5c6d-7e8f-9a0b1c2d3e4f",
        name: 'Mailbox',
        keyCode: ResourceEnum.MAILBOX
    },
    {
        id: "d0e1f23a-4b5c-6d7e-8f9a-0b1c2d3e4f5a",
        name: 'Sequence',
        keyCode: ResourceEnum.SEQUENCE
    },
    {
        id: "e1f23a4b-5c6d-7e8f-9a0b-1c2d3e4f5a6b",
        name: 'Manual Leads',
        keyCode: ResourceEnum.MANUAL_LEADS
    },
    {
        id: "f23a4b5c-6d7e-8f9a-0b1c-2d3e4f5a6b7c",
        name: 'Contact Finder',
        keyCode: ResourceEnum.CONTACT_FINDER
    },
    {
        id: "3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d",
        name: 'Settings',
        keyCode: ResourceEnum.SETTINGS
    },
    {
        id: "4b5c6d7e-8f9a-0b1c-2d3e-4f5a6b7c8d9e",
        name: 'Reporting',
        keyCode: ResourceEnum.REPORTING
    },
    {
        id: "5c6d7e8f-9a0b-1c2d-3e4f-5a6b7c8d9e0f",
        name: 'Contact List',
        keyCode: ResourceEnum.CONTACT_LIST
    },
    {
        id: "6d7e8f9a-0b1c-2d3e-4f5a-6b7c8d9e0f1a",
        name: 'Company Onboarding',
        keyCode: ResourceEnum.COMPANY_ONBOARDING
    },
    {
        id: "7e8f9a0b-1c2d-3e4f-5a6b-7c8d9e0f1a2b",
        name: 'Company Approval',
        keyCode: ResourceEnum.COMPANY_APPROVAL
    },
    {
        id: "8f9a0b1c-2d3e-4f5a-6b7c-8d9e0f1a2b3c",
        name: 'View As',
        keyCode: ResourceEnum.VIEW_AS
    },
    {
        id: "1ca55c3a-4059-4ae2-8e85-9e72511e827d",
        name: 'Credit Management',
        keyCode: ResourceEnum.CREDIT_MANAGEMENT
    }
];
// We no longer need predefined IDs since we're using generateUniqueId

// Helper function to determine if a role should have access to a permission
const shouldHaveAccess = (roleKeyCode: RoleEnum, permissionKeyCode: ResourceEnum): { read: boolean, write: boolean } => {
    // Super Admin has access to everything
    if (roleKeyCode === RoleEnum.SUPER_ADMIN) {
        return { read: true, write: true };
    }

    // Sales has read-only access
    // Other roles have no access
    if (permissionKeyCode === ResourceEnum.REPORTED_AGENCIES ||
        permissionKeyCode === ResourceEnum.DUPLICATED_JOBS) {
        if (roleKeyCode === RoleEnum.SALES) {
            return { read: true, write: false };
        } else {
            // All other roles
            return { read: false, write: false };
        }
    }

    // Only Super Admin has View As permission
    if (permissionKeyCode === ResourceEnum.VIEW_AS) {
        return { read: false, write: false };
    }

    // Admin permissions
    if (roleKeyCode === RoleEnum.ADMIN) {
        // Admin can access User Management, Accounts, Contract, Team Management
        if (permissionKeyCode === ResourceEnum.USER_MANAGEMENT ||
            permissionKeyCode === ResourceEnum.ACCOUNTS ||
            permissionKeyCode === ResourceEnum.CONTRACT ||
            permissionKeyCode === ResourceEnum.CREDIT_MANAGEMENT ||
            permissionKeyCode === ResourceEnum.TEAM_MANAGEMENT) {
            return { read: true, write: true };
        }

        // Admin cannot access Company Onboarding, Company Approval
        if (permissionKeyCode === ResourceEnum.COMPANY_ONBOARDING ||
            permissionKeyCode === ResourceEnum.COMPANY_APPROVAL) {
            return { read: false, write: false };
        }

        // Admin has access to all Management features
        if (permissionKeyCode === ResourceEnum.STAFF_PERFORMANCE ||
            permissionKeyCode === ResourceEnum.CRM ||
            permissionKeyCode === ResourceEnum.REPORTING) {
            return { read: true, write: true };
        }

        // Admin has access to all basic user features
        return { read: true, write: true };
    }

    // Management permissions
    if (roleKeyCode === RoleEnum.MANAGEMENT) {
        // Management cannot access User Management, Accounts, Contract, Company Onboarding, Company Approval
        if (permissionKeyCode === ResourceEnum.USER_MANAGEMENT ||
            permissionKeyCode === ResourceEnum.ACCOUNTS ||
            permissionKeyCode === ResourceEnum.CONTRACT ||
            permissionKeyCode === ResourceEnum.COMPANY_ONBOARDING ||
            permissionKeyCode === ResourceEnum.COMPANY_APPROVAL) {
            return { read: false, write: false };
        }

        // Management can access Staff Performance and Reporting
        if (permissionKeyCode === ResourceEnum.STAFF_PERFORMANCE ||
            permissionKeyCode === ResourceEnum.REPORTING ||
            permissionKeyCode === ResourceEnum.CRM ||
            permissionKeyCode === ResourceEnum.TEAM_MANAGEMENT) {
            return { read: true, write: true };
        }

        // Management has read/write access to basic user features
        return { read: true, write: true };
    }

    // Basic User permissions
    if (roleKeyCode === RoleEnum.BASIC_USER) {
        // Basic User cannot access User Management, Team Management, Accounts, Contract, Staff Performance, Reporting, Company Onboarding, Company Approval
        if (permissionKeyCode === ResourceEnum.USER_MANAGEMENT ||
            permissionKeyCode === ResourceEnum.TEAM_MANAGEMENT ||
            permissionKeyCode === ResourceEnum.ACCOUNTS ||
            permissionKeyCode === ResourceEnum.CONTRACT ||
            permissionKeyCode === ResourceEnum.STAFF_PERFORMANCE ||
            permissionKeyCode === ResourceEnum.REPORTING ||
            permissionKeyCode === ResourceEnum.COMPANY_ONBOARDING ||
            permissionKeyCode === ResourceEnum.COMPANY_APPROVAL) {
            return { read: false, write: false };
        }

        // Basic User has access to Dashboard, Task, Search, Sync, My Leads, Mailbox, Sequence, Manual Leads, Contact Finder, Email Finder, Email Verification, Settings
        if (permissionKeyCode === ResourceEnum.DASHBOARD ||
            permissionKeyCode === ResourceEnum.TASK ||
            permissionKeyCode === ResourceEnum.SEARCH ||
            permissionKeyCode === ResourceEnum.SYNC ||
            permissionKeyCode === ResourceEnum.MY_LEADS ||
            permissionKeyCode === ResourceEnum.MAILBOX ||
            permissionKeyCode === ResourceEnum.SEQUENCE ||
            permissionKeyCode === ResourceEnum.MANUAL_LEADS ||
            permissionKeyCode === ResourceEnum.CONTACT_FINDER ||
            permissionKeyCode === ResourceEnum.EMAIL_FINDER ||
            permissionKeyCode === ResourceEnum.EMAIL_VERIFICATION ||
            permissionKeyCode === ResourceEnum.CRM ||
            permissionKeyCode === ResourceEnum.SETTINGS) {
            return { read: true, write: true };
        }

        // Default: no access
        return { read: false, write: false };
    }

    // Sales role permissions
    if (roleKeyCode === RoleEnum.SALES) {
        // Sales cannot approve company onboarding requests
        if (permissionKeyCode === ResourceEnum.COMPANY_APPROVAL) {
            return { read: false, write: false };
        }

        // VIEW_AS is already handled above for all roles except SUPER_ADMIN

        // Sales can view all other features
        return { read: true, write: true };
    }

    // Default: no access
    return { read: false, write: false };
};

// Generate a deterministic UUID v5 for each role-permission combination
const generateUniqueId = (roleId: string, permissionId: string): string => {
    // Use UUID v5 with URL namespace and the combined roleId#permissionId as name
    // This ensures that the same role-permission combination always gets the same UUID
    const name = `${roleId}#${permissionId}`;
    return uuidv5(name, uuidv5.URL);
};

// Create role default permissions
const roleDefaultPermissions = [];

roles.forEach((role) => {
    permissions.forEach((permission) => {
        const access = shouldHaveAccess(role.keyCode, permission.keyCode);

        // Generate a unique ID for this role-permission combination
        const id = generateUniqueId(role.id, permission.id);

        roleDefaultPermissions.push({
            id,
            roleId: role.id,
            permissionId: permission.id,
            allowRead: access.read,
            allowWrite: access.write
        });
    });
});


@Injectable()
export class MasterDataSeeder {
    private readonly logger = new Logger(MasterDataSeeder.name);

    public async run(connection: DataSource): Promise<void> {
        try {
            this.logger.log('Starting to seed master data...');

            try {
                // Check if the roles_keycode_enum type has all the required values
                const enumValues = await connection.query(`
                    SELECT enum_range(NULL::roles_keycode_enum);
                `);

                const currentEnumValues = enumValues[0].enum_range;
                this.logger.log('Current roles_keycode_enum values:', currentEnumValues);

                // Check if all required enum values exist
                const missingEnumValues = [];
                for (const role of roles) {
                    if (!currentEnumValues.includes(role.keyCode)) {
                        missingEnumValues.push(role.keyCode);
                    }
                }

                if (missingEnumValues.length > 0) {
                    this.logger.error(`Missing enum values in roles_keycode_enum: ${missingEnumValues.join(', ')}`);
                    this.logger.error('Please run the UpdateRoleEnums migration first');
                    return;
                }

                // Get roles that need to be created
                const createdRoles = await this.getNotCreatedItems(RoleEntity, connection, roles);
                if (createdRoles.length > 0) {
                    this.logger.log(`Inserting ${createdRoles.length} new roles`);

                    // Insert roles one by one to handle potential errors
                    for (const role of createdRoles) {
                        try {
                            await connection.createQueryBuilder().insert().into(RoleEntity).values(role).execute();
                            this.logger.log(`Inserted role: ${role.name} (${role.keyCode})`);
                        } catch (error) {
                            this.logger.error(`Error inserting role ${role.name} (${role.keyCode}):`, error);
                        }
                    }
                }

                // Update existing roles
                this.logger.log('Updating existing roles...');
                for (const role of roles) {
                    try {
                        // Update role name if it exists (keyCode is updated by migration)
                        const result = await connection.createQueryBuilder()
                            .update(RoleEntity)
                            .set({ name: role.name, keyCode: role.keyCode })
                            .where("id = :id", { id: role.id })
                            .execute();

                        if (result.affected > 0) {
                            this.logger.log(`Updated role: ${role.name} (${role.keyCode})`);
                        }
                    } catch (error) {
                        this.logger.error(`Error updating role ${role.name} (${role.keyCode}):`, error);
                    }
                }
            } catch (error) {
                this.logger.error('Error handling roles:', error);
            }

            const createdPermissions = await this.getNotCreatedItems(PermissionEntity, connection, permissions);
            if (createdPermissions.length > 0) {
                this.logger.log(`Inserting ${createdPermissions.length} new permissions`);
                await connection.createQueryBuilder().insert().into(PermissionEntity).values(createdPermissions).execute();
            }

            // Clear existing role default permissions first
            this.logger.log('Clearing existing role default permissions...');
            await connection.createQueryBuilder().delete().from(RoleDefaultPermissionEntity).execute();

            // Then insert all the new role default permissions
            this.logger.log(`Inserting ${roleDefaultPermissions.length} role default permissions`);
            await connection.createQueryBuilder().insert().into(RoleDefaultPermissionEntity).values(roleDefaultPermissions).execute();

            this.logger.log('Master data seeding completed successfully');
        } catch (error) {
            this.logger.error('Error seeding master data:', error);
            console.error(error);
        }
    }

    private async getNotCreatedItems<T extends { id: string }>(entity: any, connection: DataSource, defaultItems: T[]): Promise<T[]> {
        const itemIds = defaultItems.map((item: T) => item.id);
        const createdItems = await connection.createQueryBuilder().from(entity, 'entity').where({ id: In(itemIds) }).execute();
        const createdItemIds = createdItems.map((item: { id: string }) => item.id);

        return defaultItems.filter((item: T) => !createdItemIds.includes(item.id));
    }
}

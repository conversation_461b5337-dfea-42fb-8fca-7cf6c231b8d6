import { Injectable, Logger } from "@nestjs/common";
import { UserEntity } from "../modules/user/entities/user.entity";
import { DataSource } from "typeorm";
import * as bcrypt from 'bcryptjs'
import { RoleDefaultPermissionEntity } from "../modules/user/entities/role-default-permission.entity";
import { UserPermissionEntity } from "../modules/user/entities/user-permission.entity";

const admin = {
    id: '937b8b2d-386c-4fda-80bf-9dcdb23490cf',
    email: 'admin',
    password: 'admin',
    roleId: "00fe7fea-95fa-4f75-b0dd-68196f447491"
}

@Injectable()
export class UsersSeeder {
    private readonly logger = new Logger(UsersSeeder.name,)
    public async run(connection: DataSource): Promise<void> {
        const foundUser = await connection.createQueryBuilder().from(UserEntity, 'u').where({ id: admin.id }).execute();
        if (foundUser.length) {
            await this.createDefaultPermissionsForAdmin(connection);
            return;
        }

        await this.createAdmin(connection);
        await this.createDefaultPermissionsForAdmin(connection);
        return;
    }
    private async createAdmin(connection: DataSource) {

        const salt = await bcrypt.genSalt();
        const hashPassword = await bcrypt.hash(admin.password, salt);
        await connection.createQueryBuilder().insert().into(UserEntity).values({ ...admin, password: hashPassword }).execute();

    }
    private async createDefaultPermissionsForAdmin(dataSource: DataSource) {

        const defaultUserRolePermissions =
            await dataSource
                .createQueryBuilder(RoleDefaultPermissionEntity, 'rdp')
                .where({ roleId: admin.roleId })
                .getMany();

        const userPermissions =
            defaultUserRolePermissions.map(item =>
            ({
                userId: admin.id,
                permissionId: item.permissionId,
                allowRead: item.allowRead,
                allowWrite: item.allowWrite
            })
            );

        await dataSource.createQueryBuilder(UserPermissionEntity, 'up')
            .delete()
            .where({ userId: admin.id }).execute();

        await dataSource.createQueryBuilder(UserPermissionEntity, 'up')
            .insert()
            .values(userPermissions).execute();

        return true;
    }
}

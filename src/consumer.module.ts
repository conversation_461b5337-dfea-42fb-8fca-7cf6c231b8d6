import * as path from 'path';
import { Keyv } from 'keyv';
import KeyvRedis from '@keyv/redis';
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { TypeOrmModule } from '@nestjs/typeorm';
import { I18nModule, HeaderResolver } from 'nestjs-i18n';
import * as redisStore from 'cache-manager-redis-store';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { BULL_QUEUES, redisConfig, redisConnection } from './configs/configs.constants';
import { typeOrmConfig } from 'src/configs/database/typeorm.config';
import { JobLeadsRepository } from 'src/modules/jobs/repository/job-leads.repository';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { WebhookSendgridEventProcessor } from 'src/consumers/webhook-sendgrid-event.processor';
import { WebhookNylasEventProcessor } from 'src/consumers/webhook-nylas-event.processor';
import { CrawlJobEventProcessor } from 'src/consumers/crawl-job-event.processor';
import { LanguageCode } from 'src/common/constants/common.constant';
import { SharedMailService } from 'src/modules/shared/shared-mail.service';
import { SequenceRepository } from 'src/modules/mail/repositories/sequence.repostiory';
import { SequenceInstanceRepository } from 'src/modules/mail/repositories/sequence-instance.repository';
import { SequenceStepRepository } from 'src/modules/mail/repositories/sequence-step.repostiory';
import { SequenceStepTaskRepository } from 'src/modules/mail/repositories/sequence-step-task.repository';
import { SequenceActivityLogRepository } from 'src/modules/mail/repositories/sequence-activity-log.repository';
import { BullHornService } from 'src/middlewares/bullhorn/bullhorn.service';
import { ContactRepository } from './modules/user/repositories/contact.repository';
import { JobBoardsRepository } from './modules/jobs/repository/job-boards.repository';
import { DuplicateJobRepository } from './modules/jobs/repository/duplicate-job.repository';
import { OpensearchService } from './modules/opensearch/service/opensearch.service';
import { SSEService } from './modules/sse/sse.service';
import { CacheService } from './modules/cache/cache.service';
import { JobSearchService } from './modules/jobs/service/job-search.service';
import { CacheModule } from '@nestjs/cache-manager';
import { JobSearchRepository } from './modules/jobs/repository/job-search.repository';
import { JobSyncRepository } from './modules/jobs/repository/job-sync.repository';
import { HttpModule, HttpService } from '@nestjs/axios';
import { AwsService } from './modules/aws/services/aws-db.service';
import { AwsConfig } from './modules/aws/aws.config';
import { UserSignatureServices } from './modules/user/user-signature.service';
import { FileUploadService } from './modules/files-upload/file-upload.service';
import { UserSignatureRepository } from './modules/user/repositories/user-signature.repository';
import { BullhornEmailNoteEventProcessor } from './consumers/bullhorn-email-note-event.processor';
import { WebhookUnipileEventProcessor } from './consumers/webhook-unipile-event.processor';
import { InternalQueueEventProcessor } from './consumers/internal-queue-event.processor';
import { CrmContactSequenceStepRepository } from './modules/crm/repositories/crm-contact-sequence-step.repository';
import { CrmContactSequenceStepReplyRepository } from './modules/crm/repositories/crm-contact-sequence-step-reply.repository';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { WebhookModule } from './modules/webhook/webhook.module';

@Module({
  imports: [
    TypeOrmModule.forRoot(typeOrmConfig),
    I18nModule.forRoot({
      fallbackLanguage: LanguageCode.United_States,
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'),
        watch: true,
      },
      resolvers: [
        new HeaderResolver(['x-lang']),
      ],
    }),
    BullModule.forRoot({
      connection: redisConnection,
      defaultJobOptions: {
        removeOnComplete: {
          age: 86400,
          count: 1000,
        },
        removeOnFail: {
          age: 86400,
          count: 1000,
        },
      }
    }),
    BullModule.registerQueue(
      {
        name: BULL_QUEUES.SEQUENCE_STEP_TASK,
        prefix: '{bull_sequence_step_task}',
      },
      {
        name: BULL_QUEUES.WEBHOOK_SENDGRID_EVENT,
        prefix: '{bull_webhook_sendgrid_event}',
      },
      {
        name: BULL_QUEUES.WEBHOOK_NYLAS_EVENT,
        prefix: '{bull_webhook_nylas_event}',
      },
      {
        name: BULL_QUEUES.WEBHOOK_UNIPILE_EVENT,
        prefix: '{bull_webhook_unipile_event}',
      },
      {
        name: BULL_QUEUES.CRAWL_JOB_EVENT,
        prefix: '{bull_crawl_job_event}',
      },
      {
        name: BULL_QUEUES.BULLHORN_EMAIL_NOTE_EVENT,
        prefix: '{bull_bullhorn_email_note_event}',
      },
      {
        name: BULL_QUEUES.INTERNAL_QUEUE_EVENT,
        prefix: '{bull_internal_queue_event}',
      },
    ),
    EventEmitterModule.forRoot(),
    CacheModule.registerAsync({
      useFactory: async () => {
        return new Keyv({
          store: new KeyvRedis(process.env.REDIS_CONNECTION),
          ttl: 24 * 60 * 60,
        });
      },
    }),
    HttpModule,
    SubscriptionModule,
    WebhookModule,
  ],
  providers: [
    SharedMailService,
    WebhookSendgridEventProcessor,
    WebhookNylasEventProcessor,
    CrawlJobEventProcessor,
    WebhookUnipileEventProcessor,
    BullhornEmailNoteEventProcessor,
    InternalQueueEventProcessor,
    SequenceRepository,
    SequenceStepRepository,
    SequenceInstanceRepository,
    SequenceStepTaskRepository,
    SequenceActivityLogRepository,
    JobLeadsRepository,
    ContactRepository,
    UserRepository,
    JobBoardsRepository,
    DuplicateJobRepository,
    JobSearchRepository,
    UserSignatureRepository,
    JobSyncRepository,
    OpensearchService,
    BullHornService,
    AwsConfig,
    AwsService,
    SSEService,
    CacheService,
    JobSearchService,
    UserSignatureServices,
    FileUploadService,
    CrmContactSequenceStepRepository,
    CrmContactSequenceStepReplyRepository,
  ],
})
export class ConsumerModule { }

import { config } from 'dotenv';
import { RoleEnum } from '../modules/user/entities/role.entity';
import { IBullhornConfigDto } from 'src/common/dto/bullhorn-config.dto';

const CURRENT_ENV = process.env.NODE_ENV || 'staging';

config({ path: `.env.${CURRENT_ENV}` });
export const JwtConfig = {
  COMMON_API_JWT_SECRET: process.env.COMMON_API_JWT_SECRET || 'secret',
  COMMON_API_JWT_EXPIRES_IN: process.env.COMMON_API_JWT_EXPIRES_IN || '5m',
  COMMON_API_JWT_REFRESH_TOKEN_SECRET: process.env.COMMON_API_JWT_REFRESH_TOKEN_SECRET || 'secret',
  COMMON_API_JWT_REFRESH_TOKEN_EXPIRES_IN: process.env.COMMON_API_JWT_REFRESH_TOKEN_EXPIRES_IN || '10m',
};

export const CookieConfig = {
  ACCESS_TOKEN_EXPIRES_IN: 500 * 60 * 1000, //5m
  REFRESH_TOKEN_EXPIRES_IN: 24 * 60 * 60 * 1000, //1d
  ACCESS_TOKEN_CODE: 'access-token-cookie',
  REFRESH_TOKEN_CODE: 'refresh-token-cookie',
};

export const SesConfig = {
  COMMON_API_SES_AWS_SMTP_SENDER: process.env.COMMON_API_SES_AWS_SMTP_SENDER,
  COMMON_API_AWS_ACCESS_KEY: process.env.ACCESS_KEY_ID_AWS,
  COMMON_API_AWS_SECRET_KEY: process.env.SECRET_ACCESS_KEY_AWS,
  COMMON_API_REGION: process.env.REGION_AWS,
  COMMON_API_S3_BUCKET_EXPIRES: +process.env.COMMON_API_S3_BUCKET_EXPIRES || 1,
  COMMON_API_S3_BUCKET_NAME: process.env.BUCKET_NAME_S3,
};

export const CommonConfig = {
  BACKEND_URL: process.env.BACKEND_URL || 'http://localhost:3000',
  COMMON_API_WEB_BASE_URL: process.env.COMMON_API_WEB_BASE_URL,
  COMMON_API_RESEND_EMAIL: +process.env.COMMON_API_RESEND_EMAIL || 5,
  COMMON_API_RESEND_PHONE: +process.env.COMMON_API_RESEND_PHONE || 1,
  COMMON_API_TTL: +process.env.COMMON_API_TTL || 300,
  COMMON_API_LIMIT: +process.env.COMMON_API_LIMIT || 1000,
  LIMIT_FILE_UPLOAD: +process.env.LIMIT_FILE_UPLOAD || 3,
  LIMIT_SIZE_FILE_UPLOAD: +process.env.LIMIT_SIZE_FILE_UPLOAD || 4194304,
  SECRET_KEY:
    process.env.SECRET_KEY ||
    'xg4GYVuHZivh966a1vH3kkYjyXe0KHw5CMXP8xXCiJIcRinRQfcnDp4BmXBWzyDFAjLN4k4VMrJgsPIvgLUywShyz3mCUv',
  SECRET_JOB_PASSWORD: process.env.SECRET_KEY || '78!3On@2pevR',
  SECRET_JOB_IV: process.env.SECRET_KEY || '1234567890123456',
  SECRET_JOB_EXPIRE: +process.env.SECRET_JOB_EXPIRE || 5,
  SECRET_OTP_EXPIRE: +process.env.SECRET_OTP_EXPIRE || 500,
};

export const databaseConfig = {
  type: process.env.DATABASE_TYPE || 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: process.env.DATABASE_PORT || '5432',
  username: process.env.DATABASE_USERNAME || 'postgres',
  password: process.env.DATABASE_PASSWORD || '12345',
  database: process.env.DATABASE_DATABASE || 'job-finder-dev',
  synchronize: +process.env.DATABASE_SYNCHRONIZE || 0,
  read_host: process.env.DATABASE_HOST_READONLY,
};

export const FIREBASE_CONFIG = {
  CLOUD_TASK_CLIENT_EMAIL: process.env.CLOUD_TASK_CLIENT_EMAIL,
  CLOUD_TASK_PRIVATE_KEY: process.env.CLOUD_TASK_PRIVATE_KEY?.replace(/\\n/g, '\n'),
};

export const twilioConfig = {
  accountSid: process.env.TWILIO_ACCOUNT_SID,
  authToken: process.env.TWILIO_AUTH_TOKEN,
  twilioSendFrom: process.env.TWILIO_SEND_FROM,
  //TODO: for development
  // accountSid: '**********************************',
  // authToken: '65363bf1ddfc7c38276da5e416a7c108',
  // twilioSendFrom: '+***********',
};
export const redisConfig = {
  REDIS_CONNECTION: process.env.REDIS_CONNECTION,
  IS_USE_ELASTICCACHE: process.env.IS_USE_ELASTICCACHE,
};

const redisUrl = new URL(process.env.REDIS_CONNECTION);
export const redisConnection = {
  host: redisUrl.hostname,
  port: Number(redisUrl.port) || 6379, // Default Redis port
  username: redisUrl.username || undefined,
  password: redisUrl.password || undefined,
  db: redisUrl.pathname ? Number(redisUrl.pathname.replace('/', '')) : 0, // Default Redis DB index is 0
  tls: redisUrl.protocol === 'rediss:' ? { rejectUnauthorized: false } : undefined, // TLS with rejectUnauthorized
};

export const timeZone = process.env.TIME_ZONE || 'Asia/Dubai';

export const PhoneNumberTest = [];
export const throttleOtp = {
  limit: +process.env.THROTTLE_OTP_LIMIT || 5,
  ttl: +process.env.THROTTLE_OTP_TTL || 2 * 60,
};
export const BullHornConfig = {
  clientId: process.env.BULLHORN_CLIENT_ID,
  username: process.env.BULLHORN_USERNAME,
  password: process.env.BULLHORN_PASSWORD,
  clientSecret: process.env.BULLHORN_CLIENT_SECRET,
  apiGetAccessCode: `https://auth-east.bullhornstaffing.com/oauth/authorize?client_id=${encodeURIComponent(
    process.env.BULLHORN_CLIENT_ID
  )}&response_type=code&action=Login&username=${encodeURIComponent(
    process.env.BULLHORN_USERNAME
  )}&password=${encodeURIComponent(process.env.BULLHORN_PASSWORD)}&redirect_uri=&state=`,
  apiGetAccessToken: 'https://auth-east.bullhornstaffing.com/oauth/token?',
  getBhRestTokenAndCorporateRestEndpoint: ({ rootRestUrl }) => {
    return `${rootRestUrl}/login?&version=*&`;
  },
  getApiGetAccessCode: ({ bhClientId, bhUsername, bhPassword, rootOauthUrl }: IBullhornConfigDto) => {
    return `${rootOauthUrl}/authorize?client_id=${encodeURIComponent(
      bhClientId
    )}&response_type=code&action=Login&username=${encodeURIComponent(bhUsername)}&password=${encodeURIComponent(
      bhPassword
    )}&redirect_uri=&state=`;
  },
};

export const SendGridConfig = {
  apiKey: process.env.SENDGRID_API_KEY,
  apiKeyEmailExplorerCandiate: process.env.SENDGRID_API_KEY_EMAIL_EXPLORER_CANDIDATE,
  apiKeyEmailValid: process.env.SENDGRID_API_KEY_EMAIL_VALID,
  fromEmail: '<EMAIL>',
  invitationSubject: 'Invitation to Zileo',
  domainListener: process.env.DOMAIN_EMAI_LISTENER || 'parse.pearsoncarter.com',
};

export const DefaultRoleIds = {
  [RoleEnum.SUPER_ADMIN]: '00fe7fea-95fa-4f75-b0dd-68196f447491',
  [RoleEnum.SALES]: '3feb0476-bff6-4fda-8b97-420f9826fb39',
  [RoleEnum.ADMIN]: '2feb0476-bff6-4fda-8b97-420f9826fb39',
  [RoleEnum.MANAGEMENT]: '5feb0476-bff6-4fda-8b97-420f9826fb39',
  [RoleEnum.BASIC_USER]: '400442ee-d48e-45c2-a560-f8442d351011'
};

//TODO: update in .env.example2
export const ProspeoConfig = {
  apiKey: 'cfb6a1804f2707077c305e2aeef30376',
  emailFinderApi: 'https://api.prospeo.io/email-finder',
  domainSearchApi: 'https://api.prospeo.io/domain-search',
};

export const CronConfig = {
  oldJobsFromToday: 45,
};

export const ProxyCurlConfig = {
  url: 'https://nubela.co/proxycurl/api/linkedin/company/employees/',
  apiKey: 'PNLShSKCqNf423TbrUF69A',
};

export const CorsConfig = {
  corsWhiteList: process.env.CORS_WHITE_LIST?.split(',') || '*',
};

export const CRAWLING_API = process.env.CRAWLING_API || 'http://localhost:8000';
export const CrawlingConfig = {
  crawlingApi: CRAWLING_API,
  startCrawlingApi: `${CRAWLING_API}/api/crawler/start`,
  deleteCrawlingProcess: `${CRAWLING_API}/api/crawler/kill_jobs`, // TODO
};

export const APOLLO_COOKIE =
  'mutiny.user.token=1d876ced-1ce6-40aa-81b5-78af193a5c16; zp__initial_referrer=https://www.google.com/; zp__initial_utm_source=www.google.com; _gcl_au=1.1.*********.1702039465; ps_mode=trackingV1; _fbp=fb.1.1702039465513.1499645257; hubspotutk=9312c053129838ac966dd4767915b55f; _cioanonid=0c036380-f642-e388-f869-39e7bebda8dc; remember_token_leadgenie_v2=eyJfcmFpbHMiOnsibWVzc2FnZSI6IklqWTFNRGd6TWpka1pHWTRZakEwTURCaE0yVTROamt4TUY5c1pXRmtaMlZ1YVdWamIyOXJhV1ZvWVhOb0lnPT0iLCJleHAiOiIyMDI0LTAxLTA4VDEyOjU4OjAxLjg2MVoiLCJwdXIiOiJjb29raWUucmVtZW1iZXJfdG9rZW5fbGVhZGdlbmllX3YyIn19--0fdf119f232cbc69ef2b04f8749298084382f2fd; _cioid=6508327ddf8b0400a3e86910; __stripe_mid=9d32068b-b64a-45ef-9e26-27f376128eb6a689e3; zp__utm_source=www.google.com; _ga_76XXTC73SP=GS1.1.1702042966.2.0.1702042966.60.0.0; amp_91ff3d=OFpgepGS89rUm7tZnTRZCW.NjUwODMyN2RkZjhiMDQwMGEzZTg2OTEw..1hh4q5g2k.1hh4r3t1j.m.3.p; _ga=GA1.1.*********.1702039465; _ga_CBPPE9HPBE=GS1.1.1702042938.1.1.1702044702.0.0.0; mutiny.user.session=3480caae-1d50-464f-ae4f-85dc6dc2e14b; mutiny.user.session_number=1; __hstc=21978340.9312c053129838ac966dd4767915b55f.1702039465961.1702042448569.1702302289438.3; __hssrc=1; ZP_Pricing_Split_Test_Variant=23Q1_EC_Y49; __stripe_sid=d2e63d65-cd2e-4bd2-8374-74876eb2df956f878b; amplitude_id_122a93c7d9753d2fe678deffe8fac4cfapollo.io=eyJkZXZpY2VJZCI6IjcxZjBmYjhiLWFjMTEtNDQ1Yi05NWYwLWVjYTYwM2I1MGY3ZFIiLCJ1c2VySWQiOiI2NTA4MzI3ZGRmOGIwNDAwYTNlODY5MTAiLCJvcHRPdXQiOnRydWUsInNlc3Npb25JZCI6MTcwMjMwMjI4MzYyMSwibGFzdEV2ZW50VGltZSI6MTcwMjMwMjQyMTAzMiwiZXZlbnRJZCI6MSwiaWRlbnRpZnlJZCI6Miwic2VxdWVuY2VOdW1iZXIiOjN9; __hssc=21978340.2.1702302289438; X-CSRF-TOKEN=RbJu2MYAhpsIoq4kTQ_ga8h0oPxOvkZQR-tcWGNrBjHnLKDZS3YMncnA8Kji83FMmtT1ac8YeFk-r8VIH0twDA; _leadgenie_session=MYLS02Zp2afHvo1fpDycLjyu6m7kFEAXdHR4cSzW2Q7Y76GMPN6towUkAVTz8Lqv%2FvXuSdbAxQbRuqj%2FgBv0P%2BpTsFOcvr2N4vn%2Fuq%2BCUkORHkUaP6ck3vN4%2BozWn59%2FR2BhUg362NvCUMPubd0KKeOKPvfa8PNjY22DsSSgHeuDLOSqBISzcWECsMZU%2Fl%2FA2ZO%2BF0G9Fpxz%2FOJ%2Fv53reYMVin1jmbe1KJVykrUCzWwEdhzH6ol7CjJvy88QeZuPylwOKlB8hv0dop1f58vhSjcsjHnDiD34Umg%3D--T9d919IFqwh7XWgo--e4y361QcB2vy11CQXWPrHw%3D%3D; _dd_s=rum=0&expire=1702304978479';
export const ApolloConfig = {
  apiKey: process.env.APOLLO_API_KEY,
};

export const SnsConfig = {
  topicArn: process.env.SNS_TOPIC_ARN,
};

export const admin = {
  id: '937b8b2d-386c-4fda-80bf-9dcdb23490cf',
  email: 'admin',
  password: 'admin',
  roleId: '00fe7fea-95fa-4f75-b0dd-68196f447491',
};

export const OpenAIConfig = {
  apiKey: process.env.OPENAI_API_KEY,
};

export const GeminiConfig = {
  apiKey: process.env.GEMINI_API_KEY,
};

export const NYLAS_CONFIG = {
  clientId: process.env.NYLAS_CLIENT_ID,
  callbackUri: `${process.env.CLIENT_URL}/mail-callback`,
  callbackMailboxUri: `${process.env.CLIENT_URL}/mailbox-callback`,
  apiKey: process.env.NYLAS_API_KEY,
  apiUri: process.env.NYLAS_API_URI,
};

export const SAVVYCAL_CONFIG = {
  BASE_URL: process.env.SAVVYCAL_BASE_URL || 'https://api.savvycal.com',
  PRIVATE_KEY: process.env.SAVVYCAL_PRIVATE_KEY,
};

export const APP_CONFIG = {
  API_URL: process.env.API_URL,
  CLIENT_URL: process.env.CLIENT_URL,
};

export const EMAIL_DOMAIN_WHITELIST = [
  'aol.com',
  'att.net',
  'comcast.net',
  'facebook.com',
  'gmail.com',
  'gmx.com',
  'googlemail.com',
  'google.com',
  'hotmail.com',
  'hotmail.co.uk',
  'mac.com',
  'me.com',
  'mail.com',
  'msn.com',
  'live.com',
  'sbcglobal.net',
  'verizon.net',
  'yahoo.com',
  'yahoo.co.uk',
  'email.com',
  'fastmail.fm',
  'games.com',
  'gmx.net',
  'hush.com',
  'hushmail.com',
  'icloud.com',
  'iname.com',
  'inbox.com',
  'lavabit.com',
  'love.com',
  'outlook.com',
  'pobox.com',
  'protonmail.ch',
  'protonmail.com',
  'tutanota.de',
  'tutanota.com',
  'tutamail.com',
  'tuta.io',
  'keemail.me',
  'rocketmail.com',
  'safe-mail.net',
  'wow.com',
  'ygm.com',
  'ymail.com',
  'zoho.com',
  'yandex.com',
  'bellsouth.net',
  'charter.net',
  'cox.net',
  'earthlink.net',
  'juno.com',
  'btinternet.com',
  'virginmedia.com',
  'blueyonder.co.uk',
  'freeserve.co.uk',
  'live.co.uk',
  'ntlworld.com',
  'o2.co.uk',
  'orange.net',
  'sky.com',
  'talktalk.co.uk',
  'tiscali.co.uk',
  'virgin.net',
  'wanadoo.co.uk',
  'bt.com',
  'sina.com',
  'sina.cn',
  'qq.com',
  'naver.com',
  'hanmail.net',
  'daum.net',
  'nate.com',
  'yahoo.co.jp',
  'yahoo.co.kr',
  'yahoo.co.id',
  'yahoo.co.in',
  'yahoo.com.sg',
  'yahoo.com.ph',
  '163.com',
  'yeah.net',
  '126.com',
  '21cn.com',
  'aliyun.com',
  'foxmail.com',
  'hotmail.fr',
  'live.fr',
  'laposte.net',
  'yahoo.fr',
  'wanadoo.fr',
  'orange.fr',
  'gmx.fr',
  'sfr.fr',
  'neuf.fr',
  'free.fr',
  'gmx.de',
  'hotmail.de',
  'live.de',
  'online.de',
  't-online.de',
  'web.de',
  'yahoo.de',
  'libero.it',
  'virgilio.it',
  'hotmail.it',
  'aol.it',
  'tiscali.it',
  'alice.it',
  'live.it',
  'yahoo.it',
  'email.it',
  'tin.it',
  'poste.it',
  'teletu.it',
  'mail.ru',
  'rambler.ru',
  'yandex.ru',
  'ya.ru',
  'list.ru',
  'hotmail.be',
  'live.be',
  'skynet.be',
  'voo.be',
  'tvcablenet.be',
  'telenet.be',
  'hotmail.com.ar',
  'live.com.ar',
  'yahoo.com.ar',
  'fibertel.com.ar',
  'speedy.com.ar',
  'arnet.com.ar',
  'yahoo.com.mx',
  'live.com.mx',
  'hotmail.es',
  'hotmail.com.mx',
  'prodigy.net.mx',
  'yahoo.ca',
  'hotmail.ca',
  'bell.net',
  'shaw.ca',
  'sympatico.ca',
  'rogers.com',
  'yahoo.com.br',
  'hotmail.com.br',
  'outlook.com.br',
  'uol.com.br',
  'bol.com.br',
  'terra.com.br',
  'ig.com.br',
  'itelefonica.com.br',
  'r7.com',
  'zipmail.com.br',
  'globo.com',
  'globomail.com',
  'oi.com.br',
];

export const STRIPE_CONFIG = {
  secretKey: process.env.STRIPE_SECRET_KEY || 'sk_test_...',
  publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_...',
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_...',
  apiVersion: '2025-04-30.basil' as const,
};

export const REDIS_KEYS = {
  UNVERIFIED_COMPANY: 'UNVERIFIED_COMPANY',
  VERIFIED_COMPANY: 'VERIFIED_COMPANY',
};

export const EMITTER_EVENTS = {
  REPORT_AGENCY_COMPANY_ADDED: 'reportAgency.companyAdded',
};

export const unipileConfig = {
  UNIPILE_KEY: process.env.UNIPILE_KEY,
  UNIPILE_SUB_DOMAIN: process.env.UNIPILE_SUB_DOMAIN,
  UNIPILE_PORT: process.env.UNIPILE_PORT,
  UNIPILE_URL: `https://${process.env.UNIPILE_SUB_DOMAIN}.unipile.com:${process.env.UNIPILE_PORT}/api/v1/accounts`,
  UNIPILE_BASE_URL: `https://${process.env.UNIPILE_SUB_DOMAIN}.unipile.com:${process.env.UNIPILE_PORT}`,
  UNIPILE_PROVIDER: 'LINKEDIN',
};

export const STANDARD_JOB_TYPES = {
  APPRENTICE: 'Apprentice',
  APPRENTICESHIP: 'Apprenticeship',
  CASUAL: 'Casual',
  CONTRACT: 'Contract',
  CONTRACTOR: 'Contractor',
  EMPLOYEE: 'Employee',
  ENTRY_LEVEL: 'Entry-Level',
  FIXED_TERM: 'Fixed Term',
  FIXED_TERM_CONTRACT: 'Fixed Term Contract',
  FREELANCE: 'Freelance',
  FULL_TIME: 'Full-Time',
  GRADUATE: 'Graduate',
  GRADUATE_SCHEMES: 'Graduate Schemes',
  HYBRID_WORK: 'Hybrid Work',
  INTERN: 'Intern',
  INTERNSHIP: 'Internship',
  LOCUM_TENENS: 'Locum Tenens',
  PART_TIME: 'Part-Time',
  PER_DIEM: 'Per Diem',
  PRN: 'PRN',
  REMOTE: 'Remote',
  REMOTE_WORK: 'Remote Work',
  REMOTE_WORKING: 'Remote Work',
  SEASONAL: 'Seasonal',
  SELF_EMPLOYED: 'Self-Employed',
  SPECIFIED_PURPOSE: 'Specified Purpose',
  TEMPORARY: 'Temporary',
  TEMPORARY_CONTRACT: 'Temporary Contract',
  TEMP_TO_HIRE: 'Temp-to-Hire',
  TEMP_TO_PERM: 'Temp-to-Perm',
  TEMP_WORK: 'Temp Work',
  TENURE_TRACK: 'Tenure Track',
  TENURED: 'Tenured',
  TRAVEL_HEALTHCARE: 'Travel Healthcare',
  VACATION: 'Vacation',
  VOLUNTEER: 'Volunteer',
  WORK_EXPERIENCE: 'Work Experience',
  ZERO_HOURS_CONTRACT: 'Zero Hours Contract',
  PERMANENT: 'Permanent',
  POSTGRADUATE_COURSES: 'Postgraduate Courses',
  PROFESSIONAL_QUALIFICATIONS: 'Professional Qualifications',
  SALES: 'Sales',
  SCHOOL_COLLEGE_LEAVER_SCHEMES: 'School/College Leaver Schemes',
  TRAINEE: 'Trainee',
  TRAINEESHIPS: 'Traineeships',
  IT_TELECOMMUNICATIONS: 'IT & Telecommunications',
  NOT_ASSIGNED: 'Not Assigned',
  OTHER: 'Other',
};

export const SIMILAR_STANDARD_JOB_TYPES = {
  TEMPORARY: ['TEMPORARY', 'TEMP_WORK', 'TEMP_TO_HIRE', 'TEMPORARY_CONTRACT', 'TEMP_TO_PERM'],
  FIXED_TERM: ['FIXED_TERM', 'FIXED_TERM_CONTRACT'],
  REMOTE: ['REMOTE', 'REMOTE_WORK', 'REMOTE_WORKING'],
  HYBRID: ['HYBRID', 'HYBRID_WORK', 'HYBRID_WORKING'],
  CONTRACT: ['CONTRACT', 'CONTRACTOR'],
};

export const JOB_PREDEFINED_KEYWORDS = [
  'ai integration',
  'application development',
  'application extensions',
  'application integration',
  'artificial intelligences',
  'audit trails',
  'backup solutions',
  'brand management',
  'business analysis',
  'business analyst',
  'business analysts',
  'business analytics',
  'business central',
  'business development',
  'business intelligence',
  'business management',
  'campaign management',
  'change management',
  'cloud computing',
  'cloud deployment',
  'cloud services',
  'collaboration tools',
  'compliance management',
  'corporate strategies',
  'cost accounting',
  'crm integration',
  'crm solutions',
  'customer engagement',
  'customer experiences',
  'customer insights',
  'customer relationship management',
  'customer service',
  'customer success',
  'data analysis',
  'data analytics',
  'data integration',
  'data management',
  'data migration',
  'data protection',
  'database administration',
  'digital marketing',
  'digital transformation',
  'disaster recovery',
  'dynamic 365',
  'dynamics 365',
  'end-user support',
  'enterprise architecture',
  'enterprise resource planning',
  'erp solutions',
  'erp systems',
  'field service',
  'finance and operations',
  'financial management',
  'financial planning',
  'financial reporting',
  'full stack',
  'hardware management',
  'healthcare management',
  'human resources',
  'information security',
  'information technology',
  'integrated solutions',
  'integration with office 365',
  'international businesses',
  'inventory management',
  'it compliance',
  'it consulting',
  'it governance',
  'it infrastructure',
  'it infrastructure management',
  'it management',
  'it operations',
  'it outsourcing',
  'it policy',
  'it risk management',
  'it solutions',
  'it strategy',
  'it support',
  'lead management',
  'legal compliances',
  'machine learning',
  'managed services',
  'manufacturing management',
  'market research',
  'marketing automation',
  'marketing strategies',
  'microsoft ax',
  'microsoft dynamics',
  'mobile access',
  'network administration',
  'network management',
  'network securities',
  'network security',
  'on-premises deployment',
  'operations management',
  'operations research',
  'opportunity management',
  'order processing',
  'performance metrics',
  'power bi integration',
  'process automation',
  'product developments',
  'product management',
  'production planning',
  'project management',
  'project manager',
  'project managers',
  'project service automation',
  'public relations',
  'purchase management',
  'quality assurances',
  'real estates',
  'reporting and analytics',
  'reporting tools',
  'retail management',
  'risk management',
  'role-based security',
  'sales automation',
  'sales forecasting',
  'sales management',
  'sales operations',
  'sales strategies',
  'sap business one',
  'search engine optimization',
  'service level agreements',
  'service management',
  'social media management',
  'software development',
  'software developments',
  'software engineer',
  'software installation',
  'software integration',
  'solution architect',
  'solution design',
  'solution partner',
  'solutions architect',
  'supplier management',
  'supply chain management',
  'support services',
  'system architecture',
  'system configuration',
  'system upgrades',
  'systems integration',
  'tax management',
  'technical lead',
  'technical leads',
  'technical support',
  'technical supports',
  'technology integration',
  'training & development',
  'unified interface',
  'user access management',
  'user training',
  'ux/ui designs',
  'warehouse management',
  'workflow automation',
  'workflow management',
];

export const JOB_STOP_WORDS = [
  'a',
  'an',
  'the',
  'and',
  'or',
  'of',
  'to',
  'in',
  'on',
  'at',
  'for',
  'with',
  'without',
  'by',
  'before',
  'after',
  'above',
  'below',
  'from',
  'as',
  'is',
  'are',
  'was',
  'were',
  'be',
  'being',
  'been',
  'have',
  'has',
  'had',
  'having',
  'do',
  'does',
  'did',
  'doing',
  'will',
  'would',
  'shall',
  'should',
  'can',
  'could',
  'may',
  'might',
  'must',
  'ought',
  'i',
  'me',
  'my',
  'myself',
  'we',
  'our',
  'ours',
  'ourselves',
  'you',
  'your',
  'yours',
  'yourself',
  'yourselves',
  'he',
  'him',
  'his',
  'himself',
  'she',
  'her',
  'hers',
  'herself',
  'it',
  'its',
  'itself',
  'they',
  'them',
  'their',
  'theirs',
  'themselves',
  'what',
  'which',
  'who',
  'whom',
  'this',
  'that',
  'these',
  'those',
  'am',
  'but',
  'if',
  'because',
  'until',
  'while',
  'about',
  'against',
  'between',
  'into',
  'through',
  'during',
  'up',
  'down',
  'out',
  'off',
  'over',
  'under',
  'again',
  'further',
  'then',
  'once',
  'here',
  'there',
  'when',
  'where',
  'why',
  'how',
  'all',
  'any',
  'both',
  'each',
  'few',
  'more',
  'most',
  'other',
  'some',
  'such',
  'no',
  'nor',
  'not',
  'only',
  'own',
  'same',
  'so',
  'than',
  'too',
  'very',
  's',
  't',
  'just',
  'don',
  'now',
];

export const JOBS_INDEX = 'jobs';

export const CONTACT_FINDER_LINKEDIN_API: string = 'recruiter';

export const RECIPIENT_PER_TASK = 1;

export const BULL_JOB_ATTEMPTS = 3;

export const UNIPILE_REQUEST_LIMIT_TIMEOUT = 12; // hours

export const LINKEDIN_CONNECTION_REQUEST_REMOVE_TIMEOUT =
  +process.env.LINKEDIN_CONNECTION_REQUEST_REMOVE_TIMEOUT || 7 * 24; // hours

export const BULL_QUEUES = {
  SEQUENCE_STEP_TASK: `${CURRENT_ENV}_sequence_step_task`,
  WEBHOOK_SENDGRID_EVENT: `${CURRENT_ENV}_webhook_sendgrid_event`,
  WEBHOOK_NYLAS_EVENT: `${CURRENT_ENV}_webhook_nylas_event`,
  CRAWL_JOB_EVENT: `${CURRENT_ENV}_crawl_job_event`,
  BULLHORN_EMAIL_NOTE_EVENT: `${CURRENT_ENV}_bullhorn_email_note_event`,
  WEBHOOK_UNIPILE_EVENT: `${CURRENT_ENV}_webhook_unipile_event`,
  INTERNAL_QUEUE_EVENT: `${CURRENT_ENV}_internal_queue_event`,
};

export const BULL_JOB_NAMES = {
  SEQUENCE_STEP_TASK: `${CURRENT_ENV}_sequence_step_task`,
  WEBHOOK_SENDGRID_EVENT: `${CURRENT_ENV}_webhook_sendgrid_event`,
  WEBHOOK_NYLAS_EVENT: `${CURRENT_ENV}_webhook_nylas_event`,
  CRAWL_JOB_EVENT: `${CURRENT_ENV}_crawl_job_event`,
  BULLHORN_EMAIL_NOTE_EVENT: `${CURRENT_ENV}_bullhorn_email_note_event`,
  WEBHOOK_UNIPILE_EVENT: `${CURRENT_ENV}_webhook_unipile_event`,
  INTERNAL_QUEUE_EVENT: `${CURRENT_ENV}_internal_queue_event`,
};

export const REFINE_API = process.env.REFINE_API || 'http://************/search';
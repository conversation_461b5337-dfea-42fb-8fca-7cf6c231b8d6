import { HttpException, HttpStatus } from '@nestjs/common';

export const csvFileFilter = (
  req: Request,
  file: Express.Multer.File,
  callback: (error: Error | null, acceptFile: boolean) => void
) => {
  if (!file.mimetype.match(/^text\/csv$/i)) {
    callback(new HttpException('Upload not allowed. Only CSV files are accepted.', HttpStatus.BAD_REQUEST), false);
  } else {
    callback(null, true);
  }
};

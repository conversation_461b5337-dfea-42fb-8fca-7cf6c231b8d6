import { QuotaResetInterval } from 'src/modules/subscription/entities/organization-quota.entity';
import { RenewInterval } from '../modules/subscription/entities/subscription.entity';
import { CreditAllocationMode } from './subscription-system.config';

export interface Plan {
  id: string;
  name: string;
  subscriptionCycle: RenewInterval;
  price: number;
  currency: string;
  description?: string;
  credits?: {
    amount: number;
    resetInterval: QuotaResetInterval;
    allocationMode?: CreditAllocationMode;
    annualAmount?: number; // For annual upfront allocation
  };
}

export const PLANS: Record<string, Plan> = {
  basic: {
    id: 'basic',
    name: 'Basic',
    subscriptionCycle: RenewInterval.MONTHLY,
    price: 135,
    currency: 'USD',
    credits: {
      amount: 15_000,
      resetInterval: QuotaResetInterval.SUBSCRIPTION,
      allocationMode: CreditAllocationMode.ANNUAL_UPFRONT,
    },
    description: 'Basic plan with limited features'
  },
  pro: {
    id: 'pro',
    name: 'Professional',
    subscriptionCycle: RenewInterval.MONTHLY,
    price: 175,
    currency: 'USD',
    credits: {
      amount: 50_000, resetInterval: QuotaResetInterval.SUBSCRIPTION,
    },
    description: 'Professional plan with advanced features'
  },
  pro_plus: {
    id: 'pro_plus',
    name: 'Professional+',
    subscriptionCycle: RenewInterval.MONTHLY,
    price: 220,
    currency: 'USD',
    credits: {
      amount: 90_000, resetInterval: QuotaResetInterval.SUBSCRIPTION,
    },
    description: 'Professional+ plan with unlimited options'
  },
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise',
    subscriptionCycle: RenewInterval.MONTHLY,
    price: 265,
    currency: 'USD',
    credits: {
      amount: 120_000, resetInterval: QuotaResetInterval.SUBSCRIPTION,
    },
    description: 'Enterprise plan with full capabilities'
  }
};

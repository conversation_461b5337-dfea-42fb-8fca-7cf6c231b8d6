export type FeatureType = 'credit' | 'usage' | 'boolean' | 'discount';
export type FeatureUnit = 'credit' | 'count' | 'percent';
export enum FeatureUnitEnum {
  CREDIT = 'credit',
  COUNT = 'count',
  PERCENT = 'percent',
}

export interface Feature {
  id: string;
  name: string;
  type: FeatureType;
  unit: FeatureUnit;
  costPerUse: number;
  description?: string;
}

export const FEATURES: Record<string, Feature> = {
  // Add credits here to ensure data is consistent
  credits: {
    id: 'credits',
    name: 'Credits',
    type: 'credit',
    unit: 'credit',
    costPerUse: 1,
    description: 'General purpose credits for various features'
  },
  contact_finder: {
    id: 'contact_finder',
    name: 'Contact Finder Searches',
    type: 'usage',
    unit: 'count',
    costPerUse: 1,
    description: 'Search for contacts using contact finder'
  },
  sequence: {
    id: 'sequence',
    name: 'Number of Live Sequence active',
    type: 'usage',
    unit: 'count',
    costPerUse: 1,
    description: 'Number of Live sequences active at a time'
  },
  email_sequence: {
    id: 'email_sequence',
    name: 'Sequence Emails per day',
    type: 'usage',
    unit: 'count',
    costPerUse: 1,
    description: 'Number of emails that can be sent via sequence per day'
  },
  linkedin_steps: {
    id: 'linkedin_steps',
    name: 'LinkedIn Steps per day',
    type: 'usage',
    unit: 'count',
    costPerUse: 1,
    description: 'LinkedIn automation steps per day'
  },
  enrichment: {
    id: 'enrichment',
    name: 'Enrichment Credit',
    type: 'credit',
    unit: 'credit',
    costPerUse: 1,
    description: 'Credits for enriching contact data'
  },
  lead_search: {
    id: 'lead_search',
    name: 'Lead Searches',
    type: 'usage',
    unit: 'count',
    costPerUse: 1,
    description: 'Lead search actions'
  },
  sync_leads: {
    id: 'sync_leads',
    name: 'Sync Leads',
    type: 'usage',
    unit: 'count',
    costPerUse: 1,
    description: 'Sync leads to system'
  },
  live_contact_search: {
    id: 'live_contact_search',
    name: 'Live Contact Searches',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Live searching LinkedIn through contact finder instead of searching through previous scraped data from data providers'
  },
  ai_recruiter: {
    id: 'ai_recruiter',
    name: 'AI Recruiter Messaging',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Use AI to generate recruiter messages'
  },
  candidate_sequencing: {
    id: 'candidate_sequencing',
    name: 'Candidate Sequencing',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Setup candidate sequences'
  },
  db_import_for_sequence_and_enrichment: {
    id: 'db_import_for_sequence_and_enrichment',
    name: 'Database Import for Sequence and Enrichment',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Import your own database through API or upload for Sequencing and/or Enrichment'
  },
  sequence_templates: {
    id: 'sequence_templates',
    name: 'Sequence Templates',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Use shared sequence templates'
  },
  integrated_mailbox: {
    id: 'integrated_mailbox',
    name: 'Integrated Mailbox',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Mailbox integration feature'
  },
  phone_enrichment: {
    id: 'phone_enrichment',
    name: 'Phone Enrichment',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Phone number enrichment from data'
  },
  crm_integration: {
    id: 'crm_integration',
    name: 'CRM/ATS Integration',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'CRM/ATS sync capability'
  },
  export_contacts: {
    id: 'export_contacts',
    name: 'Export Contacts',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Export contacts to file or CRM'
  },
  adv_linkedin: {
    id: 'adv_linkedin',
    name: 'Advanced LinkedIn Sequence',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Advanced automation features for LinkedIn'
  },
  tasks: {
    id: 'tasks',
    name: 'Tasks',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Task management system'
  },
  api_access: {
    id: 'api_access',
    name: 'API Access',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Developer access to API'
  },
  user_management: {
    id: 'user_management',
    name: 'User Management',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Management users for team divisions for managing, reporting and viewing across all core functions'
  },
  ai_recommended_contacts: {
    id: 'ai_recommended_contacts',
    name: 'AI Recommended Contacts',
    type: 'boolean',
    unit: 'count',
    costPerUse: 0,
    description: 'Your own AI learning machine for recommended contacts for your industry and from previously added contacts'
  },
  discount_bulk_enrichment: {
    id: 'discount_bulk_enrichment',
    name: 'Discount Bulk Enrichment',
    type: 'discount',
    unit: 'percent',
    costPerUse: 0,
    description: 'Discount percentage available on bulk data enriching for your existing database'
  }
};

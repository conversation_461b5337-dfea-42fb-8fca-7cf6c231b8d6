// Hardcoded system configuration for subscription and license management

export enum CreditAllocationMode {
  ANNUAL_UPFRONT = 'annual_upfront', // C<PERSON>p hết credits đầu năm
  MONTHLY_DISTRIBUTED = 'monthly_distributed', // C<PERSON><PERSON> credits theo tháng
}

export enum UpgradePolicy {
  IMMEDIATE = 'immediate', // Áp dụng ngay lập tức
  NEXT_CYCLE = 'next_cycle', // Áp dụng chu kỳ tiếp theo
}

export enum DowngradePolicy {
  IMMEDIATE = 'immediate', // Áp dụng ngay lập tức
  NEXT_CYCLE = 'next_cycle', // Áp dụng chu kỳ tiếp theo
}

export enum CreditHandlingOnChange {
  RESET = 'reset', // Reset về plan mới
  KEEP = 'keep', // Gi<PERSON> nguyên credits hiện tại
  ADD_DIFFERENCE = 'add_difference', // Thêm chênh lệch (upgrade)
  SUBTRACT_DIFFERENCE = 'subtract_difference', // Tr<PERSON> chênh lệch (downgrade)
}

export interface SubscriptionSystemConfig {
  // Grace period settings
  gracePeriodDays: number;
  allowCreditUsageDuringGrace: boolean;

  // Payment failure settings
  maxPaymentRetries: number;
  paymentRetryIntervalHours: number;

  // Credit notification settings
  lowCreditThreshold: number; // Percentage (e.g., 10 = 10%)
  creditExpiryWarningDays: number;

  // License transfer settings
  maxLicenseTransfersPerMonth: number;
  licenseTransferCooldownDays: number;

  // Trial settings
  trialDurationDays: number;
  trialCredits: number;
  requirePaymentMethodForTrial: boolean;

  // Transaction timeout settings
  paymentConfirmationTimeoutSeconds: number;
  licenseAllocationTimeoutSeconds: number;
  quotaAllocationTimeoutSeconds: number;

  // Plan change policies
  upgradePolicy: UpgradePolicy;
  downgradePolicy: DowngradePolicy;
  creditHandlingOnUpgrade: CreditHandlingOnChange;
  creditHandlingOnDowngrade: CreditHandlingOnChange;

  // Credit allocation
  creditAllocationMode: CreditAllocationMode;

  // Billing settings
  prorateUpgrades: boolean;
  prorateDowngrades: boolean;

  // Notification settings
  enableEmailNotifications: boolean;
  enableInAppNotifications: boolean;

  // Audit and compliance
  retainTransactionHistoryDays: number;
  enableDetailedAuditLog: boolean;
}

// Hardcoded system configuration - no database storage needed
export const SUBSCRIPTION_SYSTEM_CONFIG = {
  // Grace period: 3 days, no credit usage during grace
  GRACE_PERIOD_DAYS: 3,
  ALLOW_CREDIT_USAGE_DURING_GRACE: false,

  // Payment failures: 3 retries, 24 hours apart
  MAX_PAYMENT_RETRIES: 3,
  PAYMENT_RETRY_INTERVAL_HOURS: 24,

  // Credit notifications: warn at 10% remaining, 7 days before expiry
  LOW_CREDIT_THRESHOLD: 10, // percentage
  CREDIT_EXPIRY_WARNING_DAYS: 7,

  // License transfers: max 2 per month, 7 days cooldown
  MAX_LICENSE_TRANSFERS_PER_MONTH: 2,
  LICENSE_TRANSFER_COOLDOWN_DAYS: 7,

  // Trial: 14 days, 1000 credits, require payment method
  TRIAL_DURATION_DAYS: 14,
  TRIAL_CREDITS: 1000,
  REQUIRE_PAYMENT_METHOD_FOR_TRIAL: true,

  // Transaction timeouts: increased for complex payment operations
  PAYMENT_CONFIRMATION_TIMEOUT_SECONDS: 300, // 5 minutes for payment confirmation
  LICENSE_ALLOCATION_TIMEOUT_SECONDS: 300, // 5 minutes for license allocation
  QUOTA_ALLOCATION_TIMEOUT_SECONDS: 300, // 5 minutes for quota allocation

  // Plan changes: immediate by default
  UPGRADE_POLICY: UpgradePolicy.IMMEDIATE,
  DOWNGRADE_POLICY: DowngradePolicy.IMMEDIATE,
  CREDIT_HANDLING_ON_UPGRADE: CreditHandlingOnChange.ADD_DIFFERENCE,
  CREDIT_HANDLING_ON_DOWNGRADE: CreditHandlingOnChange.KEEP,

  // Credit allocation: annual upfront by default (can be overridden per plan)
  DEFAULT_CREDIT_ALLOCATION_MODE: CreditAllocationMode.ANNUAL_UPFRONT,

  // Billing: prorate upgrades, no prorate downgrades
  PRORATE_UPGRADES: true,
  PRORATE_DOWNGRADES: false,

  // Notifications: enable both
  ENABLE_EMAIL_NOTIFICATIONS: true,
  ENABLE_IN_APP_NOTIFICATIONS: true,

  // Audit: retain 2 years, enable detailed logging
  RETAIN_TRANSACTION_HISTORY_DAYS: 730,
  ENABLE_DETAILED_AUDIT_LOG: true,
} as const;

// Helper functions to get config values
export const getSubscriptionConfig = () => SUBSCRIPTION_SYSTEM_CONFIG;

export const getCreditAllocationMode = (planId?: string): CreditAllocationMode => {
  // Can be overridden per plan in the future
  return SUBSCRIPTION_SYSTEM_CONFIG.DEFAULT_CREDIT_ALLOCATION_MODE;
};

// Feature cost configuration
export interface FeatureCost {
  featureId: string;
  costPerUse: number;
  unit: string;
  description: string;
}

// Notification templates
export const NOTIFICATION_TEMPLATES = {
  LOW_CREDITS: {
    subject: 'Low Credits Warning',
    template:
      'Your account has {remaining_credits} credits remaining ({percentage}% of your allocation).',
  },
  TRIAL_ENDING: {
    subject: 'Trial Period Ending Soon',
    template:
      'Your trial period will end on {trial_end_date}. Please add a payment method to continue.',
  },
  PAYMENT_FAILED: {
    subject: 'Payment Failed',
    template: 'We were unable to process your payment. Please update your payment method.',
  },
  SUBSCRIPTION_EXPIRED: {
    subject: 'Subscription Expired',
    template: 'Your subscription has expired. You have {grace_period_days} days to renew.',
  },
} as const;

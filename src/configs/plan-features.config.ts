import { QuotaResetInterval } from '../modules/subscription/entities/organization-quota.entity';

export interface PlanFeature {
  amount?: number;
  allowUserQuota?: boolean;
  resetInterval?: QuotaResetInterval;
  unlimited?: boolean;
  costPerUse?: number;
}

export const PLAN_FEATURES: Record<string, Record<string, PlanFeature>> = {
  basic: {
    credits: { amount: 15000, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: true },
    contact_finder: { amount: 4000, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    sequence: { amount: 5, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    email_sequence: { amount: 400, resetInterval: QuotaResetInterval.DAILY, allowUserQuota: false },
    linkedin_steps: { amount: 50, resetInterval: QuotaResetInterval.DAILY, allowUserQuota: false },
    enrichment: { amount: 0, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    lead_search: { amount: 2, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    sync_leads: { amount: 2, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    live_contact_search: { amount: 0 },
    ai_recruiter: { amount: 0 },
    candidate_sequencing: { amount: 0 },
    db_import_for_sequence_and_enrichment: { amount: 0 },
    sequence_templates: { amount: 0 },
    integrated_mailbox: { amount: 0 },
    phone_enrichment: { amount: 0 },
    crm_integration: { amount: 0 },
    export_contacts: { amount: 0 },
    adv_linkedin: { amount: 0 },
    tasks: { amount: 0 },
    api_access: { amount: 0 },
    user_management: { amount: 0 },
    ai_recommended_contacts: { amount: 0 },
    discount_bulk_enrichment: { amount: 0 }
  },
  pro: {
    credits: { amount: 50000, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: true },
    contact_finder: { amount: 20000, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    sequence: { amount: 10, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    email_sequence: { amount: 2000, resetInterval: QuotaResetInterval.DAILY, allowUserQuota: false },
    linkedin_steps: { amount: 250, resetInterval: QuotaResetInterval.DAILY, allowUserQuota: false },
    enrichment: { amount: 1000, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    lead_search: { amount: 10, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    sync_leads: { amount: 5, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    live_contact_search: { unlimited: true },
    ai_recruiter: { unlimited: true },
    candidate_sequencing: { unlimited: true },
    db_import_for_sequence_and_enrichment: { unlimited: true },
    sequence_templates: { unlimited: true },
    integrated_mailbox: { unlimited: true },
    phone_enrichment: { unlimited: true },
    crm_integration: { unlimited: true },
    export_contacts: { unlimited: true },
    adv_linkedin: { unlimited: true },
    tasks: { unlimited: true },
    api_access: { unlimited: true },
    user_management: { amount: 0 },
    ai_recommended_contacts: { amount: 0 },
    discount_bulk_enrichment: { amount: 0 }
  },
  pro_plus: {
    credits: { amount: 90000, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: true },
    contact_finder: { unlimited: true },
    sequence: { amount: 20, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    email_sequence: { unlimited: true, resetInterval: QuotaResetInterval.DAILY, allowUserQuota: false },
    linkedin_steps: { amount: 500, resetInterval: QuotaResetInterval.DAILY, allowUserQuota: false },
    enrichment: { amount: 5000, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    lead_search: { unlimited: true },
    sync_leads: { unlimited: true },
    live_contact_search: { unlimited: true },
    ai_recruiter: { unlimited: true },
    candidate_sequencing: { unlimited: true },
    db_import_for_sequence_and_enrichment: { unlimited: true },
    sequence_templates: { unlimited: true },
    integrated_mailbox: { unlimited: true },
    phone_enrichment: { unlimited: true },
    crm_integration: { unlimited: true },
    export_contacts: { unlimited: true },
    adv_linkedin: { unlimited: true },
    tasks: { unlimited: true },
    api_access: { unlimited: true },
    user_management: { unlimited: true },
    ai_recommended_contacts: { unlimited: true },
    discount_bulk_enrichment: { amount: 5 }
  },
  enterprise: {
    credits: { amount: 120000, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: true },
    contact_finder: { unlimited: true },
    sequence: { unlimited: true },
    email_sequence: { unlimited: true, resetInterval: QuotaResetInterval.DAILY, allowUserQuota: false },
    linkedin_steps: { amount: 500, resetInterval: QuotaResetInterval.DAILY, allowUserQuota: false },
    enrichment: { amount: 10000, resetInterval: QuotaResetInterval.SUBSCRIPTION, allowUserQuota: false },
    lead_search: { unlimited: true },
    sync_leads: { unlimited: true },
    live_contact_search: { unlimited: true },
    ai_recruiter: { unlimited: true },
    candidate_sequencing: { unlimited: true },
    db_import_for_sequence_and_enrichment: { unlimited: true },
    sequence_templates: { unlimited: true },
    integrated_mailbox: { unlimited: true },
    phone_enrichment: { unlimited: true },
    crm_integration: { unlimited: true },
    export_contacts: { unlimited: true },
    adv_linkedin: { unlimited: true },
    tasks: { unlimited: true },
    api_access: { unlimited: true },
    user_management: { unlimited: true },
    ai_recommended_contacts: { unlimited: true },
    discount_bulk_enrichment: { amount: 15 }
  }
};

import { TypeOrmModule } from '@nestjs/typeorm';
import { databaseConfig } from '../configs.constants';
import * as dotenv from 'dotenv';
dotenv.config();
import { DataSource, DataSourceOptions } from 'typeorm';

const getMigrationsPath = () => {
  const mode = process.env.MIGRATION_MODE || 'run_migration';
  return mode === 'run_migration'
    ? `${__dirname}/../../database/migrations/*{.ts,.js}`
    : `${__dirname}/../../database/seeder/*{.ts,.js}`;
};

const getMigrationsTable = () => {
  const mode = process.env.MIGRATION_MODE || 'run_migration';
  return mode === 'run_migration'
    ? `__migrations`
    : `__seeder`;
};


const masterDBConfig = {
  host: databaseConfig.host,
  port: databaseConfig.port,
  username: databaseConfig.username,
  password: databaseConfig.password,
  database: databaseConfig.database,
};

export const typeOrmConfig: any = {
  type: databaseConfig.type || 'postgres',
  entities: [`${__dirname}/../../**/*.entity.{js,ts}`],
  migrationsTableName: getMigrationsTable(),
  migrations: [getMigrationsPath()],
  logging: false,
  synchronize: databaseConfig.synchronize,
  ssl: {
    rejectUnauthorized: false,
  },
  extra: {
    ssl: {
      rejectUnauthorized: false,
    },
    poolSize: 20,
    statementTimeout: 6000,
    idle_in_transaction_session_timeout: '600s',
  },
  ...(databaseConfig.read_host ? {
    replication: {
      master: masterDBConfig,
      slaves: [
        {
          ...masterDBConfig,
          host: databaseConfig.read_host,
        },
      ],
    },
  } : masterDBConfig)
};

const dataSource = new DataSource(typeOrmConfig);

export default dataSource;

import * as path from 'path';
import { createClient } from 'redis';
import { forwardRef, Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { CronService } from 'src/cron.service';
import { BULL_QUEUES, redisConfig, redisConnection } from 'src/configs/configs.constants';
import { BullModule } from '@nestjs/bullmq';
import { BullHornService } from 'src/middlewares/bullhorn/bullhorn.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { typeOrmConfig } from 'src/configs/database/typeorm.config';
import { I18nModule, HeaderResolver } from 'nestjs-i18n';
import { LanguageCode } from 'src/common/constants/common.constant';
import { SequenceRepository } from 'src/modules/mail/repositories/sequence.repostiory';
import { SequenceStepRepository } from 'src/modules/mail/repositories/sequence-step.repostiory';
import { SequenceInstanceRepository } from 'src/modules/mail/repositories/sequence-instance.repository';
import { SequenceActivityLogRepository } from 'src/modules/mail/repositories/sequence-activity-log.repository';
import { SequenceStepTaskRepository } from 'src/modules/mail/repositories/sequence-step-task.repository';
import { SequenceStepTaskWorker } from 'src/cron/sequence-step-task.worker';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { SharedUserService } from 'src/modules/shared/shared-user.service';
import { SharedMailService } from 'src/modules/shared/shared-mail.service';
import { ContactRepository } from 'src/modules/user/repositories/contact.repository';
import { JobLeadsRepository } from 'src/modules/jobs/repository/job-leads.repository';
import { OpensearchService } from 'src/modules/opensearch/service/opensearch.service';
import { UserSignatureServices } from 'src/modules/user/user-signature.service';
import { FileUploadService } from 'src/modules/files-upload/file-upload.service';
import { JobBoardsRepository } from 'src/modules/jobs/repository/job-boards.repository';
import { UserSignatureRepository } from 'src/modules/user/repositories/user-signature.repository';
import { HttpModule } from '@nestjs/axios';
import { CrmContactSequenceStepRepository } from './modules/crm/repositories/crm-contact-sequence-step.repository';
import { CrmContactSequenceRepository } from './modules/crm/repositories/crm-contact-sequence.repository';
import { CrmBullhornService } from './modules/crm/services/crm-bullhorn.service';
import { CrmCompanyRepository } from './modules/crm/repositories/crm-company.repository';
import { CrmContactRepository } from './modules/crm/repositories/crm-contact.repository';
import { CrmSkillsRepository } from './modules/crm/repositories/crm-skill.repository';
import { CrmIndustryRepository } from './modules/crm/repositories/crm-industry.repository';
import { CrmTagRepository } from './modules/crm/repositories/crm-tag.repository';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { JobsModule } from './modules/jobs/jobs.module';
import { FileUploadModule } from './modules/files-upload/file-upload.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RedisModule } from './modules/redis/redis.module';
import { CronJobStatusService } from './modules/redis/cronjob-status.service';
import { WebhookService } from './modules/webhook/services/webhook.service';
import { WebhookRepository } from './modules/webhook/repositories/webhook.repository';

@Module({
  imports: [
    TypeOrmModule.forRoot(typeOrmConfig),
    I18nModule.forRoot({
      fallbackLanguage: LanguageCode.United_States,
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'),
        watch: true,
      },
      resolvers: [new HeaderResolver(['x-lang'])],
    }),
    ScheduleModule.forRoot(),
    HttpModule,
    RedisModule,
    BullModule.forRoot({
      connection: redisConnection,
      defaultJobOptions: {
        removeOnComplete: {
          age: 86400,
          count: 1000,
        },
        removeOnFail: {
          age: 86400,
          count: 1000,
        },
      },
    }),
    BullModule.registerQueue(
      {
        name: BULL_QUEUES.BULLHORN_EMAIL_NOTE_EVENT,
        prefix: '{bull_bullhorn_email_note_event}',
      },
      {
        name: BULL_QUEUES.INTERNAL_QUEUE_EVENT,
        prefix: '{bull_internal_queue_event}',
      },
    ),
    SubscriptionModule,
    JobsModule,
    FileUploadModule,
    EventEmitterModule.forRoot(),
  ],
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: async () => {
        const client = createClient({
          url: redisConfig.REDIS_CONNECTION,
          socket: {
            tls: false,
            reconnectStrategy: (retries) => {
              return Math.min(retries * 50, 2000);
            },
          },
        });
        await client.connect();
        return client;
      },
    },
    CronService,
    CronJobStatusService,
    BullHornService,
    SharedUserService,
    SharedMailService,
    OpensearchService,
    FileUploadService,
    UserSignatureServices,
    CrmBullhornService,
    WebhookService,
    ContactRepository,
    JobLeadsRepository,
    SequenceRepository,
    SequenceStepRepository,
    SequenceInstanceRepository,
    SequenceStepTaskRepository,
    SequenceActivityLogRepository,
    CrmContactSequenceStepRepository,
    CrmContactSequenceRepository,
    CrmCompanyRepository,
    CrmContactRepository,
    CrmSkillsRepository,
    CrmIndustryRepository,
    CrmTagRepository,
    UserSignatureRepository,
    JobBoardsRepository,
    UserRepository,
    WebhookRepository,
    SequenceStepTaskWorker,
  ],
  exports: ['REDIS_CLIENT'],
})
export class CronModule {}

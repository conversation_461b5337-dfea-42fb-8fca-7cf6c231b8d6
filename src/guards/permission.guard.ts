import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { DefaultRoleIds } from 'src/configs/configs.constants';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { SendByEmailsDto } from 'src/modules/mail/dto/send-by-emails.dto';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { RoleEnum } from 'src/modules/user/entities/role.entity';
import { DataSource } from 'typeorm';
import { FeatureToPermissionsMapping } from 'src/common/constants/feature-permission.constant';
import { PermissionLogic, PermissionOptions } from 'src/common/decorators/permissions.decorator';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(private reflector: Reflector, private dataSource: DataSource) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const permissionOptions = this.reflector.getAllAndOverride<string | PermissionOptions>('permission', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!permissionOptions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    // Get the user from the request
    const user = request.user as IJwtPayload;

    // Use viewAsUser if available (set by ViewAsMiddleware)
    const requester = request.viewAsUser || user;

    if (!user) {
      return false;
    }

    // Super Admin can do anything
    if (user.role === RoleEnum.SUPER_ADMIN) {
      return true;
    }

    // Only users with VIEW_AS permission can use view-as endpoints
    if (user.id !== requester.id) {
      // If user is viewing someone else, check VIEW_AS permission
      const viewAsPermission = PermissionResource[ResourceEnum.VIEW_AS].Read;

      // Check explicit permissions first (faster check)
      const hasExplicitViewAsPermission = user.permissions?.includes(viewAsPermission);
      if (!hasExplicitViewAsPermission) {
        // If no explicit permission, check role-based permission (requires DB query)
        const hasViewAsPermission = await this.checkPermissionByRole(user.role, viewAsPermission);
        if (!hasViewAsPermission) {
          return false;
        }
      }
      // If we get here, the user has VIEW_AS permission
      // Continue with normal permission checks below
    }

    // Convert string permission to options format for consistent handling
    const options: PermissionOptions =
      typeof permissionOptions === 'string'
        ? { permissions: [permissionOptions], logic: PermissionLogic.OR }
        : permissionOptions;

    // Check for special cases first
    const hasSpecialPermission = await this.checkSpecialCases(requester, options.permissions, request);
    if (hasSpecialPermission !== null) {
      return hasSpecialPermission;
    }

    // Check permissions based on logic type
    if (options.logic === PermissionLogic.OR) {
      // OR logic - user needs at least one of the permissions
      // Use Promise.all with Array.some to check permissions in parallel
      const permissionChecks = await Promise.all(
        options.permissions.map((permission) => this.hasPermission(requester, permission))
      );

      // Return true if any permission check passed
      return permissionChecks.some((hasPermission) => hasPermission);
    }

    // AND logic - user needs all permissions
    // Use Promise.all with Array.every to check permissions in parallel
    const permissionChecks = await Promise.all(
      options.permissions.map((permission) => this.hasPermission(requester, permission))
    );

    // Return true if all permission checks passed
    return permissionChecks.every((hasPermission) => hasPermission);
  }

  /**
   * Check special permission cases
   * @param user The user to check
   * @param permissions The permissions to check
   * @param request The HTTP request
   * @returns True if special case grants access, false if special case denies access, null if no special case applies
   */
  private async checkSpecialCases(user: IJwtPayload, permissions: string[], request: any): Promise<boolean | null> {
    // Check for special cases using Array.some
    const hasInvitePermission = permissions.includes(PermissionResource[ResourceEnum.USER_MANAGEMENT].Invite);

    // Special case for inviting users
    if (hasInvitePermission && (user.role === RoleEnum.SALES || user.role === RoleEnum.ADMIN)) {
      const { invitedUsers } = request.body as SendByEmailsDto;
      if (invitedUsers) {
        const invalidRoleIds = invitedUsers.some(
          (invitedUser) =>
            invitedUser.roleId !== DefaultRoleIds[RoleEnum.MANAGEMENT] &&
            invitedUser.roleId !== DefaultRoleIds[RoleEnum.BASIC_USER]
        );

        if (invalidRoleIds) {
          return false;
        }
        return true;
      }
    }

    // Special case for Basic User editing their own profile
    const hasWritePermission = permissions.includes(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write);
    if (hasWritePermission && user.role === RoleEnum.BASIC_USER) {
      const userId = request.params?.id;
      if (userId === user.id) {
        return true;
      }
    }

    // No special case applies
    return null;
  }

  /**
   * Check if a user has a specific permission
   * @param user The user to check
   * @param permission The permission to check
   * @returns True if the user has the permission, false otherwise
   */
  private async hasPermission(user: IJwtPayload, permission: string): Promise<boolean> {
    // Fast check for explicit permissions (avoid DB query)
    if (user.permissions?.includes(permission)) {
      return true;
    }

    // Check if user has feature permissions that include this permission
    if (user.features?.length > 0) {
      // Use some() instead of for loop
      const hasFeaturePermission = user.features.some((feature) =>
        (FeatureToPermissionsMapping[feature] || []).includes(permission)
      );

      if (hasFeaturePermission) {
        return true;
      }
    }

    // If no explicit permission, check permissions by role using role_default_permissions table
    return this.checkPermissionByRole(user.role, permission);
  }

  /**
   * Check if a role has permission to access a resource
   * @param role The user's role
   * @param permission The permission to check
   * @returns True if the role has permission, false otherwise
   */
  private async checkPermissionByRole(role: RoleEnum, permission: string): Promise<boolean> {
    // Extract resource and action from permission
    const [resource, action] = permission.split('.');
    const isReadPermission = action === 'Read';

    try {
      // Query the role_default_permissions table to check if the role has permission
      const result = await this.dataSource.query(
        `
        SELECT rdp."allowRead", rdp."allowWrite", rdp."id", rdp."roleId", rdp."permissionId"
        FROM role_default_permissions rdp
        JOIN permissions p ON rdp."permissionId" = p.id
        JOIN roles r ON rdp."roleId" = r.id
        WHERE r."keyCode" = $1 AND p."keyCode" = $2
        LIMIT 1
      `,
        [role, resource]
      );

      if (result.length > 0) {
        // Check if the role has the required permission
        if (isReadPermission && result[0].allowRead) {
          return true;
        }
        if (!isReadPermission && result[0].allowWrite) {
          return true;
        }
      }

      return false;
    } catch (error) {
      // Log error and return false
      Logger.error(`Error checking permission by role: ${error.message}`, 'PermissionGuard');
      return false;
    }
  }
}

import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CommonConfig } from '../configs/configs.constants';
import { decryptedContent, logInfo } from '../common/utils/helpers.util';
import * as moment from 'moment-timezone';
@Injectable()
export class JobsGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    try {
      const token = request.body['token'];
      if (!token) {
        return false;
      }
      const decryptedMsg = await decryptedContent(token);
      console.log('decryptedMsg', decryptedMsg);
      if (!decryptedMsg) {
        return false;
      }
      const { time } = JSON.parse(decryptedMsg);
      if (!time) {
        return false;
      }
      const currentTime = moment.tz('Asia/Singapore').format('x');
      console.log('currentTime', currentTime);
      return +currentTime - time > CommonConfig.SECRET_JOB_EXPIRE * 60 * 1000 ? false : true;
    } catch (error) {
      logInfo('JobsGuard', error);
      return false;
    }
  }
}

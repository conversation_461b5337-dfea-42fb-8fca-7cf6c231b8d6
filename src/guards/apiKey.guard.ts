import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { CommonConfig } from '../configs/configs.constants';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const key = req.headers['x_api_key'] ?? req.query.api_key;
    return key && key == CommonConfig.SECRET_KEY;
  }
}

import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';

@Injectable()
export class BasicAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Basic ')) {
      // Set WWW-Authenticate header to trigger browser popup
      response.setHeader('WWW-Authenticate', 'Basic realm="CronJob Dashboard"');
      throw new UnauthorizedException('Basic authentication required');
    }

    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    const [username, password] = credentials.split(':');

    const expectedUsername = process.env.CRON_DASHBOARD_USERNAME;
    const expectedPassword = process.env.CRON_DASHBOARD_PASSWORD;

    if (!expectedUsername || !expectedPassword) {
      response.setHeader('WWW-Authenticate', 'Basic realm="CronJob Dashboard"');
      throw new UnauthorizedException('Basic auth credentials not configured');
    }

    if (username !== expectedUsername || password !== expectedPassword) {
      response.setHeader('WWW-Authenticate', 'Basic realm="CronJob Dashboard"');
      throw new UnauthorizedException('Invalid credentials');
    }

    return true;
  }
}

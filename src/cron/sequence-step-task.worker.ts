/* eslint-disable camelcase */
import { Injectable } from '@nestjs/common';
import {
  BULL_JOB_ATTEMPTS,
  BULL_JOB_NAMES,
  LINKEDIN_CONNECTION_REQUEST_REMOVE_TIMEOUT,
  UNIPILE_REQUEST_LIMIT_TIMEOUT,
} from 'src/configs/configs.constants';
import { SequenceRepository } from 'src/modules/mail/repositories/sequence.repostiory';
import { SequenceStepRepository } from 'src/modules/mail/repositories/sequence-step.repostiory';
import { SequenceInstanceRepository } from 'src/modules/mail/repositories/sequence-instance.repository';
import { SequenceActivityLogRepository } from 'src/modules/mail/repositories/sequence-activity-log.repository';
import { JobLeadsRepository } from 'src/modules/jobs/repository/job-leads.repository';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { SequenceActivityType } from 'src/modules/mail/entities/sequence-activity-log.entity';
import * as os from 'node:os';
import * as unipileClient from 'src/modules/jobs/utils/unipile-service.utils';
import { getMergeTagsContent, safeParseJSON } from 'src/common/utils/helpers.util';
import { SequenceStepType } from 'src/modules/mail/entities/sequence-step.entity';
import { SequenceStepFlag, SequenceStepStatus } from 'src/modules/mail/entities/sequence-instance.entity';
import { SequenceStatus } from 'src/modules/mail/entities/sequence.entity';
import { JobLead } from 'src/modules/jobs/entities/job-leads.entity';
import { SequenceStepTaskRepository } from 'src/modules/mail/repositories/sequence-step-task.repository';
import { SharedMailService } from 'src/modules/shared/shared-mail.service';
import { validate as uuidValidate } from 'uuid';
import { CrmContactSequenceStepRepository } from 'src/modules/crm/repositories/crm-contact-sequence-step.repository';
import { CrmContactSequenceRepository } from 'src/modules/crm/repositories/crm-contact-sequence.repository';
import { CrmBullhornService } from 'src/modules/crm/services/crm-bullhorn.service';
import { CrmContactRepository } from 'src/modules/crm/repositories/crm-contact.repository';
import { DataSource } from 'typeorm';
import { CrmContactSequenceEntity } from 'src/modules/crm/entities/crm-contact-sequence.entity';
import { CreditService } from 'src/modules/subscription/services/credit.service';
import { FEATURES } from 'src/configs/features.config';
import { UnipileAccountStatus } from 'src/modules/user/entities/user.entity';

@Injectable()
export class SequenceStepTaskWorker {
  constructor(
    private readonly dataSource: DataSource,
    private readonly sharedMailService: SharedMailService,
    private readonly crmBullhornService: CrmBullhornService,
    private readonly creditService: CreditService,
    private readonly userRepository: UserRepository,
    private readonly sequenceRepository: SequenceRepository,
    private readonly sequenceStepRepository: SequenceStepRepository,
    private readonly sequenceInstanceRepository: SequenceInstanceRepository,
    private readonly sequenceActivityLogRepository: SequenceActivityLogRepository,
    private readonly sequenceStepTaskRepository: SequenceStepTaskRepository,
    private readonly jobLeadRepository: JobLeadsRepository,
    private readonly crmContactSequenceStepRepository: CrmContactSequenceStepRepository,
    private readonly crmContactSequenceRepository: CrmContactSequenceRepository,
    private readonly crmContactRepository: CrmContactRepository,
  ) {}

  async handler(job: any) {
    const { id, data, attemptsMade } = job;
    console.log(`[SEQUENCE QUEUE] ID ${id} is running...`);

    if (data?.type === 'UNFOLLOW_LINKEDIN') {
      return this.unfollowLinkedIn(job);
    }

    if (data?.type === 'CANCEL_LINKEDIN_INVITATION') {
      return this.cancelLinkedInInvitation(job);
    }

    let isStepDeleted = true;
    const featureId = FEATURES.email_sequence.id;
    try {
      const [sequence, sequenceStep] = await Promise.all([
        this.sequenceRepository.findOne({ where: { id: data.sequence?.id }, relations: { user: true } }),
        this.sequenceStepRepository.findOneBy({ id: data.sequenceStep?.id }),
      ]);
      isStepDeleted = !sequenceStep;
      if (!sequence) {
        const unrecoverableError: any = new Error('Sequence not found');
        unrecoverableError.code = 'UNRECOVERABLE';
        throw unrecoverableError;
      }

      if (sequence.status !== SequenceStatus.LIVE) {
        const unrecoverableError: any = new Error('Sequence was stopped');
        unrecoverableError.code = 'UNRECOVERABLE';
        throw unrecoverableError;
      }

      const user = await this.userRepository.findOne({ where: { id: data.user?.id } });
      const jobLeadCondition = [];
      if (data.sequenceStep?.externalJobId) {
        jobLeadCondition.push({
          job_lead_external_id: data.sequenceStep.externalJobId,
          creatorId: user.id,
        });
      }
      if (data.sequenceStep?.jobBoardId) {
        jobLeadCondition.push({
          job_board_id: data.sequenceStep.jobBoardId,
          creatorId: user.id,
        });
      }

      let [jobLead] = jobLeadCondition.length
        ? await this.jobLeadRepository.find({
            where: jobLeadCondition,
            order: { updatedAt: 'DESC' },
          })
        : [];

      if (!jobLead && (data.sequenceStep.externalJobId || data.sequenceStep.jobBoardId)) {
        const jobFromBH = await this.sharedMailService.getJobFromSequence(sequence);
        jobLead = {
          title: jobFromBH.jobtitle,
          description: jobFromBH.description,
          jobType: jobFromBH.jobtype,
          company_name: jobFromBH.company,
          logoCompany: jobFromBH.logoCompany,
          salary: jobFromBH.salary as number,
          date_added: jobFromBH.posted,
          address_city: jobFromBH.address_city,
          address_country: jobFromBH.address_country,
          address_line_1: jobFromBH.address_line_1,
          address_line_2: jobFromBH.address_line_2,
          employment_type: jobFromBH.employment_type,
        } as JobLead;
      }

      if (data.sequenceStep.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
        console.log(`[SEQUENCE QUEUE] ID ${id} - Process LinkedIn Connection Request`);

        return this.sendLinkedInRequest(job, user, jobLead, isStepDeleted);
      }

      console.log(`[SEQUENCE QUEUE] ID ${id} - Process Mail`);
      // Use credits - TODO costPerUse from plan
      const usageInfo = await this.creditService.useCredits(user.id, FEATURES.email_sequence.costPerUse, `Send email for task ${id}`, featureId);
      if (!usageInfo) {
        throw new Error(`Failed to use credits for user ${user.id} and feature ${featureId} - task ${id}`);
      }

      let sentIds = [];
      const logRecipients = data.recipients;
      const useSendGrid = true; // ['CONTACTLIST', 'HOTLIST'].includes(data.sourceType);
      let subject;

      const crmContactsUnordered = await this.crmBullhornService.findAndSyncBullhornContacts(data.recipients, user.organizationId, user.licenseType);
      const crmContactsMap = crmContactsUnordered.reduce((acc, contact) => {
        acc[contact.id] = contact;
        if (contact.bullhornId) {
          acc[contact.bullhornId] = contact;
        }

        return acc;
      }, {});
      const crmContacts = data.recipients.map(recipient => crmContactsMap[recipient.id]);

      // We'll store the processed email content here
      const processedEmails = {};

      try {
        // First, send the emails
        const res = await this.sharedMailService.sendEmail({
          recipients: data.recipients as any,
          detailEmail: data.sequenceStep,
          currentUser: user,
          signatureId: data.sequenceStep?.userSignature?.id,
          job: {...jobLead},
          fromHotList: false,
          sequenceId: sequence.id,
          threadId: data?.sequenceStep?.threadId,
          timeZone: sequence?.timeZone,
          utc: sequence?.scheduleInformation?.utc,
          useSendGrid,
          sourceType: data.sourceType,
        });
        sentIds = res.map((item) => item.data.messageId || item.data.id);
        subject = res[0]?.data?.subject;

        res.forEach((result, i) => {
          const recipient = crmContacts[i];
          if (recipient && recipient.id) {
            // The actual HTML content sent to this recipient
            const emailContent = result.data?.html || result.data?.body || data.sequenceStep.content;

            processedEmails[recipient.id] = {
              content: emailContent,
              subject: res[i]?.data?.subject,
              signatureId: result.data?.signatureId,
              signatureContent: result.data?.signature,
              signatureImages: result.data?.originalImages,
              attachments: result.data?.attachments || data.sequenceStep.attachments
            };
          }
        });
      } catch (err) {
        if (!useSendGrid) {
          console.error(
            `[NYLAS] @UserID: ${user.id}, @GrantID: ${user.grantId}, @Sequence: ${data.sequence?.id}, @Message: ${err?.message}, @Error:`,
            JSON.stringify(err?.providerError)
          );
        } else if (err?.response?.body?.errors) {
          console.error('[SENDGRID] Error:', err?.response?.body?.errors);
        }

        if (err.message?.startsWith('Could not parse response from the server')) {
          const sentId = err.message.match(/"id":"([^"]+)"/)?.[1];
          sentIds = sentId ? [sentId] : [];
        } else {
          await this.creditService.restoreCredits(
            usageInfo,
            `Send email for task ${id} error with message: ${err.message}`
          ).then(restored => {
            if (restored) {
              console.log(`[SEQUENCE QUEUE] ID ${id} - Credits restored for user ${user.id} and feature ${featureId} after error: ${err.message}`);
            } else {
              console.log(`[SEQUENCE QUEUE] ID ${id} - Failed to restore credits for user ${user.id} and feature ${featureId} after error: ${err.message}`);
            }
          }).catch(restoreError => {
            console.log(`[SEQUENCE QUEUE] ID ${id} - Error restoring credits: ${restoreError.message}`, restoreError.stack);
          });

          throw err;
        }
      }
      const sentCount = sentIds.length;
      const crmLeadId = data.sequenceStep.crmLeadId || jobLead?.crmLeadId;

      await Promise.all([
        ...crmContacts.flatMap(async (recipient, i) =>
          recipient ? [
            this.crmContactSequenceStepRepository.insert({
              sequenceStep: { id: data.sequenceStep.id },
              sequenceId: data.sequence.id,
              contact: { id: recipient.id },
              company: recipient.companyId ? { id: recipient.companyId } : null,
              lead: crmLeadId ? { id: crmLeadId } : null,
              status: 'SENT',
              fromEmail: user?.grantEmail || user?.email,
              toEmail: recipient.email,
              referenceId: sentIds[i],
              subject: processedEmails[recipient.id]?.subject || data.sequenceStep.subject,
              content: processedEmails[recipient.id]?.content || data.sequenceStep.content,
              signatureId: processedEmails[recipient.id]?.signatureId || data.sequenceStep?.userSignature?.id,
              signatureContent: processedEmails[recipient.id]?.signatureContent || data.sequenceStep?.userSignature?.sigContent,
              signatureImages: processedEmails[recipient.id]?.signatureImages || data.sequenceStep?.userSignature?.imgMetadata,
              attachments: processedEmails[recipient.id]?.attachments || data.sequenceStep.attachments,
              sentAt: new Date(),
            }),
            this.crmContactSequenceRepository.upsert({
              contact: { id: recipient.id },
              sequence: { id: data.sequence.id },
              company: recipient.companyId ? { id: recipient.companyId } : null,
              lead: crmLeadId ? { id: crmLeadId } : null,
              status: 'SENT',
              fromEmail: user?.grantEmail || user?.email,
              sentAt: new Date(),
            },
            ['contact', 'sequence']),
          ] : [],
        ).flat(),
        this.sequenceRepository.update(data.sequence?.id, {
          pendingCount: () => `GREATEST(pending_count - ${sentCount}, 0)`,
          sentCount: () => `sent_count + ${sentCount}`,
        }),
        this.sequenceInstanceRepository
          .createQueryBuilder()
          .update()
          .set({
            sentIds: () => `COALESCE(sent_ids, '[]'::jsonb) || '${JSON.stringify(sentIds)}'::jsonb`,
            executedAt: new Date(),
            status: SequenceStepStatus.SENT,
          })
          .where('id = :id', { id: data.sequenceInstance?.id })
          .execute(),
        this.sequenceActivityLogRepository.insert({
          type: SequenceActivityType.SENT,
          sequence: { id: data.sequence?.id },
          ...(isStepDeleted ? {} : { sequenceStep: { id: data.sequenceStep?.id } }),
          content: {
            sequenceInstance: data.sequenceInstance?.id,
            type: data.sequenceStep?.type,
            count: sentCount,
            hostname: os.hostname(),
            recipients: logRecipients.map(({ email }) => email),
            recipientType: data.sourceType,
            clientContacts: logRecipients.reduce((acc, recipient) => {
              if (recipient.id && !uuidValidate(recipient.id)) {
                acc.push({
                  id: recipient.id,
                });
              }

              return acc;
            }, []),
            subject,
          },
          occurredAt: new Date().toISOString(),
        }),
      ]);
      // await sleep(120 * 1000);
    } catch (error) {
      if (error.code === 'LINKEDIN_RETRYABLE_ERROR') {
        throw error;
      }

      console.log(`Job ${BULL_JOB_NAMES.SEQUENCE_STEP_TASK} failed: `, error);
      const failedCount = data.recipients.length;

      const tasks = [
        this.sequenceInstanceRepository.update(data.sequenceInstance?.id, {
          executedAt: new Date(),
          status: SequenceStepStatus.SENT,
        }),
        this.sequenceActivityLogRepository.insert({
          type: SequenceActivityType.FAILED,
          sequence: { id: data.sequence?.id },
          ...(isStepDeleted ? {} : { sequenceStep: { id: data.sequenceStep?.id } }),
          content: {
            sequenceStepTask: data.sequenceStep?.id,
            sequenceInstance: data.sequenceInstance?.id,
            type: data.sequenceStep?.type,
            count: failedCount,
            recipients: data.recipients,
            hostname: os.hostname(),
            reason:
              error.response?.body?.errors?.[0]?.message?.split('.')?.[0] ||
              error.providerError ||
              error.message,
            displayText: error.displayText,
            attempts: attemptsMade,
          },
          occurredAt: new Date().toISOString(),
        }),
      ];
      if (attemptsMade === BULL_JOB_ATTEMPTS - 1 && data.sequenceStep.type === SequenceStepType.EMAIL) {
        tasks.unshift(
          this.sequenceRepository.update(data?.sequence?.id, {
            pendingCount: () => `GREATEST(pending_count - ${failedCount}, 0)`,
            sentCount: () => `sent_count + ${failedCount}`,
          })
        );
      }
      await Promise.all(tasks);
      // await sleep(1000);
      // Some error codes are not recoverable so we don't need to retry
      if (['NO_LINKEDIN_ACCOUNT'].includes(error.code)) {
        return 'UNRECOVERABLE ERROR';
      }

      throw error;
    }

    return {};
  }

  private convertUtcToTimeZone = (utc: string) => {
    if (!utc) return null;
    const offset = parseInt(utc.replace('UTC', ''), 10);
    return `Etc/GMT${-offset}`;
  };

  async sendLinkedInRequest(job: any, user: any, jobLead?: any, isStepDeleted?: boolean) {
    const { data, attemptsMade } = job;
    const { content, subject } = data.sequenceStep;
    const actionLinkedIn = safeParseJSON(content);

    if (!user.unipileAccountId || !actionLinkedIn?.length || user.unipileAccountStatus === UnipileAccountStatus.DISCONNECTED) {
      const unlinkedError: any = new Error('There is no LinkedIn account linked to your Unipile account');
      unlinkedError.code = 'NO_LINKEDIN_ACCOUNT';
      unlinkedError.displayText =
        'Step {{step_number}} was failed because your account has disconnected with your LinkedIn Profile please visit the settings to link it back up.';

      throw unlinkedError;
    }
    const isFinalRetry = attemptsMade === BULL_JOB_ATTEMPTS - 1;

    const invitationData = actionLinkedIn.find(({ type }) => type === 'INVITATION');
    const inMailData = actionLinkedIn.find(({ type }) => type === 'INMAIL');
    const normalMessage = actionLinkedIn.find(({ type }) => type === 'NORMAL_MESSAGE');
    const followData = actionLinkedIn.find(({ type }) => type === 'FOLLOW');
    const reactPostData = actionLinkedIn.find(({ type }) => type === 'REACT_POST');
    const viewData = actionLinkedIn.find(({ type }) => type === 'VIEW_PROFILE');
    const currentLimitRequests = user.unipileRequestStats?.limit_requests || {};
    const actionFlags: any = {};
    const isLimitReached = (date?: string | boolean) => {
      if (date === true) {
        return false;
      }

      if (!date || Number.isNaN(new Date(date).getTime())) {
        return false;
      }

      const currentDate = new Date();
      const limitDate = new Date(date);
      // Add x hours to the limitDate
      limitDate.setHours(limitDate.getHours() + UNIPILE_REQUEST_LIMIT_TIMEOUT);

      return limitDate > currentDate;
    };
    const getFinalSkipActivityStatus = (status: string) => (isFinalRetry ? SequenceActivityType.SKIPPED : status);
    const getReachLimitText = () =>
      isFinalRetry
        ? 'Step {{step_number}} was skipped because after three times retrying, the limit is not reset.'
        : 'Step {{step_number}} tried to send to {{contact_name}} but failed because your requests have reached the limit. We will retry in the next 12 hours';
      const utc = data?.sequence?.scheduleInformation?.utc
      const lastSentItem =
        data?.sequence?.id && data?.sequenceStep?.threadId
          ? await this.sequenceActivityLogRepository.findOne({
              where: {
                sequence: { id: data?.sequence?.id },
                sequenceStep: { id: data?.sequenceStep?.threadId },
                type: SequenceActivityType.SENT,
              },
              order: {
                createdAt: 'DESC',
              },
            })
          : null;

          const timeZone  =  data?.sequence?.timeZone;


          const finalTimeZone =
              utc && utc !== 'UTC-12:00' ? this.convertUtcToTimeZone(utc) : timeZone || this.convertUtcToTimeZone('UTC+0');

          const lastSent = lastSentItem?.createdAt
          ? new Intl.DateTimeFormat('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              timeZoneName: 'short',
              timeZone: finalTimeZone,
            }).format(lastSentItem.createdAt)
          : null;

    const unfollowTasks: any = [];
    const cancelInvitationTasks: any = [];
    const results = await Promise.allSettled(
      data.recipients.map(async (recipient) => {
        const linkedinProfileUrl =
          recipient.linkedinProfileUrl || recipient.customText1 || recipient.linkedInProfileUrl;
        const username = unipileClient.getLinkedInIdentifier(linkedinProfileUrl);
        let {
          provider_id: linkedinProviderId,
          is_relationship,
          // eslint-disable-next-line prefer-const
          invitation,
        } = await unipileClient.getProfile(user.unipileAccountId, username, null, true);
        const result: any = {};
        let isPerformed = false;

        // eslint-disable-next-line no-param-reassign
        recipient.linkedinProviderId = linkedinProviderId;
        // eslint-disable-next-line no-param-reassign
        recipient.linkedinProfileUrl = linkedinProfileUrl;
        const mergeTags = {
          sender: {
            name: user.fullName,
            email: user.email,
          },
          recipient: {
            ...recipient,
            firstName: recipient?.firstName?.trim() ? recipient?.firstName : recipient?.name,
            clientCorporation: {name: recipient?.clientCorporation?.name || recipient?.companyName},
            occupation: recipient?.occupation || recipient?.contactTitle
          },
          job: {
            ...jobLead,
            company_name: data.sequence?.companyName,
            owner: {
              firstName: user.fullName.split(' ')[0] ?? '',
              fullName: user.fullName,
              email: user.email,
            }
          },
          sequence: {
            ...data.sequence,
          },
          sentDateTime: lastSent,
          currentSentDateTime: new Intl.DateTimeFormat('en-GB', {
            dateStyle: 'full',
            timeStyle: 'long',
          }).format(),
        };

        if (invitationData && !is_relationship && invitation?.type !== 'SENT') {
          try {
            if (isLimitReached(currentLimitRequests.INVITATION)) {
              const limitError: any = new Error('Skipped as invitation request limit reached');
              limitError.code = 'SKIPPED_AS_REQUEST_LIMIT_REACHED';
              throw limitError;
            }

            const invitationResult = await unipileClient.sendInvitation({
              message: getMergeTagsContent(invitationData.message, mergeTags),
              provider_id: linkedinProviderId,
              account_id: user.unipileAccountId,
            });
            result.invitation = SequenceActivityType.SENT;
            result.invitationId = invitationResult.invitation_id;
            actionFlags.INVITATION = true;
            if (LINKEDIN_CONNECTION_REQUEST_REMOVE_TIMEOUT) {
              const removeAfterDate = new Date();
              removeAfterDate.setHours(
                removeAfterDate.getHours() + LINKEDIN_CONNECTION_REQUEST_REMOVE_TIMEOUT,
              );
              cancelInvitationTasks.push(
                this.sequenceStepTaskRepository.create({
                  recipients: [{ ...recipient, invitationId: invitationResult.invitation_id }],
                  user,
                  sequence: { id: data.sequence?.id },
                  sequenceStep: { id: data.sequenceStep?.id },
                  sequenceInstance: { id: data.sequenceInstance?.id },
                  scheduledAt: removeAfterDate,
                  type: 'CANCEL_LINKEDIN_INVITATION',
                }),
              );
            }
          } catch (err) {
            result.invitationReason = err.response?.data || err.message;
            result.invitation = SequenceActivityType.FAILED;
            result.displayTextInvitation =
              'Step was sent to {{contact_name}} but failed unexpectedly. Please contact support with the issue.';
            if (err.code === 'SKIPPED_AS_REQUEST_LIMIT_REACHED') {
              result.invitation = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              result.retryable = true;
              result.retryReason = err.message || 'Request limit reached';
              result.displayTextInvitation = getReachLimitText();
            }
            if (['errors/cannot_resend_yet', 'errors/limit_too_high'].includes(result.invitationReason?.type)) {
              actionFlags.INVITATION = new Date().toISOString();
              result.retryable = true;
              result.invitation = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              result.retryReason = `Request limit reached with error ${
                result.invitationReason.type || result.invitationReason.toString()
              }`;
              result.displayTextInvitation = getReachLimitText();
            }
          }

          isPerformed = true;
        }

        if (inMailData) {
          try {
            if (isLimitReached(currentLimitRequests.INMAIL)) {
              const limitError: any = new Error('Skipped as InMail request limit reached');
              limitError.code = 'SKIPPED_AS_REQUEST_LIMIT_REACHED';
              throw limitError;
            }

            const myLinkedIn = await unipileClient.retrieveAccount(user.unipileAccountId);
            const isLinkedInRecruiter = myLinkedIn?.connection_params?.im?.premiumFeatures?.includes('recruiter');
            const isSalesNavigator = myLinkedIn?.connection_params?.im?.premiumFeatures?.includes('sales_navigator');
            if (isLinkedInRecruiter) {
              ({ provider_id: linkedinProviderId, is_relationship } = await unipileClient.getProfile(
                user.unipileAccountId,
                linkedinProviderId,
                'recruiter'
              ));
            }
            let requestApi = 'classic';
            if (isLinkedInRecruiter) {
              requestApi = 'recruiter';
            }
            if (isSalesNavigator) {
              requestApi = 'sales_navigator';
            }
            const inMailResult = await unipileClient.sendInMailMessage(
              user.unipileAccountId,
              [linkedinProviderId],
              {
                text: getMergeTagsContent(inMailData.message, mergeTags),
                // subject,
              },
              requestApi
            );

            result.inmail = SequenceActivityType.SENT;
            result.inmailId = inMailResult.chat_id;
            actionFlags.INMAIL = true;
          } catch (err) {
            result.inmailReason = err.response?.data || err.message;
            result.inmail = SequenceActivityType.FAILED;
            result.displayTextInMail =
              'Step was sent to {{contact_name}} but failed unexpectedly. Please contact support with the issue.';
            if (err.code === 'SKIPPED_AS_REQUEST_LIMIT_REACHED') {
              result.inmail = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              result.displayTextInMail = getReachLimitText();
            }
            if (['errors/insufficient_credits', 'errors/limit_too_high'].includes(result.inmailReason?.type)) {
              result.displayTextInMail = getReachLimitText();
              result.inmail = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              actionFlags.INMAIL = new Date().toISOString();
            }
          }

          isPerformed = true;
        }

        if (normalMessage && is_relationship) {
          try {
            if (isLimitReached(currentLimitRequests.MESSAGE)) {
              const limitError: any = new Error('Skipped as send message request limit reached');
              limitError.code = 'SKIPPED_AS_REQUEST_LIMIT_REACHED';
              throw limitError;
            }

            const normalMessageResult = await unipileClient.sendNormalMessage(
              user.unipileAccountId,
              [linkedinProviderId],
              {
                text: getMergeTagsContent(normalMessage.message, mergeTags),
              }
            );

            result.normalMessage = SequenceActivityType.SENT;
            result.normalMessageId = normalMessageResult.chat_id;
            actionFlags.MESSAGE = true;
          } catch (err) {
            result.normalMessageReason = err.response?.data || err.message;
            result.normalMessage = SequenceActivityType.FAILED;
            result.displayTextNormal =
              'Step was sent to {{contact_name}} but failed unexpectedly. Please contact support with the issue.';
            if (err.code === 'SKIPPED_AS_REQUEST_LIMIT_REACHED') {
              result.normalMessage = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              result.displayTextNormal = getReachLimitText();
            }
            if (['errors/insufficient_credits', 'errors/limit_too_high'].includes(result.normalMessageReason?.type)) {
              result.normalMessage = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              result.displayTextNormal = getReachLimitText();
              actionFlags.MESSAGE = new Date().toISOString();
            }
          }

          isPerformed = true;
        }

        if (followData) {
          try {
            if (isLimitReached(currentLimitRequests.FOLLOW)) {
              const limitError: any = new Error('Skipped as follow request limit reached');
              limitError.code = 'SKIPPED_AS_REQUEST_LIMIT_REACHED';
              throw limitError;
            }

            const followResult = await unipileClient.followUser(linkedinProviderId, user.unipileAccountId);
            result.follow = SequenceActivityType.SENT;
            actionFlags.FOLLOW = true;

            if (followData.period) {
              const unfollowAfter = followData.period * (this.sharedMailService.DELAY_UNIT_MULTIPLE[followData.unit] || this.sharedMailService.DELAY_UNIT_MULTIPLE.DAY)
              const unfollowAt = new Date().getTime() + unfollowAfter;
              unfollowTasks.push(
                this.sequenceStepTaskRepository.create({
                  recipients: [{ ...recipient, followId: linkedinProviderId }],
                  user,
                  sequence: { id: data.sequence?.id },
                  sequenceStep: { id: data.sequenceStep?.id },
                  sequenceInstance: { id: data.sequenceInstance?.id },
                  scheduledAt: new Date(unfollowAt),
                  type: 'UNFOLLOW_LINKEDIN',
                }),
              );
            }
          } catch (err) {
            result.followReason = err.response?.data || err.message;
            result.follow = SequenceActivityType.FAILED;
            result.displayTextFollow =
              'Step was sent to {{contact_name}} but failed unexpectedly. Please contact support with the issue.';
            if (err.code === 'SKIPPED_AS_REQUEST_LIMIT_REACHED') {
              result.follow = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              result.retryable = true;
              result.retryReason = err.message || 'Request limit reached';
              result.displayTextFollow = getReachLimitText();
            }
            if (['errors/cannot_resend_yet', 'errors/limit_too_high'].includes(result.followReason?.type)) {
              actionFlags.FOLLOW = new Date().toISOString();
              result.retryable = true;
              result.follow = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              result.retryReason = `Request limit reached with error ${
                result.followReason.type || result.followReason.toString()
              }`;
              result.displayTextFollow = getReachLimitText();
            }
          }

          isPerformed = true;
        }

        if (reactPostData) {
          try {
            if (isLimitReached(currentLimitRequests.REACT_POST)) {
              const limitError: any = new Error('Skipped as follow request limit reached');
              limitError.code = 'SKIPPED_AS_REQUEST_LIMIT_REACHED';
              throw limitError;
            }

            const getListPostOfUser = await unipileClient.listAllPostOfUser(user.unipileAccountId, linkedinProviderId)
            const socialIds = getListPostOfUser?.items?.map((item: any) => item?.social_id);
            const socialId = reactPostData.type === 'RANDOM' ? socialIds[Math.floor(Math.random() * socialIds.length)] : socialIds[0];
            if (socialId) {
              const reactPostResult = await unipileClient.reactAPost(user.unipileAccountId, socialId, reactPostData.reactionType);
            }
            result.reactPost = SequenceActivityType.SENT;
            actionFlags.REACT_POST = true;
          } catch (err) {
            result.reactPostReason = err.response?.data || err.message;
            result.reactPost = SequenceActivityType.FAILED;
            result.displayTextReactPost =
              'Step was sent to {{contact_name}} but failed unexpectedly. Please contact support with the issue.';
            if (err.code === 'SKIPPED_AS_REQUEST_LIMIT_REACHED') {
              result.reactPost = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              result.retryable = true;
              result.retryReason = err.message || 'Request limit reached';
              result.displayTextReactPost = getReachLimitText();
            }
            if (['errors/cannot_resend_yet', 'errors/limit_too_high'].includes(result.reactPostReason?.type)) {
              actionFlags.REACT_POST = new Date().toISOString();
              result.retryable = true;
              result.reactPost = getFinalSkipActivityStatus(SequenceActivityType.SENT);
              result.retryReason = `Request limit reached with error ${
                result.reactPostReason.type || result.reactPostReason.toString()
              }`;
              result.displayTextReactPost = getReachLimitText();
            }
          }

          isPerformed = true;
        }

        if (viewData) {
          result.viewProfile = SequenceActivityType.SENT;
          result.viewProfileId = linkedinProviderId;
          isPerformed = true;
        }

        if (!isPerformed) {
          const settings = [];
          if (invitationData) {
            settings.push('Invitation');
          }
          if (inMailData) {
            settings.push('InMail');
          }
          if (normalMessage) {
            settings.push('Normal Message');
          }

          const errorMessage = `No LinkedIn action was performed. The recipient ${
            is_relationship
              ? 'is already in your network'
              : `is not yet connected${invitation?.type === 'SENT' ? '(the invitation was sent)' : ''}`
          } but your settings is enable for: ${settings.join(', ')}`;

          const performError: any = new Error(errorMessage);
          if (is_relationship) {
            if (invitationData) {
              performError.displayText =
                'Step {{step_number}} - Invitation was not sent to {{contact_name}} because this contact was already in your network.';
            }
            if (inMailData) {
              performError.displayText =
                'Step {{step_number}} - InMail was not sent to {{contact_name}} because this contact was already in your network so we dont need to use InMail Credit.';
            }
          } else if (normalMessage) {
            performError.displayText =
              'Step {{step_number}} - Normal Message was not sent to {{contact_name}} because this contact is not connected to you.';
          }

          throw performError;
        }

        // if (normalMessage && is_relationship) {
        //   try {
        //     const normalMessageResult = await unipileClient.sendNormalMessage(
        //       user.unipileAccountId,
        //       [linkedinProviderId],
        //       {
        //         text: normalMessage.message,
        //       },
        //     );

        //     result.normalMessage = SequenceActivityType.SENT;
        //     result.normalMessageId = normalMessageResult.chat_id;
        //   } catch (err) {
        //     result.normalMessageReason = err.response?.data || err.message;
        //     result.normalMessage = SequenceActivityType.FAILED;
        //   }
        // }

        return result;
      })
    );
    const activityLogs = [];

    const { sentIds, isRetryable, retryReason } = await results.reduce(async (acc, item: any, index) => {
      const result = await acc;
      const { id, name, email, linkedinProfileUrl, linkedinProviderId } = data.recipients[index];
      if (item.status === 'fulfilled') {
        if (item.value.invitation) {
          activityLogs.push(
            this.sequenceActivityLogRepository.insert({
              //TODO: to check if this includes null as type
              type: item.value.invitation,
              sequence: { id: data.sequence?.id },
              ...(isStepDeleted ? {} : { sequenceStep: { id: data.sequenceStep?.id } }),
              content: {
                sequenceInstance: data.sequenceInstance?.id,
                type: data.sequenceStep?.type,
                count: 1,
                hostname: os.hostname(),
                recipients: [name],
                linkedinProfileUrl,
                reason: item.value.invitationReason,
                sentId: item.value.invitationId,
                linkedInType: 'INVITATION',
                contact: {
                  id,
                  name,
                  email,
                  linkedinProfileUrl,
                  linkedinProviderId,
                },
                displayText: item.value.displayTextInvitation,
              },
              occurredAt: new Date().toISOString(),
            })
          );
          if (
            !result.isRetryable &&
            (item.value.retryable ||
              ['errors/insufficient_credits', 'errors/limit_too_high', 'errors/cannot_resend_yet'].includes(
                item.value.invitationReason?.type
              ))
          ) {
            result.isRetryable = true;
            result.retryReason = item.value.retryReason || 'Request limit reached';
          }
        }
        if (item.value.inmail) {
          activityLogs.push(
            this.sequenceActivityLogRepository.insert({
              type: SequenceActivityType.MESSAGE,
              sequence: { id: data.sequence?.id },
              ...(isStepDeleted ? {} : { sequenceStep: { id: data.sequenceStep?.id } }),
              content: {
                sequenceInstance: data.sequenceInstance?.id,
                type: data.sequenceStep?.type,
                count: 1,
                hostname: os.hostname(),
                recipients: [name],
                linkedinProfileUrl,
                reason: item.value.inmailReason,
                sentId: item.value.inmailId,
                linkedInType: 'INMAIL',
                contact: {
                  id,
                  name,
                  email,
                  linkedinProfileUrl,
                  linkedinProviderId,
                },
                displayText: item.value.displayTextInMail,
              },
              occurredAt: new Date().toISOString(),
            })
          );
        }
        if (item.value.normalMessage) {
          activityLogs.push(
            this.sequenceActivityLogRepository.insert({
              type: SequenceActivityType.MESSAGE,
              sequence: { id: data.sequence?.id },
              ...(isStepDeleted ? {} : { sequenceStep: { id: data.sequenceStep?.id } }),
              content: {
                sequenceInstance: data.sequenceInstance?.id,
                type: data.sequenceStep?.type,
                count: 1,
                hostname: os.hostname(),
                recipients: [name],
                linkedinProfileUrl,
                reason: item.value.normalMessageReason,
                sentId: item.value.normalMessageId,
                linkedInType: 'NORMAL_MESSAGE',
                contact: {
                  id,
                  name,
                  email,
                  linkedinProfileUrl,
                  linkedinProviderId,
                },
                displayText: item.value.displayTextNormal,
              },
              occurredAt: new Date().toISOString(),
            })
          );
        }
        if (item.value.follow) {
          activityLogs.push(
            this.sequenceActivityLogRepository.insert({
              //TODO: to check if this includes null as type
              type: item.value.follow,
              sequence: { id: data.sequence?.id },
              ...(isStepDeleted ? {} : { sequenceStep: { id: data.sequenceStep?.id } }),
              content: {
                sequenceInstance: data.sequenceInstance?.id,
                type: data.sequenceStep?.type,
                count: 1,
                hostname: os.hostname(),
                recipients: [name],
                linkedinProfileUrl,
                reason: item.value.followReason,
                sentId: item.value.followId,
                linkedInType: 'FOLLOW',
                contact: {
                  id,
                  name,
                  email,
                  linkedinProfileUrl,
                  linkedinProviderId,
                },
                displayText: item.value.displayTextFollow,
              },
              occurredAt: new Date().toISOString(),
            })
          );
          if (
            !result.isRetryable &&
            (item.value.retryable ||
              ['errors/insufficient_credits', 'errors/limit_too_high', 'errors/cannot_resend_yet'].includes(
                item.value.followReason?.type
              ))
          ) {
            result.isRetryable = true;
            result.retryReason = item.value.retryReason || 'Request limit reached';
          }
        }
        if (item.value.reactPost) {
          activityLogs.push(
            this.sequenceActivityLogRepository.insert({
              //TODO: to check if this includes null as type
              type: item.value.reactPost,
              sequence: { id: data.sequence?.id },
              ...(isStepDeleted ? {} : { sequenceStep: { id: data.sequenceStep?.id } }),
              content: {
                sequenceInstance: data.sequenceInstance?.id,
                type: data.sequenceStep?.type,
                count: 1,
                hostname: os.hostname(),
                recipients: [name],
                linkedinProfileUrl,
                reason: item.value.reactPostReason,
                sentId: item.value.reactPostId,
                linkedInType: 'REACT_POST',
                contact: {
                  id,
                  name,
                  email,
                  linkedinProfileUrl,
                  linkedinProviderId,
                },
                displayText: item.value.displayTextReactPost,
              },
              occurredAt: new Date().toISOString(),
            })
          );
          if (
            !result.isRetryable &&
            (item.value.retryable ||
              ['errors/insufficient_credits', 'errors/limit_too_high', 'errors/cannot_resend_yet'].includes(
                item.value.reactPostReason?.type
              ))
          ) {
            result.isRetryable = true;
            result.retryReason = item.value.retryReason || 'Request limit reached';
          }
        }
        if (item.value.viewProfile) {
          activityLogs.push(
            this.sequenceActivityLogRepository.insert({
              //TODO: to check if this includes null as type
              type: item.value.viewProfile,
              sequence: { id: data.sequence?.id },
              ...(isStepDeleted ? {} : { sequenceStep: { id: data.sequenceStep?.id } }),
              content: {
                sequenceInstance: data.sequenceInstance?.id,
                type: data.sequenceStep?.type,
                count: 1,
                hostname: os.hostname(),
                recipients: [name],
                linkedinProfileUrl,
                sentId: item.value.viewProfileId,
                linkedInType: 'VIEW_PROFILE',
                contact: {
                  id,
                  name,
                  email,
                  linkedinProfileUrl,
                  linkedinProviderId,
                },
                displayText: item.value.displayTextViewProfile,
              },
              occurredAt: new Date().toISOString(),
            })
          );
        }
        if (item.value.invitation === SequenceActivityType.SENT) {
          result.sentIds.push({
            name,
            email,
            linkedinProfileUrl,
            type: 'INVITATION',
            linkedinProviderId,
          });
        }
        if (item.value.inmail === SequenceActivityType.SENT) {
          result.sentIds.push({
            name,
            email,
            linkedinProfileUrl,
            type: 'INMAIL',
            linkedinProviderId,
          });
        }
        if (item.value.normalMessage === SequenceActivityType.SENT) {
          result.sentIds.push({
            name,
            email,
            linkedinProfileUrl,
            type: 'MESSAGE',
            linkedinProviderId,
          });
        }
        if (item.value.follow === SequenceActivityType.SENT) {
          result.sentIds.push({
            name,
            email,
            linkedinProfileUrl,
            type: 'FOLLOW',
            linkedinProviderId,
          });
        }
        if (item.value.reactPost === SequenceActivityType.SENT) {
          result.sentIds.push({
            name,
            email,
            linkedinProfileUrl,
            type: 'REACT_POST',
            linkedinProviderId,
          });
        }
        if (item.value.view === SequenceActivityType.SENT) {
          result.sentIds.push({
            name,
            email,
            linkedinProfileUrl,
            type: 'VIEW_PROFILE',
            linkedinProviderId,
          });
        }
      }
      if (item.status === 'rejected') {
        result.isRetryable = result.isRetryable || item.reason.code === 'SKIPPED_AS_REQUEST_LIMIT_REACHED';
        activityLogs.push(
          this.sequenceActivityLogRepository.insert({
            type: SequenceActivityType.FAILED,
            sequence: { id: data.sequence?.id },
            ...(isStepDeleted ? {} : { sequenceStep: { id: data.sequenceStep?.id } }),
            content: {
              sequenceInstance: data.sequenceInstance?.id,
              type: data.sequenceStep?.type,
              count: 0,
              hostname: os.hostname(),
              recipients: [{ email, linkedinProfileUrl, name }],
              rawError: item.reason?.toString(),
              reason: item.reason.response?.data || item.reason.message || item.reason,
              displayText:
                item.reason?.displayText ||
                (item.reason.code === 'SKIPPED_AS_REQUEST_LIMIT_REACHED'
                  ? getReachLimitText()
                  : 'Step was sent to {{contact_name}} but failed unexpectedly. Please contact support with the issue.'),
            },
            occurredAt: new Date().toISOString(),
          })
        );

        if (!result.isRetryable && item.reason.code === 'SKIPPED_AS_REQUEST_LIMIT_REACHED') {
          result.isRetryable = true;
          result.retryReason = item.reason.message || 'Request limit reached';
        }
      }

      return result;
    }, Promise.resolve({ sentIds: [], isRetryable: false, retryReason: '' }));
    const hasInvitation = sentIds.some((item) => item.type === 'INVITATION' && item.linkedinProviderId);
    const profileSentIds = sentIds.map((item) => `${item.type}#${item.linkedinProviderId}#${item.email}`);

    const tasks = [
      this.sequenceInstanceRepository
        .createQueryBuilder()
        .update()
        .set({
          sentIds: () => `COALESCE(sent_ids, '[]'::jsonb) || '${JSON.stringify(profileSentIds)}'::jsonb`,
          // For other steps check if instance is completed (executedAt is filled)
          ...(isRetryable && !isFinalRetry ? {} : { executedAt: new Date(), status: SequenceStepStatus.SENT }),
          ...(hasInvitation ? { flag: SequenceStepFlag.INVITATION_SENT } : {}),
        })
        .where('id = :id', { id: data.sequenceInstance?.id })
        .execute(),
      ...activityLogs,
      ...(unfollowTasks.length ? [this.sequenceStepTaskRepository.insert(unfollowTasks)] : []),
      ...(cancelInvitationTasks.length ? [this.sequenceStepTaskRepository.insert(cancelInvitationTasks)] : []),
    ];
    // It's perform at least one action
    if (Object.values(actionFlags).some(Boolean)) {
      const limitRequests = {
        ...user.unipileRequestStats?.limit_requests,
        ...actionFlags,
      };

      tasks.push(
        this.userRepository.update(user.id, {
          unipileRequestStats: () => `
            jsonb_set(
              COALESCE(unipile_request_stats, '{}')::jsonb,
              '{limit_requests}',
              '${JSON.stringify(limitRequests)}'::jsonb
            )
          `,
        })
      );
    }

    await Promise.all(tasks);

    if (isRetryable && !isFinalRetry) {
      const linkedInError: any = Error(`Step failed with retryable error: ${retryReason}`);
      linkedInError.code = 'LINKEDIN_RETRYABLE_ERROR';
      throw linkedInError;
    }
  }

  async unfollowLinkedIn(job: any) {
    const { data } = job;
    try {
      const user = await this.userRepository.findOne({ where: { id: data.user?.id } });
      if (!user?.unipileAccountId) {
        throw new Error(`There is no Unipile account linked to this user ${user?.username}`);
      }

      const { recipients } = data;
      await Promise.allSettled(
        recipients.map(async (recipient) => {
          const { followId } = recipient;
          try {
            await unipileClient.unfollowUser(followId, user.unipileAccountId);

            return { status: 'fulfilled' };
          } catch (err) {
            // Unipile has logging for this error so we don't need to log it here
            return { status: 'rejected', reason: err };
          }
        }),
      );
    } catch (error) {
      console.error(`Job ${BULL_JOB_NAMES.SEQUENCE_STEP_TASK} to unfollow LinkedIn failed: `, error, data?.recipients);
    }
  }

  async cancelLinkedInInvitation(job: any) {
    const { data } = job;
    try {
      const user = await this.userRepository.findOne({ where: { id: data.user?.id } });
      if (!user?.unipileAccountId) {
        throw new Error(`There is no Unipile account linked to this user ${user?.username}`);
      }

      const { recipients } = data;
      await Promise.allSettled(
        recipients.map(async (recipient) => {
          const { invitationId } = recipient;
          try {
            await unipileClient.cancelInvitation({
              account_id: user.unipileAccountId,
              invitation_id: invitationId,
            });

            return { status: 'fulfilled' };
          } catch (err) {
            // Unipile has logging for this error so we don't need to log it here
            return { status: 'rejected', reason: err };
          }
        }),
      );
    } catch (error) {
      console.error(`Job ${BULL_JOB_NAMES.SEQUENCE_STEP_TASK} to cancel invitation failed: `, error, data?.recipients);
    }
  }
}

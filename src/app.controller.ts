import { <PERSON>, Get, HttpC<PERSON>, HttpStatus, Query } from "@nestjs/common";
import { SkipThrottle } from "@nestjs/throttler";
import { AppService } from "./app.service";
import { readFileSync } from "fs";
import { join } from "path";

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get("/health")
  @SkipThrottle()
  getHello(@Query('test') test: string) {
    return this.appService.getHello(test);
  }

  @Get("/version")
  @SkipThrottle()
  getVersion() {
    try {
      const revision = readFileSync(join(__dirname, './revision.txt'), 'utf8');
      return {
        revision: revision.trim(),
      };
    } catch (error) {
      return {
        revision: null,
      };
    }
  }
}

/* eslint-disable camelcase */
import axios from 'axios';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import {
  BULL_JOB_NAMES,
  BULL_QUEUES,
  EMAIL_DOMAIN_WHITELIST,
  EMITTER_EVENTS,
} from 'src/configs/configs.constants';
import { SequenceActivityType } from 'src/modules/mail/entities/sequence-activity-log.entity';
import { SharedMailService } from 'src/modules/shared/shared-mail.service';
import { SequenceEntity, SequenceEvent } from 'src/modules/mail/entities/sequence.entity';
import { MessageFields } from 'nylas';
import { DataSource, In, MoreThan, MoreThanOrEqual, Not } from 'typeorm';
import { SequenceActivityLogRepository } from 'src/modules/mail/repositories/sequence-activity-log.repository';
import { SequenceRepository } from 'src/modules/mail/repositories/sequence.repostiory';
import { SequenceInstanceRepository } from 'src/modules/mail/repositories/sequence-instance.repository';

@Processor(BULL_QUEUES.INTERNAL_QUEUE_EVENT, {
  concurrency: 10,
  stalledInterval: 3 * 60 * 1000, // 3 minutes
  maxStalledCount: 2,
  removeOnComplete: {
    age: 12 * 60 * 60,
    count: 1000,
  }
})
export class InternalQueueEventProcessor extends WorkerHost {
  constructor(
    private readonly dataSource: DataSource,
    private readonly sequenceActivityLogRepository: SequenceActivityLogRepository,
    private readonly sharedMailService: SharedMailService,
  ) {
    super();
  }

  async process(job: Job<any>) {
    const { id, data, attemptsMade } = job;
    console.log(`[INTERNAL QUEUE] ID ${id} is running...`);
    if (!['SEND_EMAIL_SEQUENCE_STOP'].includes(data?.type)) {
      return 'INVALID_EVENT_TYPE';
    }
    try {
      if (data.type === 'SEND_EMAIL_SEQUENCE_STOP') {
        await this.sharedMailService.sendEmailSequenceStop(data.data.user, data.data.sequence, data.data.isCompleted);

        return 'SENT';
      }

      return 'N/A'
    } catch (error) {
      console.log("🚀 Error while handling internal queue:", error)
      throw error;
    } finally {
      console.log(`[INTERNAL QUEUE] ID ${id} is complete`);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  async onCompleted(job: Job<any>) {
    console.log(
      `Job ${BULL_JOB_NAMES.INTERNAL_QUEUE_EVENT} - ${job.id} completed at ${new Date(job.finishedOn).toISOString()}`
    );
  }

  // eslint-disable-next-line class-methods-use-this
  async onFailed(job: Job<any>, error: any) {
    console.log(
      `Job ${BULL_JOB_NAMES.INTERNAL_QUEUE_EVENT} - ${job.id} failed at ${job.attemptsMade} attempt(s) with error: `,
      error
    );
  }
}

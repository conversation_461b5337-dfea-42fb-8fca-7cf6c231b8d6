/* eslint-disable camelcase */
import axios from 'axios';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import {
  BULL_JOB_NAMES,
  BULL_QUEUES,
  EMAIL_DOMAIN_WHITELIST,
  EMITTER_EVENTS,
  JOBS_INDEX,
} from 'src/configs/configs.constants';
import { SequenceActivityType } from 'src/modules/mail/entities/sequence-activity-log.entity';
import { SharedMailService } from 'src/modules/shared/shared-mail.service';
import { SequenceEntity, SequenceEvent } from 'src/modules/mail/entities/sequence.entity';
import { MessageFields } from 'nylas';
import { DataSource, In, MoreThan, MoreThanOrEqual, Not } from 'typeorm';
import { SequenceActivityLogRepository } from 'src/modules/mail/repositories/sequence-activity-log.repository';
import { SequenceRepository } from 'src/modules/mail/repositories/sequence.repostiory';
import { SequenceInstanceRepository } from 'src/modules/mail/repositories/sequence-instance.repository';
import { SequenceStepType } from 'src/modules/mail/entities/sequence-step.entity';
import { SequenceStepStatus } from 'src/modules/mail/entities/sequence-instance.entity';
import { STOP_BY_TYPE } from 'src/modules/mail/dto/upsert-email.dto';
import { SequenceStatus } from 'src/modules/mail/entities/sequence.entity';
import { CACHE_PREFIX, CACHE_TTL, getLogoCompany } from 'src/modules/jobs/constants/job.const';
import { isWithinXMonths, standardizeJobDescription, standardizeJobType, subtractTime } from 'src/common/utils/helpers.util';
import { JobBoardsRepository } from 'src/modules/jobs/repository/job-boards.repository';
import { DuplicateJobRepository } from 'src/modules/jobs/repository/duplicate-job.repository';
import { OpensearchService } from 'src/modules/opensearch/service/opensearch.service';
import { SSEService } from 'src/modules/sse/sse.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CacheService } from 'src/modules/cache/cache.service';
import { JobSearchService } from 'src/modules/jobs/service/job-search.service';
import { RECENTLY_ADDED_JOB_EVENT_NAME } from 'src/modules/sse/sse.constant';
import { GB_CITY_TO_COUNTY } from 'src/modules/jobs/constants/cities-counties.constant';
import { COUNTRY_CODE } from 'src/modules/jobs/constants/country-code.constant';
import { WebhookService } from 'src/modules/webhook/services/webhook.service';
import { WebHookNameEnum } from 'src/modules/webhook/enums/webhook.enum';

@Processor(BULL_QUEUES.CRAWL_JOB_EVENT, {
  concurrency: 10,
  stalledInterval: 3 * 60 * 1000, // 3 minutes
  maxStalledCount: 2,
  removeOnComplete: {
    age: 12 * 60 * 60,
    count: 1000,
  }
})
export class CrawlJobEventProcessor extends WorkerHost {
  constructor(
    private readonly dataSource: DataSource,
    private readonly jobRepository: JobBoardsRepository,
    private readonly duplicateJobRepository: DuplicateJobRepository,
    private readonly opensearchService: OpensearchService,
    private readonly sseService: SSEService,
    private readonly eventEmitter: EventEmitter2,
    private readonly cacheService: CacheService,
    private readonly jobSearchService: JobSearchService,
    private readonly webhookService: WebhookService,
  ) {
    super();
  }

  async process(job: Job<any>) {
    const { id, data, attemptsMade } = job;
    console.log(`[CRAWLJOB QUEUE] ID ${id} is running...`);
    try {
      const logoCompany = data.logoCompany ?? getLogoCompany(data.company, data.source);
      data.logoCompany = logoCompany;

      // Process posted date from data
      let checkPostedAt = new Date(data.posted);
      if (Number.isNaN(checkPostedAt.getTime())) {
        // Use current date minus 2 months instead of subtractTime
        const twoMonthsAgo = new Date();
        twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);
        checkPostedAt = twoMonthsAgo;
      }

      const rawDescription = data.description;
      // Standardize job description - keep only text with line breaks and bold/strong tags
      data.description = standardizeJobDescription(rawDescription);

      await job.log(`${new Date().toISOString()}: GET JOB LOCATION`);
      data.joblocationinput = await this.getJobLocationInput(data);
      if (!data.joblocationcity && data.country) {
        data.joblocationcity = data.country;
      }
      // As the current job type is saved as a string - join the result with comma
      data.jobtype = standardizeJobType(data.jobtype).join(', ');

      // Remove embedding from data
      delete data.embedding;

      //posted order
      const query = {
        index: JOBS_INDEX,
        body: {
          sort: [
            {
              posted: {
                order: 'desc',
              },
            },
          ],
          query: {
            bool: {
              must: [
                {
                  match: {
                    description: {
                      query: `${standardizeJobDescription(rawDescription, true)}`,
                      minimum_should_match: '90%',
                    },
                  },
                },
                {
                  match: {
                    jobtitle: {
                      query: `${data.jobtitle}`,
                      minimum_should_match: '90%',
                    },
                  },
                },
                {
                  term: {
                    'company.keyword': data.company,
                  },
                },
                // {
                //   match: {
                //     joblocationcity: data.joblocationcity,
                //   },
                // },
                {
                  range: {
                    posted: {
                      gte: checkPostedAt.toISOString(),
                    },
                  },
                },
                {
                  bool: {
                    must_not: [
                      {
                        terms: {
                          job_id: [data.job_id],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        },
      };
      await job.log(`${new Date().toISOString()}: CHECK DUPLICATED JOB`);
      const response = await this.opensearchService.getClient().search(query);
      const duplicatedJobs = response.body.hits.hits.map((e: { _source: any }) => ({
        ...e._source,
        logoCompany: e._source.logo_company,
        sendToBullHornByUserIds: e._source.send_to_bullhorn_by_user_ids,
      }));

      if (duplicatedJobs.length) {
        await job.log(`${new Date().toISOString()}: HAS DUPLICATED JOB`);
        const overridePropertiesJob = {
          joblocationcity: duplicatedJobs[0].joblocationcity ?? data.joblocationcity,
          salary: duplicatedJobs[0].salary ?? data.salary,
          min_salary: duplicatedJobs[0].min_salary ?? data.min_salary,
          max_salary: duplicatedJobs[0].max_salary ?? data.max_salary,
          description: duplicatedJobs[0].description ?? data.description,
          jobtype: duplicatedJobs[0].jobtype ?? data.jobtype,
          logoCompany: duplicatedJobs[0].logoCompany?.length ? duplicatedJobs[0].logoCompany : logoCompany,
          job_id: duplicatedJobs[0].job_id,
        };

        // update origin job
        const { job_id, ...updatedJob } = overridePropertiesJob;
        // const updatedRes = await this.jobRepository.update({ job_id }, updatedJob);
        // if (updatedRes.affected) {
        await this.opensearchService.updateDocument(JOBS_INDEX, job_id, updatedJob);
        // }

        //remove duplicated jobs
        const deletedDuplicatedJobIds = duplicatedJobs.map((item) => item.job_id).slice(1);
        if (deletedDuplicatedJobIds.length) {
          console.log(`[DUPLICATED] ${data?.job_id} is duplicated with Job IDs: `, deletedDuplicatedJobIds, job_id);
          const deleteRes = await this.jobRepository.delete({ job_id: In(deletedDuplicatedJobIds) });
          if (deleteRes.affected) {
            await this.opensearchService.deleteDocuments(JOBS_INDEX, deletedDuplicatedJobIds);
          }
        }

        await job.log(`${new Date().toISOString()}: UPDATE/REMOVE DUPLICATED JOB`);
        //save duplicated jobs
        return this.duplicateJobRepository.save({
          ...data,
          logoCompany,
          sameWithJobIds: duplicatedJobs[0].job_id,
        });
      }

      if (isWithinXMonths(data.posted, 2)) {
        await job.log(`${new Date().toISOString()}: VALID JOB`);
        // within 2 months
        // const insertRes = await this.jobRepository.save({ ...data, logoCompany });
        const insertRes = { ...data, logoCompany };
        if (insertRes) {
          const existingJobOS = await this.opensearchService.getById(JOBS_INDEX, data.job_id);
          const searchKeywords = existingJobOS?.search_keywords?.split(',')?.map((keyword) => keyword.trim()) || [];
          if (!searchKeywords.includes(data.search_keywords)) {
            searchKeywords.push(data.search_keywords);
          }

          await this.opensearchService.insertOrUpdate(JOBS_INDEX, data.job_id, {
            ...data,
            search_keywords: searchKeywords.join(', '),
            created_at: new Date(),
            insert_from_app: true,
          }, { logging: false });

          // If job posted within the last hour, trigger webhook to outreach recruiters
          const oneHourAgo = new Date();
          oneHourAgo.setHours(oneHourAgo.getHours() - 1);
          if (new Date(data.posted) >= oneHourAgo) {
            this.webhookService.triggerWebhookByName(WebHookNameEnum['jobs.created'], { jobId: data.job_id });
          }

          await job.log(`${new Date().toISOString()}: STORED JOB`);

          this.sseService.emitEvent(RECENTLY_ADDED_JOB_EVENT_NAME, insertRes);
          this.eventEmitter.emit(EMITTER_EVENTS.REPORT_AGENCY_COMPANY_ADDED, {
            company: insertRes.company,
          });
          await job.log(`${new Date().toISOString()}: EMITTED JOB EVENT`);
        }
        this.jobSearchService.removeSearchJobCachedByCompany(data.company).catch(() => { });

        return insertRes;
      }
    } catch (error) {
      console.log("🚀 Error while handling add job:", error)
      throw error;
    } finally {
      console.log(`[SEQUENCE QUEUE] ID ${id} is complete`);
    }
  }


  async getJobLocationInput(job) {
    if (!job.joblocationcity) {
      job.joblocationcity = '';
    }

    if (['remote', 'anywhere'].includes(job.joblocationcity?.toLowerCase())) {
      return `${job.joblocationcity}, ${job.joblocationinput}`;
    }

    const cacheKeyPrefix = CACHE_PREFIX.JOB_LOCATION;
    const cachedKey = `${cacheKeyPrefix}${job.joblocationcity.toLowerCase().replace(/\s+/g, ' ').trim()}`;
    const cachedLocation: any = await this.cacheService.get(cachedKey);

    if (cachedLocation && typeof cachedLocation === 'object') {
      return cachedLocation?.address || cachedLocation?.formattedAddress;
    }

    const [locationMapping] = await this.opensearchService.searchLocationMappings(job.joblocationcity);

    if (locationMapping) {
      await this.cacheService.set(cachedKey, locationMapping._source, CACHE_TTL.JOB_LOCATION);

      return locationMapping._source.address;
    }

    const formattedLocation = await this.getFormattedLocationByProvider(job);
    if (formattedLocation?.address) {
      const county =
        formattedLocation.countryCode === 'GB' && formattedLocation.city
          ? GB_CITY_TO_COUNTY[formattedLocation.city]
          : '';
      const extraCounty = county ? `, ${county}` : '';
      let address = `${formattedLocation.address}${extraCounty}`;
      if (
        // Case Australia only
        job.joblocationcity?.toLowerCase() === formattedLocation.country?.toLowerCase()
      ) {
        address = job.joblocationcity;
      }
      formattedLocation.address = address;
      await this.cacheService.set(cachedKey, formattedLocation, CACHE_TTL.JOB_LOCATION);
      this.opensearchService.addLocationMapping(job.joblocationcity, formattedLocation);

      return address;
    }

    //add this job as unknown location jobs, then after later
    await this.opensearchService.createUnknownLocationJob({ job_id: job.job_id, location: job.joblocationcity });

    // this could lead to less jobs
    return `${job.joblocationcity}`;
  }

  async getFormattedLocationByProvider(job) {
    try {
      //call http to lambda function
      const { data } = await axios.get(
        `https://xxieanq762xwskg7wfzs5n3b6u0fwxwa.lambda-url.eu-west-2.on.aws/?q=${job.joblocationcity}`
      );

      if (!data.formattedAddress) {
        throw new Error('Retry');
      }
      const standardCountryName = COUNTRY_CODE[data.countryCode]?.name;
      const isCorrectCountryName = standardCountryName === data.country;
      if (!isCorrectCountryName) {
        data.formattedAddress = (data.formattedAddress as string).replace(data.country, standardCountryName);
        data.country = standardCountryName;
      }

      return {
        address: data.formattedAddress,
        city: data.city,
        state: data.state,
        country: data.country,
        countryCode: data.countryCode,
      };
    } catch (error) {
      if (error.message !== 'Retry') {
        console.error('Error at getFormattedLocationByProvider', {
          error: error.response?.data,
          job: {
            ...job,
            description: undefined,
          },
        });
      }
      const location = job.joblocationcity;

      //remove the unknown part from left to right
      // ABC, DEF => search (DEF)
      const stringLocationPart = location.split(', ');
      stringLocationPart.shift();
      if (stringLocationPart.length === 0) {
        await this.opensearchService.createUnknownLocationJob({ job_id: job.job_id, location: job.joblocationcity });
        return null;
      }

      const updatedLocation = stringLocationPart.join(', ');
      return this.getFormattedLocationByProvider({ ...job, joblocationcity: updatedLocation });
    }
  }

  // eslint-disable-next-line class-methods-use-this
  async onCompleted(job: Job<any>) {
    console.log(
      `Job ${BULL_JOB_NAMES.CRAWL_JOB_EVENT} - ${job.id} completed at ${new Date(job.finishedOn).toISOString()}`
    );
  }

  // eslint-disable-next-line class-methods-use-this
  async onFailed(job: Job<any>, error: any) {
    console.log(
      `Job ${BULL_JOB_NAMES.CRAWL_JOB_EVENT} - ${job.id} failed at ${job.attemptsMade} attempt(s) with error: `,
      error
    );
  }
}

/* eslint-disable camelcase */
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import {
  BULL_JOB_NAMES,
  BULL_QUEUES,
  EMAIL_DOMAIN_WHITELIST,
} from 'src/configs/configs.constants';
import { SequenceActivityType } from 'src/modules/mail/entities/sequence-activity-log.entity';
import { SharedMailService } from 'src/modules/shared/shared-mail.service';
import { SequenceEntity, SequenceEvent } from 'src/modules/mail/entities/sequence.entity';
import { MessageFields } from 'nylas';
import { DataSource, In, MoreThan, MoreThanOrEqual, Not } from 'typeorm';
import { SequenceActivityLogRepository } from 'src/modules/mail/repositories/sequence-activity-log.repository';
import { SequenceRepository } from 'src/modules/mail/repositories/sequence.repostiory';
import { SequenceInstanceRepository } from 'src/modules/mail/repositories/sequence-instance.repository';
import { CrmContactSequenceStepRepository } from 'src/modules/crm/repositories/crm-contact-sequence-step.repository';
import { CrmContactSequenceStepReplyRepository } from 'src/modules/crm/repositories/crm-contact-sequence-step-reply.repository';
import { CrmReplyType } from 'src/modules/crm/entities/crm-contact-sequence-step-reply.entity';
import { SequenceStepType } from 'src/modules/mail/entities/sequence-step.entity';
import { SequenceStepStatus } from 'src/modules/mail/entities/sequence-instance.entity';
import { STOP_BY_TYPE } from 'src/modules/mail/dto/upsert-email.dto';
import { SequenceStatus } from 'src/modules/mail/entities/sequence.entity';

@Processor(BULL_QUEUES.WEBHOOK_NYLAS_EVENT, {
  concurrency: 5,
  stalledInterval: 3 * 60 * 1000, // 3 minutes
  maxStalledCount: 2,
  removeOnComplete: {
    age: 12 * 60 * 60,
    count: 1000,
  }
})
export class WebhookNylasEventProcessor extends WorkerHost {
  constructor(
    private readonly dataSource: DataSource,
    private readonly sharedMailService: SharedMailService,
    private readonly sequenceRepository: SequenceRepository,
    private readonly sequenceInstanceRepository: SequenceInstanceRepository,
    private readonly sequenceActivityLogRepository: SequenceActivityLogRepository,
    private readonly crmContactSequenceStepRepository: CrmContactSequenceStepRepository,
    private readonly crmContactSequenceStepReplyRepository: CrmContactSequenceStepReplyRepository,
  ) {
    super();
  }

  async process(job: Job<any>) {
    const { id, data, attemptsMade } = job;
    console.log(`[NYLAS QUEUE] ID ${id} is running...`);
    console.time(`[NYLAS] ${id}`);
    try {
      const { type } = data;
      if (type === SequenceEvent.MESSAGE_OPENED) {
        return this.handleOpenedEvent(job);
      }

      if (type === SequenceEvent.MESSAGE_LINK_CLICKED) {
        return this.handleClickedEvent(job);
      }

      // Handle other events - replied, bounced,...

      const {
        grant_id: grantId,
        from: senders,
        snippet,
        message_id,
        thread_id,
        body,
      } = data?.object || '';

      const id = data?.object?.root_message_id || data?.object?.id || null;

      if (!grantId || !id) {
        return 'NO_GRANT_OR_ID';
      }

      await job.log(`${new Date().toISOString()}: START GET NYLAS EMAIL`);
      const emailWithHeader = await this.sharedMailService.nylas.messages.find({
        identifier: grantId,
        messageId: id,
        queryParams: {
          fields: MessageFields.INCLUDE_HEADERS,
        },
      });
      await job.log(`${new Date().toISOString()}: END GET NYLAS EMAIL`);

      const rawMessageId = emailWithHeader?.data?.headers?.find((item) => item?.name === 'In-Reply-To')?.value;

      if (!rawMessageId) {
        return 'NO_REPLY_ID';
      }

      const regex = /<([^@>]+)@/;
      const referenceId = rawMessageId.match(regex)?.[1] || '';

      // Extract the message ID for the current email
      const messageIdHeader = (emailWithHeader?.data?.headers || []).find(
        (item) => item?.name?.toLowerCase() === 'message-id'
      );

      const replyReferenceId = messageIdHeader?.value?.match(regex)?.[1] || messageIdHeader?.value || '';

      await job.log(`${new Date().toISOString()}: START FIND INSTANCE`);
      const mail = await this.sharedMailService.findMailByRawId(rawMessageId);
      await job.log(`${new Date().toISOString()}: END FIND INSTANCE`);

      if (!mail) {
        return 'NO_SEQUENCE_INSTANCE';
      }

      const isAutoReply = !!emailWithHeader.data.headers.find(
        (header) =>
          header.name.toLowerCase() === 'x-autoreply' ||
          (header.name.toLowerCase() === 'auto-submitted' && header.value !== 'no')
      );

      const listRecipients = mail.recipients.map((item) => item.email?.toLowerCase());

      const fromEmail = emailWithHeader.data.from[0].email?.toLowerCase();
      if (!listRecipients.includes(fromEmail) && isAutoReply) {
        await job.log(`${new Date().toISOString()}: BOUNCED`);

        // Find the CRM contact sequence step that corresponds to this email
        const crmContactSequenceStep = await this.crmContactSequenceStepRepository.findOne({
          where: {
            referenceId,
          },
        });

        // If we found a corresponding CRM contact sequence step, create a bounced email record
        if (crmContactSequenceStep) {
          this.crmContactSequenceStepReplyRepository.save({
            sequenceStep: { id: crmContactSequenceStep.id },
            content: body || snippet,
            subject: emailWithHeader.data.subject,
            fromEmail: fromEmail,
            toEmail: crmContactSequenceStep.fromEmail || mail?.user?.grantEmail || mail?.user?.email,
            repliedAt: new Date(),
            type: CrmReplyType.BOUNCED,
            status: 'BOUNCED',
            referenceId,
            replyReferenceId,
          }).catch(error => {
            console.error('Error saving bounced email:', error);
          });
        }
        // As the bounce email is an auto-reply email by server postmaster
        // So, we must get the bounced email from the original email (the first email in the thread)
        let startEmailId;
        try {
          const emailThread = await this.sharedMailService.nylas.threads.find({
            identifier: grantId,
            threadId: thread_id,
          });

          ([startEmailId] = emailThread.data.messageIds.filter((threadMessageId) => threadMessageId !== id));
          if (!startEmailId) {
            startEmailId = id;
          }
        } catch(err) {
          startEmailId = id;
        }

        let startEmailWithHeader = emailWithHeader;
        if (startEmailId !== id) {
          startEmailWithHeader = await this.sharedMailService.nylas.messages.find({
            identifier: grantId,
            messageId: startEmailId,
            queryParams: {
              fields: MessageFields.INCLUDE_HEADERS,
            },
          });
        }

        const startMessageRawId = startEmailWithHeader?.data?.headers?.find(
          (item) => item?.name?.toLowerCase() === 'message-id'
        )?.value;

        // Must be replied to start thread email
        if (rawMessageId === startMessageRawId) {
          const [bounceEmail] = startEmailWithHeader.data.to.map(({ email: toEmail }) => toEmail.toLowerCase());

          const skipRecipients = await this.sequenceInstanceRepository
            .createQueryBuilder()
            .select('skip_recipients')
            .where('sequence_id = :seqId AND version = :version', { seqId: mail.sequence.id, version: mail?.version })
            .getRawMany();
          const bounceEmails = skipRecipients
            .flatMap(({ skip_recipients }) =>
              skip_recipients?.map((item) =>
                item.type === SequenceActivityType.BOUNCE ? item.email.toLowerCase() : null
              )
            )
            .filter(Boolean);
          // If it already bounced, we don't need to process it again
          if (bounceEmails.includes(bounceEmail)) {
            return true;
          }

          await Promise.all([
            this.sequenceActivityLogRepository.insert({
              type: SequenceActivityType.BOUNCE,
              sequence: { id: mail?.sequence.id },
              sequenceStep: { id: mail.sequenceStep?.id },
              content: {
                sequenceInstance: mail.id,
                recipient: bounceEmail,
                email: { content: body },
              },
              occurredAt: new Date().toISOString(),
            }),

            this.sequenceRepository
              .createQueryBuilder()
              .update(SequenceEntity)
              .set({ bounced: () => `"bounced" + 1` })
              .where('id = :id', { id: mail?.sequence.id })
              .execute(),

            this.sequenceInstanceRepository.update(
              {
                user: { id: mail.user.id },
                sequence: { id: mail.sequence.id },
                version: mail.version,
                id: Not(mail.id),
                scheduledAt: MoreThan(mail.scheduledAt),
              },
              {
                // Append
                skipRecipients: () =>
                  `COALESCE(skip_recipients, '[]'::jsonb) || '${JSON.stringify([
                    { email: bounceEmail, type: SequenceActivityType.BOUNCE },
                  ])}'::jsonb`,
                // Remove
                recipients: () => `(
                  SELECT jsonb_agg(elem)
                  FROM jsonb_array_elements(recipients) as elem
                  WHERE elem->>'email' != '${bounceEmail}'
                )`,
              }
            ),
          ]);

          // Re-calculate pendingCount
          const seqInstancesEmails = await this.sequenceInstanceRepository
            .createQueryBuilder('si')
            .innerJoin('sequence_steps', 'ss', 'ss.id = si.sequence_step_id')
            .select(
              'si.recipients AS recipients, si.status AS status, ss.step_index AS step_index, si.skip_recipients as skip_recipients'
            )
            .where('si.sequence_id = :seqId AND ss.type = :type', {
              // status: SequenceStepStatus.SENT,
              seqId: mail.sequence.id,
              type: SequenceStepType.EMAIL,
            })
            .orderBy('ss.step_index', 'ASC')
            .getRawMany();

          const { pendingCount, stopCount } = seqInstancesEmails.reduce(
            (acc, seqInstancesEmail) => {
              if (seqInstancesEmail.status === SequenceStepStatus.PENDING) {
                acc.pendingCount += seqInstancesEmail.recipients.length;
              } else if ([SequenceStepStatus.STOP, SequenceStepStatus.OFF].includes(seqInstancesEmail.status)) {
                acc.stopCount += seqInstancesEmail.recipients.length;
              }
              acc.stopCount += seqInstancesEmail.skip_recipients?.length || 0;

              return acc;
            },
            { pendingCount: 0, stopCount: 0 }
          );

          await this.sequenceRepository.update(mail.sequence.id, {
            pendingCount,
            stopCount,
          });

          return true;
        }
      }

      const listUsersSenders = senders.map((sender: any) => sender.email?.toLowerCase());
      const [senderEmail] = listUsersSenders;
      const senderDomain = senderEmail.split('@').at(1)?.toLowerCase();
      const userReplied = mail.repliers ?? [];
      const listUsersSentAndReplied = [...listUsersSenders, ...userReplied];
      const uniqueListRepliers = [...new Set(listUsersSentAndReplied)];
      const stopRules = mail.sequenceStep.stopRules ?? [];

      const ownerEmail = mail?.user?.grantEmail || mail?.user?.email; // The email of the user who owns the sequence
      if (ownerEmail === senderEmail) {
        // The user replied to himself/forwards the email
        return 'SELF';
      }

      if (isAutoReply) {
        await job.log(`${new Date().toISOString()}: AUTO REPLIED`);
        // We store auto-reply emails in the CRM contact sequence step reply table
        await Promise.all([
          this.sequenceRepository
            .createQueryBuilder()
            .update(SequenceEntity)
            .set({ repliedCount: () => 'replied_count + 1' })
            .where('id = :id', { id: mail.sequence.id })
            .execute(),
          this.sequenceActivityLogRepository.insert({
            type: SequenceActivityType.AUTO_REPLIED,
            sequence: { id: mail?.sequence.id },
            sequenceStep: { id: mail.sequenceStep?.id },
            content: {
              sequenceInstance: mail.id,
              recipient: senderEmail,
              email: { content: body },
              type: SequenceStepType.EMAIL,
            },
            occurredAt: new Date().toISOString(),
          }),
        ]);

        // Find the CRM contact sequence step that corresponds to this email
        const crmContactSequenceStep = await this.crmContactSequenceStepRepository.findOne({
          where: {
            referenceId,
          },
        });

        // If we found a corresponding CRM contact sequence step, create an auto-reply record
        if (crmContactSequenceStep) {
          this.crmContactSequenceStepReplyRepository.save({
            sequenceStep: { id: crmContactSequenceStep.id },
            content: body || snippet,
            subject: emailWithHeader.data.subject,
            fromEmail: senderEmail,
            toEmail: crmContactSequenceStep.fromEmail || mail?.user?.grantEmail || mail?.user?.email,
            repliedAt: new Date(),
            type: CrmReplyType.AUTO_REPLIED,
            status: 'AUTO_REPLIED',
            referenceId,
            replyReferenceId,
          }).catch(error => {
            console.error('Error saving auto-reply:', error);
          });
        }

        return 'AUTO_REPLY';
      }

      const { stopDomains, stopEmails } = stopRules.reduce(
        (acc, { email, domain, stopAll, stopBy }) => {
          if (stopBy) {
            if (stopBy === STOP_BY_TYPE.INDIVIDUALLY) {
              acc.stopEmails.push(senderEmail);
              return acc;
            }

            if (!EMAIL_DOMAIN_WHITELIST.includes(senderDomain)) {
              acc.stopDomains.push(senderDomain);
            }
          }

          if (!stopAll) {
            return acc;
          }

          if (!EMAIL_DOMAIN_WHITELIST.includes(senderDomain) && domain?.toLowerCase() === senderDomain) {
            acc.stopDomains.push(senderDomain);
          }
          if (email?.toLowerCase() === senderEmail.toLowerCase()) {
            acc.stopEmails.push(senderEmail);
          }

          return acc;
        },
        { stopDomains: [], stopEmails: [] }
      );

      await this.sequenceInstanceRepository.update(mail.id, {
        repliers: uniqueListRepliers,
        status: SequenceStepStatus.SENT,
      });

      /**
       * If there are stop rules that match the sender's email domain to stop all
       * We'll remove the recipients who have the same domain as the sender email for the pending follow-up emails.
       * And if there are no more recipients left, that email/and sequence will be stopped
       */
      if (stopDomains.length || stopEmails.length) {
        await job.log(`${new Date().toISOString()}: STOP RULES`);
        const followUpRecipients = (mail.recipients || []).filter(
          ({ email }) => !stopDomains.includes(email.split('@').at(1)?.toLowerCase()) && !stopEmails.includes(email)
        );
        const stopRecipientDomains = [...new Set(stopDomains)].map((domain) => ({
          domain,
          type: SequenceActivityType.REPLIED,
        }));

        await this.sharedMailService.updateFollowUpEmails(
          {
            recipients: followUpRecipients,
            skipRecipients: () =>
              `COALESCE(skip_recipients, '[]'::jsonb) || '${JSON.stringify([
                ...uniqueListRepliers.map((email) => ({ email, type: SequenceActivityType.REPLIED })),
                ...stopRecipientDomains,
              ])}'::jsonb`,
            // ...(followUpRecipients.length ? {} : { status: SequenceStepStatus.STOP }),
          },
          mail.user.id,
          mail.sequence.id,
          mail.version
        );

        // if (!followUpRecipients.length) {
        //   this.sequenceRepository.update(
        //     {
        //       id: mail.sequence.id,
        //       status: SequenceStatus.LIVE,
        //     },
        //     {
        //       pendingCount: 0,
        //       stopCount: mail.sequence.pendingCount,
        //       status: SequenceStatus.STOP,
        //     }
        //   );
        // }
      }

      if (userReplied.includes(senderEmail)) {
        const crmContactSequenceStep = await this.crmContactSequenceStepRepository.findOne({
          where: {
            referenceId,
          },
        });

        if (crmContactSequenceStep) {
          this.crmContactSequenceStepReplyRepository.save({
            sequenceStep: { id: crmContactSequenceStep.id },
            content: body || snippet,
            subject: data.subject,
            fromEmail: senderEmail,
            toEmail: crmContactSequenceStep.fromEmail || mail?.user?.grantEmail || mail?.user?.email,
            repliedAt: new Date(),
            type: CrmReplyType.REPLIED,
            status: 'REPLIED',
            referenceId,
            replyReferenceId,
          }).catch(error => {
            console.error('Error saving reply:', error);
          });
        }

        return 'ALREADY_REPLIED';
      }

      // TODO
      // risk: The system may encounter data inaccuracies when there are continuous webhooks coming in but the backend hasn't processed them in time
      // action: 1. Use Lock DB
      // action: 2. Put all the webhooks into a queue and then process the data sequentially

      let emailFromHotList = [];
      if (mail.hotlistIds?.length) {
        await job.log(`${new Date().toISOString()}: CHECK EMAIL FROM HOTLIST`);
        emailFromHotList = await this.sharedMailService.findHotListContact(mail.hotlistIds, {}, fromEmail);
        await job.log(`${new Date().toISOString()}: END CHECK EMAIL FROM HOTLIST`);
      }

      const followInstanceCondition = {
        user: { id: mail.user.id },
        sequence: { id: mail.sequence.id },
        version: mail.version,
        id: Not(mail.id),
        scheduledAt: MoreThan(mail.scheduledAt),
        status: SequenceStepStatus.PENDING,
      };

      const followInstanceIds = await this.sequenceInstanceRepository.find({
        where: followInstanceCondition,
        select: ['id'],
      });

      const senderUser = mail?.sequenceStep?.sequenceStepTasks?.find((item) => item.recipients?.[0]?.email === senderEmail).recipients

      // Find the CRM contact sequence step that corresponds to this email
      const crmContactSequenceStep = await this.crmContactSequenceStepRepository.findOne({
        where: {
          referenceId,
        },
      });

      const repliedTasks = [
        this.sequenceRepository
          .createQueryBuilder()
          .update(SequenceEntity)
          .set({ repliedCount: () => `"replied_count" + 1` })
          .where('id = :id', { id: mail?.sequence.id })
          .execute(),
        this.sequenceActivityLogRepository.insert({
          type: emailFromHotList?.length === 0 ? SequenceActivityType.REPLIED : SequenceActivityType.REPLIED_BY_HOTLIST,
          sequence: { id: mail?.sequence.id },
          sequenceStep: { id: mail.sequenceStep?.id },
          content: {
            sequenceInstance: mail.id,
            recipient: senderEmail,
            email: { content: snippet },
            contact: {
              ...mail.recipients.find((recipientsItem) => recipientsItem?.email?.toLowerCase() === senderEmail),
              ...senderUser[0],
            },
          },
          occurredAt: new Date().toISOString(),
        }),
      ];

      // If we found a corresponding CRM contact sequence step, create a reply record and update first reply time
      if (crmContactSequenceStep) {
        // Create the reply separately from the main tasks to avoid TypeScript errors
        this.crmContactSequenceStepReplyRepository.save({
          sequenceStep: { id: crmContactSequenceStep.id },
          content: body || snippet,
          subject: emailWithHeader.data.subject,
          fromEmail: senderEmail,
          toEmail: crmContactSequenceStep.fromEmail || mail?.user?.grantEmail || mail?.user?.email,
          repliedAt: new Date(),
          type: CrmReplyType.REPLIED,
          status: 'REPLIED',
          referenceId,
          replyReferenceId,
        }).catch(error => {
          console.error('Error saving reply:', error);
        });

        // Update the replied at time if it's not already set
        if (!crmContactSequenceStep.repliedAt) {
          this.crmContactSequenceStepRepository.update(
            crmContactSequenceStep.id,
            { repliedAt: new Date() }
          ).catch(error => {
            console.error('Error updating reply time:', error);
          });
        }
      }
      // if there are existing hotlist or contact list - don't stop following steps
      if (!mail.hotlistIds?.length && !mail.contactListIds.length) {
        repliedTasks.unshift(
          this.sequenceInstanceRepository.update(followInstanceCondition, {
            status: SequenceStepStatus.STOP,
          })
        );
      }

      await Promise.all(repliedTasks);

      // In send job to BH, we don't have stop rules & hotlist
      if (
        !mail.hotlistIds?.length &&
        !stopDomains.length &&
        !stopEmails.length &&
        mail.recipients.find(({ email }) => email?.toLowerCase() === senderEmail)
      ) {
        // In this case, we will stop sequence, and add activity log
        const emailActivityLogEntity = this.sequenceActivityLogRepository.create({
          type: SequenceActivityType.STOPPED,
          sequence: { id: mail.sequence.id },
          sequenceStep: { id: mail.sequenceStep?.id },
          // Only save snippet of email content
          content: { recipient: senderEmail, email: { content: snippet } },
          occurredAt: new Date().toISOString(),
        });

        const skipRecipients = listUsersSenders.map((email) => ({
          email: email?.toLowerCase(),
          type: SequenceActivityType.REPLIED,
        }));

        await job.log(`${new Date().toISOString()}: ADDING ACTIVITIES`);
        await Promise.all([
          this.sequenceActivityLogRepository.insert(emailActivityLogEntity),
          this.sharedMailService.updateFollowUpEmails(
            {
              status: SequenceStepStatus.STOP,
            },
            mail.user.id,
            mail.sequence.id
          ),
          this.sequenceInstanceRepository.update(
            {
              id: In(followInstanceIds.map(({ id: fiId }) => fiId)),
            },
            {
              skipRecipients: () =>
                `COALESCE(skip_recipients, '[]'::jsonb) || '${JSON.stringify(skipRecipients)}'::jsonb`,
              recipients: mail.recipients.filter(({ email }) => email?.toLowerCase() !== senderEmail),
            }
          ),
        ]);

        await job.log(`${new Date().toISOString()}: END ADDING ACTIVITIES`);
        // Re-calculate pendingCount
        const seqInstancesEmails = await this.sequenceInstanceRepository
          .createQueryBuilder('si')
          .innerJoin('sequence_steps', 'ss', 'ss.id = si.sequence_step_id')
          .select(
            'si.recipients AS recipients, si.status AS status, ss.step_index AS step_index, si.skip_recipients as skip_recipients'
          )
          .where('si.sequence_id = :seqId AND ss.type = :type', {
            // status: SequenceStepStatus.SENT,
            seqId: mail.sequence.id,
            type: SequenceStepType.EMAIL,
          })
          .orderBy('ss.step_index', 'ASC')
          .getRawMany();

        const { pendingCount, stopCount } = seqInstancesEmails.reduce(
          (acc, seqInstancesEmail) => {
            if (seqInstancesEmail.status === SequenceStepStatus.PENDING) {
              acc.pendingCount += seqInstancesEmail.recipients.length;
            } else if ([SequenceStepStatus.STOP, SequenceStepStatus.OFF].includes(seqInstancesEmail.status)) {
              acc.stopCount += seqInstancesEmail.recipients.length;
            }
            acc.stopCount += seqInstancesEmail.skip_recipients?.length || 0;

            return acc;
          },
          { pendingCount: 0, stopCount: 0 }
        );

        await this.sequenceRepository.update(mail.sequence.id, {
          pendingCount,
          stopCount,
          status: SequenceStatus.STOP,
        });

        await this.sharedMailService.sendEmailSequenceStop(mail.user, mail.sequence, false);
      }

      return 'OTHER';
    } catch (error) {
      console.log("🚀 Error while handling Nylas:", error)
      throw error;
    } finally {
      console.timeEnd(`[NYLAS] ${id}`);
    }
  }

  private getUrlOnClick(initialData: any, newData: any) {
    let url_onclick = '';
    if (initialData === null) {
      let maxCount = -1;
      for (let i = 0; i < newData.length; i++) {
        if (newData[i].count > maxCount) {
          maxCount = newData[i].count;
          url_onclick = newData[i].url;
        }
      }
    } else {
      for (let i = 0; i < newData.length; i++) {
        if (newData[i].count !== initialData[i].count) {
          url_onclick = newData[i].url;
        }
      }
    }

    return url_onclick;
  }

  async handleOpenedEvent(job: any = {}) {
    try {
      const data = job.data || {};
      const { grant_id: grantId, object } = data;

      const id = object?.id || object?.message_id || object?.origin?.id;

      if (!grantId || !id) {
        return 'NO_GRANT_OR_ID';
      }

      await job.log(`${new Date().toISOString()}: START GET NYLAS EMAIL`);
      const emailWithHeader = await this.sharedMailService.nylas.messages.find({
        identifier: grantId,
        messageId: id,
        queryParams: {
          fields: MessageFields.INCLUDE_HEADERS,
        },
      });
      await job.log(`${new Date().toISOString()}: END GET NYLAS EMAIL`);

      const replyHeader = (emailWithHeader?.data?.headers || []).find(
        (item) => item?.name?.toLowerCase() === 'message-id'
      );

      if (!replyHeader?.value) {
        return 'NO_MESSAGE_ID';
      }

      const rawMessageId = replyHeader.value;
      await job.log(`${new Date().toISOString()}: START FIND INSTANCE`);
      const mail = await this.sharedMailService.findMailByRawId(rawMessageId);
      await job.log(`${new Date().toISOString()}: END FIND INSTANCE`);

      if (!mail) {
        return 'NO_SEQUENCE_INSTANCE';
      }

      const now = new Date();
      const secondsToCheck = 10000; // 10 seconds
      const createdAtCheckTime = new Date(now.getTime() - secondsToCheck);

      const lastActivityLogs = await this.sequenceActivityLogRepository.find({
        where: {
          sequence: { id: mail?.sequence.id },
          type: SequenceActivityType.OPENED,
          createdAt: MoreThanOrEqual(createdAtCheckTime),
        },
      });

      const recipientEmail = emailWithHeader?.data?.to[0]?.email;
      if (lastActivityLogs.length > 0 && lastActivityLogs[0]?.content?.['email']?.['content'] === recipientEmail) {
        return true;
      }

      const emailActivityLogEntity = this.sequenceActivityLogRepository.create({
        type: SequenceActivityType.OPENED,
        sequence: { id: mail?.sequence.id },
        occurredAt: new Date().toISOString(),
        sequenceStep: { id: mail.sequenceStep?.id },
        content: {
          email: { content: recipientEmail },
          contact: {
            ...mail.recipients.find((recipientsItem) => recipientsItem?.email === recipientEmail),
          },
        },
      });

      await Promise.all([
        this.dataSource
          .createQueryBuilder()
          .update(SequenceEntity)
          .set({ opened: () => `"opened" + 1` })
          .where('id = :id', { id: mail?.sequence.id })
          .execute(),

        this.sequenceActivityLogRepository.insert(emailActivityLogEntity),
      ]);

      return 'OPENED';
    } catch (error) {
      throw error;
    }
  }

  async handleClickedEvent(job: any = {}) {
    try {
      const data = job.data || {};
      const { grant_id: grantId, object } = data;
      const id = object?.id || object?.message_id || object?.origin?.id;

      if (!grantId || !id) {
        return 'NO_GRANT_OR_ID';
      }

      await job.log(`${new Date().toISOString()}: START GET NYLAS EMAIL`);
      const emailWithHeader = await this.sharedMailService.nylas.messages.find({
        identifier: grantId,
        messageId: id,
        queryParams: {
          fields: MessageFields.INCLUDE_HEADERS,
        },
      });
      await job.log(`${new Date().toISOString()}: END GET NYLAS EMAIL`);

      const replyHeader = (emailWithHeader?.data?.headers || []).find(
        (item) => item?.name?.toLowerCase() === 'message-id'
      );

      if (!replyHeader?.value) {
        return 'NO_MESSAGE_ID';
      }

      const rawMessageId = replyHeader.value;
      await job.log(`${new Date().toISOString()}: START FIND INSTANCE`);
      const mail = await this.sharedMailService.findMailByRawId(rawMessageId);
      await job.log(`${new Date().toISOString()}: END FIND INSTANCE`);

      if (!mail) {
        return 'NO_SEQUENCE_INSTANCE';
      }

      const lastActivityLogs = await this.sequenceActivityLogRepository.find({
        where: {
          sequence: { id: mail?.sequence.id },
          type: SequenceActivityType.LINK_CLICKED,
        },
        order: { createdAt: 'DESC' },
        take: 1,
      });

      let linkOnClick = '';
      linkOnClick = this.getUrlOnClick(
        lastActivityLogs[0]?.content['link_data'] ?? null,
        data?.object?.link_data
      );
      const recipientEmail = emailWithHeader?.data?.to[0]?.email;

      const emailActivityLogEntity = this.sequenceActivityLogRepository.create({
        type: SequenceActivityType.LINK_CLICKED,
        sequence: { id: mail?.sequence.id },
        occurredAt: new Date().toISOString(),
        sequenceStep: { id: mail.sequenceStep?.id },
        content: {
          email: { content: emailWithHeader?.data?.to[0]?.email },
          contact: {
            ...mail.recipients.find((recipientsItem) => recipientsItem?.email === recipientEmail),
          },
          link_data: data?.object?.link_data,
          link_onclick: linkOnClick,
        },
      });

      await Promise.all([
        this.dataSource
          .createQueryBuilder()
          .update(SequenceEntity)
          .set({ linkClicked: () => `link_clicked + 1` })
          .where('id = :id', { id: mail?.sequence.id })
          .execute(),

        this.sequenceActivityLogRepository.insert(emailActivityLogEntity),
      ]);

      return 'CLICKED';
    } catch (error) {
      throw error;
    }
  }

  // eslint-disable-next-line class-methods-use-this
  async onCompleted(job: Job<any>) {
    console.log(
      `Job ${BULL_JOB_NAMES.WEBHOOK_NYLAS_EVENT} - ${job.id} completed at ${new Date(job.finishedOn).toISOString()}`
    );
  }

  // eslint-disable-next-line class-methods-use-this
  async onFailed(job: Job<any>, error: any) {
    console.log(
      `Job ${BULL_JOB_NAMES.WEBHOOK_NYLAS_EVENT} - ${job.id} failed at ${job.attemptsMade} attempt(s) with error: `,
      error
    );
  }
}

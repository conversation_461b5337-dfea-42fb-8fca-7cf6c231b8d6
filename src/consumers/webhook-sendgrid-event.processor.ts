/* eslint-disable camelcase */
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import {
  BULL_JOB_NAMES,
  BULL_QUEUES,
} from 'src/configs/configs.constants';
import { SequenceActivityType } from 'src/modules/mail/entities/sequence-activity-log.entity';
import { SharedMailService } from 'src/modules/shared/shared-mail.service';

@Processor(BULL_QUEUES.WEBHOOK_SENDGRID_EVENT, {
  concurrency: 30,
  stalledInterval: 2 * 60 * 1000, // 2 minutes
  maxStalledCount: 2,
  removeOnComplete: {
    age: 12 * 60 * 60,
    count: 1000,
  }
})
export class WebhookSendgridEventProcessor extends WorkerHost {
  constructor(
    private readonly sharedMailService: SharedMailService,
  ) {
    super();
  }

  async process(job: Job<any>) {
    const { id, data, attemptsMade } = job;
    console.log(`[SENDGRID QUEUE] ID ${id} is running...`);
    console.time(`[SENDGRID] ${id}`);
    try {
      const { 'smtp-id': rawId, sg_message_id, event, timestamp, type, email: recipient, reason, url, attempt = 0 } = data;
      let rawMessageId = rawId;
      if (!rawId && sg_message_id) {
        [rawMessageId] = sg_message_id.split('.');
      }

      await job.log(`${new Date().toISOString()}: START FIND INSTANCE`);
      const mail = await this.sharedMailService.findMailByRawId(rawMessageId);
      await job.log(`${new Date().toISOString()}: END FIND INSTANCE`);

      if (!mail) {
        return true;
      }

      await job.log(`${new Date().toISOString()}: START FIND RECIPIENT`);
      const recipientSend = await this.sharedMailService.findRecipientByEmail(mail.id, recipient);
      await job.log(`${new Date().toISOString()}: END FIND RECIPIENT`);

      const emailSeq = mail.sequence;
      const eventTime = new Date(timestamp * 1000).toISOString();
      let eventType = '';
      switch (event) {
        case SequenceActivityType.BOUNCE: {
          eventType = type === 'bounce' ? 'bounce' : 'blocked';
          break;
        }
        default: {
          eventType = event;
          break;
        }
      }
      if (event === 'dropped') {
        if (reason?.toLowerCase()?.includes('bounce')) {
          eventType = 'bounce';
        } else {
          return;
        }
      }

      const result = await this.sharedMailService.updateCorrespondingField({
        field: eventType,
        mail,
        emailSeq,
        eventTime,
        recipient,
        recipientSend,
        url,
        reason: (eventType === 'bounce' || eventType === 'blocked' || event === 'bounce') ? reason : undefined,
      });

      return result;
    } finally {
      console.timeEnd(`[SENDGRID] ${id}`);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  async onCompleted(job: Job<any>) {
    console.log(
      `Job ${BULL_JOB_NAMES.WEBHOOK_SENDGRID_EVENT} - ${job.id} completed at ${new Date(job.finishedOn).toISOString()}`
    );
  }

  // eslint-disable-next-line class-methods-use-this
  async onFailed(job: Job<any>, error: any) {
    console.log(
      `Job ${BULL_JOB_NAMES.WEBHOOK_SENDGRID_EVENT} - ${job.id} failed at ${job.attemptsMade} attempt(s) with error: `,
      error
    );
  }
}

/* eslint-disable camelcase */
import axios from 'axios';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import {
  BULL_JOB_NAMES,
  BULL_QUEUES,
  EMAIL_DOMAIN_WHITELIST,
  EMITTER_EVENTS,
} from 'src/configs/configs.constants';
import { SequenceActivityType } from 'src/modules/mail/entities/sequence-activity-log.entity';
import { SharedMailService } from 'src/modules/shared/shared-mail.service';
import { SequenceEntity, SequenceEvent } from 'src/modules/mail/entities/sequence.entity';
import { MessageFields } from 'nylas';
import { DataSource, In, MoreThan, MoreThanOrEqual, Not } from 'typeorm';
import { SequenceActivityLogRepository } from 'src/modules/mail/repositories/sequence-activity-log.repository';
import { SequenceRepository } from 'src/modules/mail/repositories/sequence.repostiory';
import { SequenceInstanceRepository } from 'src/modules/mail/repositories/sequence-instance.repository';

@Processor(BULL_QUEUES.BULLHORN_EMAIL_NOTE_EVENT, {
  concurrency: 10,
  stalledInterval: 3 * 60 * 1000, // 3 minutes
  maxStalledCount: 2,
  removeOnComplete: {
    age: 12 * 60 * 60,
    count: 1000,
  }
})
export class BullhornEmailNoteEventProcessor extends WorkerHost {
  constructor(
    private readonly dataSource: DataSource,
    private readonly sequenceActivityLogRepository: SequenceActivityLogRepository,
    private readonly sharedMailService: SharedMailService,
  ) {
    super();
  }

  async process(job: Job<any>) {
    const { id, data, attemptsMade } = job;
    console.log(`[BULLHORN EMAIL NOTE] ID ${id} is running...`);
    try {
      const results = await this.sequenceActivityLogRepository
        .createQueryBuilder('entity')
        .select([
          "jsonb_array_elements(COALESCE(entity.content->'clientContacts', '[]'::jsonb)) AS clientContact",
          "entity.content->>'subject' AS subject",
        ])
        .where('entity.type = :status', { status: SequenceActivityType.SENT })
        .andWhere("entity.content->>'type' = :type", { type: 'EMAIL' })
        .andWhere("entity.content->>'sequenceInstance' = :sequenceInstanceId", { sequenceInstanceId: data.sequenceInstanceId })
        .getRawMany();
      const clientContacts = results.map((row) => row.clientcontact);
      const subject = results[0]?.subject;
      if (clientContacts.length) {
        await this.sharedMailService.addBHNoteForEmailSent(
          {
            clientContacts,
            subject: subject || data.subject,
            body: data.body,
          },
          data.user,
        );
      }
    } catch (error) {
      console.log("🚀 Error while handling adding note for email sent job:", error)
      throw error;
    } finally {
      console.log(`[BULLHORN EMAIL NOTE] ID ${id} is complete`);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  async onCompleted(job: Job<any>) {
    console.log(
      `Job ${BULL_JOB_NAMES.BULLHORN_EMAIL_NOTE_EVENT} - ${job.id} completed at ${new Date(job.finishedOn).toISOString()}`
    );
  }

  // eslint-disable-next-line class-methods-use-this
  async onFailed(job: Job<any>, error: any) {
    console.log(
      `Job ${BULL_JOB_NAMES.BULLHORN_EMAIL_NOTE_EVENT} - ${job.id} failed at ${job.attemptsMade} attempt(s) with error: `,
      error
    );
  }
}


import * as v8 from 'v8';
import { IPaginationData } from '../interfaces/common.interface';
import * as AWS from 'aws-sdk';
import { parsePhoneNumberFromString, CountryCode } from 'libphonenumber-js';
import {
  APP_CONFIG,
  JOB_PREDEFINED_KEYWORDS,
  JOB_STOP_WORDS,
  SesConfig,
  SIMILAR_STANDARD_JOB_TYPES,
  STANDARD_JOB_TYPES,
} from '../../configs/configs.constants';
import { StatusCode } from '../constants/common.constant';
import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { CommonConfig } from '../../configs/configs.constants';
import { MailComposer } from 'mailcomposer';
import { HttpStatus, Logger } from '@nestjs/common';
import { createCipheriv, scrypt, publicEncrypt, generateKeyPairSync, constants } from 'crypto';
import { promisify } from 'util';
import * as path from 'path';
import * as fs from 'fs';
import { getCode } from 'country-list';
import { get } from 'lodash';
import * as kill from 'tree-kill';
import { MERGE_TAGS_PLACE_HOLDERS, MergeTagMappingEnum } from 'src/modules/mail/dto/send-email-process.dto';
import { ScheduleInformationDto } from 'src/modules/mail/dto/upsert-email.dto';
import * as moment from 'moment-timezone';
import { UserStatus } from 'src/modules/user/entities/user.entity';
import { OrganizationStatusEnum } from 'src/modules/user/enums/organization.enum';
import { FeatureEnum, FeatureToPermissionsMapping, RoleFeatureMapping } from '../constants/feature-permission.constant';

const BREAK_LINE_TAG = '<br>';
const LINKEDIN_UNSUPPORTED = {
  UL_TAG: {
    START: '<ul',
    END: '</ul>',
  },
  LI_TAG: '<li',
};

export const S3 = new AWS.S3({
  accessKeyId: SesConfig.COMMON_API_AWS_ACCESS_KEY,
  secretAccessKey: SesConfig.COMMON_API_AWS_SECRET_KEY,
  region: SesConfig.COMMON_API_REGION,
});

const SES = new AWS.SES({
  accessKeyId: SesConfig.COMMON_API_AWS_ACCESS_KEY,
  secretAccessKey: SesConfig.COMMON_API_AWS_SECRET_KEY,
  region: SesConfig.COMMON_API_REGION,
});

// const twilioClient = twilio(twilioConfig.accountSid, twilioConfig.authToken);

// export const sendMessage = async (message: string, mobile: string) => {
//   try {
//     const result = await twilioClient.messages.create({
//       body: message,
//       from: twilioConfig.twilioSendFrom,
//       to: mobile,
//     });

//     return result;
//   } catch (error) {
//     console.log(JSON.stringify(error));
//     return {
//       error,
//       status: ETwilioMessage.STATUS,
//     };
//   }
// };

export const sendRawMessageToEmail = async (params?) => {
  let sendRawEmailPromise;

  const mail = new MailComposer(params);
  return new Promise((resolve, reject) => {
    mail.compile().build((err, message) => {
      if (err) {
        reject(`Error sending raw email: ${err}`);
      }
      sendRawEmailPromise = SES.sendRawEmail({
        RawMessage: { Data: message },
      }).promise();
    });

    resolve(sendRawEmailPromise);
  });
};

export const sendMessageToEmail = async (params) => {
  const sendPromise = SES.sendEmail(params).promise();
  sendPromise
    .then((data) => {
      console.log('sendMessageToEmail', JSON.stringify(data));
      return data;
    })
    .catch((error) => {
      console.log('sendMessageToEmail error', JSON.stringify(error));
      throw error;
    });
};

export const getSignedUrlPromise = async (operation: string, params: any) => {
  return S3.getSignedUrlPromise(operation, params);
};

export const getSignedUrl = async (operation: string, params: any) => {
  return S3.getSignedUrl(operation, params);
};

export function paginationTransformer(input): IPaginationData {
  return {
    data: input.docs,
    total: input.totalDocs,
    page: input.page,
    limit: input.limit,
  };
}

export function makeCode() {
  let text = '';
  const possibleNumbers = '0123456789';
  for (let i = 0; i < 4; i += 1) text += possibleNumbers.charAt(Math.floor(Math.random() * possibleNumbers.length));

  return text;
}

export const decodePassword = function decodePassword(password) {
  return decodeURIComponent(Buffer.from(password, 'base64').toString('utf-8'));
};

export const getRndInteger = function (min, max) {
  return Math.floor(Math.random() * (max - min)) + min;
};

export const randomNumber = function (length: number) {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return String(getRndInteger(min, max));
};

export const validatePhoneNumber = function (phone: string, countryCode: CountryCode) {
  if (!phone) {
    return {
      country: null,
      countryCallingCode: null,
      nationalNumber: null,
      number: null,
    };
  }
  const phoneNumber = parsePhoneNumberFromString(phone, countryCode);
  if (!phoneNumber || !phoneNumber.isValid()) {
    return false;
  }

  return phoneNumber;
};

export function addMinutes(date: Date, minutes: number) {
  return new Date(date.getTime() + minutes * 60000);
}

export const getTokenFromAuthorization = (authorization: string) => authorization.split(' ')[1];

export const calculateAge = (birthday: Date) => {
  if (!birthday) {
    return 48;
  }
  const ageDifMs = Date.now() - new Date(birthday).getTime();
  const ageDate = new Date(ageDifMs);
  return ageDate.getUTCFullYear() - 1970;
};

export const getCommonMessageError = (error: any) =>
  error?.response?.data?.error?.message ??
  error?.response?.data?.message ??
  error?.response?.message ??
  error?.raw?.message ??
  error?.response?.body?.errors ??
  (error?.driverError?.message || 'Something went wrong');

export const getCommonStatusCodeError = (error: any) => error?.response?.statusCode;

export const uniqueArray = (array: string[]) => {
  return array?.filter(function (item, index, arr) {
    return arr.indexOf(item) === index;
  });
};

export const convertArrayToObject = (array: any[], field: string) => {
  return Object.assign({}, ...array.map((item) => ({ [item[field]]: item })));
};

export const subtractYears = (date: Date, years: number) => {
  date.setFullYear(date.getFullYear() - years);
  return date;
};

export const jsonParse = (value: string | any) => {
  try {
    if (typeof value !== 'string') {
      return value;
    }
    return JSON.parse(value);
  } catch (error) {
    return value;
  }
};

export const jsonParseStringOrArray = (value: any) => {
  return typeof value === 'string' ? jsonParse(value) : value?.map((item) => jsonParse(item));
};

export const convertToArray = (value: any) => (Array.isArray(value) ? value : [value]);

const imageFilter = (
  req: Request,
  file: Express.Multer.File,
  callback: (error: Error, acceptFile: boolean) => void
) => {
  if (!Boolean(file.mimetype.match(/(jpg|jpeg|png|svg)/))) callback(null, false);
  callback(null, true);
};

export const imageOptions: MulterOptions = {
  limits: { fileSize: CommonConfig.LIMIT_SIZE_FILE_UPLOAD },
  fileFilter: imageFilter,
};

export const isDevMode = () => {
  return process.env.NODE_ENV === 'dev';
};

export const isLocalMode = () => process.env.NODE_ENV === 'local';

export const logInfo = (funtionName: string, content): void => {
  try {
    if (typeof content === 'string') {
      Logger.log(`[${funtionName}]: ${content}`);
    } else {
      Logger.log(`[${funtionName}] : ${JSON.stringify(content)}`);
    }
  } catch (error) {
    Logger.log(`[${funtionName}] : <data is too big>`);
  }
};

export const logWarn = (funtionName: string, warning): void => {
  try {
    if (warning?.message) warning = warning.message;
    if (typeof warning === 'string') {
      Logger.warn(`[${funtionName}]`, warning);
    } else {
      Logger.warn(`[${funtionName}]`, JSON.stringify(warning));
    }
  } catch (error) {
    Logger.warn(`[${funtionName}] : <data is too big>`);
  }
};

export const logError = (funtionName: string, error): void => {
  try {
    const logError: any = {
      message: error?.message,
      stack: error?.stack,
    };

    if (error?.response?.status) {
      logError.status = error.response.status;
    }

    if (error?.response?.data) {
      logError.data = error.response.data;
    }

    Logger.error(`[${funtionName}]: ${JSON.stringify(logError)}`);
  } catch (error) {
    Logger.warn(`[${funtionName}] : <data is too big>`);
  }
};

export const transformToStandardValue = (value: any) => (value === 'null' ? null : value);

export const removeNullFieldsObject = (object: any) => {
  return Object.assign(
    {},
    ...Object.keys(object)
      .filter((key) => object[key])
      .map((key) => ({ [key]: object[key] }))
  );
};

export const getStandardStatusCode = (statusCode: number) => {
  if (statusCode === HttpStatus.UNAUTHORIZED) {
    return StatusCode.UNAUTHORIZED;
  }
  return statusCode;
};

export const addBackslashes = (str: string) => {
  let newStr = '';
  for (let i = 0; i < str.length; i++) {
    if (str[i] == `\\` || str[i] == '%' || str[i] == '_') {
      const character = '\\';
      newStr += character;
    }
    newStr += str[i];
  }
  return newStr;
};

export const decryptedContent = async (encryptedMsg: string) => {
  try {
    const password = CommonConfig.SECRET_JOB_PASSWORD;
    const iv = CommonConfig.SECRET_JOB_IV;
    const key = (await promisify(scrypt)(password, 'salt', 32)) as Buffer;
    const decrypter = createCipheriv('aes-256-ctr', key, iv);
    const decryptedMsg = decrypter.update(encryptedMsg, 'hex', 'utf8');
    console.log('decryptedMsg', decryptedMsg);

    return decryptedMsg;
  } catch (error) {
    return null;
  }
};

export const encryptedContent = async (content: object) => {
  try {
    const password = CommonConfig.SECRET_JOB_PASSWORD;
    const iv = CommonConfig.SECRET_JOB_IV;
    const key = (await promisify(scrypt)(password, 'salt', 32)) as Buffer;
    const encrypter = createCipheriv('aes-256-ctr', key, iv);
    const encryptedMsg = encrypter.update(JSON.stringify(content), 'utf8', 'hex');
    return encryptedMsg;
  } catch (error) {
    logInfo('encryptedContent', error);

    return null;
  }
};

export const encrypted = (encryptedData: string) => {
  const publicKey = process.env.PUBLIC_KEY_ENCRYPTED;
  const decryptedBuffer = publicEncrypt(
    {
      key: publicKey,
      padding: constants.RSA_PKCS1_PADDING,
    },
    Buffer.from(encryptedData)
  );
  return decryptedBuffer.toString('base64');
};

// export const decrypted = async (encryptedData: string, timeExpire?: number) => {
//   const privateKey = process.env.PRIVATE_KEY_DECRYPTED;
//   const decryptedBuffer = privateDecrypt(
//     {
//       key: privateKey,
//       padding: constants.RSA_PKCS1_PADDING,
//     },
//     Buffer.from(encryptedData, "base64")
//   );
//   const decryptedMsg = decryptedBuffer.toString();
//   if (!decryptedMsg) {
//     return {
//       status: false,
//       message: "translate.SOMETHING_WRONG_WITH_ENCRYPT_CODE",
//       statusCode: StatusCode.SOMETHING_WRONG_WITH_ENCRYPT_CODE,
//     };
//   }
//   if (timeExpire) {
//     console.log("decryptedMsg", decryptedMsg);
//     const { time } = JSON.parse(decryptedMsg);
//     if (!time) {
//       return {
//         status: false,
//         message: "translate.SOMETHING_WRONG_WITH_ENCRYPT_CODE",
//         statusCode: StatusCode.SOMETHING_WRONG_WITH_ENCRYPT_CODE,
//       };
//     }
//     const currentTime = moment.tz("UTC").format("x");
//     console.log("currentTime", currentTime);
//     if (+currentTime - time > timeExpire * 60 * 1000) {
//       return {
//         status: false,
//         message: "translate.EXPIRE_TIME_ENCRYPT_CODE",
//         statusCode: StatusCode.EXPIRE_TIME_ENCRYPT_CODE,
//       };
//     }
//   }
//   return {
//     status: true,
//     decryptedMsg,
//   };
// };

export const generateKeys = () => {
  const { privateKey, publicKey } = generateKeyPairSync('rsa', {
    modulusLength: 1024,
    publicKeyEncoding: {
      type: 'pkcs1',
      format: 'pem',
    },
    privateKeyEncoding: {
      type: 'pkcs1',
      format: 'pem',
    },
  });
  const filePathPrivate = path.join(__dirname, '..', '..', 'fields/pem', 'privateee.pem');
  const filePathPublic = path.join(__dirname, '..', '..', 'fields/pem', 'publiccc.pem');
  console.log('filePathPrivate, filePathPublic', filePathPrivate, filePathPublic);
  fs.writeFileSync(filePathPrivate, privateKey);
  fs.writeFileSync(filePathPublic, publicKey);
};

export const getCountryCodeFlagByCountryName = async (country = 'UAE') => {
  if (!country) {
    return '';
  }
  if (country === 'UAE') {
    return 'AE';
  }
  const code = await getCode(country.trim());
  return code;
};

export const calcSumOfArrayObject = (array: any[], fieldToCalc: string) =>
  array.reduce((prev, curr) => prev + curr?.[fieldToCalc], 0);

export const simpleSumCalculate = (array: any[]) => {
  let sum = 0;
  for (let i = 0; i < array.length; i++) {
    const element = array[i];
    sum += element.price;
  }
  return sum;
};

export const getPaginationResponse = (data: any[], totalItems: number, limit = 10, currentPage: number) => {
  return {
    data,
    totalPages: Math.ceil(totalItems / limit),
    totalItems,
    currentPage,
    limit,
  };
};

export const getShortestArray = (array: any[][]) => {
  let minIndexArray = 0;
  array.forEach((item, index) => {
    const length = item.length;

    if (array[minIndexArray].length === 0) {
      minIndexArray = index;
    }

    if (length < array[minIndexArray].length) {
      minIndexArray = index;
    }
  });

  return array[minIndexArray];
};

export const roundFloatNumber = (value: number, digit: number) => (value ? Number(value.toFixed(digit)) : value);

export const delayTime = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const convertNumberToCharacter = (number: number): string => {
  const numberFormat = Math.round((number / 1000 + Number.EPSILON) * 10) / 10;
  const numberFormatMillion = Math.round((number / 1000000 + Number.EPSILON) * 10) / 10;

  if (numberFormat < 1) return number.toString();
  if (numberFormat >= 1 && numberFormat < 1000) return `${numberFormat}k`;
  return `${numberFormatMillion}M`;
};

export const parseIntResult = (number: number) => parseInt((number / 10).toString(), 10);

export const getUniqueArrayObjectByKey = (array: any, key: string) => [
  ...new Map(array.map((item) => [item[key], item])).values(),
];

//save cookie function
export const saveCookie = async (cookies) => {
  const cookieJson = JSON.stringify(cookies, null, 2);
  console.log('cookieJson', cookieJson);
  const filePathPrivate = path.join(__dirname, '..', '..', 'fields/cookies', 'cookies.json');
  const absolutePath = path.resolve(filePathPrivate);
  await fs.writeFileSync(absolutePath, cookieJson);
};

//load cookie function
export const loadCookie = async () => {
  // const filePathPrivate = path.join(__dirname, '..', '..', 'fields/cookies', 'cookies.json');
  const absolutePath = path.resolve('./src/fields/cookies/cookies.json');
  const cookieJson = await fs.readFileSync(absolutePath, null).toString();

  const cookies = JSON.parse(cookieJson);
  logInfo('---> load cookie', cookies);
  return cookies;
};

export const safeStringify = (obj) => {
  const seen = new WeakSet();
  return JSON.stringify(obj, (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular]';
      }
      seen.add(value);
    }
    return value;
  });
};

export const roundDownNumber = (number: number) => Math.floor(number);

export const LAUNCH_PUPPETEER_OPTIONS = isLocalMode()
  ? {
      headless: true,
    }
  : {
      headless: true,
      args: ['--no-sandbox', '--disabled-setupid-sandbox'],
      executablePath: '/usr/bin/chromium-browser',
      ignoreDefaultArgs: ['--disable-extensions'],
    };

export const getValueByPath = (object: any, path: string, defaultValue: any = '') => {
  return get(object, path, defaultValue) ?? defaultValue;
};

export const convertPriceByCurrencyToUSD = (price, exchangeRate = 1) => {
  return Math.round((price / exchangeRate) * 1000) / 1000;
};

export const roundUSDPrice = (price) => {
  return Math.round((price + Number.EPSILON) * 100) / 100;
};

export const isValidEnumValue = (enumObject: any, value: string) =>
  enumObject && Object.values(enumObject).includes(value);

export const capitalizeFirstLetter = (str) => {
  str = str.toLowerCase();
  str = str.charAt(0).toUpperCase() + str.slice(1);
  return str;
};

export const removeTripleBars = (str) => {
  if (!str) {
    return '';
  }

  return str.replace(/\|\|\|/g, ', ');
};

export const killPort = (port: number) => {
  try {
    return kill(port);
  } catch {
    return true;
  }
};

export const addTimeToDate = (timeAmount: string): Date => {
  const currentDate = new Date();
  const timeUnit = timeAmount.slice(-1);
  const timeValue = parseInt(timeAmount.slice(0, -1), 10);

  if (isNaN(timeValue)) {
    return null;
  }

  const newDate = new Date(currentDate);

  switch (timeUnit) {
    case 's':
      newDate.setSeconds(newDate.getSeconds() + timeValue);
      break;
    case 'm':
      newDate.setMinutes(newDate.getMinutes() + timeValue);
      break;
    case 'h':
      newDate.setHours(newDate.getHours() + timeValue);
      break;
    case 'd':
      newDate.setDate(newDate.getDate() + timeValue);
      break;
    case 'w':
      newDate.setDate(newDate.getDate() + timeValue * 7);
      break;
    case 'M':
      newDate.setMonth(newDate.getMonth() + timeValue);
      break;
    case 'y':
      newDate.setFullYear(newDate.getFullYear() + timeValue);
      break;
    default:
      return null;
  }

  return newDate;
};

export const killProcess = (pid: number) => {
  if (!pid) {
    return;
  }
  kill(pid, 'SIGKILL');
};

export const convertToNumber = (value: string) => (!isNaN(Number(value)) ? Number(value) : 0);

export const splitArray = <T>(array: T[], x: number) => {
  const result: T[][] = [];
  for (var i = 0; i < array.length; i += x) {
    result.push(array.slice(i, i + x));
  }
  return result;
};

export const isWithinXMonths = (dateString: string | Date, xMonth: number): boolean => {
  const givenDate = new Date(dateString);
  const currentDate = new Date();

  // Calculate the difference in milliseconds between the current date and the given date
  const differenceInMs = currentDate.getTime() - givenDate.getTime();

  // Convert the milliseconds to months
  const differenceInMonths = differenceInMs / (1000 * 60 * 60 * 24 * 30);

  return differenceInMonths <= xMonth;
};

export const subtractTime = (amount: number, type: 'month' | 'days') => {
  const today = moment(); // Get the current date

  const previousMonth = today.subtract(amount, type);

  // Format the date as desired (e.g., "YYYY-MM-DD")
  const formattedDate = previousMonth.format('YYYY-MM-DD');

  return formattedDate;
};

export const getCommonItems = (arrayA: any[], arrayB: any[]): any[] => {
  return arrayA.filter((item) => arrayB.includes(item));
};

export const getMissingItems = (arr1, arr2) => {
  return arr1.filter((item) => !arr2.includes(item));
};

export const chunkArray = (arr: any[], size: number) =>
  Array.from({ length: Math.ceil(arr.length / size) }, (v, i) => arr.slice(i * size, i * size + size));

export const queryAllByChunk = async (queryBuilder, page: number = 0, size: number = 500) => {
  let result = [];
  const data = await queryBuilder
    .limit(size)
    .offset(page * size)
    .getRawMany();
  result = result.concat(data);
  if (data.length === 0 || data.length < size) {
    return result;
  }

  return result.concat(await queryAllByChunk(queryBuilder, page + 1));
};

export const safeParseJSON = (value: string) => {
  try {
    return JSON.parse(value);
  } catch {
    return null;
  }
};

export const decodeHtmlEntities = (htmlText: string) =>
  htmlText?.includes(BREAK_LINE_TAG) ? htmlText.replace(new RegExp(BREAK_LINE_TAG, 'g'), '\n') : htmlText;

export const stripHTMLTags = (str: string) => str?.replace(/<[^>]*>/g, '');

export const handleUnsupportULTags = (htmlText: string) =>
  htmlText?.includes(LINKEDIN_UNSUPPORTED.UL_TAG.START)
    ? htmlText
        .replace(new RegExp(LINKEDIN_UNSUPPORTED.UL_TAG.START, 'g'), '\n<ul')
        .replace(new RegExp(LINKEDIN_UNSUPPORTED.UL_TAG.END, 'g'), '</ul>\n')
    : htmlText;

export const handleUnsupportLITags = (htmlText: string) =>
  htmlText?.includes(LINKEDIN_UNSUPPORTED.LI_TAG)
    ? htmlText.replace(new RegExp(LINKEDIN_UNSUPPORTED.LI_TAG, 'g'), '\n- <li')
    : htmlText;


/**
 * Process unordered lists (bulleted lists) in HTML content
 * Converts <ul><li> structure to text with bullet points
 *
 * @param html HTML content containing unordered lists
 * @returns HTML with unordered lists converted to text with bullet points
 */
function processUnorderedLists(html: string): string {
  // Find all <ul> blocks
  const ulRegex = /<ul[^>]*>([\s\S]*?)<\/ul>/gi;

  // Process each list item within this <ul>
  return html.replace(ulRegex, (match) => match.replace(/<li[^>]*>([\s\S]*?)<\/li>/gi, (_liMatch, liContent) => {
    // Check if this list item has a nested list
    if (/<ul[^>]*>|<ol[^>]*>/i.test(liContent)) {
      // For nested lists, process the content with indentation
      const processedContent = liContent.replace(
        /<ul[^>]*>([\s\S]*?)<\/ul>/gi,
        (nestedMatch: string) => nestedMatch.replace(/<li[^>]*>([\s\S]*?)<\/li>/gi, ' + $1\n')
      );
      return `- ${processedContent}`;
    }
    // For regular list items, add a bullet point
    return `- ${liContent}\n`;
  }));
}

/**
 * Process ordered lists (numbered lists) in HTML content
 * Converts <ol><li> structure to text with numbers
 *
 * @param html HTML content containing ordered lists
 * @returns HTML with ordered lists converted to text with numbers
 */
function processOrderedLists(html: string): string {
  // Find all <ol> blocks
  const olRegex = /<ol[^>]*>([\s\S]*?)<\/ol>/gi;

  return html.replace(olRegex, (match) => {
    // Process each list item within this <ol>
    let counter = 1;
    return match.replace(/<li[^>]*>([\s\S]*?)<\/li>/gi, (_liMatch, liContent) => {
      // Check if this list item has a nested list
      if (/<ul[^>]*>|<ol[^>]*>/i.test(liContent)) {
        // For nested lists, process the content with indentation and sub-numbering
        let subCounter = 1;
        const processedContent = liContent
          .replace(
            /<ol[^>]*>([\s\S]*?)<\/ol>/gi,
            (nestedMatch: string) => nestedMatch.replace(
              /<li[^>]*>([\s\S]*?)<\/li>/gi,
              (nestedLi: string) => {
                const num = subCounter;
                subCounter += 1;
                return `   ${counter}.${num} ${nestedLi.replace(/<li[^>]*>|<\/li>/gi, '')}\n`;
              },
            ),
          )
          .replace(
            /<ul[^>]*>([\s\S]*?)<\/ul>/gi,
            (nestedMatch: string) => nestedMatch.replace(/<li[^>]*>([\s\S]*?)<\/li>/gi, '   • $1\n')
          );

        const currentCounter = counter;
        counter += 1;
        return `${currentCounter}. ${processedContent}`;
      }
      // For regular list items, add a number
      const currentCounter = counter;
      counter += 1;
      return `${currentCounter}. ${liContent}\n`;
    });
  });
}

/**
 * Standardizes job description text by:
 * 1. Preserving bold/strong tags (unless cleanText is true)
 * 2. Preserving list structures (bulleted and numbered lists) (unless cleanText is true)
 * 3. Removing other HTML tags while maintaining line breaks
 * 4. Converting "\n" text to actual newline characters
 *
 * @param description The HTML job description to standardize
 * @param cleanText If true, returns only plain text without any formatting (default: false)
 * @returns Standardized text with preserved formatting or clean text for search purposes
 */
export const standardizeJobDescription = (description: string, cleanText: boolean = false): string => {
  if (!description) return '';

  // First, convert "\n" text to a temporary marker
  let result = description.replace(/\\n/g, '[[NEWLINE_MARKER]]');

  if (cleanText) {
    // For clean text mode, remove all HTML tags but preserve line break logic
    // Add line breaks for HTML elements that create visual breaks (same logic as original)
    result = result
      .replace(/<br\s*\/?>/gi, '<br>\n')
      .replace(/<\/p>/gi, '</p>\n')
      .replace(/<\/div>/gi, '</div>\n')
      .replace(/<\/h[1-6]>/gi, '</h>\n')
      .replace(/<\/li>/gi, '</li>\n');

    // Remove all HTML tags
    result = result.replace(/<[^>]*>/g, '');

    // Convert the temporary newline markers back to actual newline characters
    result = result.replace(/\[\[NEWLINE_MARKER\]\]/g, '\n');

    // Normalize line breaks (remove multiple consecutive line breaks)
    result = result.replace(/\n{3,}/g, '\n\n');

    // Normalize spaces and tabs but keep line breaks
    result = result.replace(/[ \t]+/g, ' ');

    return result.trim();
  }

  // Process lists before removing tags
  // Convert unordered lists (bulleted)
  result = processUnorderedLists(result);

  // Convert ordered lists (numbered)
  result = processOrderedLists(result);

  // Add line breaks for HTML elements that create visual breaks
  result = result
    .replace(/<br\s*\/?>/gi, '<br>\n')
    .replace(/<\/p>/gi, '</p>\n')
    .replace(/<\/div>/gi, '</div>\n')
    .replace(/<\/h[1-6]>/gi, '</h>\n');

  // Temporarily replace bold/strong tags with markers
  result = result
    .replace(/<(b|strong)>/gi, '[[BOLD_START]]')
    .replace(/<\/(b|strong)>/gi, '[[BOLD_END]]');

  // Remove all remaining HTML tags
  result = result.replace(/<[^>]*>/g, '');

  // Restore bold/strong tags
  result = result
    .replace(/\[\[BOLD_START\]\]/g, '<strong>')
    .replace(/\[\[BOLD_END\]\]/g, '</strong>');

  // Convert the temporary newline markers back to actual newline characters
  result = result.replace(/\[\[NEWLINE_MARKER\]\]/g, '\n');

  // Normalize line breaks (remove multiple consecutive line breaks)
  result = result.replace(/\n{3,}/g, '\n\n');

  return result.trim();
};

export const processReformat = (htmlText: string) => handleUnsupportULTags(handleUnsupportLITags(htmlText));

export const getMergeTagsContent = (content: string = '', mergeTags: any = {}) => {
  let result = stripHTMLTags(decodeHtmlEntities(processReformat(content?.replace(new RegExp('</p>', 'g'), '</p>\n'))));

  MERGE_TAGS_PLACE_HOLDERS?.forEach((rawPlaceHolder) => {
    const purePlaceHolder = rawPlaceHolder.split('{{').join('').split('}}').join('');
    const replacedContent = getValueByPath(mergeTags, MergeTagMappingEnum[purePlaceHolder], '');

    result = result
      .split(rawPlaceHolder)
      .join(Array.isArray(replacedContent) ? replacedContent.join(', ') : replacedContent);
  });

  result = result.replace(
    /\$(first_name|first name)\$/g,
    getValueByPath(mergeTags, 'recipient.name', '')?.split(' ')[0]
  );

  return result;
};

const standardizeJobTypeSimilar = (jobType: string) => {
  const [commonValue] =
    Object.entries(SIMILAR_STANDARD_JOB_TYPES).find(([_, variations]) => variations.includes(jobType.toUpperCase())) ||
    [];

  return commonValue || jobType; // Return the job type as is if no standardization is needed
};

export const standardizeJobType = (rawJobType: string = '') => [
  ...new Set(
    (rawJobType || '').split(/,|\/| or /).flatMap((item) => {
      const standardizedItem = item
        .trim()
        .toUpperCase()
        .replace(/[\s\-–_]+/g, '_');

      // If no given value, return empty string
      if (!standardizedItem) {
        return '';
      }
      // return the mapped value
      if (STANDARD_JOB_TYPES[standardizedItem]) {
        return standardizeJobTypeSimilar(standardizedItem);
      }
      console.log('[OTHER STANDARDIZED JOB TYPE]: ', standardizedItem);

      // or "OTHER" if not found
      return 'OTHER';
    })
  ),
];

export const extractJobKeywords = (inputString: string): string[] => {
  let text = inputString.toLowerCase();

  // Extract phrases
  const extractedPhrases = [];
  JOB_PREDEFINED_KEYWORDS.sort()
    .reverse()
    .forEach((phrase) => {
      const regex = new RegExp(`\\b${phrase}\\b`, 'g');
      if (regex.test(text)) {
        extractedPhrases.push(phrase);
        text = text.replace(regex, ''); // Remove the phrase from the text
      }
    });

  // Tokenize the remaining string into words
  const words = text.split(/\W+/);

  // Filter out the stopwords
  const keywords = words.filter((word) => !JOB_STOP_WORDS.includes(word) && word.length > 0);

  // Combine the extracted phrases with the remaining keywords
  return [...extractedPhrases, ...keywords];
};

//currentOffset: (UTC-05:00)
// targetOffset: (UTC+01:00)

export const convertTimezone = (datetimeString, currentOffset) => {
  const date = new Date(datetimeString + 'Z');
  const standardCurrentOffset = currentOffset.replace('(', '').replace(')', '');
  const signCurrent = standardCurrentOffset.startsWith('UTC+') ? 1 : -1;
  const currentOffsetMinutes = Number(standardCurrentOffset.split(':')[1].split(' ')[0].replace(')', ''));
  const currentOffsetHours = Math.abs(Number(standardCurrentOffset.split('UTC')[1].split(':')[0]));

  const currentOffsetTotalMinutes = signCurrent * (currentOffsetHours * 60 + currentOffsetMinutes);
  const targetOffset = -new Date().getTimezoneOffset();
  const offsetDiff = (targetOffset - currentOffsetTotalMinutes) * 60 * 1000;

  const newTime = new Date(date.getTime() + offsetDiff);

  const targetTimezone = Math.floor(targetOffset / 60);
  const targetMinute = (targetOffset / 60 - targetTimezone) * 60;
  const signTargetTimezone = targetTimezone >= 0 ? '+' : '-';
  const formattedDate = newTime
    .toISOString()
    .replace('T', ' ')
    .replace(
      /\.\d{3}Z/,
      ` ${signTargetTimezone}${targetTimezone < 10 ? '0' : ''}${targetTimezone}${
        targetMinute < 10 ? '0' : ''
      }${targetMinute}`
    );
  return formattedDate;
};

export const processingContent = ({
  emailContent,
  organizationId,
  signature = '',
  email = '',
  sourceType = false,
  sourceId = '',
}: {
  emailContent: string;
  organizationId: string;
  signature: string;
  email: string;
  sourceType?: any;
  sourceId?: string;
}) => {
  let addParam = '';
  if (sourceType && sourceType === 'CONTACTLIST') {
    addParam = `&source=CONTACTLIST`;
  }
  const LINK_UNSUBCRIBE = `${APP_CONFIG.API_URL}/emails/unsubscribe-email?email=${email}&organizationId=${organizationId}${addParam}`;
  const unsubcribeContainer = `<center><strong><font face="Arial" size="-3" style="color:rgb(66,66,66)">If you no longer wish to receive emails. Here's where you can <a href="${LINK_UNSUBCRIBE}" style="color:rgb(87,87,87)" target="_blank">unsubscribe</a>.</font></strong></center>`;
  let newEmailContent = emailContent
    ?.replace(new RegExp('><br></span><br>', 'g'), '></span><br>')
    ?.replace(new RegExp('<br><br>', 'g'), '<br>');

  if (!signature) return newEmailContent + unsubcribeContainer;
  const clearedSig = signature;

  return (
    newEmailContent
      .replace(new RegExp('<span class="signature-place-holder"></span>', 'g'), clearedSig) // Interacting with new data
      .replace(new RegExp('<span class="signature-container"><span></span></span>', 'g'), clearedSig) // Interacting with old data
      .replace(new RegExp('<figure', 'g'), '<div') // Remove figure tag
      .replace(new RegExp('</figure', 'g'), '</div') + // Remove figure tag
    unsubcribeContainer
  );
};

export const getSequenceTriggerAt = (scheduleInformation: ScheduleInformationDto = {}) => {
  const { isTriggeredNow, triggerAt, timezone, utc } = scheduleInformation;

  if (isTriggeredNow || !triggerAt) {
    return new Date().toISOString();
  }

  if (timezone) {
    return new Date(moment.tz(triggerAt, timezone).format()).toISOString();
  }

  return new Date(convertTimezone(triggerAt, utc)).toISOString();
};

export const convertCamelToNormalCase = (inputString: string) => {
  return inputString
    .replace(/([a-z])([A-Z])/g, '$1 $2') // Add a space before all capital letters
    .replace(/_/g, ' ') // Replace underscores with spaces
    .toLowerCase() // Convert the string to lowercase
    .replace(/\b\w/g, function (char) {
      return char.toUpperCase();
    }); // Capitalize the first letter of each word
};

/**
 * Removes duplicate objects from an array based on a specific key.
 *
 * @param {Array} array - The array of objects.
 * @param {string|Function} key - The key to check for duplicates.
 * @param {string} preferKey - The key to if it exist the object will be prefered than others
 * @return {Array} A new array with duplicates removed.
 */
export const removeDuplicatesByKey = (array: any[], key: string | Function, preferKey?: string): Array<any> => {
  const seen = new Map();

  array.forEach((item) => {
    const keyValue = typeof key === 'string' ? item[key] : key(item);
    if (!seen.has(keyValue) || (item[preferKey] && !seen.get(keyValue)[preferKey])) {
      seen.set(keyValue, item);
    }
  });

  return Array.from(seen.values());
};

/**
 * Filters out objects from the source array
 * if they have the same key value as objects in the filter array.
 *
 * @param {Array} sourceArray - The array of objects to be filtered.
 * @param {Array} filterArray - The array of objects to filter against.
 * @param {string|Function} key - The key to check for duplicates.
 * @return {Array} A new array with filtered objects.
 */
export const filterOutByKey = (sourceArray: any[], filterArray: any[], key: string | Function): Array<any> => {
  const filterSet = new Set(filterArray.map((item) => (typeof key === 'string' ? item[key] : key(item))));

  return sourceArray.filter((item) => !filterSet.has(typeof key === 'string' ? item[key] : key(item)));
};

export const validateEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@.]+$/;

  return emailRegex.test(email);
};

//convert this string "2020-10-01" to an object like {
// month: 10,
// year: 2022,
// }
export const convertDateStringToObject = (dateString) => {
  const [year, month, day] = dateString.split('-').map(Number);

  return {
    day,
    month,
    year,
  };
};

/**
 * Strips non-serializable elements from an object to make it serializable by v8
 * @param obj The object to process
 * @returns A new object with non-serializable elements removed or transformed
 */
export const stripNonSerializable = (obj: any): any => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  // Handle special primitive types
  if (typeof obj === 'function') {
    return undefined; // Skip functions
  }

  if (typeof obj === 'symbol') {
    return undefined; // Skip symbols
  }

  if (typeof obj === 'bigint') {
    return Number(obj); // Convert BigInt to Number
  }

  // Handle non-object types (remaining primitives)
  if (typeof obj !== 'object') {
    return obj;
  }

  // Handle special objects
  if (obj instanceof Error) {
    // Convert Error to a simple object with message and name
    return {
      name: obj.name,
      message: obj.message,
      stack: obj.stack
    };
  }

  if (obj instanceof Date) {
    return new Date(obj); // Clone Date objects
  }

  if (obj instanceof RegExp) {
    return new RegExp(obj.source, obj.flags); // Clone RegExp objects
  }

  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => stripNonSerializable(item));
  }

  // Handle normal objects
  const result = {};
  const seen = new WeakSet();

  const processObject = (object: any, target: any) => {
    if (object === null || object === undefined) {
      return;
    }

    // Skip circular references
    if (seen.has(object)) {
      return;
    }

    seen.add(object);

    // Check if object has custom toJSON method
    if (typeof object.toJSON === 'function') {
      try {
        const jsonResult = object.toJSON();
        Object.assign(target, stripNonSerializable(jsonResult));
        return;
      } catch (e) {
        // If toJSON fails, continue with normal processing
      }
    }

    // Process each property
    for (const key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key)) {
        const value = object[key];

        // Skip functions and symbols
        if (typeof value === 'function' || typeof value === 'symbol') {
          continue;
        }

        // Handle null values
        if (value === null) {
          target[key] = null;
          continue;
        }

        // Handle objects
        if (typeof value === 'object') {
          if (Array.isArray(value)) {
            target[key] = value.map(item => stripNonSerializable(item));
          } else if (value instanceof Date) {
            target[key] = new Date(value);
          } else if (value instanceof RegExp) {
            target[key] = new RegExp(value.source, value.flags);
          } else if (value instanceof Error) {
            target[key] = {
              name: value.name,
              message: value.message,
              stack: value.stack
            };
          } else if (seen.has(value)) {
            // Handle circular references
            target[key] = '[Circular]';
          } else {
            // Process nested objects
            target[key] = {};
            processObject(value, target[key]);
          }
        } else if (typeof value === 'bigint') {
          // Convert BigInt to Number
          target[key] = Number(value);
        } else {
          // Copy primitive values
          target[key] = value;
        }
      }
    }
  };

  processObject(obj, result);
  return result;
};

/**
 * Measures the size of an object in bytes using v8 serialization
 * @param data The data to measure
 * @returns The size in bytes
 */
export const sizeOf = (data: any): number => {
  try {
    // Strip non-serializable elements before serializing
    const cleanData = stripNonSerializable(data);
    return v8.serialize(cleanData).length;
  } catch (error: any) {
    console.warn('Error measuring size of object:', error.message);
    return 0;
  }
};

export const convertToLuceneQuery = (sqlQuery: string): string =>
  sqlQuery
    .replace(/\b(\w+)\s*=\s*false\b/g, '$1:false') // Convert `field=false` to `field:false`
    .replace(/\b(\w+)\s*=\s*true\b/g, '$1:true') // Convert `field=true` to `field:true`
    .replace(/\bNOT\s+(\w+)\s*=\s*'([^']+)'/g, 'NOT $1:"$2"') // Convert `NOT field='value'`
    .replace(/\b(\w+)\s*=\s*'([^']+)'/g, '$1:"$2"') // Convert `field='value'`
    .replace(/\b(\w+)\s*=\s*(\d+)\b/g, '$1:$2') // Convert `field=number`
    .replace(/\b(\w+)\s+LIKE\s+'%([^']+)%'/g, '$1:*$2*'); // Convert `field LIKE '%value%'`

export const UserStatusByOrgStatus = {
  [OrganizationStatusEnum.APPROVED]: UserStatus.ACTIVE,
  [OrganizationStatusEnum.PENDING]: UserStatus.PENDING,
  [OrganizationStatusEnum.REJECTED]: UserStatus.DEACTIVATED,
};

/**
 * Calculate feature permissions for a user based on role and permissions
 * @param roleCode The role code of the user
 * @param permissions The permissions of the user
 * @returns An array of features that the user has access to
 */
export function calculateUserFeatures(roleCode: string, permissions: string[]): FeatureEnum[] {
  const userFeatures = new Set<FeatureEnum>();

  if (roleCode && RoleFeatureMapping[roleCode]) {
    RoleFeatureMapping[roleCode].forEach((feature: any) => {
      userFeatures.add(feature);
    });
  }

  if (permissions && permissions.length > 0) {
    Object.entries(FeatureToPermissionsMapping).forEach(([feature, featurePermissions]) => {
      if (featurePermissions.some((permission) => permissions.includes(permission))) {
        userFeatures.add(feature as FeatureEnum);
      }
    });
  }

  return Array.from(userFeatures);
}

import { Readable } from 'stream';
import * as csv from 'csv-parser';
import { CsvRowDto } from 'src/modules/user/dto/create-contact-list.dto';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

export const parseCSVStream = async (csvFile: Express.Multer.File, results: any[], errorsArray: any[], fullData: any[]) => {
  return new Promise<void>((resolve, reject) => {
    let index = 0;
    const stream = Readable.from(csvFile.buffer.toString());

    stream
      .pipe(csv())
      .on('data', async (data) => {
        index++;
        await processRow(data, index, results, errorsArray, fullData);
      })
      .on('end', () => resolve())
      .on('error', (error) => reject(error));
  });
};

export const processRow = async (data: any, index: number, results: any[], errorsArray: any[], fullData: any[]) => {
  const row = plainToClass(CsvRowDto, data);
  const errors = await validate(row);
  fullData.push(row)
  if (errors.length > 0) {
    const errMessages = errors.map((err) => Object.values(err.constraints).join(', '));
    errorsArray.push({ ...row, index: index, errors: errMessages.join(', ') });
  } else {
    results.push(row);
  }
};

export const handleProcessCSV = async (csvFile: Express.Multer.File) => {
  const results = [];
  const errorsArray = [];
  const fullData = []
  await parseCSVStream(csvFile, results, errorsArray, fullData);
  const data = {
    data: results,
    errors: errorsArray,
    fullData: fullData,
  };

  return data;
};

export enum LanguageCode {
  German = 'de',
  United_States = 'en',
}

export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  ALL = 'ALL',
}

export enum StatusCode {
  GET_ACCESS_TOKEN_SUCCESSFULLY = 1000,
  GET_ACCESS_REFRESH_TOKEN_SUCCESSFULLY = 1001,
  USER_NOT_FOUND = 1002,
  UPDATE_USER_UNSUCCESSFULLY = 1003,
  UPLOAD_IMAGE_SUCCESSFULLY = 1004,
  UPLOAD_IMAGE_UNSUCCESSFULLY = 1005,
  UNAUTHORIZED = 1006,
  REGISTER_SUCCESSFULLY = 1007,
  USER_EXIST = 1008,
}

export const OAUTH_EXCEPTION_INSTAGRAM = 190;

export const ADV_PAYMENT_FEE = 0;
export const IF_PAYMENT_FEE = 0.1;

export const ResponseMessage = {
  Common: {
    BAD_REQUEST: 'Bad Request. Try again',
    GET_SUCCESS: 'Get success',
    NOTHING_TO_SAVE: 'Nothing to save',
    DELETED: 'Deleted',
    FORBIDDEN_RESOURCE: `You don't have the permission to access this resource`,
    NOT_FOUND: 'Not found. Try again',
    UNAUTHORIZED: 'Unauthorized. Try again',
    FORBIDDEN: 'Forbidden. Try again',
  },
  User: {
    LOGIN_SUCCESSFULLY: 'Login successfully',
    REGISTER_SUCCESSFULLY: 'Register successfully',
    REFRESH_TOKEN: 'Refresh Token',
    UPDATED_DATA: 'Updated user successfully in database',
    CANNOT_UPDATE: 'Cannot update this user',
    NOT_FOUND: 'User not found',
    GET_SUCCESS: 'Successfully get user data',
    UPDATE_USER_SUCCESSFULLY: 'Update user successfully',
    GET_ME: 'Get my information successfully',
    REMOVE_USER: 'Remove user successfully',
    GET_ACCESS_TOKEN_SUCCESSFULLY: 'Get access token successfully',
    GET_USER_DETAIL: 'Get user information successfully',
    DELETE_USER_SUCCESSFULLY: 'Delete user successfully',
  },
};

export const ApiSummary = {
  User: {
    SEND_OTP: 'Send OTP',
    VERIFY_EMAIL: 'Verify email',
    UPDATE_USER: 'Update user information',
    UPDATE_MAIN_PICTURE_USER: 'Update main picture of user',
    GET_ME: 'Get my information',
    GET_JOBS_SUCCESSFULLY: 'Get Jobs successfully',
    REMOVE_USER: 'Remove User successfully',
    GET_USER_DETAIL: 'Get user information',
    GET_INSTAGRAM_LOCATION: 'Get instagram location',
    REGISTER_AMBASSADOR: 'Register ambassador',
    GET_USER_OTP: 'Get User Otp',
  },
};

export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/;
export const USERNAME_REGEX = /^[a-zA-Z0-9_.@]+$/;

export const DEFAULT_TIMEOUT = 1000 * 60 * 3;
export const DEFAULT_REQUEST_SIZE_LIMIT = '50mb';
export const MIN_LENGTH_OF_ARRAY = 1;

export const PUBLIC_DIR = 'public';

export const TAB_NUM = 9;

export enum STOP_BY_ACTOR {
  SYSTEM = 'SYSTEM',
}

export const CACHE_REDIS_UNIPILE_ACCOUNT_NAME = "unipile_account_items"

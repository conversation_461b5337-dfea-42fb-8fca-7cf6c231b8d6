import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { PermissionResource } from './permission.constant';
import { RoleEnum } from 'src/modules/user/entities/role.entity';

// Permission Features
export enum FeatureEnum {
  SEQUENCE = 'SEQUENCE',
  CRM = 'CRM',
  USER_MANAGEMENT = 'USER_MANAGEMENT',
  DUPLICATED_JOB = 'DUPLICATED_JOB',
  REPORTED_AGENCY = 'REPORTED_AGENCY',
  COMPANY_ONBOARDING = 'COMPANY_ONBOARDING',
  COMPANY_APPROVAL = 'COMPANY_APPROVAL',
  DASHBOARD = 'DASHBOARD',
  BULLHORN = 'BULLHORN',
  TASK = 'TASK',
  SEARCH = 'SEARCH',
  SYNC = 'SYNC',
  MY_LEADS = 'MY_LEADS',
  MAILBOX = 'MAILBOX',
  MANUAL_LEADS = 'MANUAL_LEADS',
  CONTACT_FINDER = 'CONTACT_FINDER',
  SETTINGS = 'SETTINGS',
  REPORTING = 'REPORTING',
  COMPANY = 'COMPANY',
  VIEW_AS = 'VIEW_AS',
  CREDIT_MANAGEMENT = 'CREDIT_MANAGEMENT',
  SUBSCRIPTION = 'SUBSCRIPTION',
}

export enum DefaultFeatureEnum {
  GLOBAL = 'GLOBAL',
  USER = 'USER',
}

// Mapping from feature to detailed permissions
// TODO: Update permission resource when it is defined
export const FeatureToPermissionsMapping: Record<string, string[]> = {
  GLOBAL: [PermissionResource[ResourceEnum.VIEW_AS].Read],
  USER: [
    PermissionResource[ResourceEnum.USER_MANAGEMENT].Read,
    PermissionResource[ResourceEnum.USER_MANAGEMENT].Write,
    PermissionResource[ResourceEnum.USER_MANAGEMENT].Invite,
  ],
  [FeatureEnum.SEQUENCE]: [
    PermissionResource[ResourceEnum.SEQUENCE].Read,
    PermissionResource[ResourceEnum.SEQUENCE].Write,
    PermissionResource[ResourceEnum.CRM].Read,
    PermissionResource[ResourceEnum.MY_LEADS].Read,
    PermissionResource[ResourceEnum.CONTACT_LIST].Read,
    PermissionResource[ResourceEnum.CONTACT_LIST].Write,
  ],
  [FeatureEnum.CRM]: [
    PermissionResource[ResourceEnum.CRM].Read,
    PermissionResource[ResourceEnum.CRM].Write,
  ],
  [FeatureEnum.USER_MANAGEMENT]: [
    PermissionResource[ResourceEnum.USER_MANAGEMENT].Read,
    PermissionResource[ResourceEnum.USER_MANAGEMENT].Write,
    PermissionResource[ResourceEnum.USER_MANAGEMENT].Invite,
  ],
  [FeatureEnum.DUPLICATED_JOB]: [
    PermissionResource[ResourceEnum.DUPLICATED_JOBS].Read,
    PermissionResource[ResourceEnum.DUPLICATED_JOBS].Write,
  ],
  [FeatureEnum.REPORTED_AGENCY]: [
    PermissionResource[ResourceEnum.REPORTED_AGENCIES].Read,
    PermissionResource[ResourceEnum.REPORTED_AGENCIES].Write,
  ],
  [FeatureEnum.DASHBOARD]: [PermissionResource[ResourceEnum.DASHBOARD].Read],
  [FeatureEnum.CREDIT_MANAGEMENT]: [PermissionResource[ResourceEnum.CREDIT_MANAGEMENT].Read],
  [FeatureEnum.BULLHORN]: [
    PermissionResource[ResourceEnum.JOB_SYNC].Read,
    PermissionResource[ResourceEnum.JOB_SYNC].Write,
  ],
  [FeatureEnum.TASK]: [
    PermissionResource[ResourceEnum.TASK].Read,
    PermissionResource[ResourceEnum.TASK].Write,
  ],
  [FeatureEnum.SEARCH]: [
    PermissionResource[ResourceEnum.JOB_SEARCH].Read,
    PermissionResource[ResourceEnum.JOB_SEARCH].Write,
  ],
  [FeatureEnum.SYNC]: [
    PermissionResource[ResourceEnum.JOB_SYNC].Read,
    PermissionResource[ResourceEnum.JOB_SYNC].Write,
  ],
  [FeatureEnum.MY_LEADS]: [
    PermissionResource[ResourceEnum.MY_LEADS].Read,
    PermissionResource[ResourceEnum.MY_LEADS].Write,
  ],
  [FeatureEnum.MAILBOX]: [
    PermissionResource[ResourceEnum.JOB_LEAD].Read, // Don't know why FE getting job lead
    PermissionResource[ResourceEnum.MAILBOX].Read,
    PermissionResource[ResourceEnum.MAILBOX].Write,
  ],
  [FeatureEnum.MANUAL_LEADS]: [
    PermissionResource[ResourceEnum.MANUAL_LEADS].Read,
    PermissionResource[ResourceEnum.MANUAL_LEADS].Write,
  ],
  [FeatureEnum.CONTACT_FINDER]: [
    PermissionResource[ResourceEnum.CONTACT_FINDER].Read,
    PermissionResource[ResourceEnum.EMAIL_FINDER].Read,
    PermissionResource[ResourceEnum.EMAIL_VERIFICATION].Read,
  ],
  [FeatureEnum.SETTINGS]: [
    PermissionResource[ResourceEnum.SETTINGS].Read,
    PermissionResource[ResourceEnum.SETTINGS].Write,
  ],
  [FeatureEnum.REPORTING]: [PermissionResource[ResourceEnum.REPORTING].Read],
  [FeatureEnum.COMPANY]: [
    PermissionResource[ResourceEnum.COMPANY_ONBOARDING].Read,
    PermissionResource[ResourceEnum.COMPANY_ONBOARDING].Write,
    PermissionResource[ResourceEnum.COMPANY_APPROVAL].Read,
    PermissionResource[ResourceEnum.COMPANY_APPROVAL].Write,
  ],
  [FeatureEnum.SUBSCRIPTION]: [
    PermissionResource[ResourceEnum.SUBSCRIPTION].Read,
    PermissionResource[ResourceEnum.SUBSCRIPTION].Write,
  ],
  [FeatureEnum.VIEW_AS]: [PermissionResource[ResourceEnum.VIEW_AS].Read],
};

export const PermissionToFeatureMapping: Record<string, string> = {};

Object.entries(FeatureToPermissionsMapping).forEach(([feature, permissions]) => {
  permissions.forEach((permission) => {
    PermissionToFeatureMapping[permission] = feature;
  });
});

const BASIC_USER_PERMISSIONS = [
  ...Object.values(DefaultFeatureEnum),
  FeatureEnum.SEQUENCE,
  FeatureEnum.CRM,
  FeatureEnum.DASHBOARD,
  FeatureEnum.TASK,
  FeatureEnum.SEARCH,
  FeatureEnum.SYNC,
  FeatureEnum.MY_LEADS,
  FeatureEnum.MANUAL_LEADS,
  FeatureEnum.MAILBOX,
  FeatureEnum.CONTACT_FINDER,
  FeatureEnum.SETTINGS,
];

const MANAGEMENT_PERMISSIONS = [...BASIC_USER_PERMISSIONS, FeatureEnum.REPORTING];

const ADMIN_PERMISSIONS = [
  ...MANAGEMENT_PERMISSIONS,
  FeatureEnum.USER_MANAGEMENT,
  FeatureEnum.CREDIT_MANAGEMENT,
  FeatureEnum.REPORTING,
  FeatureEnum.BULLHORN,
  FeatureEnum.COMPANY,
  FeatureEnum.SUBSCRIPTION,
];

const SALES_PERMISSION = [...ADMIN_PERMISSIONS, FeatureEnum.REPORTED_AGENCY];
export const RoleFeatureMapping = {
  [RoleEnum.SUPER_ADMIN]: [...Object.values(DefaultFeatureEnum), ...Object.values(FeatureEnum)], // Super Admin has all features
  [RoleEnum.ADMIN]: ADMIN_PERMISSIONS,
  [RoleEnum.MANAGEMENT]: MANAGEMENT_PERMISSIONS,
  [RoleEnum.BASIC_USER]: BASIC_USER_PERMISSIONS,
  [RoleEnum.SALES]: SALES_PERMISSION,
};

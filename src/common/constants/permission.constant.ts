import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

export const PermissionResource = {
  [ResourceEnum.USER_MANAGEMENT]: {
    Read: `${ResourceEnum.USER_MANAGEMENT}.Read`,
    Write: `${ResourceEnum.USER_MANAGEMENT}.Write`,
    Invite: `${ResourceEnum.USER_MANAGEMENT}.Invite`,
  },
  [ResourceEnum.JOB_SEARCH]: {
    Read: `${ResourceEnum.JOB_SEARCH}.Read`,
    Write: `${ResourceEnum.JOB_SEARCH}.Write`,
  },
  [ResourceEnum.REPORTED_AGENCIES]: {
    Read: `${ResourceEnum.REPORTED_AGENCIES}.Read`,
    Write: `${ResourceEnum.REPORTED_AGENCIES}.Write`,
  },
  [ResourceEnum.JOB_LEAD]: {
    Read: `${ResourceEnum.JOB_LEAD}.Read`,
    Write: `${ResourceEnum.JOB_LEAD}.Write`,
  },
  [ResourceEnum.JO<PERSON>_SYNC]: {
    Read: `${ResourceEnum.JOB_SYNC}.Read`,
    Write: `${ResourceEnum.JOB_SYNC}.Write`,
  },
  [ResourceEnum.EMAIL_VERIFICATION]: {
    Read: `${ResourceEnum.EMAIL_VERIFICATION}.Read`,
    Write: `${ResourceEnum.EMAIL_VERIFICATION}.Write`,
  },
  [ResourceEnum.EMAIL_FINDER]: {
    Read: `${ResourceEnum.EMAIL_FINDER}.Read`,
    Write: `${ResourceEnum.EMAIL_FINDER}.Write`,
  },
  [ResourceEnum.STAFF_PERFORMANCE]: {
    Read: `${ResourceEnum.STAFF_PERFORMANCE}.Read`,
    Write: `${ResourceEnum.STAFF_PERFORMANCE}.Write`
  },
  [ResourceEnum.COMPANY_ONBOARDING]: {
    Read: `${ResourceEnum.COMPANY_ONBOARDING}.Read`,
    Write: `${ResourceEnum.COMPANY_ONBOARDING}.Write`
  },
  [ResourceEnum.COMPANY_APPROVAL]: {
    Read: `${ResourceEnum.COMPANY_APPROVAL}.Read`,
    Write: `${ResourceEnum.COMPANY_APPROVAL}.Write`
  },
  [ResourceEnum.REPORTING]: {
    Read: `${ResourceEnum.REPORTING}.Read`,
    Write: `${ResourceEnum.REPORTING}.Write`
  },
  [ResourceEnum.DUPLICATED_JOBS]: {
    Read: `${ResourceEnum.DUPLICATED_JOBS}.Read`,
    Write: `${ResourceEnum.DUPLICATED_JOBS}.Write`
  },
  [ResourceEnum.TEAM_MANAGEMENT]: {
    Read: `${ResourceEnum.TEAM_MANAGEMENT}.Read`,
    Write: `${ResourceEnum.TEAM_MANAGEMENT}.Write`
  },
  [ResourceEnum.ACCOUNTS]: {
    Read: `${ResourceEnum.ACCOUNTS}.Read`,
    Write: `${ResourceEnum.ACCOUNTS}.Write`
  },
  [ResourceEnum.CONTRACT]: {
    Read: `${ResourceEnum.CONTRACT}.Read`,
    Write: `${ResourceEnum.CONTRACT}.Write`
  },
  [ResourceEnum.CRM]: {
    Read: `${ResourceEnum.CRM}.Read`,
    Write: `${ResourceEnum.CRM}.Write`
  },
  [ResourceEnum.CONTACT_LIST]: {
    Read: `${ResourceEnum.CONTACT_LIST}.Read`,
    Write: `${ResourceEnum.CONTACT_LIST}.Write`
  },
  [ResourceEnum.SEQUENCE]: {
    Read: `${ResourceEnum.SEQUENCE}.Read`,
    Write: `${ResourceEnum.SEQUENCE}.Write`
  },
  [ResourceEnum.TASK]: {
    Read: `${ResourceEnum.TASK}.Read`,
    Write: `${ResourceEnum.TASK}.Write`
  },
  [ResourceEnum.SEARCH]: {
    Read: `${ResourceEnum.SEARCH}.Read`,
    Write: `${ResourceEnum.SEARCH}.Write`
  },
  [ResourceEnum.SYNC]: {
    Read: `${ResourceEnum.SYNC}.Read`,
    Write: `${ResourceEnum.SYNC}.Write`
  },
  [ResourceEnum.MY_LEADS]: {
    Read: `${ResourceEnum.MY_LEADS}.Read`,
    Write: `${ResourceEnum.MY_LEADS}.Write`
  },
  [ResourceEnum.MAILBOX]: {
    Read: `${ResourceEnum.MAILBOX}.Read`,
    Write: `${ResourceEnum.MAILBOX}.Write`
  },
  [ResourceEnum.MANUAL_LEADS]: {
    Read: `${ResourceEnum.MANUAL_LEADS}.Read`,
    Write: `${ResourceEnum.MANUAL_LEADS}.Write`
  },
  [ResourceEnum.CONTACT_FINDER]: {
    Read: `${ResourceEnum.CONTACT_FINDER}.Read`,
    Write: `${ResourceEnum.CONTACT_FINDER}.Write`
  },
  [ResourceEnum.SETTINGS]: {
    Read: `${ResourceEnum.SETTINGS}.Read`,
    Write: `${ResourceEnum.SETTINGS}.Write`
  },
  [ResourceEnum.DASHBOARD]: {
    Read: `${ResourceEnum.DASHBOARD}.Read`,
    Write: `${ResourceEnum.DASHBOARD}.Write`
  },
  [ResourceEnum.VIEW_AS]: {
    Read: `${ResourceEnum.VIEW_AS}.Read`,
    Write: `${ResourceEnum.VIEW_AS}.Write`
  },
  [ResourceEnum.CREDIT_MANAGEMENT]: {
    Read: `${ResourceEnum.CREDIT_MANAGEMENT}.Read`,
    Write: `${ResourceEnum.CREDIT_MANAGEMENT}.Write`
  },
  [ResourceEnum.SUBSCRIPTION]: {
    Read: `${ResourceEnum.SUBSCRIPTION}.Read`,
    Write: `${ResourceEnum.SUBSCRIPTION}.Write`
  },
}

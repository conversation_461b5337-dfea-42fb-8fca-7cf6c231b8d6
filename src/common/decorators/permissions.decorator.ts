import { SetMetadata } from '@nestjs/common';

export enum PermissionLogic {
  OR = 'OR',
  AND = 'AND',
}

export interface PermissionOptions {
  permissions: string[];
  logic: PermissionLogic;
}

/**
 * Permission decorator for checking user permissions
 * @param permissionOrOptions A single permission string or an options object with multiple permissions
 * @returns Decorator function
 *
 * @example
 * // Check for a single permission
 * @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
 *
 * // Check for multiple permissions with OR logic (user needs at least one)
 * @Permission({
 *   permissions: [
 *     PermissionResource[ResourceEnum.USER_MANAGEMENT].Read,
 *     PermissionResource[ResourceEnum.USER_MANAGEMENT].Write
 *   ],
 *   logic: PermissionLogic.OR
 * })
 *
 * // Check for multiple permissions with AND logic (user needs all)
 * @Permission({
 *   permissions: [
 *     PermissionResource[ResourceEnum.USER_MANAGEMENT].Read,
 *     PermissionResource[ResourceEnum.CRM].Read
 *   ],
 *   logic: PermissionLogic.AND
 * })
 */
export const Permission = (permissionOrOptions: string | PermissionOptions) => {
  // If it's a string, convert to options object with single permission
  const options = typeof permissionOrOptions === 'string'
    ? { permissions: [permissionOrOptions], logic: PermissionLogic.OR }
    : permissionOrOptions;

  return SetMetadata('permission', options);
};

import { roundDownNumber } from '../utils/helpers.util';

export const contentVerifyEmail = (params) => {
  return `
  <!DOCTYPE html>
  <html lang="en">
  
  <head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>VMO Email Verification</title>
  
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;700&display=swap" rel="stylesheet" />
  <style>
    html,
    body {
      margin: 0;
      padding: 0;
      font-family: "Montserrat", sans-serif;
      font-size: 16px;
    }
  </style>
  </head>
  
<body>
<div style="padding:58px 83px 145px 53px;background-color:#fff;width: 600px;margin: auto;">
  <div style="height:35px;width:73.8px;font-weight:600;margin: auto;margin-bottom: 80px;font-size:28px">hype </div>
  <span style="font-family:SegoeUI-Semibold;font-size:12px;font-weight:600;font-stretch:normal;font-style:normal;line-height:1;letter-spacing:0.5px;color:#fb3593;margin-bottom: 15px;text-align:left;width:600px">REGISTRATION</span>
  <div style="font-family:SegoeUI-Semibold;font-size:28px;font-weight:600;font-stretch:normal;font-style:normal;line-height:1;letter-spacing:normal;color:#333;margin-bottom:55px;padding-top: 15px;text-align:left;width:600px">You are almost done!</div>
  <span class="im" style="
">
    <div style="margin: auto;width:330px;height:50px;padding:11px 31.6px 11px 32.5px;border-radius:8px;background-color:#fb3593;display:flex;margin-bottom:55px">
      <span style="font-family:SegoeUI-Semibold;font-size:14px;font-weight:500;font-stretch:normal;font-style:normal;line-height:normal;letter-spacing:0.16px;text-align:center;color:#fff;margin: auto;">
        <a style="text-decoration:none;color:white" href="${params.refLink}">Click this link to confirm your e-mail </a>
      </span>
    </div>
    <div style="width:600px;height:1px;background-color:#d8d8d8;margin-bottom:20px"></div>
  </span>
  <div style="width:600px">
    <span style="height:19px;font-weight:600;width:31px;font-size:15px">hype </span>
    <span style="width:75px;height:31px;font-family:SegoeUI-Semibold;font-size:11px;font-weight:600;font-stretch:normal;font-style:normal;line-height:1;letter-spacing:0.2px;text-align:right;color:#a6a6a6;float:right">Unsubscribe</span>
    <div class="yj6qo"></div>
    <div class="adL"></div>
  </div>
  <div class="adL"></div>
</div>`;
};

export const contentFeedBackEmail = (content) => {
  return `
  <!DOCTYPE html>
  <html lang="en">
  
  <head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>VMO Email Verification</title>
  
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;700&display=swap" rel="stylesheet" />
  <style>
    html,
    body {
      margin: 0;
      padding: 0;
      font-family: "Montserrat", sans-serif;
      font-size: 16px;
    }
  </style>
  </head>
  
<body>
    <div>
      <p>
        ${content}
      </p>
    </div>
</body>`;
};

export const sendInvoiceEmail = (params) => {
  return `<!DOCTYPE html>
  <html lang="en">
  
  <head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>VMO Email Verification</title>
  
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;700&display=swap" rel="stylesheet" />
  <style>
    html,
    body {
      margin: 0;
      padding: 0;
      font-family: "Montserrat", sans-serif;
      font-size: 16px;
    }
  </style>
  </head>
  
<body>
<div style="padding:58px 83px 145px 53px;background-color:#fff;width: 600px;margin: auto;">
  <div style="height:35px;width:73.8px;margin: auto;margin-bottom: 80px;font-size:28px"><b>hype</b>society</div>
  
  <div style="line-height: 1.8; margin-bottom: 70px">
    <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;text-align:left;width:600px">BILL TO</div>
    <div style="font-size:10px;;font-stretch:normal;font-style:normal;line-height:1;letter-spacing:normal;color:#333;padding-top: 5px;text-align:left;width:600px"><b>${
      params.companyName
    }</b></div>
    <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;text-align:left;width:600px">${
      params.address
    }</div>
    <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;text-align:left;width:600px">Dubai</div>
    <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;text-align:left;width:600px">UAE</div>
  </div>
  
  <table style="width:340px; height:200px;font-weight: 500">
    <tr>
    <th>
      <div style="font-size:28px;font-weight:600;font-stretch:normal;font-style:normal;line-height:1;letter-spacing:normal;color:#333;padding-top: 15px;text-align:left">INVOICE</div>
    </th>
    <th style="min-width: 100px; padding-left: 10px">
       <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;padding-top: 15px">${
         params.fullCode
       }</div>
    </th>
    <th>
       <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;padding-top: 15px;text-align:right">${
         params.currentDate
       }</div>
    </th>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left; margin-top:30px">INFLUENCER CAMPAIGN</div>
    </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left">${
        params.name
      }</div>
      </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left;margin-bottom: 25px">${
        params.code
      }</div>
      </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left">Subtotal</div>
      </td>
      <td>
      </td>
      <td>
        <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:right">$${
          params.subTotal
        }</div>
      </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left">Service 0%</div>
      </td>
      <td>
      </td>
      <td>
        <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:right">$${roundDownNumber(
          params.serviceFee,
        )}</div>
      </td>
    </tr>
    <tr style="">
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left;margin-bottom: 25px">VAT 0%</div>
      </td>
      <td>
      </td>
      <td>
        <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:right;margin-bottom: 25px">$0.00</div>
      </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:20px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left;">TOTAL</div>
      </td>
      <td>
      </td>
      <td>
        <div style="font-size:20px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:right">$${
          params.total
        }</div>
      </td>
    </tr>
    <tr>
      <td>
      </td>
      <td>
      </td>
      <td>
        <div style="font-size:9px;font-weight:550;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:right">(1$ = 3.6725 AED)</div>
      </td>
    </tr>
  </table>
  <div style="width:600px;margin-top:30px">
    <span style="height:10px;width:31px;font-size:15px">Paid with ${params.paymentMethod}</span>
    <span style="width:200px;height:40px;font-size:11px;font-stretch:50%;font-style:normal;line-height:1;letter-spacing:0.2px;text-align:right;float:right; margin-top:70px">
      <div style="font-size:10px;margin-bottom:5px;font-weight:550"><b>hypesociety</b></div>
      <div style="font-size:10px;margin-bottom:20px;font-weight:500">WE INFLUENCE</div>  
      <div style="font-size:10px;margin-bottom:5px;font-weight:500">Licence</div>  
      <div style="font-size:10px;margin-bottom:20px;font-weight:500">DMCC-871044</div>  
      <div style="font-size:10px;margin-bottom:5px;font-weight:500">DMCC Business Centre</div>  
      <div style="font-size:10px;margin-bottom:5px;font-weight:500">Unit no. BA1076, Level no. 1</div>  
      <div style="font-size:10px;margin-bottom:20px;font-weight:500">Dubai, UAE</div>  
      <div style="font-size:10px;margin-bottom:5px;font-weight:500">Support Whatsapp</div>  
      <div style="font-size:10px;margin-bottom:20px;font-weight:500">+971 585432026</div>  
      <div style="font-size:10px;margin-bottom:5px;font-weight:500"><EMAIL></div>  
      <div style="font-size:10px;margin-bottom:5px;font-weight:600">www.hypesociety.com</div>  
      </span>
  </div>
</div>`;
};

export const sendInvoiceEmailIF = (params) => {
  return `<!DOCTYPE html>
  <html lang="en">
  
  <head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>VMO Email Verification</title>
  
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;700&display=swap" rel="stylesheet" />
  <style>
    html,
    body {
      margin: 0;
      padding: 0;
      font-family: "Montserrat", sans-serif;
      font-size: 16px;
    }
  </style>
  </head>
  
<body>
<div style="padding:58px 83px 145px 53px;background-color:#fff;width: 600px;margin: auto;">
  <div style="height:35px;width:73.8px;margin: auto;margin-bottom: 70px;font-size:28px"><b>hype</b>society</div>

  <div style="line-height: 1.8; margin-bottom: 70px">
    <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;text-align:left;width:600px">BILL TO</div>
    <div style="font-size:10px;;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;padding-top: 5px;text-align:left;width:600px"><b>${params.userName}</b></div>
    <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;text-align:left;width:600px">${params.address}</div>
    <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;text-align:left;width:600px">Dubai</div>
    <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;text-align:left;width:600px">UAE</div>
  </div>

  <table style="width:340px; height:200px;font-weight: 500">
    <tr>
    <th>
      <div style="font-size:28px;font-weight:600;font-stretch:normal;font-style:normal;line-height:1;letter-spacing:normal;color:#333;padding-top: 15px;text-align:left">INVOICE</div>
    </th>
    <th style="min-width: 120px; padding-left: 10px">
       <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;padding-top: 15px;text-align:left;">${params.fullCode}</div>
    </th>
    <th>
       <div style="font-size:10px;font-weight:500;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;padding-top: 15px;text-align:right">${params.currentDate}</div>
    </th>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left">INFLUENCER CAMPAIGN</div>
    </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left">${params.name}</div>
      </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left;margin-bottom: 50px">${params.code}</div>
      </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left">Subtotal</div>
      </td>
      <td>
      </td>
      <td>
        <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:right">$${params.subTotal}</div>
      </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left;margin-bottom: 25px">VAT 0%</div>
      </td>
      <td>
      </td>
      <td>
        <div style="font-size:10px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:right;margin-bottom: 25px">$0.00</div>
      </td>
    </tr>
    <tr>
      <td>
      <div style="font-size:20px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:left">TOTAL</div>
      </td>
      <td>
      </td>
      <td>
        <div style="font-size:20px;font-weight:600;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:right">$${params.subTotal}</div>
      </td>
    </tr>
    <tr>
      <td>
      </td>
      <td>
      </td>
      <td>
        <div style="font-size:9px;font-weight:550;font-stretch:normal;font-style:normal;letter-spacing:normal;color:#333;text-align:right">(1$ = 3.6725 AED)</div>
      </td>
    </tr>
  </table>
  <div style="width:600px;margin-top:30px">
  <span style="height:10px;width:31px;font-size:15px">Paid with ${params.paymentMethod}</span>
  <span style="width:200px;height:40px;font-size:11px;font-stretch:50%;font-style:normal;line-height:1;letter-spacing:0.2px;text-align:right;float:right; margin-top:70px">
    <div style="font-size:10px;margin-bottom:5px;font-weight:550"><b>hypesociety</b></div>
    <div style="font-size:10px;margin-bottom:20px;font-weight:500">WE INFLUENCE</div>  
    <div style="font-size:10px;margin-bottom:5px;font-weight:500">Licence</div>  
    <div style="font-size:10px;margin-bottom:20px;font-weight:500">DMCC-871044</div>  
    <div style="font-size:10px;margin-bottom:5px;font-weight:500">DMCC Business Centre</div>  
    <div style="font-size:10px;margin-bottom:5px;font-weight:500">Unit no. BA1076, Level no. 1</div>  
    <div style="font-size:10px;margin-bottom:20px;font-weight:500">Dubai, UAE</div>  
    <div style="font-size:10px;margin-bottom:5px;font-weight:500">Support Whatsapp</div>  
    <div style="font-size:10px;margin-bottom:20px;font-weight:500">+971 585432026</div>  
    <div style="font-size:10px;margin-bottom:5px;font-weight:500"><EMAIL></div>  
      <div style="font-size:10px;margin-bottom:5px;font-weight:600">www.hypesociety.com</div>  
      </span>
  </div>
</div>`;
};

export const contentResetPasswordEmail = (params) => {
  return `
   <!DOCTYPE html>
  <html lang="en">
  
  <head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Hype Society Email Verification</title>
  
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;700&display=swap" rel="stylesheet" />
  <style>
    html,
    body {
      margin: 0;
      padding: 0;
      font-family: "Montserrat", sans-serif;
      font-size: 16px;
    }
  </style>
  </head>
  
<body>
<div style="padding:58px 83px 145px 53px;background-color:#fff;width: 600px;margin: auto;">
  <div style="height:35px;width:73.8px;font-weight:600;margin: auto;margin-bottom: 120px;font-size:40px ">hype </div>
  <span style="font-family:SegoeUI-Semibold;font-size:15px;font-weight:500;font-stretch:normal;font-style:normal;line-height:1;letter-spacing:0.5px;color:#fb3593;margin-bottom: 15px;margin-top:400px;text-align:left;width:600px">RESET PASSWORD</span>
  <div style="font-family:SegoeUI-Semibold;font-size:35px;font-weight:520;font-stretch:normal;font-style:normal;line-height:1;letter-spacing:normal;color:#333;margin-bottom:40px;padding-top: 15px;text-align:left;width:600px">Here is your 4-digit code</div>
  <span class="im" style="
">
<div style="
    margin-left:0;
    margin-bottom: 40px;
    font-family: 'Hind';
    font-style: normal;
    font-weight: 549;
    font-size: 50px;
    line-height: 38px;
    color: #fb3593;
    letter-spacing: 20px;"
     >${params.otpCode}</div>
      <div style="font-family:SegoeUI-Semibold;font-size:18px;font-weight:520;font-stretch:normal;font-style:normal;line-height:1;letter-spacing:normal;color:#333;margin-bottom:100px;padding-top: 15px;text-align:left;width:600px">If you did not requested a new password, please ignore this email and report that.</div>

  <div style="width:600px;height:1px;background-color:#d8d8d8;margin-bottom:20px"> </div>
  </span>
  <div style="width:600px">
    <span style="height:19px;font-weight:600;width:31px;font-size:15px">hype </span>
    <div class="yj6qo"></div>
    <div class="adL"></div>
  </div>
  <div class="adL"></div>
</div>
  `;
};

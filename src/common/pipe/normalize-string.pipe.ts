import { Injectable, PipeTransform, ArgumentMetadata } from '@nestjs/common';
import { trimAndNormalizeText } from '../utils/normalize-text';

@Injectable()
export class NormalizeStringPipe implements PipeTransform {
  private isObj(obj: any): boolean {
    return typeof obj === 'object' && obj !== null;
  }

  private trim(values) {
    Object.keys(values).forEach((key) => {
      if (this.isObj(values[key])) {
        values[key] = this.trim(values[key]);
      } else {
        if (typeof values[key] === 'string') {
          values[key] = trimAndNormalizeText(values[key]);
        }
      }
    });
    return values;
  }

  transform(values: any, metadata: ArgumentMetadata) {
    const { type } = metadata;
    if (this.isObj(values) && type === 'body') {
      return this.trim(values);
    } else {
      return values;
    }
  }
}

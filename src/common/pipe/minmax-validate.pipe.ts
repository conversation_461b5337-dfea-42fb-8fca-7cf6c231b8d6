import { Injectable } from '@nestjs/common';
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ name: 'MinAndMaxValidate', async: true })
@Injectable()
export class ValidateMinAndMaxPipe implements ValidatorConstraintInterface {
  validate(value, args): boolean | Promise<boolean> {
    const propertyName = args.property;
    if (propertyName.includes('min')) {
      const baseField = propertyName.split('min')[1];
      const max = args.object[`max${baseField}`];
      return checkMinAndMax(value, max);
    } else if (propertyName.includes('max')) {
      const baseField = propertyName.split('max')[1];
      const min = args.object[`min${baseField}`];
      return checkMinAndMax(min, value);
    }
    return false;
  }

  defaultMessage?(validationArguments?: ValidationArguments): string {
    return `${validationArguments.property} exceed the allowed amount`;
  }
}

export function MinAndMaxValidate(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: ValidateMinAndMaxPipe,
    });
  };
}

const checkMinAndMax = (min: number, max: number) => {
  if (min > max) {
    return false;
  }
  return true;
};

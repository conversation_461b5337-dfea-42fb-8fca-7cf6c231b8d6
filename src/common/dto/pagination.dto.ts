import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsDefined,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsPositive,
  IsString,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export enum SortBy {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class FilterItem {
  @ApiProperty()
  @IsNotEmpty()
  key: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  values: string[];
}

export class CommonPaginationDto {
  @ApiProperty({ default: 1 })
  @Type(() => Number)
  @Transform(({ value }) => parseInt(value))
  @IsPositive()
  @IsDefined()
  page = 1;

  @ApiProperty({ default: 10 })
  @Transform(({ value }) => parseInt(value))
  @Type(() => Number)
  @IsPositive()
  @IsDefined()
  limit = 10;

  @ApiPropertyOptional({ enum: SortBy, default: SortBy.ASC })
  @IsOptional()
  @IsEnum(SortBy)
  sorted?: SortBy = SortBy.ASC;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sortedKey?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  searchKey?: string;

  @ApiPropertyOptional({ type: () => [FilterItem], isArray: true })
  @ValidateNested({ each: true })
  @Type(() => FilterItem)
  @IsOptional()
  filterCriteria?: FilterItem[];
}

export class PageOptionsDto {
  @ApiPropertyOptional({ enum: SortBy, default: SortBy.ASC })
  @IsEnum(SortBy)
  @IsOptional()
  readonly sorted?: SortBy = SortBy.ASC;

  @ApiPropertyOptional({
    minimum: 1,
    default: 1,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  readonly page?: number = 1;

  @ApiPropertyOptional({
    minimum: 1,
    maximum: 50,
    default: 10,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(50)
  @IsOptional()
  readonly limit?: number = 10;

  get skip(): number {
    return (this.page - 1) * this.limit;
  }
}

export class PaginationResDto {
  @ApiProperty({ default: 1 })
  total: number;

  @ApiProperty({ default: 1 })
  page: number;

  @ApiProperty({ default: 10 })
  limit: number;
}

export class SearchSortDto extends CommonPaginationDto {
  @ApiProperty({ required: false })
  @Type(() => String)
  @IsOptional()
  keywords: string;
}

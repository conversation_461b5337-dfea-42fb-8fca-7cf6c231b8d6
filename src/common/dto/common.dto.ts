import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from 'class-validator';
import { IResponseCommon } from '../interfaces/common.interface';
import { LanguageCode } from '../constants/common.constant';
import { CommonPaginationDto } from './pagination.dto';

export class ResponseDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty({ example: 1000 })
  statusCode: number;
}

export class HeaderDto {
  @ApiProperty({ default: LanguageCode.United_States })
  @IsEnum(LanguageCode)
  lang: LanguageCode;
}
export class RemoveUserDto {
  @ApiProperty({ default: '' })
  @IsString()
  secretKey: string;
}

export class GetUserOtpDto {
  @ApiProperty({ default: '' })
  @IsString()
  secretKey: string;
}

export class PermissionDto {
  action: string;
  description?: string;
}

export class QueryCommonDto extends CommonPaginationDto {
  @ApiProperty()
  sortBy: string;
}

export class BaseResponseDto<T> implements IResponseCommon {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  result: T;

  @ApiProperty()
  statusCode: number;
}

export class BaseErrorResponseDto extends BaseResponseDto<object | object[]> {
  @ApiProperty({ default: false })
  success: boolean;

  @ApiProperty({ default: null })
  result = null;
}

export interface IPaginationItem {
  items: object[];

  total: number;
}

export interface IOptionsCheckValidSource {
  toggleRapidKey: boolean;
  isThrowError: boolean;
  isCancelCampaignImmediately: boolean;
}

export interface ICriteriaItem {
  [x: string]: string[];
}

export interface ICriteriaCommon {
  [x: string]: string[] | ICriteriaItem[];
}

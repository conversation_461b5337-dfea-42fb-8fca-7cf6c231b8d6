import {
  Catch,
  ArgumentsHost,
  HttpException,
  ExceptionFilter,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { getStandardStatusCode, logError, logInfo } from '../utils/helpers.util';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    try {
      const status =
        exception instanceof HttpException
          ? exception.getStatus()
          : HttpStatus.INTERNAL_SERVER_ERROR;
      let message = exception?.message || 'Internal server error';

      const responseObject: any =
        exception instanceof HttpException ? exception?.getResponse() : null;
      message = responseObject?.message || message;

      if (request.path !== '/') {
        this.logger.error(
          responseObject
            ? `
            [${request.method}] ${request.path}
            [RESPONSE] ${JSON.stringify(responseObject)}
            [STACK] ${exception?.stack}
          `
            : `[URL] ${request.path} [STACK] ${exception?.stack}`,
        );
      }

      const statusCode = Object.values(HttpStatus).includes(status) ? status : 500;

      logInfo('HttpExceptionFilter', `${request.method} ${request.path} - ${statusCode}`);
      logInfo('HttpExceptionFilter', responseObject?.statusCode);

      response.status(statusCode).json({
        success: false,
        message: Array.isArray(message) ? message.join(', ') : message,
        result: responseObject?.result ?? null,
        timestamp: new Date().toISOString(),
        statusCode: getStandardStatusCode(responseObject?.statusCode),
      });
    } catch (error) {
      logError('HttpExceptionFilter error', error);
      response.status(500);
    }
  }
}

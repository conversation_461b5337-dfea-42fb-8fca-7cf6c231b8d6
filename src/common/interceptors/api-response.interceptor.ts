import { Injectable, NestInterceptor, Execution<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { sizeOf } from '../utils/helpers.util';

@Injectable()
export class ApiResponseInterceptor implements NestInterceptor {
  private logger: Logger = new Logger(ApiResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { originalUrl, method, params, query, body } = request;

    const IGNORE_LOG_KEYWORDS = ['job-search'];
    if (IGNORE_LOG_KEYWORDS.some(keyword => originalUrl.includes(keyword))) {
      return next.handle();
    }
    const token = request.headers['authorization'];

    if (!request.path.includes('/health')) {
      console.log(`Before...
      ${JSON.stringify({
        token,
        originalUrl,
        method,
        params,
        query,
        body,
      })}`);
    }

    const now = Date.now();
    return next.handle().pipe(
      tap((data) => {
        if (typeof data === 'object' && !Array.isArray(data) && data !== null) {
          console.log(`After ${Date.now() - now}ms... ${originalUrl}, ${sizeOf(data) > 50000 ? 'Data too large data for logging' : JSON.stringify(data)} `);
        }
      })
    );
  }
}

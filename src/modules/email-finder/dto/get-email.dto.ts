import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";

export class EmailFinderQueryDto {
    @ApiProperty()
    @IsNotEmpty()
    companyName: string;

    @ApiProperty()
    @IsNotEmpty()
    fullName: string;

    @ApiPropertyOptional()
    @IsOptional()
    companyLinkedinURL?: string
}

export class EmailsQueryDto {
    @ApiProperty()
    @IsNotEmpty()
    company: string;

    @ApiPropertyOptional()
    @IsOptional()
    limit?: number;

    @ApiPropertyOptional()
    @IsOptional()
    fullName?: string;
}
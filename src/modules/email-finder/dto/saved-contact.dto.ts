import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";
import { SavedSearchesTypeEnum } from "../enum/saved-search.enum";
export class CreateSavedSearchesDto {
    @ApiProperty()
    @IsNotEmpty()
    searchName: string;

    @ApiProperty()
    @IsNotEmpty()
    searchType: SavedSearchesTypeEnum;

    @ApiProperty()
    @IsNotEmpty()
    filterFields: any;
}

export class UpdateSavedSearchesDto {
    @ApiProperty()
    @IsOptional()
    searchName: string;
}
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";
import { SavedSearchesTypeEnum } from "../enum/saved-search.enum";

export class InformationDto {
    @ApiProperty()
    @IsNotEmpty()
    linkedInUrl: string;

    @ApiProperty()
    @IsNotEmpty()
    companyUnipileId: string;

    @ApiProperty()
    @IsNotEmpty()
    recordId: string;
}

export class createBulkEnrichTaskDto {
    @ApiProperty()
    @IsNotEmpty()
    additionalField: any;

    @ApiProperty()
    @IsNotEmpty()
    contactInformation: InformationDto[];
}

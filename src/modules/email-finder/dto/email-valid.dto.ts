import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { EnrichmentContactType, EnrichmentStatus } from "../entities/contact-enrichment-tasks.entity";

export class EmailValidItemDto {
    @ApiProperty()
    @IsNotEmpty()
    email: string;

    @ApiProperty()
    @IsNotEmpty()
    status: string;
}

export class EmailValidDto {
    @ApiProperty({ type: [EmailValidItemDto] })
    @IsNotEmpty()
    data: EmailValidItemDto[];
}

export class GetEmailValidDto {
    @ApiProperty()
    @IsNotEmpty()
    emails: string[];
}
export class CreateEnrichItem {
    @ApiProperty()
    @IsNotEmpty()
    fullName: string;

    @ApiProperty()
    @IsNotEmpty()
    recordId: string;

    @ApiProperty()
    @IsNotEmpty()
    companyName: string;

    @ApiPropertyOptional()
    @IsOptional()
    linkedInUrl?: string;

    @ApiPropertyOptional()
    @IsOptional()
    industry?: string;

    @ApiPropertyOptional()
    @IsOptional()
    location?: string;

    @ApiPropertyOptional()
    @IsOptional()
    companyId?: string;

    @ApiProperty()
    @IsNotEmpty()
    companyDomain?: string;

    @ApiProperty()
    @IsNotEmpty()
    enrichFields?: string[];
}


export class CreateEnrichDataDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsArray()
    data: CreateEnrichItem[]

    @ApiProperty()
    @IsOptional()
    listId: string;

    @ApiProperty()
    @IsOptional()
    type: EnrichmentContactType;
}

export class CreateEnrichContactDataDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsArray()
    data: CreateEnrichItem[]
}

export class GetEnrichmentTaskQueryDTO {
    @IsOptional()
    @IsString()
    type: EnrichmentContactType;

    @IsOptional()
    @IsString()
    status: EnrichmentStatus;

    @ApiPropertyOptional()
    @IsOptional()
    page?: number;

    @ApiPropertyOptional()
    @IsOptional()
    limit?: number;
}

export class CreateSearchFloqerhData {
    @IsOptional()
    @IsString()
    companyName: string;

    @IsOptional()
    @IsString()
    linkedinUrl: string;

    @IsOptional()
    @IsString()
    recordId: string;
}

export class CreateCompanyDetailSearch {
    @IsOptional()
    @IsString()
    companyDomain: string;

    @IsOptional()
    @IsString()
    recordId: string;
}

export class CreateAccessEmailDto {
    @IsOptional()
    @IsString()
    linkedInUrl: string;

    @IsOptional()
    @IsString()
    recordId: string;
}

export class GetEnrichClayResultsDto {
    @IsNotEmpty()
    @IsArray()
    contactIds: string[];
}
import {
  Controller,
  Get,
  Query,
  HttpStatus,
  Res,
  Body,
  Post,
  Req,
  Param,
  Delete,
  Patch,
  UseGuards,
} from '@nestjs/common';
import { EmailFinderService } from './email-finder.service';
import { SkipThrottle } from '@nestjs/throttler';
import { Permission } from '../../common/decorators/permissions.decorator';
import { PermissionResource } from '../../common/constants/permission.constant';
import { ResourceEnum } from '../user/entities/permission.entity';
import { EmailFinderQueryDto, EmailsQueryDto } from './dto/get-email.dto';
import {
  CreateAccessEmailDto,
  CreateCompanyDetailSearch,
  CreateEnrichContactDataDto,
  CreateEnrichDataDto,
  CreateSearchFloqerhData,
  EmailValidDto,
  GetEmailValidDto,
  GetEnrichClayResultsDto,
  GetEnrichmentTaskQueryDTO,
} from './dto/email-valid.dto';
import { EnrichmentContactType } from './entities/contact-enrichment-tasks.entity';
import { SavedSearchesTypeEnum } from './enum/saved-search.enum';
import { CreateSavedSearchesDto, UpdateSavedSearchesDto } from './dto/saved-contact.dto';
import { createBulkEnrichTaskDto } from './dto/bulk-enrich.dto';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { UseFeatureCredits } from '../subscription/decorators/use-feature-credits.decorator';
@Controller('email-finder')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class EmailFinderController {
  constructor(private readonly emailFinderService: EmailFinderService) {}

  @Get('/employees')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async searchEmployees(
    @Query('companyName') companyName: string,
    @Query('role') role: string,
    @Query('companyLinkedinURL') companyLinkedinURL: string,
    @Res() response: any,
  ) {
    try {
      const employeesData = await this.emailFinderService.searchEmployees(
        companyName,
        role,
        companyLinkedinURL,
      );
      response.status(HttpStatus.OK).json(employeesData);
    } catch (error) {
      console.error('Error fetching data:', error.message);
      response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Error fetching data ',
      });
    }
  }

  @Get('/email')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getEmail(@Query() queryDto: EmailFinderQueryDto) {
    return this.emailFinderService.getEmail(queryDto);
  }

  @Get('emails')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getEmails(@Query() getEmailsQuery: EmailsQueryDto) {
    return this.emailFinderService.getEmailsByCompany(getEmailsQuery);
  }

  @Post('/email-validation-results')
  @Permission(PermissionResource[ResourceEnum.EMAIL_VERIFICATION].Write)
  async updateEmailValid(@Body() body: EmailValidDto) {
    return this.emailFinderService.createEmailValidationResult(body);
  }

  @Post('/get-email-validation-results')
  @Permission(PermissionResource[ResourceEnum.EMAIL_VERIFICATION].Read)
  async getValidEmail(@Body() body: GetEmailValidDto) {
    return this.emailFinderService.getEmailValidationResults(body);
  }

  @Post('/clay/create-enrich-data')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async createEnrichData(@Body() body: CreateEnrichContactDataDto) {
    return this.emailFinderService.createEnrichContactData(body);
  }

  @Post('/clay/get-enrich-data')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  @UseFeatureCredits({
    featureId: 'credits',
    purpose: 'Enrich data - Unlock email',
  })
  async getEnrichData(@Req() req) {
    return this.emailFinderService.createEnrichEvent(req.query, req);
  }

  @Post('/clay/get-enrich-data-results')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getEnrichDataResult(@Body() body: GetEnrichClayResultsDto) {
    return this.emailFinderService.getEnrichDataResult(body);
  }

  @Get('/enrich-tasks/:listId')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getEnrichTasks(@Param('listId') listId: string, @Query() query: GetEnrichmentTaskQueryDTO) {
    return this.emailFinderService.getEnrichTasks(listId, query);
  }

  @Post('enrich-all/hotlist/:id')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async enrichAllHotlist(@Req() req: any, @Param('id') id: string) {
    const userId = req.viewAsUser.id;

    return this.emailFinderService.enrichAll(id, EnrichmentContactType.HOTLIST, userId);
  }

  @Post('/enrich-all/contact-list/:id')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async enrichAllContactList(@Req() req: any, @Param('id') id: string) {
    const userId = req.viewAsUser.id;
    return this.emailFinderService.enrichAll(id, EnrichmentContactType.CONTACTLIST, userId);
  }

  // TODO: Remove this after client migration is complete
  @Post('/view-as/:userId/enrich-all/hotlist/:id')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async enrichAllHotlistLegacy(@Param('userId') userId: string, @Param('id') id: string) {
    return this.emailFinderService.enrichAll(id, EnrichmentContactType.HOTLIST, userId);
  }

  // TODO: Remove this after client migration is complete
  @Post('/view-as/:userId/enrich-all/contact-list/:id')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async enrichAllContactListLegacy(@Param('userId') userId: string, @Param('id') id: string) {
    return this.emailFinderService.enrichAll(id, EnrichmentContactType.CONTACTLIST, userId);
  }

  @Post('/floqer/create-search-data')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async createFloquerSearchData(@Body() body: CreateSearchFloqerhData) {
    return this.emailFinderService.createFloquerSearchData(body);
  }

  @Post('/floqer/get-search-data')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getFloquerSearchData(@Req() req) {
    return this.emailFinderService.getFloquerSearchData(req.body);
  }

  @Post('/floqer/create-enrich-linkedin-data')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async createFloquerLinkedinData(@Body() body: CreateEnrichDataDto) {
    return this.emailFinderService.createFloquerLinkedinData(body);
  }

  @Post('/floqer/get-enrich-linkedin-data')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getFloquerLinkedinData(@Req() req) {
    return this.emailFinderService.getFloquerLinkedinData(req);
  }

  @Get('/floqer/get-locations-data')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getFloquerGetLocation(@Req() req) {
    return this.emailFinderService.getFloquerGetLocation();
  }

  @Get('/floqer/get-skills-data')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getFloquerGetSkills(@Req() req) {
    return this.emailFinderService.getFloquerGetSkills();
  }

  @Post('/floqer/create-search-company-detail')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async createSearchCompanyDetail(@Body() body: CreateCompanyDetailSearch) {
    return this.emailFinderService.createSearchCompanyDetail(body);
  }

  @Post('/floqer/get-search-company-detail')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getSearchCompanyDetail(@Req() req) {
    return this.emailFinderService.getSearchCompanyDetail(req.body);
  }

  @Post('/floqer/create-access-email')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async createAccessEmailRequest(@Body() body: CreateAccessEmailDto) {
    return this.emailFinderService.createAccessEmailRequest(body);
  }

  @Post('/floqer/get-create-access-email')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getAccessEmailRequest(@Req() req) {
    return this.emailFinderService.getAccessEmailRequest(req.body);
  }

  @Post('/saved-searches')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async createSavedContact(@Req() req: any, @Body() body: CreateSavedSearchesDto) {
    const userId = req.viewAsUser.id;
    return this.emailFinderService.createSavedSearches(userId, body);
  }

  @Get('/saved-searches')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getListSavedContact(
    @Req() req: any,
    @Query('searchType') searchType: SavedSearchesTypeEnum,
  ) {
    const userId = req.viewAsUser.id;
    return this.emailFinderService.getSavedSearches(userId, searchType);
  }

  @Delete('/saved-searches/:savedContactId')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async deleteSavedContact(@Req() req: any, @Param('savedContactId') savedContactId: string) {
    const userId = req.viewAsUser.id;
    return this.emailFinderService.deleteSavedSearches(userId, savedContactId);
  }

  @Patch('/saved-searches/:savedContactId')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async updateSavedContact(
    @Req() req: any,
    @Param('savedContactId') savedContactId: string,
    @Body() body: UpdateSavedSearchesDto,
  ) {
    const userId = req.viewAsUser.id;
    return this.emailFinderService.updateSavedSearches(userId, savedContactId, body);
  }

  @Post('/bulk-enrich/create-task')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async createBulkEnrichTask(@Req() req: any, @Body() body: createBulkEnrichTaskDto) {
    const userId = req.viewAsUser.id;
    return this.emailFinderService.createBulkEnrichTask(userId, body);
  }

  // TODO: Remove this after client migration is complete
  @Post('/saved-searches/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async createSavedContactLegacy(
    @Param('userId') userId: string,
    @Body() body: CreateSavedSearchesDto,
  ) {
    return this.emailFinderService.createSavedSearches(userId, body);
  }

  // TODO: Remove this after client migration is complete
  @Get('/saved-searches/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getListSavedContactLegacy(
    @Param('userId') userId: string,
    @Query('searchType') searchType: SavedSearchesTypeEnum,
  ) {
    return this.emailFinderService.getSavedSearches(userId, searchType);
  }

  // TODO: Remove this after client migration is complete
  @Delete('/saved-searches/view-as/:userId/delete-contact/:savedContactId')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async deleteSavedContactLegacy(
    @Param('userId') userId: string,
    @Param('savedContactId') savedContactId: string,
  ) {
    return this.emailFinderService.deleteSavedSearches(userId, savedContactId);
  }

  // TODO: Remove this after client migration is complete
  @Patch('/saved-searches/view-as/:userId/update-contact/:savedContactId')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async updateSavedContactLegacy(
    @Param('userId') userId: string,
    @Param('savedContactId') savedContactId: string,
    @Body() body: UpdateSavedSearchesDto,
  ) {
    return this.emailFinderService.updateSavedSearches(savedContactId, userId, body);
  }

  // TODO: Remove this after client migration is complete
  @Post('bulk-enrich/:userId/create-task')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Write)
  async createBulkEnrichTaskLegacy(
    @Param('userId') userId: string,
    @Body() body: createBulkEnrichTaskDto,
  ) {
    return this.emailFinderService.createBulkEnrichTask(userId, body);
  }
}

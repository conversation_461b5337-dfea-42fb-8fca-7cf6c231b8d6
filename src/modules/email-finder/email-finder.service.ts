/* eslint-disable camelcase */
import { Injectable, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import axios from 'axios';
import { Promise as BBPromise } from 'bluebird';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { EmailFinderQueryDto, EmailsQueryDto } from './dto/get-email.dto';
import { BullHornConfig, ProspeoConfig } from 'src/configs/configs.constants';
import { CreateAccessEmailDto, CreateCompanyDetailSearch, CreateEnrichContactDataDto, CreateEnrichDataDto, CreateEnrichItem, CreateSearchFloqerhData, EmailValidDto, GetEmailValidDto, GetEnrichClayResultsDto, GetEnrichmentTaskQueryDTO } from './dto/email-valid.dto';
import { DataSource, In } from 'typeorm';
import { EmailValidationResultRepository } from './repositories/email-validation-result.repository';
import { EmployeeFinderService } from '../employee-finder/services/employee-finder.service';
import { ApolloService } from '../employee-finder/services/apollo.service';
import { ClayService } from './clay.service';
import { SSEService } from '../sse/sse.service';
import { ENRICH_CONTACT_DATA, ENRICH_CONTACT_FLOQER, ENRICH_FLOQER_DATA_LINKEDIN_URL, FLOQUER_ACCESS_EMAIL, FLOQUER_COMPANY_DETAIL, RECENTLY_ADDED_JOB_EVENT_NAME } from '../sse/sse.constant';
import { UserRepository } from '../user/repositories/user.repository';
import { ContactEnrichmentTaskRepository } from './repositories/contact-enrichment-tasks.repository';
import { EnrichmentContactType, EnrichmentStatus } from './entities/contact-enrichment-tasks.entity';
import { OrganizationEntity } from '../user/entities/organization.entity';
import { BullHornService } from 'src/middlewares/bullhorn/bullhorn.service';
import { GetCorporateUserQueryDto } from '../jobs/dto/get-corporate.dto';
import { insertEntity, queryClientContacts } from '../jobs/utils/bullhorn-service.util';
import { ContactRepository } from '../user/repositories/contact.repository';
import { v4 as uuid } from 'uuid';
import { ContactEntity } from '../user/entities/contact.entity';
import { CreateSavedSearchesDto, UpdateSavedSearchesDto } from './dto/saved-contact.dto';
import { SavedSearchesRepository } from './repositories/saved-searches.repository';
import { ClaySearchTypeEnum, SavedSearchesTypeEnum } from './enum/saved-search.enum';
import { createBulkEnrichTaskDto } from './dto/bulk-enrich.dto';
import { BulkAddBullhornStatus, BulkAddBullhornTaskEntity } from './entities/bulk-add-bullhorn-task.entity';
import { BulkAddBullhornTasksRepository } from './repositories/bulk-add-bullhorn-tasks.repository';
import { NotificationRepository } from '../notification/repositories/notification.repository';
import { NotificationEnum } from 'src/modules/notification/entities/notification.entity';
import { LinkedInFinderService } from '../linkedin-finder/linkedin-finder.service';
import { URL } from 'url';
import * as unipileClient from '../jobs/utils/unipile-service.utils';


@Injectable()
@UseGuards(AuthenticationGuard)
export class EmailFinderService extends BaseAbstractService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly employeeFinderService: EmployeeFinderService,
    private readonly apolloService: ApolloService,
    private readonly emailValidationResultRepository: EmailValidationResultRepository,
    private readonly clayService: ClayService,
    private readonly sseService: SSEService,
    private readonly userRepository: UserRepository,
    private readonly contactRepository: ContactRepository,
    private readonly bulkAddBullhornTaskRepository: BulkAddBullhornTasksRepository,
    private readonly savedSearchesRepository: SavedSearchesRepository,
    private readonly bullhornService: BullHornService,
    private readonly contactEnrichmentTaskRepository: ContactEnrichmentTaskRepository,
    private readonly notificationRepository: NotificationRepository,
    private readonly linkedinFinderService: LinkedInFinderService
  ) {
    super(i18nService);
  }
  async searchEmployees(companyName: string, jobTitle: string, companyLinkedinURL: string): Promise<any[]> {
    if (!jobTitle || !(companyName || companyLinkedinURL)) {
      return [];
    }
    const apiKey = 'SGk8y50n';
    const { data } = await axios.get(`https://gateway.datagma.net/api/ingress/v2/full?`, {
      params: {
        companyFull: true,
        companyFrench: false,
        findEmailV2: false,
        companyKeyword: companyName,
        data: companyName,
        linkedInSlug: companyLinkedinURL || '',
        companyPremium: true,
        findEmailV2Country: 'General',
        companyEmployees: true,
        employeeTitle: jobTitle,
        maxEmployeesReturn: 10,
        employeeCountry: 'GB',
        apiId: apiKey,
      },
    });
    return data;
  }

  async getEmail(queryDto: EmailFinderQueryDto) {
    const { fullName, companyName } = queryDto;
    try {
      //TODO: update later on .env
      const apiKey = ProspeoConfig.apiKey;

      let url = ProspeoConfig.emailFinderApi;

      let standardCompanyName = companyName;
      if (companyName.includes('.com')) {
        if (companyName.includes('linkedin')) {
          const rawExtractedCompanyName = companyName.split('.com/company/')[1].split('/')[0].replace('-', ' ');

          standardCompanyName = await this.getDomainByName(rawExtractedCompanyName);
        }
        // other case: it is supposed to be a domain already
      } else {
        standardCompanyName = await this.getDomainByName(standardCompanyName);
      }

      const { data: employeeEmailData } = await axios.post(
        url,
        {
          full_name: fullName,
          company: standardCompanyName,
        },
        {
          headers: {
            'X-KEY': apiKey,
          },
        }
      );
      const data = employeeEmailData.response;

      const standardData = {
        email: data.email,
        domain: data.domain,
        firstName: data.first_name,
        lastName: data.last_name,
        emailStatus: employeeEmailData?.response?.email_status,
      };
      return this.formatOutputData({ key: 'GET_EMAIL' }, { data: standardData });
    } catch (error) {
      return this.formatOutputData({ key: 'GET_EMAIL' }, { data: null });
    }
  }

  async getDomainByName(name: string, retry = 2) {
    try {
      const apiKey = ProspeoConfig.apiKey;
      const url = ProspeoConfig.domainSearchApi;

      const { data } = await axios.post(
        url,
        {
          company: name,
        },
        {
          headers: {
            'X-KEY': apiKey,
          },
        }
      );

      const domain = data.response.meta.domain;

      return domain;
    } catch (error) {
      console.error('GET_DOMAIN_BY_NAME', error);
      //TODO: create a mapping for company => cache or DB
      if (retry === 2) {
        return this.getDomainByName(name.replace(' ', '_'), 1);
      }

      if (retry === 1) {
        return this.getDomainByName(name.replace('_', '').concat('.com'), 0);
      }

      throw error;
    }
  }

  async getEmailsByCompany(getEmailsQuery: EmailsQueryDto) {
    try {
      const { company, limit, fullName } = getEmailsQuery;

      let standardCompanyName = company;
      if (company.includes('.com')) {
        if (company.includes('linkedin')) {
          const rawExtractedCompanyName = company
            .split('.com/company/')[1]
            .split('/')[0]
            .split('?')[0]
            .replace('-', ' ');

          standardCompanyName = await this.getDomainByName(rawExtractedCompanyName);
        }
      } else {
        standardCompanyName = await this.getDomainByName(company);
      }

      const bodyRequest: { company: string; limit?: number; full_name?: string } = {
        company: standardCompanyName,
      };

      if (limit) {
        bodyRequest.limit = limit;
      }

      const apiKey = ProspeoConfig.apiKey;
      const url = ProspeoConfig.domainSearchApi;
      const { data } = await axios.post(url, bodyRequest, {
        headers: {
          'X-KEY': apiKey,
        },
      });

      const standardResponse = data.response?.email_list?.map((item) => ({
        email: item.email,
        firstName: item.first_name,
        lastName: item.last_name,
        emailStatus: item.verification.status,
      }));

      let result = [];

      if (fullName) {
        const fullNameLowercase = fullName.toLowerCase();
        standardResponse.forEach((item) => {
          const { firstName, lastName } = item;

          const firstNameLowerCase = firstName?.toLowerCase() ?? '';
          const lastNameLowerCase = lastName?.toLowerCase() ?? '';

          const fullNameOfItem = firstNameLowerCase
            ? firstNameLowerCase + (lastNameLowerCase ? ` ${lastNameLowerCase}` : '')
            : lastNameLowerCase ?? '';
          if (fullNameLowercase.toLowerCase() === fullNameOfItem.toLowerCase()) {
            result.unshift(item);
          } else {
            if (
              (firstNameLowerCase && fullNameLowercase.includes(firstNameLowerCase)) ||
              (lastNameLowerCase && fullNameLowercase.includes(lastNameLowerCase))
            ) {
              result.push(item);
            }
          }
        });

        if (result.length === 0) {
          //TODO: update to get more data here
        }
      } else {
        result = standardResponse;
      }

      return this.formatOutputData(
        { key: 'GET_EMAILS_BY_COMPANY' },
        {
          data: {
            data: result,
            metaData: {
              total: data.response.meta.total_emails,
              moreResult: data.response.meta.more_results,
            },
          },
        }
      );
    } catch (error) {
      return this.throwCommonMessage('GET_EMAILS_BY_COMPANY', new NotFoundException('No emails available'));
    }
  }

  async createEmailValidationResult(bodyDto: EmailValidDto) {
    try {
      const data = bodyDto.data;

      const emailList = bodyDto.data.map((item) => item.email);

      const emailFind = await this.emailValidationResultRepository.find({where: {email: In(emailList)}});
      const foundEmails = emailFind.map((item) => item.email);

      let dataToInsert = data.filter((item) => !foundEmails.includes(item.email));

      const uniqueEmails = new Map();
      dataToInsert.forEach((item) => {
        if (!uniqueEmails.has(item.email)) {
          uniqueEmails.set(item.email, item);
        }
      });

      dataToInsert = Array.from(uniqueEmails.values());
      const dataInsert = await this.emailValidationResultRepository.insert(dataToInsert);

      const statusGroup = new Map();
      data.forEach((item) => {
        if (foundEmails.includes(item.email)) {
          if (!statusGroup.has(item.status)) {
            statusGroup.set(item.status, []);
          }
          statusGroup.get(item.status).push(item.email);
        }
      });

      const updatePromises = [];

      for (const [status, emails] of statusGroup) {
        const updatePromise = this.emailValidationResultRepository.update(
          { email: In(emails) },
          { status: status }
        );
        updatePromises.push(updatePromise);
      }

      await Promise.all(updatePromises);

      return this.formatOutputData(
        { key: 'CREATE_EMAIL_VALID' },
        {
          data: [],
        }
      );
    } catch (error) {
      return this.throwCommonMessage('CREATE_EMAIL_VALID', new NotFoundException('No emails available'));
    }
  }

  async getEmailValidationResults(body: GetEmailValidDto) {
    try {
      const emails = body.emails;

      const emailFind = await this.emailValidationResultRepository.find({
        where: {
          email: In(emails),
        },
      });

      return this.formatOutputData(
        { key: 'GET_EMAIL_VALID' },
        {
          data: emailFind,
        }
      );
    } catch (error) {
      return this.throwCommonMessage('GET_EMAIL_VALID', new NotFoundException('No emails available'));
    }
  }

  async getEnrichDataResult(body: GetEnrichClayResultsDto) {
    try {
      
      const data = await this.contactEnrichmentTaskRepository.find({where: {contactId: In(body.contactIds)}});

      return this.formatOutputData(
        { key: 'ENRICH_CONTACTS_RESULTS' },
        {
          data: {
            data: data,
          },
        },
      );
    } catch (e) {
      return this.throwCommonMessage('ENRICH_CONTACTS_RESULTS', new NotFoundException('No emails available'));
    }
  }

  async createEnrichContactData(body: CreateEnrichContactDataDto) {
    try {

      const payload = await Promise.all(
        body.data.map(async (item: any) => {
          let domain = '';
      
          if (item?.companyId) {
            try {
              const accountId = await this.linkedinFinderService.handleGetAccountUnipile();
              const companyUnipile = await unipileClient.getCompanyDetail(accountId, item?.companyId);
              domain = new URL(companyUnipile?.website).hostname;
            } catch (error) {
              domain = '';
            }
          }
      
          return {
            recordId: item.recordId,
            linkedInUrl: item.linkedInUrl,
            type: (item?.enrichFields?.includes("phone") && !item?.enrichFields?.includes("email") ? ClaySearchTypeEnum.ACCESS_PHONE : ClaySearchTypeEnum.ACCESS_EMAIL),
            fullName: item?.fullName,
            companyName: item?.companyName,
            companyDomain: domain.replace(/^www\./, ''),
            enrichEmail: item?.enrichFields?.includes("email") ? "False" : "True",
            enrichPhone: item?.enrichFields?.includes("phone") ? "False" : "True",
          };
        })
      );
      

      const results = await Promise.all(
        payload.map((data) => {
          this.clayService.handleCreateDataInClay(data);
          this.contactEnrichmentTaskRepository.insert([
            {
              listId: "access_email",
              type: EnrichmentContactType.ACCESS_EMAIL,
              status: EnrichmentStatus.IN_PROGRESS,
              sourceContact: payload,
              enrichContact: null,
              contactId: data.recordId
            },
          ])
        })
      );

      return this.formatOutputData(
        { key: 'ENRICH_CONTACTS' },
        {
          data: {
            total: results,
          },
        },
      );
    } catch (e) {
      console.log("errr", e)
      return this.throwCommonMessage('CREATE_ENRICH_DATA', new NotFoundException('No emails available'));
    }
  }

  async createEnrichData(body: CreateEnrichDataDto) {
    try {
      const { data, listId, type } = body;
  
      const clayEnrichmentRecords = await this.enrichContacts(data, listId, type);

      return this.formatOutputData(
        { key: 'ENRICH_CONTACTS' },
        {
          data: {
            total: clayEnrichmentRecords.length,
          },
        },
      );
      // const listEnrich = [];
  
      // for (const item of data) {
      //   if (item.linkedInUrl) {
      //     listEnrich.push({
      //       recordId: item.recordId,
      //       linkedInUrl: item.linkedInUrl,
      //     });
      //   } else {
      //     const apolloInfo = await this.getApolloInfo(item.companyName, item.fullName);
      //     if(apolloInfo?.contacts?.length > 0 || apolloInfo?.people?.length > 0) {
      //       listEnrich.push({
      //         recordId: item.recordId,
      //         linkedInUrl: apolloInfo?.contacts?.[0]?.linkedin_url || apolloInfo?.people?.[0]?.linkedin_url,
      //       })
      //     } 
      //   }
      // }

      // const promises = [];

      // listEnrich.forEach((item) => {
      //   const promise = this.clayService.handleCreateDataInClay(item);
      //   promises.push(promise);
      // });

      // await Promise.all(promises);

      // return this.formatOutputData(
      //   { key: 'CREATE_ENRICH_DATA' },
      //   {
      //     data: {},
      //   }
      // );
    } catch (e) {
      return this.throwCommonMessage('CREATE_ENRICH_DATA', new NotFoundException('No emails available'));
    }
  }

  private async getApolloInfo(companyName: string, fullName: string) {
    const bodyToSearch = {
      searchText: fullName + ' ' + companyName,
      page: 1,
    };

    const data = await this.apolloService.getPeople(bodyToSearch);

    return data;
  }

  async createEnrichEvent(data: any, req: any) {

    console.log("datadatadatadatadata", data)
    const type = data?.type
    if (type === ClaySearchTypeEnum.ACCESS_EMAIL) {
      await this.contactEnrichmentTaskRepository.update(
        { contactId: data.record_id },
        { status: EnrichmentStatus.SUCCESSFUL, enrichContact: {...data, valid_work_email: data?.email_1 || data?.email_2 || data?.email_3, phone: data?.phone_1 || data?.phone_2 || null } },
      );

      return this.sseService.emitEvent(ENRICH_CONTACT_DATA,  {...data, valid_work_email: data?.email_1 || data?.email_2 || data?.email_3, phone: data?.phone_1 || data?.phone_2 || null});
    }
    else if (type === ClaySearchTypeEnum.BULK_ADD_TO_BULLHORN) {
      const taskId = data?.task_id;
      const recordId = data?.record_id;
  
      const taskRecord = await this.bulkAddBullhornTaskRepository.findOne({where: {id: taskId}})
  
      const sourceContact = taskRecord.sourceContact;
      const enrichedContact = taskRecord.enrichedContact || [];
  
      const newHistory = [...(taskRecord.history || []), data]
  
      await this.bulkAddBullhornTaskRepository.update(taskRecord.id, {
        history: newHistory,
        status: newHistory.length === sourceContact.length ? BulkAddBullhornStatus.SUCCESSFUL : BulkAddBullhornStatus.IN_PROGRESS
      })
  
      if (sourceContact && (data?.email_1 || data?.email_2 || data?.email_3)) {
        const existingItem = enrichedContact.find(item => item.recordId === recordId);
  
        if (existingItem) {
          existingItem.email = data?.email_1 || data?.email_2 || data?.email_3;
        } else {
          enrichedContact.push({
            ...(sourceContact?.find(item => item.recordId === recordId)),
            email: data?.email_1 || data?.email_2 || data?.email_3
          });
  
        }
  
        const user = await this.userRepository.findOne({where: {id: taskRecord.createdBy}})
  
        const organizationId = user.organizationId
  
        const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};
  
        const bullhornPayload = {
          ...taskRecord.additionalField,
          ...(sourceContact?.find(item => item.recordId === recordId)),
          email: data?.email_1 || data?.email_2 || data?.email_3,
          entityName: "ClientContact",
        }
  
        const result = await insertEntity(bullhornPayload, 'ClientContact', bhRestToken, corporateRestUrl);
        await this.bulkAddBullhornTaskRepository.update(taskRecord.id, { enrichedContact, bullhornAdded: [...(taskRecord.bullhornAdded || []), result] });
      } else {
        console.log("Nothing to save")
      }
    }
  }

  public async getBhToken(organizationId) {
    // get access token which use to call to bullhorn
    const org = await this.dataSource
      .createQueryBuilder(OrganizationEntity, 'o')
      .where({ id: organizationId })
      .getOne();

    let {
      access_token, expires_at, refresh_token, bhRestToken, corporateRestUrl,
    } = org?.bhToken ?? {};

    if (!expires_at || expires_at < Date.now() - 60 * 1000) {
      const { bhClientId, bhUsername, bhPassword, bhClientSecret } = org ?? {
        bhClientId: BullHornConfig.clientId,
        bhUsername: BullHornConfig.username,
        bhPassword: BullHornConfig.password,
        bhClientSecret: BullHornConfig.clientSecret,
      };

      if (!bhClientId || !bhUsername || !bhPassword || !bhClientSecret) {
        return null;
      }

      const { oauthUrl, restUrl } = await this.bullhornService.getDataCenter(bhUsername);
      const {
        access_token: accessToken,
        expires_in,
        refresh_token: refreshToken,
      } = await this.bullhornService.getAccessTokenFromScratch(refresh_token, {
        bhClientId,
        bhUsername,
        bhPassword,
        bhClientSecret,
        rootOauthUrl: oauthUrl,
      });

      access_token = accessToken;
      expires_at = Date.now() + expires_in * 1000 - 60 * 1000;
      refresh_token = refreshToken;

      const { BhRestToken: newBhRestToken, restUrl: newCorporateRestUrl } =
        await this.bullhornService.getBhRestTokenAndCorporateRestEndpoint(accessToken, restUrl);

      bhRestToken = newBhRestToken;
      corporateRestUrl = newCorporateRestUrl;

      const bhToken = {
        access_token,
        expires_at,
        refresh_token,
        bhRestToken,
        corporateRestUrl,
      };

      if (org) {
        await this.dataSource.getRepository(OrganizationEntity).update(org.id, {
          bhToken,
        });
      }

      return { ...bhToken, organizationId: organizationId ?? 0 };
    }

    return { ...org.bhToken, organizationId: organizationId ?? 0 };
  }

  async findHotListContact(tearSheetId, user: any = {}, email = '') {
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(user?.organizationId)) || {};
    const entityName = 'ClientContact';

    const getHotListPayload: GetCorporateUserQueryDto = {
      entityName,
      tearSheetId,
      start: '0',
      query: '',
      email,
      count: '10000',
    };

    try {
      return queryClientContacts(getHotListPayload, entityName, bhRestToken, corporateRestUrl);
    } catch (error) {
      console.warn('Get hotlist contacts failed: ', error);

      return [];
    }
  }

  async enrichAll(id: string, type: EnrichmentContactType, userId: string) {
    let contacts: ContactEntity[] | any[] = [];
    if (type === EnrichmentContactType.HOTLIST) {
      const currentUser = await this.userRepository.findOne({ where: { id: userId } });
      contacts = await this.findHotListContact(id, currentUser);
    } else {
      contacts = await this.contactRepository.find({ where: { contactList: { id } } });
    }

    const clayEnrichmentRecords = await this.enrichContacts(contacts, id, type);

    return this.formatOutputData(
      { key: 'ENRICH_ALL' },
      {
        data: {
          total: clayEnrichmentRecords.length,
        },
      },
    );
  }

  //TODO: need to define type of contacts
  async enrichContacts(contacts: any, listId: string, type: EnrichmentContactType) {
    const getLinkedInProfileOfContact = (contact: any) => {
      return contact.linkedinProfileUrl || contact.linkedInProfileUrl || contact.linkedInUrl;
    }

    contacts = await BBPromise.map(contacts, async (contact) => {
      const linkedinProfileUrl = getLinkedInProfileOfContact(contact);
      if (linkedinProfileUrl) {
        return contact;
      }

      const result = await this.getApolloInfo(contact.companyName, contact.fullName);
      return {
        ...contact,
        linkedinProfileUrl: result?.contacts?.[0]?.linkedin_url
          || result?.people?.[0]?.linkedin_url,
      };
    }, { concurrency: 20 });

    const {
      enrichmentTasks,
      clayEnrichmentRecords,
    } = contacts.reduce((acc, contact: any) => {
      const linkedinProfileUrl = getLinkedInProfileOfContact(contact);
      const taskId = uuid();
      if (listId && type) {
        acc.enrichmentTasks.push(
          this.contactEnrichmentTaskRepository.create({
            id: taskId,
            listId,
            type,
            status: EnrichmentStatus.PENDING,
            sourceContact: contact,
            contactId: contact.id || contact.recordId,
          }),
        );
      }

      if (linkedinProfileUrl) {
        acc.clayEnrichmentRecords.push({
          taskId,
          recordId: contact.id || contact.recordId,
          linkedInUrl: linkedinProfileUrl,
          type: ClaySearchTypeEnum.ACCESS_EMAIL,
        });
      }

      return acc;
    }, { enrichmentTasks: [], clayEnrichmentRecords: [] });
    if (enrichmentTasks.length) {
      await this.contactEnrichmentTaskRepository.insert(enrichmentTasks);
    }
    if (clayEnrichmentRecords.length) {
      const inprogressIds = [];
      await BBPromise.map(
        clayEnrichmentRecords,
        async (contact) => {
          await this.clayService.handleCreateDataInClay(contact);
          inprogressIds.push(contact.recordId);
        },
        { concurrency: 20 },
      );

      await this.contactEnrichmentTaskRepository.update(
        { contactId: In(inprogressIds) },
        { status: EnrichmentStatus.IN_PROGRESS },
      );

      return clayEnrichmentRecords;
    }
  }

  async getEnrichTasks(id: string, queryParams: GetEnrichmentTaskQueryDTO) {
    const { page = 1, limit = 100 } = queryParams;
    const tasks = await this.contactEnrichmentTaskRepository.find({
      where: {
        listId: id,
        type: queryParams.type,
        status: queryParams.status,
      },
      take: limit,
      skip: (page - 1) * limit,
    });

    return this.formatOutputData(
      { key: 'ENRICH_ALL' },
      {
        data: {
          items: tasks,
          currentPage: Number(page),
          pageSize: Number(limit),
        },
      },
    );
  }

  async createFloquerSearchData(body: CreateSearchFloqerhData) {
    const payload = {
      company_name: body.companyName
    }

    const url = process.env.FLOQER_PUSH_DATA_URL
    const response = await this.httpService.axiosRef.post(
      url,
      { ...payload },
    );
    return this.formatOutputData(
      { key: 'CREATE_FLOQUER_SEARCH' },
      {
        data: {
          total: response.data,
        },
      },
    );
  }

  async createSearchCompanyDetail(body: CreateCompanyDetailSearch) {
    const payload = {
      domain: body.companyDomain,
      record_id: body.recordId
    }

    const url = process.env.FLOQER_SEARCH_COMPANY_DETAIL_URL
    const response = await this.httpService.axiosRef.post(
      url,
      { ...payload },
    );
    return this.formatOutputData(
      { key: 'CREATE_SEARCH_COMPANY_DETAIL' },
      {
        data: {
          total: response.data,
        },
      },
    );
  }

  async getFloquerSearchData(body) {
    const res = body.data?.replace(/^[/\\]+|[/\\]+$/g, '')
    const results =  JSON.parse(res)
    const data = results.map((item) => ({
      ...item,
      id: null,
      name: item.name || null,
      first_name: item.name ? item.name.split(' ')[0] : null,
      last_name: item.name ? item.name.split(' ').slice(1).join(' ') : null,
      profile_pic: item.profile_picture_url || null,
      lookup_type: 1,
      links: {
        linkedin: item.linkedin_profile_url || null,
        facebook: null,
        twitter: item.twitter_handle || null,
      },
      url: null,
      linkedin_url: item.linkedin_profile_url || null,
      location: item.location || null,
      current_title: item.current_title || null,
      current_employer: item.default_position_title || null,
      company_id: item.default_position_company_linkedin_id || null,
      company_info_url: null,
      teaser: {
        emails: item.emails || [],
        phones: [],
        personal_emails: [],
        professional_emails: [],
      },
      education: item.education_background?.map(edu => edu.name) || [],
      jobs: item.employer?.map(emp => `${emp.title} @ ${emp.company}`) || [],
      skills: item.skills || [],
      last_job_change: null,
      company_details: {
        id: item.employer[0]?.company_linkedin_id || null,
        name: item.employer[0]?.company_name || null,
        logo_url: item.employer[0]?.company_logo_url || null,
        info_url: null,
        website_category: null,
      },
    }));

    await this.sseService.emitEvent(ENRICH_CONTACT_FLOQER, data);
    return true
  }

  async createFloquerLinkedinData(body: CreateEnrichDataDto) {
    const records = body.data
    const url = process.env.FLOQER_ENRICH_LINKEDIN_DATA_URL;
    if (Array.isArray(records) && records.length > 0) {
      const responses = await Promise.all(
        records.map(async (record) => {
          const payload = {
            linkedin_url: record.linkedInUrl,
            record_id: record.recordId,
            industry: record?.industry,
            location: record?.location
          };
          const recordPayload = { ...payload, record };
          const response = await this.httpService.axiosRef.post(url, recordPayload);
          return response.data;
        }),
      );
  
      return this.formatOutputData(
        { key: 'CREATE_FLOQUER_LINKEDIN_DATA' },
        {
          data: {
            total: responses,
          },
        },
      );
    }
  }

  async getFloquerLinkedinData(body) {
    const res = body
    await this.sseService.emitEvent(ENRICH_FLOQER_DATA_LINKEDIN_URL,  { ...res.body, person: res.body?.first_name + " " + res.body?.last_name});
    return true
  }

  async getFloquerGetLocation() {
    try {
      const url = process.env.FLOQER_REGIONS_DATA_URL;
      const response = await this.httpService.axiosRef.get(url);
      return this.formatOutputData(
        { key: 'GET_FLOQUER_LOCATION' },
        {
          data:  response.data,
        },
      );

    }  catch (err) {
      console.log("err", err)
    }
  }

  async getFloquerGetSkills() {
    try {
      const url = process.env.FLOQER_SKILLS_DATA_URL;
      const response = await this.httpService.axiosRef.get(url);
      return this.formatOutputData(
        { key: 'GET_FLOQUER_SKILLS' },
        {
          data:  response.data,
        },
      );

    }  catch (err) {
      console.log("err", err)
    }
  }

  async getSearchCompanyDetail(req) {
    await this.sseService.emitEvent(FLOQUER_COMPANY_DETAIL,  req);
    return true
  }

  async createAccessEmailRequest(body: CreateAccessEmailDto) {
    const payload = {
      linkedin_url: body.linkedInUrl,
      record_id: body.recordId
    }

    const url = process.env.FLOQER_ACCESS_EMAIL_URL
    const response = await this.httpService.axiosRef.post(
      url,
      { ...payload },
    );
    return this.formatOutputData(
      { key: 'CREATE_ACCESS_EMAIL_REQUEST' },
      {
        data: {
          total: response.data,
        },
      },
    );
  }

  async getAccessEmailRequest(req) {
    await this.sseService.emitEvent(FLOQUER_ACCESS_EMAIL,  req);
    return true
  }

  async createSavedSearches(userId: string, body: CreateSavedSearchesDto) {
    try {
      const payload = {
        searchName: body.searchName,
        filterFields: body.filterFields,
        createdBy: userId,
        searchType: body.searchType,
      }
      await this.savedSearchesRepository.insert(payload)
      return this.formatOutputData(
        { key: 'CREATE_SAVED_CONTACT' },
        {
          data: {
            data: payload,
          },
        },
      );
    } catch (err) {
      return this.throwCommonMessage('CREATE_SAVED_CONTACT', err);
    }
  }

  async getSavedSearches(userId: string, searchType: SavedSearchesTypeEnum) {
    try {
      const data = await this.savedSearchesRepository.find({
        where: {
          createdBy: userId,
          searchType
        }
      })

      return this.formatOutputData(
        { key: 'GET_SAVED_CONTACT' },
        {
          data: {
            data,
          },
        },
      );
    } catch (err) {
      return this.throwCommonMessage('GET_SAVED_CONTACT', err);
    }
  }

  async deleteSavedSearches(userId: string, savedContactId: string) {
    try {
      const savedContact = await this.savedSearchesRepository.findOne({where: {id: savedContactId, createdBy: userId}})

      if (!savedContact) {
        return this.throwCommonMessage('SAVED_CONTACT_NOT_FOUND', new NotFoundException('Save contact not found'));
      }

      await this.savedSearchesRepository.delete(savedContactId)

      return this.formatOutputData(
        { key: 'DELETE_SAVED_CONTACT' },
        {
          data: {
            data: {},
          },
        },
      );
    } catch (err) {
      return this.throwCommonMessage('DELETE_SAVED_CONTACT', err);
    }
  }


  async updateSavedSearches(savedContactId: string, userId: string, body: UpdateSavedSearchesDto) {
    try {
      const savedContact = await this.savedSearchesRepository.findOne({where: {id: savedContactId, createdBy: userId}})

      if (!savedContact) {
        return this.throwCommonMessage('SAVED_CONTACT_NOT_FOUND', new NotFoundException('Save contact not found'));
      }

      await this.savedSearchesRepository.update(savedContact.id, {
        searchName: body.searchName
      })

      return this.formatOutputData(
        { key: 'UPDATE_SAVED_CONTACT' },
        {
          data: {
            data: savedContact,
          },
        },
      );
    } catch (err) {
      console.error("UPDATE_SAVED_CONTACT", err);
      return this.throwCommonMessage('UPDATE_SAVED_CONTACT', err);
    }
  }

  async createBulkEnrichTask(userId: string, body: createBulkEnrichTaskDto) {
    try {

      const user = await this.userRepository.findOne({where: {id: userId}})

      if (!user) {
        return this.throwCommonMessage('USER_NOT_FOUND', new NotFoundException('User not found'));
      }

      const payloadBulk = {
        additionalField: body.additionalField,
        sourceContact: body.contactInformation,
        createdBy: userId,
        status: BulkAddBullhornStatus.PENDING,
      }

      const dataCreate = await this.bulkAddBullhornTaskRepository.save(payloadBulk)

      // create task to clay
      const payload = await Promise.all(
        body.contactInformation.map(async (item: any) => {
          let domain = '';
      
          if (item?.companyId) {
            try {
              const accountId = await this.linkedinFinderService.handleGetAccountUnipile();
              const companyUnipile = await unipileClient.getCompanyDetail(accountId, item?.companyId);
              domain = new URL(companyUnipile?.website)?.hostname?.replace(/^www\./, '');;
            } catch (error) {
              domain = '';
            }
          }
      
          return {
            recordId: item.recordId,
            linkedInUrl: item.linkedInUrl,
            taskId: dataCreate.id,
            type: ClaySearchTypeEnum.BULK_ADD_TO_BULLHORN,
            companyDomain: item?.companyDomain || domain,
            fullName: item?.name,
            companyName: item?.companyName
          };
        })
      );

      const results = Promise.all(
        payload.map((data) => {
          this.clayService.handleCreateDataInClay(data);
        })
      );

      return this.formatOutputData(
        { key: 'CREATE_BULK_ENRICH_TASK' },
        {
          data: {
            data: [],
          },
        },
      );

    } catch (err) {
      console.error("CREATE_BULK_ENRICH_TASK", err);
      return this.throwCommonMessage('CREATE_BULK_ENRICH_TASK', err);
    } 
  }
}

import { Controller, Get, Query, HttpStatus, Res, Body, Post, Req, Param, Delete, Patch, UseGuards } from '@nestjs/common';
import { EmailFinderService } from './email-finder.service';
import { SkipThrottle } from "@nestjs/throttler";


@Controller('clay-webhook')
@SkipThrottle()
export class ClayWebhookController {
  constructor(private readonly emailFinderService: EmailFinderService) { }
  @Post("/get-enrich-data")
  async getEnrichData(@Req() req,) {
    return this.emailFinderService.createEnrichEvent(req.query, req);
  }
}

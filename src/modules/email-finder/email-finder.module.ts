import { <PERSON>du<PERSON> } from '@nestjs/common';
import { EmailFinderController } from './email-finder.controller';
import { EmailFinderService } from './email-finder.service';
import {HttpModule} from "@nestjs/axios";
import { EmailValidationResultRepository } from './repositories/email-validation-result.repository';
import { EmployeeFinderService } from '../employee-finder/services/employee-finder.service';
import { ApolloService } from '../employee-finder/services/apollo.service';
import { ClayService } from './clay.service';
import { SSEService } from '../sse/sse.service';
import { UserRepository } from '../user/repositories/user.repository';
import { ContactEnrichmentTaskRepository } from './repositories/contact-enrichment-tasks.repository';
import { ContactRepository } from '../user/repositories/contact.repository';
import { BullHornService } from 'src/middlewares/bullhorn/bullhorn.service';
import { SavedSearchesRepository } from './repositories/saved-searches.repository';
import { BulkAddBullhornTasksRepository } from './repositories/bulk-add-bullhorn-tasks.repository';
import { NotificationRepository } from '../notification/repositories/notification.repository';
import { LinkedInFinderService } from '../linkedin-finder/linkedin-finder.service';
import { MyCacheModule } from '../cache/cache.module';
import { ClayWebhookController } from './clay-webhook.controller';


@Module({
  imports: [
    HttpModule,
    MyCacheModule
  ],
  controllers: [EmailFinderController, ClayWebhookController],
  providers: [
    EmailFinderService,
    EmailValidationResultRepository,
    EmployeeFinderService,
    ApolloService,
    ClayService,
    SSEService,
    UserRepository,
    ContactEnrichmentTaskRepository,
    ContactRepository,
    BullHornService,
    SavedSearchesRepository,
    BulkAddBullhornTasksRepository,
    NotificationRepository,
    LinkedInFinderService
  ],
})
export class EmailFinderModule {}

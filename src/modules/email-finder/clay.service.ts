import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { CLAY_ENDPOINT } from './constants/clay.constants';

@Injectable()
export class ClayService {
  constructor() {}

  async handleCreateDataInClay(body: any){
    try {
        const response = await axios.post(CLAY_ENDPOINT.PULL_DATA_HOOK, {body})
        return response.data
    } catch (e) {
        console.log(e);
    }
  }
}

import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';


export enum EnrichmentContactType {
  HOTLIST = 'HOTLIST',
  CONTACTLIST = 'CONTACTLIST',
  ACCESS_EMAIL = 'ACCESS_EMAIL',
}

export enum BulkAddBullhornStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  SUCCESSFUL = 'SUCCESSFUL',
  ERROR = 'ERROR',
}


@Entity({ name: 'bulk_add_bullhorn_tasks' })
export class BulkAddBullhornTaskEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'created_by', nullable: true })
  createdBy: string;

  @Column({ name: 'status', enum: BulkAddBullhornStatus })
  status: BulkAddBullhornStatus;

  @Column({ name: 'additional_field', type: 'jsonb' })
  additionalField: any;

  @Column({ name: 'source_contact', type: 'jsonb', nullable: true })
  sourceContact: any;

  @Column({ name: 'enriched_contact', type: 'jsonb', nullable: true })
  enrichedContact: any;

  @Column({ name: 'bullhorn_added', type: 'jsonb', nullable: true })
  bullhornAdded: any;

  @Column({ name: 'history', type: 'jsonb', nullable: true })
  history: any;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';


export enum EnrichmentContactType {
  HOTLIST = 'HOTLIST',
  CONTACTLIST = 'CONTACTLIST',
  ACCESS_EMAIL = 'ACCESS_EMAIL',
}

export enum EnrichmentStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  SUCCESSFUL = 'SUCCESSFUL',
  ERROR = 'ERROR',
}


@Entity({ name: 'contact_enrichment_tasks' })
export class ContactEnrichmentTaskEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'list_id' })
  listId: string;

  @Column({ name: 'type', enum: EnrichmentContactType })
  type: EnrichmentContactType;

  @Column({ name: 'status', enum: EnrichmentStatus })
  status: EnrichmentStatus;

  @Column({ name: 'source_contact', type: 'jsonb' })
  sourceContact: any;

  @Column({ name: 'enrich_contact', type: 'jsonb', nullable: true })
  enrichContact: any;

  @Column({ name: 'contact_id' })
  contactId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

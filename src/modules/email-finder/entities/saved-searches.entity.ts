import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SavedSearchesTypeEnum } from '../enum/saved-search.enum';

@Entity({ name: 'saved_searches' })
export class SavedSearchesEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'search_name' })
  searchName: string;

  @Column({ name: 'search_type', enum: SavedSearchesTypeEnum, nullable: true })
  searchType: SavedSearchesTypeEnum;

  @Column({ name: 'created_by'})
  createdBy: string;

  @Column({ name: 'filter_fields', type: "jsonb", nullable: true })
  filterFields: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

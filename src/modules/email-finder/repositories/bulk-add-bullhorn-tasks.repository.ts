import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { BulkAddBullhornTaskEntity } from '../entities/bulk-add-bullhorn-task.entity';

@Injectable()
export class BulkAddBullhornTasksRepository extends Repository<BulkAddBullhornTaskEntity> {
  constructor(private dataSource: DataSource) {
    super(BulkAddBullhornTaskEntity, dataSource.createEntityManager());
  }
}

import { Injectable } from '@nestjs/common';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import { EmailValidationResultEntity } from '../entities/email-validation-results.entity';

@Injectable()
export class EmailValidationResultRepository extends Repository<EmailValidationResultEntity> {
  constructor(private dataSource: DataSource) {
    super(EmailValidationResultEntity, dataSource.createEntityManager());
  }
}

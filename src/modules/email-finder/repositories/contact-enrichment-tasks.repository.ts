import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { ContactEnrichmentTaskEntity } from '../entities/contact-enrichment-tasks.entity';

@Injectable()
export class ContactEnrichmentTaskRepository extends Repository<ContactEnrichmentTaskEntity> {
  constructor(private dataSource: DataSource) {
    super(ContactEnrichmentTaskEntity, dataSource.createEntityManager());
  }
}

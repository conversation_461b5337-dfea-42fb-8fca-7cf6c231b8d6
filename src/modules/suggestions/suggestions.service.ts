import { Injectable, InternalServerErrorException } from '@nestjs/common';
import axios from 'axios-https-proxy-fix';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { CacheService } from '../cache/cache.service';

@Injectable()
export class SuggestionsService extends BaseAbstractService {
  constructor(readonly i18nService: I18nService, private readonly cacheService: CacheService) {
    super(i18nService);
  }

  async getKeywordSuggestions(query: string) {
    try {
      if (!query) {
        return [];
      }

      const cachedKey = `suggestions.keywords.${query}`;

      if (query) {
        const standardResponseJSON = await this.cacheService.get(cachedKey);
        if (standardResponseJSON) {
          return this.formatOutputData(
            { key: 'GET_LIST_SUGGESTIONS_KEYWORD' },
            { data: JSON.parse(standardResponseJSON) },
          );
        }
      }

      const { data } = await axios.get(`https://www.linkedin.com/jobs-guest/api/typeaheadHits?query=${query}`, {
        proxy: {
          host: process.env.PROXY_HOST,
          port: +process.env.PROXY_PORT,
          auth: {
            username: process.env.PROXY_USERNAME,
            password: process.env.PROXY_PASSWORD,
          },
        },
      });

      const result = data?.map((item: any) => ({
        id: item?.id,
        displayName: item?.displayName,
      }));

      await this.cacheService.set(cachedKey, JSON.stringify(result), 60 * 60 * 6);

      return this.formatOutputData({ key: 'GET_LIST_SUGGESTIONS_KEYWORD' }, { data: result });
    } catch (error) {
      throw new InternalServerErrorException('Error fetching data keyword suggestions ');
    }
  }
}

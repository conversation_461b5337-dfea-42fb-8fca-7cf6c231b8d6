import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { ApiTags } from '@nestjs/swagger';
import { SuggestionsService } from './suggestions.service';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@ApiTags('Suggestions')
@Controller('suggestions')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class SuggestionsController {
  constructor(private readonly suggestionsService: SuggestionsService) {}

  @Get('keywords')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async searchAddress(@Query('query') query: string) {
    return this.suggestionsService.getKeywordSuggestions(query);
  }
}

import { Module } from '@nestjs/common';
import { HttpModule, HttpService } from '@nestjs/axios';
import { MyCacheModule } from '../cache/cache.module';
import { CrmCompanyController } from './controller/crm-company.controller';
import { CrmIndustryController } from './controller/crm-industry.controller';
import { CrmIndustryService } from './services/crm-industry.service';
import { CrmIndustryRepository } from './repositories/crm-industry.repository';
import { Repository } from 'typeorm';
import { CrmCompanyService } from './services/crm-company.service';
import { CrmCompanyRepository } from './repositories/crm-company.repository';
import { UserRepository } from '../user/repositories/user.repository';
import { CrmLeadRepository } from './repositories/crm-lead.repository';
import { CrmContactRepository } from './repositories/crm-contact.repository';
import { CrmSkillsRepository } from './repositories/crm-skill.repository';
import { CrmSkillService } from './services/crm-skill.service';
import { CrmSkillController } from './controller/crm-skill.controller';
import { CrmContactController } from './controller/crm-contact.controller';
import { CrmContactService } from './services/crm-contact.service';
import { CrmLeadController } from './controller/crm-lead.controller';
import { CrmLeadService } from './services/crm-lead.service';
import { CrmNoteController } from './controller/crm-note.controller';
import { CrmNoteService } from './services/crm-note.service';
import { CrmNoteRepository } from './repositories/crm-note.repository';
import { CrmController } from './controller/crm.controller';
import { CrmService } from './services/crm.service';
import { OrganizationEntity } from '../user/entities/organization.entity';
import { CrmTagController } from './controller/crm-tag.controller';
import { CrmTagEntity } from './entities/crm-tag.entity';
import { CrmTagRepository } from './repositories/crm-tag.repository';
import { CrmTagService } from './services/crm-tag.service';
import { CrmFileService } from './services/crm-file.service';
import { CrmFileRepository } from './repositories/crm-file.repository';
import { CrmFileController } from './controller/crm-file.controller';
import { CrmBullhornService } from './services/crm-bullhorn.service';
import { CrmContactSequenceRepository } from './repositories/crm-contact-sequence.repository';
import { CrmContactSequenceStepRepository } from './repositories/crm-contact-sequence-step.repository';
import { CrmContactSequenceStepReplyRepository } from './repositories/crm-contact-sequence-step-reply.repository';
import { OrganizationQuotaRepository } from '../subscription/repositories/organization-quota.repository';
import { BullhornIntegrationService } from '../jobs/service/bullhorn-integration.service';
import { StatsEntityDataRepository } from '../jobs/repository/stats-entity-data.repository';

@Module({
  imports: [HttpModule, MyCacheModule],
  controllers: [
    CrmCompanyController,
    CrmIndustryController,
    CrmSkillController,
    CrmContactController,
    CrmLeadController,
    CrmNoteController,
    CrmController,
    CrmTagController,
    CrmFileController
  ],
  providers: [
    CrmIndustryService,
    CrmIndustryRepository,
    Repository,
    CrmCompanyService,
    CrmCompanyRepository,
    UserRepository,
    CrmLeadRepository,
    CrmContactRepository,
    CrmSkillsRepository,
    CrmSkillService,
    CrmContactService,
    CrmLeadService,
    CrmNoteService,
    CrmNoteRepository,
    CrmService,
    OrganizationEntity,
    CrmTagRepository,
    CrmTagEntity,
    CrmTagService,
    CrmFileService,
    CrmFileRepository,
    CrmBullhornService,
    CrmContactSequenceRepository,
    CrmContactSequenceStepRepository,
    CrmContactSequenceStepReplyRepository,
    OrganizationQuotaRepository,
    StatsEntityDataRepository
  ],
  exports: [
    CrmService,
    CrmCompanyService,
    CrmIndustryService,
    CrmSkillService,
    CrmContactService,
    CrmLeadService,
    CrmNoteService,
    CrmTagService,
    CrmBullhornService,
  ]
})
export class ZileoCrmModule {}

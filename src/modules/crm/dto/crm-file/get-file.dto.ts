import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional } from "class-validator";
import { CrmNoteResourceName } from "../../entities/crm-file.entity";

export class getFilesDto {
    @ApiProperty()
    @IsNotEmpty()
    resourceName: CrmNoteResourceName;

    @ApiProperty()
    @IsNotEmpty()
    resourceId: string;

    @ApiPropertyOptional()
    @IsOptional()
    page: number;

    @ApiPropertyOptional()
    @IsOptional()
    limit: number;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';

export class CreateSkillDto {
  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  organizationId: string;

  @ApiPropertyOptional()
  @IsOptional()
  description?: string;
}

export class CreateSkillsDto {
  @ApiProperty()
  @Type(() => CreateSkillDto)
  @ValidateNested({ each: true })
  data: CreateSkillDto[];
}

import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional } from "class-validator";
import { BulkAddBullhornStatus } from "src/modules/email-finder/entities/bulk-add-bullhorn-task.entity";
import { CrmLeadType } from "../../entities/crm-lead.entity";
import { CrmNoteAction, CrmNoteResourceName, CrmNoteStatus } from "../../entities/crm-note.entity";

export class EditNoteDto {
    @ApiProperty()
    @IsNotEmpty()
    title: string;

    @ApiProperty()
    @IsNotEmpty()
    content: string;

    @ApiPropertyOptional()
    @IsOptional()
    resourceName: CrmNoteResourceName;

    @ApiPropertyOptional()
    @IsOptional()
    resourceId: string;

    @ApiPropertyOptional()
    @IsOptional()
    status: CrmNoteStatus;

    @ApiPropertyOptional()
    @IsOptional()
    action: CrmNoteAction;
}

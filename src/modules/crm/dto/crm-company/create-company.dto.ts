import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional } from "class-validator";

export class createCompanyDto {
    @ApiProperty()
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional()
    @IsOptional()
    description: string;

    @ApiPropertyOptional()
    @IsOptional()
    website: string;

    @ApiPropertyOptional()
    @IsOptional()
    telephone: string;

    @ApiPropertyOptional()
    @IsOptional()
    address: any;

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    industries: [];

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    tags: [];
}

export class getCompanyQuery{
    @ApiPropertyOptional()
    @IsOptional()
    companyIds: string[];

    @ApiPropertyOptional()
    @IsOptional()
    keyword?: string;

    @ApiPropertyOptional()
    @IsOptional()
    page: number;

    @ApiPropertyOptional()
    @IsOptional()
    limit: number;

    @ApiPropertyOptional()
    @IsOptional()
    sortBy: string;

    @ApiPropertyOptional()
    @IsOptional()
    sortOrder: string;

    @ApiPropertyOptional()
    @IsOptional()
    industries?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    tags?: string[];
}
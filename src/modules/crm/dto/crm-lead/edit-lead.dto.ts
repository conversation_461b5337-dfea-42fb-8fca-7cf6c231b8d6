import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional, Matches } from "class-validator";
import { CrmLeadType } from "../../entities/crm-lead.entity";

export class EditLeadDto {
    @ApiProperty()
    @IsNotEmpty()
    jobTitle: string;

    @ApiPropertyOptional()
    @IsOptional()
    companyId: string;

    @ApiPropertyOptional()
    @IsOptional()
    description: string;

    @ApiPropertyOptional()
    @IsOptional()
    website: string;

    @ApiPropertyOptional()
    @IsOptional()
    address: any;

    @ApiPropertyOptional()
    @IsOptional()
    jobType: CrmLeadType;

    @ApiPropertyOptional()
    @IsOptional()
    salary: string;

    @ApiPropertyOptional()
    @IsOptional()
    @Matches(/^[A-Z]{3}$/, { message: 'Currency is not valid' })
    currencyUnit: string;

    @ApiPropertyOptional()
    @IsOptional()
    payRate: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    industries: [];

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    skills: [];

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    tags: [];
}

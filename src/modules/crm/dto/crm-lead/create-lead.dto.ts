import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional, Matches } from "class-validator";
import { BulkAddBullhornStatus } from "src/modules/email-finder/entities/bulk-add-bullhorn-task.entity";
import { CrmLeadType } from "../../entities/crm-lead.entity";

export class CreateLeadDto {
    @ApiProperty()
    @IsNotEmpty()
    jobTitle: string;

    @ApiProperty()
    @IsNotEmpty()
    companyId: string;

    @ApiPropertyOptional()
    @IsOptional()
    description: string;

    @ApiPropertyOptional()
    @IsOptional()
    website: string;

    @ApiPropertyOptional()
    @IsOptional()
    address: any;

    @ApiPropertyOptional()
    @IsOptional()
    jobType: CrmLeadType;

    @ApiPropertyOptional()
    @IsOptional()
    salary: string;

    @ApiPropertyOptional()
    @IsOptional()
    @Matches(/^[A-Z]{3}$/, { message: 'Currency is not valid' })
    currencyUnit: string;

    @ApiPropertyOptional()
    @IsOptional()
    payRate: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    industries: [];

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    skills: [];

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    tags: [];
}

export class getLeadsQuery{
    @ApiPropertyOptional()
    @IsOptional()
    companyIds: string[];

    @ApiPropertyOptional()
    @IsOptional()
    keyword?: string;

    @ApiPropertyOptional()
    @IsOptional()
    page: number;

    @ApiPropertyOptional()
    @IsOptional()
    limit: number;

    @ApiPropertyOptional()
    @IsOptional()
    sortBy: string;

    @ApiPropertyOptional()
    @IsOptional()
    sortOrder: string;

    @ApiPropertyOptional()
    @IsOptional()
    industries?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    jobType?: CrmLeadType[];

    @ApiPropertyOptional()
    @IsOptional()
    tags?: string[];
}
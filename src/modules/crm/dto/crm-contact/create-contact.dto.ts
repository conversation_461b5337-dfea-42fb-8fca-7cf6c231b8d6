import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional, IsUrl } from "class-validator";

export class createContactDto {
    @ApiProperty()
    @IsNotEmpty()
    firstName: string;

    @ApiPropertyOptional()
    @IsOptional()
    surName: string;

    @ApiPropertyOptional()
    @IsOptional()
    middleName: string;

    @ApiPropertyOptional()
    @IsOptional()
    phone: string;

    @ApiPropertyOptional()
    @IsOptional()
    address: any;

    @ApiPropertyOptional()
    @IsOptional()
    companyId: string;

    @ApiPropertyOptional()
    @IsOptional()
    email: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    industries: [];

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    skills: [];

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    tags: [];

    @ApiPropertyOptional()
    @IsOptional()
    jobTitle: string;

    @ApiPropertyOptional()
    @IsOptional()
    consultantId: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsUrl()
    linkedinProfileUrl: string;
}

export class getContactQuery{
    @ApiPropertyOptional()
    @IsOptional()
    companyIds: string[];

    @ApiPropertyOptional()
    @IsOptional()
    keyword?: string;

    @ApiPropertyOptional()
    @IsOptional()
    page: number;

    @ApiPropertyOptional()
    @IsOptional()
    limit: number;

    @ApiPropertyOptional()
    @IsOptional()
    sortBy: string;

    @ApiPropertyOptional()
    @IsOptional()
    sortOrder: string;

    @ApiPropertyOptional()
    @IsOptional()
    industries?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    tags?: string[];
}
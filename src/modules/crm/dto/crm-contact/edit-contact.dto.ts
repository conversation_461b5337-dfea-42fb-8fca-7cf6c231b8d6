import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional, IsUrl } from "class-validator";

export class EditContactDto {
    @ApiProperty()
    @IsNotEmpty()
    firstName: string;

    @ApiPropertyOptional()
    @IsOptional()
    surName: string;

    @ApiPropertyOptional()
    @IsOptional()
    middleName: string;

    @ApiPropertyOptional()
    @IsOptional()
    phone: string;

    @ApiPropertyOptional()
    @IsOptional()
    address: any;

    @ApiPropertyOptional()
    @IsOptional()
    companyId: string;

    @ApiPropertyOptional()
    @IsOptional()
    email: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    industries: [];

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    tags: [];

    @ApiPropertyOptional()
    @IsOptional()
    @IsUrl()
    linkedinProfileUrl: string;
}

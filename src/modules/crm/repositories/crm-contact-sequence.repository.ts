import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { CrmContactSequenceEntity } from '../entities/crm-contact-sequence.entity';

@Injectable()
export class CrmContactSequenceRepository extends Repository<CrmContactSequenceEntity> {
  constructor(private dataSource: DataSource) {
    super(CrmContactSequenceEntity, dataSource.createEntityManager());
  }
}

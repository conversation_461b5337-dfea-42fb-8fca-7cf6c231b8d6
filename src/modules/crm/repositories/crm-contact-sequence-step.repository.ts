import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { CrmContactSequenceStepEntity } from '../entities/crm-contact-sequence-step.entity';

@Injectable()
export class CrmContactSequenceStepRepository extends Repository<CrmContactSequenceStepEntity> {
  constructor(private dataSource: DataSource) {
    super(CrmContactSequenceStepEntity, dataSource.createEntityManager());
  }
}

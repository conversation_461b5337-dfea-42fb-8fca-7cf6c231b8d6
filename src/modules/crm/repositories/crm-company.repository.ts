import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { CrmCompanyEntity } from '../entities/crm-company.entity';

@Injectable()
export class CrmCompanyRepository extends Repository<CrmCompanyEntity> {
  constructor(private dataSource: DataSource) {
    super(CrmCompanyEntity, dataSource.createEntityManager());
  }

  async findOrCreateByBullhornId(bullhornId: string, organizationId: string, companyData: any): Promise<CrmCompanyEntity> {
    let company = await this.findOne({
      where: { bullhornId, organizationId },
    });

    if (!company) {
      await this.createQueryBuilder()
        .insert()
        .values([
          {
            ...companyData,
            id: undefined,
            bullhornId,
            organizationId,
          } as CrmCompanyEntity,
        ])
        .orUpdate(
          ['bullhorn_id', 'organization_id'],
          ['id'],
        )
        .execute();

      // Get the company with the new data after insert/update
      company = await this.findOne({
        where: { bullhornId, organizationId },
      });
    }

    return company;
  }
}

import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { CrmContactSequenceStepReplyEntity } from '../entities/crm-contact-sequence-step-reply.entity';

@Injectable()
export class CrmContactSequenceStepReplyRepository extends Repository<CrmContactSequenceStepReplyEntity> {
  constructor(private dataSource: DataSource) {
    super(CrmContactSequenceStepReplyEntity, dataSource.createEntityManager());
  }
}

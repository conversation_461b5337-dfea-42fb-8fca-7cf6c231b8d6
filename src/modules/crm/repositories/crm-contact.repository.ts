import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { CrmContactEntity } from '../entities/crm-contact.entity';
import { CrmCompanyEntity } from '../entities/crm-company.entity';

@Injectable()
export class CrmContactRepository extends Repository<CrmContactEntity> {
  constructor(private dataSource: DataSource) {
    super(CrmContactEntity, dataSource.createEntityManager());
  }

  async findOrCreateByBullhornId(
    bullhornId: string,
    organizationId: string,
    contactData: any,
  ): Promise<CrmContactEntity> {
    let contact = await this.findOne({
      where: { bullhornId, organizationId }
    });

    if (!contact) {
      contact = this.create({
        ...contactData,
        id: undefined,
        bullhornId,
        organizationId,
      } as CrmContactEntity);
      return await this.save(contact);
    }

    return contact;
  }
}

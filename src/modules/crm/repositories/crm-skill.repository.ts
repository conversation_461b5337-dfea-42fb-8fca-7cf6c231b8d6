import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { CrmCompanyEntity } from '../entities/crm-company.entity';
import { CrmSkillsEntity } from '../entities/crm-skill.entity';

@Injectable()
export class CrmSkillsRepository extends Repository<CrmSkillsEntity> {
  constructor(private dataSource: DataSource) {
    super(CrmSkillsEntity, dataSource.createEntityManager());
  }
}

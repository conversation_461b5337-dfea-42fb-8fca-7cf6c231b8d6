import { Injectable } from '@nestjs/common';
import { CrmCompanyEntity } from '../entities/crm-company.entity';
import { CrmCompanyRepository } from '../repositories/crm-company.repository';
import { CrmContactRepository } from '../repositories/crm-contact.repository';
import { validate as uuidValidate } from 'uuid';
import { In } from 'typeorm';
import { CrmIndustryEntity } from '../entities/crm-industry.entity';
import { CrmSkillsEntity } from '../entities/crm-skill.entity';
import { CrmIndustryRepository } from '../repositories/crm-industry.repository';
import { CrmSkillsRepository } from '../repositories/crm-skill.repository';
import { CrmTagRepository } from '../repositories/crm-tag.repository';
import { CrmTagEntity } from '../entities/crm-tag.entity';

@Injectable()
export class CrmBullhornService {
  constructor(
    private readonly crmCompanyRepository: CrmCompanyRepository,
    private readonly crmContactRepository: CrmContactRepository,
    private readonly crmSkillsRepository: CrmSkillsRepository,
    private readonly crmIndustryRepository: CrmIndustryRepository,
    private readonly crmTagRepository: CrmTagRepository,
  ) {}

  async getOrCreateSkills(skillNames: string[]): Promise<CrmSkillsEntity[]> {
    if (!skillNames?.length) return [];

    const existingSkills = await this.crmSkillsRepository.findBy({
      name: In(skillNames)
    });
    const existingSkillNames = existingSkills.map(skill => skill.name);
    const newSkillNames = skillNames.filter(name => !existingSkillNames.includes(name));

    const newSkills = await this.crmSkillsRepository.save(
      newSkillNames.map(name => ({ name }))
    );

    return [...existingSkills, ...newSkills];
  }

  async getOrCreateIndustries(industryNames: string[]): Promise<CrmIndustryEntity[]> {
    if (!industryNames?.length) return [];

    const existingIndustries = await this.crmIndustryRepository.findBy({
      name: In(industryNames)
    });
    const existingIndustryNames = existingIndustries.map(industry => industry.name);
    const newIndustryNames = industryNames.filter(name => !existingIndustryNames.includes(name));

    const newIndustries = await this.crmIndustryRepository.save(
      newIndustryNames.map(name => ({ name }))
    );

    return [...existingIndustries, ...newIndustries];
  }

  async getOrCreateTags(tagNames: string[]): Promise<CrmTagEntity[]> {
    if (!tagNames?.length) return [];

    const existingTags = await this.crmTagRepository.findBy({
      name: In(tagNames)
    });
    const existingTagNames = existingTags.map(tag => tag.name);
    const newTagNames = tagNames.filter(name => !existingTagNames.includes(name));

    const newTags = await this.crmTagRepository.save(
      newTagNames.map(name => ({ name }))
    );

    return [...existingTags, ...newTags];
  }

  async syncBullhornCompany(bullhornCompany: any, organizationId: string): Promise<CrmCompanyEntity> {
    const companyData: any = {
      bullhornId: bullhornCompany.id.toString(),
      name: bullhornCompany.name,
      description: bullhornCompany.companyDescription,
      website: bullhornCompany.companyURL,
      telephone: bullhornCompany.phone,
      address: {
        city: bullhornCompany.address?.state,
        county: bullhornCompany.address?.county,
        country: bullhornCompany.address?.countryName,
        address1: bullhornCompany.address?.address1,
        address2: bullhornCompany.address?.address2,
        postCode: bullhornCompany.address?.zip
      },
      creator: bullhornCompany.userId,
      organizationId,
    };
    if (bullhornCompany.businessSectorList?.length) {
      companyData.industries = await this.getOrCreateIndustries(bullhornCompany.businessSectorList);
    }
    if (bullhornCompany.skills?.length) {
      companyData.skills = await this.getOrCreateSkills(bullhornCompany.skills);
    }

    return this.crmCompanyRepository.findOrCreateByBullhornId(
      companyData.bullhornId,
      organizationId,
      companyData,
    );
  }

  async syncBullhornContact(bullhornContact: any, organizationId: string) {
    // Sync company first
    const company = await this.syncBullhornCompany(
      bullhornContact.clientCorporation,
      organizationId,
    );

    const contactData = {
      ...bullhornContact,
      firstName: bullhornContact.firstName,
      middleName: bullhornContact.middleName,
      surName: bullhornContact.lastName,
      telephone: bullhornContact.phone,
      jobTitle: bullhornContact.occupation,
      email: bullhornContact.email,
      address: bullhornContact.address,
      linkedinProfileUrl: bullhornContact.customText1,
      company,
    };
    if (bullhornContact.businessSectorList?.length) {
      contactData.industries = await this.getOrCreateIndustries(bullhornContact.businessSectorList);
    }
    if (bullhornContact.skills?.length) {
      contactData.skills = await this.getOrCreateSkills(bullhornContact.skills);
    }

    // Then sync contact
    const contact = await this.crmContactRepository.findOrCreateByBullhornId(
      bullhornContact.id.toString(),
      organizationId,
      contactData,
    );

    return contact;
  }

  async findAndSyncBullhornContacts(bullhornContacts: any[], organizationId: string, userLicenseType: string) {
    const internelIds = bullhornContacts.filter(r => uuidValidate(r.id)).map(r => r.id);
    // Since BH contacts have ids as numbers, we need to filter them
    const externalIds = bullhornContacts.filter(r => typeof r.id === 'number' && userLicenseType === 'CONNECTED');

    const [stringContacts, numberContacts] = await Promise.all([
      internelIds.length ? this.crmContactRepository.find({
        where: {
          id: In(internelIds),
        }
      }) : [],
      Promise.all(externalIds.map(recipient => 
        this.syncBullhornContact(recipient, organizationId)
      ))
    ]);

    return [...stringContacts, ...numberContacts];
  }
}
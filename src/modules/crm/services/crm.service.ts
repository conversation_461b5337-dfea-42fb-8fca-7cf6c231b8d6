import { CrmContactRepository } from './../repositories/crm-contact.repository';
import { CrmCompanyRepository } from './../repositories/crm-company.repository';
import { CrmContactSequenceRepository } from '../repositories/crm-contact-sequence.repository';
import { CrmContactSequenceStepRepository } from '../repositories/crm-contact-sequence-step.repository';
import { CrmContactSequenceStepReplyRepository } from '../repositories/crm-contact-sequence-step-reply.repository';
/* eslint-disable camelcase */
import { Injectable, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource } from 'typeorm';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmLeadRepository } from '../repositories/crm-lead.repository';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { CrmTagRepository } from '../repositories/crm-tag.repository';
import { SequenceStepEntity } from 'src/modules/mail/entities/sequence-step.entity';


@Injectable()
@UseGuards(AuthenticationGuard)
export class CrmService extends BaseAbstractService {
    constructor(
        private readonly dataSource: DataSource,
        private readonly httpService: HttpService,
        private readonly i18nService: I18nService,
        private readonly crmCompanyRepository: CrmCompanyRepository,
        private readonly crmLeadRepository: CrmLeadRepository,
        private readonly crmContactRepository: CrmContactRepository,
        private readonly userRepository: UserRepository,
        private readonly crmTagRepository: CrmTagRepository,
        private readonly crmContactSequenceRepository: CrmContactSequenceRepository,
        private readonly crmContactSequenceStepRepository: CrmContactSequenceStepRepository,
        private readonly crmContactSequenceStepReplyRepository: CrmContactSequenceStepReplyRepository
    ) {
        super(i18nService);
    }

    async getCrmStats(userId: string) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            return this.throwCommonMessage('GET_CRM_STATS', new NotFoundException('User not found'));
        }
        const [companyCount, leadCount, contactCount, tagCount] = await Promise.all([
            this.crmCompanyRepository.count({ where: { organizationId: user.organizationId } }),
            this.crmLeadRepository.count({ where: { organizationId: user.organizationId } }),
            this.crmContactRepository.count({ where: { organizationId: user.organizationId } }),
            this.crmTagRepository.count({ where: { organization: {id: user.organizationId}}})
        ]);
        return this.formatOutputData(
            { key: 'GET_CRM_STATS' },
            {
                data: {
                    companyCount,
                    leadCount,
                    contactCount,
                    tagCount
                },
            }
        );
    }

    async getCrmSequence(userId: string, entityType: string, entityId: string, { page = 1, limit = 10 }: { page?: number, limit?: number }) {
        page = isNaN(Number(page)) ? 1 : Number(page);
        limit = isNaN(Number(limit)) ? 10 : Number(limit);
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            return this.throwCommonMessage('GET_CRM_SEQUENCE', new NotFoundException('User not found'));
        }

        const skip = (page - 1) * limit || 0;
        let sequences = [];
        let totalCount = 0;

        if (entityType === 'contact') {
            // For contacts, use standard pagination without grouping
            [sequences, totalCount] = await this.crmContactSequenceRepository.findAndCount({
                where: {
                    [entityType]: {
                        id: entityId,
                    }
                },
                relations: {
                    sequence: true
                },
                select: {
                    id: true,
                    createdAt: true,
                    status: true,
                    fromEmail: true,
                    sequence: {
                        id: true,
                        name: true,
                        status: true,
                        isMarkedAsCompleted: true
                    }
                },
                skip,
                take: limit,
                order: {
                    createdAt: 'DESC'
                }
            });
        } else {
            // For company and lead, use a custom query with GROUP BY
            // First, get the total count of unique sequences
            const countQuery = await this.dataSource.createQueryBuilder('crm_contact_sequences', 'ccs')
                .select('COUNT(DISTINCT(ccs.sequence_id))', 'count')
                .where(`ccs.${entityType}_id = :entityId`, { entityId })
                .getRawOne();

            totalCount = parseInt(countQuery.count);

            // Then, get the paginated results with a subquery to get the most recent sequence for each group
            // This uses a window function to rank sequences by creation date within each group
            const query = this.dataSource.createQueryBuilder()
                .select('sq.id', 'id')
                .addSelect('sq.created_at', 'createdAt')
                .addSelect('sq.sequence_id', 'sequenceId')
                .addSelect('sq.sequence_name', 'sequenceName')
                .addSelect('sq.sequence_status', 'sequenceStatus')
                .addSelect('sq.sequence_is_marked_as_completed', 'sequenceIsMarkedAsCompleted')
                .addSelect('sq.status', 'status')
                .addSelect('sq.from_email', 'fromEmail')
                .from(subQuery => {
                    return subQuery
                        .select('ccs.id', 'id')
                        .addSelect('ccs.created_at', 'created_at')
                        .addSelect('ccs.status', 'status')
                        .addSelect('ccs.from_email', 'from_email')
                        .addSelect('s.id', 'sequence_id')
                        .addSelect('s.name', 'sequence_name')
                        .addSelect('s.status', 'sequence_status')
                        .addSelect('s.is_marked_as_completed', 'sequence_is_marked_as_completed')
                        .addSelect(
                            'ROW_NUMBER() OVER (PARTITION BY s.id ORDER BY ccs.created_at DESC)',
                            'rn'
                        )
                        .from('crm_contact_sequences', 'ccs')
                        .innerJoin('sequences', 's', 'ccs.sequence_id = s.id')
                        .where(`ccs.${entityType}_id = :entityId`, { entityId });
                }, 'sq')
                .where('sq.rn = 1')
                .orderBy('sq.created_at', 'DESC')
                .limit(limit)
                .offset(skip);

            const results = await query.getRawMany();

            // Transform the raw results to match the expected format
            sequences = results.map(row => ({
                id: row.id,
                createdAt: row.createdAt,
                status: row.status,
                fromEmail: row.fromEmail,
                sequence: {
                    id: row.sequenceId,
                    name: row.sequenceName,
                    status: row.sequenceStatus,
                    isMarkedAsCompleted: row.sequenceIsMarkedAsCompleted
                }
            }));
        }

        // Get sequence IDs
        const sequenceIds = [...new Set(sequences.map(seq => seq.sequence.id))];

        // Get total steps in one query
        const stepsQuery = sequenceIds.length > 0 ? await this.dataSource.createQueryBuilder(SequenceStepEntity, 'ss')
            .select('ss.sequence_id', 'sequenceId')
            .addSelect('COUNT(*)', 'total')
            .where('ss.sequence_id IN (:...ids)', { ids: sequenceIds })
            .groupBy('ss.sequence_id')
            .getRawMany() : [];

        // Create steps count map
        const stepsCountMap = stepsQuery.reduce((acc, curr) => {
            acc[curr.sequenceId] = parseInt(curr.total);
            return acc;
        }, {});

        // Add total steps to sequences
        const sequencesWithTotalSteps = sequences.map(seq => ({
            ...seq,
            totalSteps: stepsCountMap[seq.sequence.id] || 0
        }));

        return this.formatOutputData(
            { key: 'GET_CRM_SEQUENCE' },
            {
                data: {
                    sequences: sequencesWithTotalSteps,
                    pagination: {
                        page,
                        limit,
                        total: totalCount,
                        totalPages: Math.ceil(totalCount / limit)
                    }
                }
            }
        );
    }

    async getCrmSequenceStep(userId: string, entityType: string, entityId: string, { page = 1, limit = 10 }: { page?: number, limit?: number }) {
        page = isNaN(Number(page)) ? 1 : Number(page);
        limit = isNaN(Number(limit)) ? 10 : Number(limit);
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            return this.throwCommonMessage('GET_CRM_EMAIL', new NotFoundException('User not found'));
        }

        const skip = (page - 1) * limit || 0;
        const [emails, total] = await this.crmContactSequenceStepRepository.findAndCount({
            where: {
                [entityType]: {
                    id: entityId,
                }
            },
            relations: {
                replies: true
            },
            skip,
            take: limit,
            order: {
                createdAt: 'DESC'
            }
        });

        // Get sequence IDs
        const sequenceIds = [...new Set(emails.map(email => email.sequenceId))];

        // Get sequences in one query
        const sequences = sequenceIds.length > 0 ? await this.dataSource.createQueryBuilder()
            .select([
                'seq.id as id',
                'seq.name as name', 
                'seq.status as status',
                'seq.is_marked_as_completed as isMarkedAsCompleted'
            ])
            .from('sequences', 'seq')
            .where('seq.id IN (:...ids)', { ids: sequenceIds })
            .getRawMany() : [];

        // Create sequence map
        const sequenceMap = sequences.reduce((acc, seq) => {
            acc[seq.id] = {
                id: seq.id,
                name: seq.name,
                status: seq.status,
                isMarkedAsCompleted: seq.isMarkedAsCompleted
            };
            return acc;
        }, {});

        const emailsWithSequence = emails.map(email => ({
            ...email,
            sequence: sequenceMap[email.sequenceId] || null
        }));

        return this.formatOutputData(
            { key: 'GET_CRM_EMAIL' },
            {
                data: {
                    emails: emailsWithSequence,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit)
                    }
                }
            }
        );
    }

    async getCrmSequenceStepReplies(userId: string, emailId: string, { page = 1, limit = 10 }: { page?: number, limit?: number }) {
        page = isNaN(Number(page)) ? 1 : Number(page);
        limit = isNaN(Number(limit)) ? 10 : Number(limit);
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            return this.throwCommonMessage('GET_CRM_EMAIL_REPLIES', new NotFoundException('User not found'));
        }

        const skip = (page - 1) * limit || 0;
        const [replies, total] = await this.crmContactSequenceStepReplyRepository.findAndCount({
            where: {
                sequenceStep: { id: emailId }
            },
            skip,
            take: limit,
            order: {
                repliedAt: 'DESC'
            }
        });

        return this.formatOutputData(
            { key: 'GET_CRM_EMAIL_REPLIES' },
            {
                data: {
                    replies,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit)
                    }
                }
            }
        );
    }
}

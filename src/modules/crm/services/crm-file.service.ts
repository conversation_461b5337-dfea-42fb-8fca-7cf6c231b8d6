import { CrmFileRepository } from './../repositories/crm-file.repository';
import { CrmCompanyRepository } from '../repositories/crm-company.repository';
/* eslint-disable camelcase */
import { Injectable, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In, Repository } from 'typeorm';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { CrmNoteResourceName } from '../entities/crm-note.entity';
import { EditNoteDto } from '../dto/crm-note/edit-note.dto';
import { CreateFileDto } from '../dto/crm-file/create-file.dto';
import { EditFileDto } from '../dto/crm-file/edit-file.dto';
import { getFilesDto } from '../dto/crm-file/get-file.dto';

@Injectable()
@UseGuards(AuthenticationGuard)
export class CrmFileService extends BaseAbstractService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly userRepository: UserRepository,
    private readonly crmFileRepository: CrmFileRepository
  ) {
    super(i18nService);
  }

  async handleCreateFile(body: CreateFileDto, userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('CREATE_FILE', new NotFoundException('User not found'));
      }

      const payload: any = {
        ...body,
        creator: user,
        company: body?.resourceName === CrmNoteResourceName.COMPANY ? body?.resourceId : null,
        contact: body?.resourceName === CrmNoteResourceName.CONTACT ? body?.resourceId : null,
        lead: body?.resourceName === CrmNoteResourceName.LEAD ? body?.resourceId : null
      };

      const data = await this.crmFileRepository.insert(payload);

      return this.formatOutputData(
        { key: 'CREATE_FILE' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('CREATE_FILE', e);
    }
  }

  async deleteFile(userId: string, fileId: string) {
    try {
      const company = await this.crmFileRepository.findOne({ where: { id: fileId, creator: { id: userId } } });
      if (!company) {
        return this.throwCommonMessage('DELETE_FILE', new NotFoundException('No note available'));
      }

      await this.crmFileRepository.delete({ id: fileId });
      return this.formatOutputData(
        { key: 'DELETE_FILE' },
        {
          data: null,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('DELETE_FILE', e);
    }
  }

  async handleEditFile(body: EditFileDto, userId: string, noteId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('UPDATE_FILE', new NotFoundException('User not found'));
      }
      const note = await this.crmFileRepository.findOne({
        where: { id: noteId },
      });

      const payload: any = {
        ...note,
        ...body,
        company: body?.resourceName === CrmNoteResourceName.COMPANY ? body?.resourceId : null,
        contact: body?.resourceName === CrmNoteResourceName.CONTACT ? body?.resourceId : null,
        lead: body?.resourceName === CrmNoteResourceName.LEAD ? body?.resourceId : null
      };

      const data = await this.crmFileRepository.update(note.id,payload);

      return this.formatOutputData(
        { key: 'UPDATE_FILE' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('UPDATE_FILE', e);
    }
  } 

  async handleGetListFile(userId: string, query: getFilesDto) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('GET_LIST_FILE', new NotFoundException('User not found'));
      }
      const { resourceName, resourceId, page = 1, limit = 10 } = query;
      const skip = (page - 1) * limit;
      const [files, total] = await this.crmFileRepository.findAndCount({
        where: {
          resourceName,
          resourceId,
        },
        skip,
        take: limit,
        order: {
          createdAt: 'DESC',
        },
      });

      const data = {
        files,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };

      return this.formatOutputData(
        { key: 'GET_LIST_FILE' },
        {
          data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('GET_LIST_FILE', e);
    }
  }
}


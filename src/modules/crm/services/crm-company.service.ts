import { CrmTagRepository } from './../repositories/crm-tag.repository';
import { CrmCompanyRepository } from './../repositories/crm-company.repository';
/* eslint-disable camelcase */
import { Injectable, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In, Repository } from 'typeorm';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmIndustryRepository } from '../repositories/crm-industry.repository';
import { CrmIndustryEntity } from '../entities/crm-industry.entity';
import { createCompanyDto, getCompanyQuery } from '../dto/crm-company/create-company.dto';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { CrmCompanyEntity } from '../entities/crm-company.entity';
import { CrmContactSequenceEntity } from '../entities/crm-contact-sequence.entity';
import { CrmContactSequenceStepEntity } from '../entities/crm-contact-sequence-step.entity';
import { BullhornIntegrationService } from 'src/modules/jobs/service/bullhorn-integration.service';
import { StatisticItemEntity } from 'src/modules/jobs/entities/statistic-item.entity';
import { StatsEntityDataRepository } from 'src/modules/jobs/repository/stats-entity-data.repository';

@Injectable()
@UseGuards(AuthenticationGuard)
export class CrmCompanyService extends BaseAbstractService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly crmCompanyRepository: CrmCompanyRepository,
    private readonly crmIndustryRepository: CrmIndustryRepository,
    private readonly crmTagRepository: CrmTagRepository,
    private readonly userRepository: UserRepository, 
    private readonly statsEntityDataRepository: StatsEntityDataRepository,
    
  ) {
    super(i18nService);
  }

   async upsertStatsEvent(userId, type, country) {
        const date = new Date().toISOString().split('T')[0];
        const statsEntity = await this.statsEntityDataRepository.findOne({
            where: {
              type: type,
              date,
              user_id: userId, 
              country: country,
            },
          });
    
        if (statsEntity?.id) {
            await this.statsEntityDataRepository
              .createQueryBuilder()
              .update(StatisticItemEntity)
              .set({ count: () => 'count + 1' })
              .where('id = :id', { id: statsEntity.id })
              .execute();
          } else {
            await this.statsEntityDataRepository.insert({
              type: type,
              date,
              user_id: userId,
              country: country,
              count: 1,
            });
          }
    }
  

  async handleCreateCompany(body: createCompanyDto, userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('CREATE COMPANY', new NotFoundException('User not found'));
      }
      const orgId = user.organizationId;
      let industries = [];
      let tags = []
      if (body?.industries?.length > 0) {
        industries = await this.crmIndustryRepository.findByIds(body.industries);
      }
      if (body?.tags?.length > 0) {
        tags = await this.crmTagRepository.findByIds(body.tags);
      }
      const payload = {
        ...body,
        creator: userId,
        industries: industries,
        organizationId: orgId,
        tags: tags
      };

      const data = await this.crmCompanyRepository.save(payload);

      await this.upsertStatsEvent(userId, "ADDED_COMPANY_TO_CRM", body.address?.country || "")

      return this.formatOutputData(
        { key: 'CREATE_COMPANY' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('CREATE_COMPANY', e);
    }
  }

  async getAllCompanies(userId: string, pageOptions: getCompanyQuery) {
    const {
      companyIds,
      keyword,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      industries,
      tags
    } = pageOptions;

    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      return this.throwCommonMessage('GET COMPANY', new NotFoundException('User not found'));
    }
    const orgId = user.organizationId;
    const industryToSearch = typeof industries == 'string' ? [industries] : industries;
    const companyToSearch = typeof companyIds == 'string' ? [companyIds] : companyIds;
    const tagsToSearch = typeof tags == 'string' ? [tags] : tags;

    const queryBuilder = this.crmCompanyRepository
      .createQueryBuilder('company')
      .leftJoinAndSelect('company.industries', 'industries')
      .leftJoinAndSelect('company.tags', 'tags')
      .where('company.organization_id = :organization_id', { organization_id: orgId });

    if (companyIds && companyIds.length > 0) {
      queryBuilder.andWhere('company.id = ANY(:companyToSearch)', { companyToSearch: companyToSearch });
    }

    if (keyword) {
      queryBuilder.andWhere('(company.name LIKE :keyword OR company.description LIKE :keyword)', {
        keyword: `%${keyword}%`,
      });
    }

    if (industries && industries.length > 0) {
      queryBuilder.andWhere('industries.id = ANY(:industryIds)', { industryIds: industryToSearch });
    }

    if (tagsToSearch && tagsToSearch.length > 0) {
      queryBuilder.andWhere('tags.id = ANY(:tagIds)', { tagIds: tagsToSearch });
    }

    // if (tagsToSearch && tagsToSearch.length > 0) {
    //     queryBuilder.andWhere(
    //         'company.tags @> :tags',
    //         { tags: tagsToSearch }
    //     );
    // }

    queryBuilder.orderBy(`company.${sortBy}`, sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC');

    // Apply pagination
    queryBuilder.skip((page - 1) * limit).take(limit);

    const [companies, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    const data = {
      data: companies,
      pagination: {
        total,
        page,
        limit,
        totalPages,
      },
    };

    return this.formatOutputData(
      { key: 'GET_COMPANY_LIST' },
      {
        data,
      }
    );
  }

  async deleteCompany(userId: string, companyId: string) {
    try {
      const company = await this.crmCompanyRepository.findOne({ where: { id: companyId } });
      if (!company) {
        return this.throwCommonMessage('DELETE_COMPANY', new NotFoundException('No company available'));
      }

      await this.crmCompanyRepository.delete({ id: companyId });
      return this.formatOutputData(
        { key: 'DELETE_COMPANY' },
        {
          data: {},
        }
      );
    } catch (e) {
      return this.throwCommonMessage('DELETE_COMPANY', e);
    }
  }

  async getDetailCompany(userId: string, companyId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      return this.throwCommonMessage('GET COMPANY', new NotFoundException('User not found'));
    }
    const companyDetail = await this.crmCompanyRepository.findOne({
      where: { id: companyId, organizationId: user.organizationId },
      relations: {
        industries: true,
        contacts: {
          industries: true,
        },
        notes: true,
        leads: true,
        files: true,
        tags: true,
      },
    });

    return this.formatOutputData(
      { key: 'GET_COMPANY_DETAIL' },
      {
        data: { ...companyDetail, creator: user },
      }
    );
  }

  async handleEditCompany(body: createCompanyDto, userId: string, companyId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('CREATE COMPANY', new NotFoundException('User not found'));
      }
      const company = await this.crmCompanyRepository.findOne({
        where: { id: companyId },
        relations: { industries: true },
      });
      let industries = [];
      let tags = []
      if (body?.industries?.length > 0) {
        industries = await this.crmIndustryRepository.findByIds(body.industries);
      }
      
      if (body?.tags?.length > 0) {
        tags = await this.crmTagRepository.findByIds(body.tags);
      }

      const dataUpdate = {
        ...company,
        ...body,
        industries: industries,
        tags: tags
      };

      const data = await this.crmCompanyRepository.save(dataUpdate);

      return this.formatOutputData(
        { key: 'EDIT_COMPANY' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('EDIT_COMPANY', e);
    }
  }

  async getCompanyStats(userId: string, companyId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('GET_CRM_STATS', new NotFoundException('User not found'));
      }
      const queryBuilder = this.crmCompanyRepository
        .createQueryBuilder('company')
        .leftJoin('company.contacts', 'contacts')
        .leftJoin('company.leads', 'leads')
        .leftJoin('company.notes', 'notes')
        .select([
          'company.id AS company_id',
          'COUNT(DISTINCT contacts.id) AS total_contacts',
          'COUNT(DISTINCT leads.id) AS total_leads',
          'COUNT(DISTINCT notes.id) AS total_notes',
        ])
        .where('company.id = :companyId', { companyId })
        .andWhere('company.organizationId = :organizationId', { organizationId: user.organizationId })
        .groupBy('company.id');

      const result = await queryBuilder.getRawOne();
      const [totalSequence, totalEmails] = await Promise.all([
        this.dataSource.getRepository(CrmContactSequenceEntity).count({
          where: {
            company: {
              id: companyId,
            }
          }
        }),
        this.dataSource.getRepository(CrmContactSequenceStepEntity).count({
          where: {
            company: {
              id: companyId,
            }
          }
        })
      ]);

      if (!result) {
        return this.formatOutputData(
          { key: 'GET_COMPANY_STATS' },
          {
            data: {
              companyId,
              totalContacts: 0,
              totalLeads: 0,
              totalNotes: 0,
              totalFile: 0,
              totalSequence: 0,
              totalEmails: 0,
            },
          }
        );
      }

      const payload = {
        companyId: 1,
        totalContacts: parseInt(result.total_contacts) || 0,
        totalLeads: parseInt(result.total_leads) || 0,
        totalNotes: parseInt(result.total_notes) || 0,
        totalFile: 0,
        totalSequence,
        totalEmails,
      };

      return this.formatOutputData(
        { key: 'GET_COMPANY_STATS' },
        {
          data: payload,
        }
      );
    } catch (error) {
      return this.throwCommonMessage('GET_COMPANY_STATS', error);
    }
  }

  async findOrCreateCompany(companyName: string, bullhornId: string): Promise<CrmCompanyEntity> {
    // Find company by name and bullhorn_id
    let company = await this.crmCompanyRepository.findOne({
      where: {
        name: companyName,
        bullhornId,
      },
    });

    // If not found, create new
    if (!company) {
      company = this.crmCompanyRepository.create({
        name: companyName,
        bullhornId,
      });
      await this.crmCompanyRepository.save(company);
    }

    return company;
  }
}

import { CrmCompanyRepository } from './../repositories/crm-company.repository';
/* eslint-disable camelcase */
import { Injectable, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In, Repository } from 'typeorm';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmIndustryRepository } from '../repositories/crm-industry.repository';
import { CrmIndustryEntity } from '../entities/crm-industry.entity';
import { createCompanyDto, getCompanyQuery } from '../dto/crm-company/create-company.dto';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { createContactDto, getContactQuery } from '../dto/crm-contact/create-contact.dto';
import { CrmContactRepository } from '../repositories/crm-contact.repository';
import { EditContactDto } from '../dto/crm-contact/edit-contact.dto';
import { CrmTagRepository } from '../repositories/crm-tag.repository';

@Injectable()
@UseGuards(AuthenticationGuard)
export class CrmContactService extends BaseAbstractService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly crmCompanyRepository: CrmCompanyRepository,
    private readonly crmContactRepository: CrmContactRepository,
    private readonly crmIndustryRepository: CrmIndustryRepository,
    private readonly crmTagRepository: CrmTagRepository,
    private readonly userRepository: UserRepository
  ) {
    super(i18nService);
  }

  async handleCreateContact(body: createContactDto, userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('CREATE CONTACT', new NotFoundException('User not found'));
      }
      const company = await this.crmCompanyRepository.findOne({ where: { id: body.companyId } });

      if (!company) {
        return this.throwCommonMessage('CREATE CONTACT', new NotFoundException('Company not found'));
      }
      const orgId = user.organizationId;
      let industries = [];
      let tags = [];
      if (body?.industries?.length > 0) {
        industries = await this.crmIndustryRepository.findByIds(body.industries);
      }
      if (body?.tags?.length > 0) {
        tags = await this.crmTagRepository.findByIds(body.tags);
      }
      const payload = {
        ...body,
        creator: user,
        industries: industries,
        tags: tags,
        organizationId: orgId,
        company,
      };

      const data = await this.crmContactRepository.save(payload);

      return this.formatOutputData(
        { key: 'CREATE_CONTACT' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('CREATE_CONTACT', e);
    }
  }

  async getAllContacts(userId: string, pageOptions: getContactQuery) {
    const {
      companyIds,
      keyword,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      industries,
      tags,
    } = pageOptions;

    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      return this.throwCommonMessage('GET COMPANY', new NotFoundException('User not found'));
    }
    const orgId = user.organizationId;
    const industryToSearch = typeof industries == 'string' ? [industries] : industries;
    const companyToSearch = typeof companyIds == 'string' ? [companyIds] : companyIds;
    const tagsToSearch = typeof tags == 'string' ? [tags] : tags;

    const queryBuilder = this.crmContactRepository
      .createQueryBuilder('contact')
      .leftJoinAndSelect('contact.industries', 'industries')
      .leftJoinAndSelect('contact.company', 'company')
      .leftJoinAndSelect('contact.tags', 'tags')
      .where('contact.organization_id = :organization_id', { organization_id: orgId });

    if (keyword) {
      queryBuilder.andWhere(
        '(contact.first_name LIKE :keyword OR contact.middle_name LIKE :keyword OR contact.sur_name LIKE :keyword)',
        {
          keyword: `%${keyword}%`,
        }
      );
    }

    if (companyToSearch && companyToSearch.length > 0) {
      queryBuilder.andWhere('company.id = ANY(:companyToSearch)', { companyToSearch: companyToSearch });
    }

    if (industries && industries.length > 0) {
      queryBuilder.andWhere('industries.id = ANY(:industryIds)', { industryIds: industryToSearch });
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere('tags.id = ANY(:tagIds)', { tagIds: tagsToSearch });
    }

    queryBuilder.orderBy(`contact.${sortBy}`, sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC');

    // Apply pagination
    queryBuilder.skip((page - 1) * limit).take(limit);

    const [companies, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    const data = {
      data: companies,
      pagination: {
        total,
        page,
        limit,
        totalPages,
      },
    };

    return this.formatOutputData(
      { key: 'GET_CONTACT' },
      {
        data,
      }
    );
  }

  async deleteContact(userId: string, contactId: string) {
    try {
      const contact = await this.crmContactRepository.findOne({ where: { id: contactId, creator: { id: userId } } });
      if (!contact) {
        return this.throwCommonMessage('DELETE_CONTACT', new NotFoundException('No contact available'));
      }

      await this.crmContactRepository.delete({ id: contactId });
      return this.formatOutputData(
        { key: 'DELETE_CONTACT' },
        {
          data: null,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('DELETE_CONTACT', e);
    }
  }

  async getDetailContact(userId: string, contactId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      return this.throwCommonMessage('GET_DETAIL_CONTACT', new NotFoundException('User not found'));
    }
    const contactDetail = await this.crmContactRepository.findOne({
      where: { id: contactId, organizationId: user.organizationId },
      relations: {
        industries: true,
        files: true,
        tags: true,
        company: {
          industries: true,
        },
      },
    });

    if (!contactDetail) {
      return this.throwCommonMessage('GET_DETAIL_CONTACT', new NotFoundException('Contact not found'));
    }

    return this.formatOutputData(
      { key: 'GET_DETAIL_CONTACT' },
      {
        data: { ...contactDetail, creator: user },
      }
    );
  }

  async handleEditContact(body: EditContactDto, userId: string, contactId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('UPDATE_CONTACT', new NotFoundException('User not found'));
      }
      const contact = await this.crmContactRepository.findOne({
        where: { id: contactId },
        relations: { industries: true, company: true },
      });
      const company = await this.crmCompanyRepository.findOne({ where: { id: body.companyId } });

      if (!company) {
        return this.throwCommonMessage('UPDATE_CONTACT', new NotFoundException('Company not found'));
      }
      let industries = [];
      let tags = [];
      if (body?.industries?.length > 0) {
        industries = await this.crmIndustryRepository.findByIds(body.industries);
      }

      if (body?.tags?.length > 0) {
        tags = await this.crmTagRepository.findByIds(body.tags);
      }

      const dataUpdate: any = {
        ...contact,
        ...body,
        industries: industries?.length > 0 ? industries : contact.industries,
        tags: tags?.length > 0 ? tags : contact.tags,
        company,
      };

      const data = await this.crmContactRepository.save(dataUpdate);

      return this.formatOutputData(
        { key: 'UPDATE_CONTACT' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('UPDATE_CONTACT', e);
    }
  }
}

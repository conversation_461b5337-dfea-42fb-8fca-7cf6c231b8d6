import { CrmTagRepository } from './../repositories/crm-tag.repository';
/* eslint-disable camelcase */
import { Injectable, InternalServerErrorException, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource, ILike, In, Repository } from 'typeorm';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmIndustryRepository } from '../repositories/crm-industry.repository';
import { CrmIndustryEntity } from '../entities/crm-industry.entity';
import { CreateIndustryDto } from '../dto/crm-industry/create-industry.dto';
import { EditIndustrtDto } from '../dto/crm-industry/edit-industry.dto';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { GetAdminIndustryParams } from '../dto/crm-industry/get-industry.dto';
import { CreateTagDto } from '../dto/crm-tag/create-tag.dto';
import { EditTagDto } from '../dto/crm-tag/edit-tag.dto';
import { GetAdminTagParams, GetUserTagParams } from '../dto/crm-tag/get-tag.dto';

@Injectable()
@UseGuards(AuthenticationGuard)
export class CrmTagService extends BaseAbstractService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly crmTagRepository: CrmTagRepository,
    private readonly userRepository: UserRepository
  ) {
    super(i18nService);
  }

  async createTag(body: CreateTagDto) {
    try {
      // TODO: UPDATE PERMISSION
      const industry = await this.crmTagRepository.find({
        where: { 
          name: ILike(body.name),
          organization: {id: body.organization}
        }
      })
      if (industry.length > 0) {
        return this.throwCommonMessage('CREATE_TAG', new InternalServerErrorException("Tag already exists in the system"));
      }

      const org = await this.dataSource
        .createQueryBuilder(OrganizationEntity, 'o')
        .where({ id: body.organization })
        .getOne();

      if (!org) {
        return this.throwCommonMessage('CREATE_TAG', new NotFoundException("Organization not found"));
      }

      const payload = {
        name: body.name,
        description: body?.description,
        organization: org
      }

      const data = await this.crmTagRepository.save(payload)
      return this.formatOutputData(
        { key: 'CREATE_TAG' },
        {
          data: data
        }
      );
    } catch (e) {
      return this.throwCommonMessage('CREATE_TAG', e);
    }
  }

  async editTag(body: EditTagDto, tagId: string) {
    try {
      // TODO: UPDATE PERMISSION

      const currentIndustry = await this.crmTagRepository.find({
        where: { id: tagId }
      })

      if (!currentIndustry) {
        return this.throwCommonMessage('EDIT_TAG', new NotFoundException("Industry not exits !!!"));
      }
      const checkIndustry = await this.crmTagRepository.find({
        where: { name: ILike(body.name), }
      })

      if (checkIndustry.length > 0) {
        return this.throwCommonMessage('EDIT_TAG', new InternalServerErrorException("Industry already exists in the system"));
      }

      const payload = {
        name: body.name,
        description: body?.description,
      }

      await this.crmTagRepository.update(tagId, payload)
      return this.formatOutputData(
        { key: 'EDIT_TAG' },
        {
          data: []
        }
      );
    } catch (e) {
      return this.throwCommonMessage('EDIT_TAG', e);
    }
  }

  async getTagList(params: GetAdminTagParams) {
    // TODO:  UPDATE PERMISSION
    const query = this.crmTagRepository.createQueryBuilder('crmTag')
    .leftJoinAndSelect('crmTag.organization', 'organization');
  
    if (params.organizationId) {
      query.where('crmTag.id = :orgId', { orgId: params.organizationId });
    }
  
  const data = await query.getMany();

    return this.formatOutputData(
      { key: 'GET_TAG_LIST' },
      {
        data: data
      }
    );
  }

  async getTagListByUser(userId: string, query: GetUserTagParams) {
    // TODO:  UPDATE PERMISSION
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      return this.throwCommonMessage('GET_TAG_BY_USER', new NotFoundException('User not found'));
    }

    const data = await this.crmTagRepository.find({
      where: {
        organization: {id: user.organizationId},
        ...(query.keyword && { name: ILike(`%${query.keyword}%`) })
      },
      relations: {organization: true}
    })

    return this.formatOutputData(
      { key: 'GET_TAG_BY_USER' },
      {
        data: data
      }
    );
  }

  async deleteTag(tagId: string) {
    try {
      // TODO: UPDATE PERMISSION
      const currentIndustry = await this.crmTagRepository.find({
        where: { id: tagId }
      })

      if (!currentIndustry) {
        return this.throwCommonMessage('DELETE_TAG', new NotFoundException("Tag not found !!!"));
      }

      await this.crmTagRepository.delete(tagId)
      return this.formatOutputData(
        { key: 'DELETE_TAG' },
        {
          data: null
        }
      );
    } catch (e) {
      return this.throwCommonMessage('DELETE_TAG', e);
    }
  }
}

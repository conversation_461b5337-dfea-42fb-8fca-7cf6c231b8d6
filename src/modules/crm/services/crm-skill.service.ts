import { CrmSkillsRepository } from '../repositories/crm-skill.repository';
/* eslint-disable camelcase */
import { Injectable, InternalServerErrorException, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource, ILike, In, Repository } from 'typeorm';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CreateSkillsDto } from '../dto/crm-skill/crm-skill.dto';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';
import { UserRepository } from 'src/modules/user/repositories/user.repository';

@Injectable()
@UseGuards(AuthenticationGuard)
export class CrmSkillService extends BaseAbstractService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly crmSkillsRepository: CrmSkillsRepository,
    private readonly userRepository: UserRepository
  ) {
    super(i18nService);
  }

  async createSkills(bodyDto: CreateSkillsDto) {
    const { data } = bodyDto;
    if (!data.length) {
      return this.formatOutputData({ key: 'CREATE_SKILLS' }, { data: {} });
    }
    //assuming that each request is for one org only
    const organizationId = data[0].organizationId;
    const existingSkills = await this.crmSkillsRepository.find({
      where: {
        name: In(data.map((item) => item.name)),
        organization: { id: organizationId },
      },
    });
    const existingSkillNames = existingSkills.map((item) => item.name);
    const org = organizationId
      ? await this.dataSource.createQueryBuilder(OrganizationEntity, 'o').where({ id: organizationId }).getOne()
      : null;

    const skillsToCreate = data
      .filter((item) => !existingSkillNames.includes(item.name))
      .map((item) => ({ ...item, organization: org }));
    try {
      const data = await this.crmSkillsRepository.insert(skillsToCreate);
      return this.formatOutputData(
        { key: 'CREATE_SKILLS' },
        {
          data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('CREATE_SKILLS', e);
    }
  }

  // not using anymore
  async getSkills({ userId }: { userId?: string }) {
    const data = userId
      ? await this.crmSkillsRepository.find()
      : await this.crmSkillsRepository.findBy({ organization: { users: { id: userId } } });
    return this.formatOutputData(
      { key: 'GET_SKILLS' },
      {
        data: data,
      }
    );
  }

  async getSkillListByUser(userId: string) {
    try {
      // TODO:  UPDATE PERMISSION

      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('GET_SKILLS_BY_USER', new NotFoundException('User not found'));
      }

      const data = await this.crmSkillsRepository.find({
        where: {
          organization: { id: user.organizationId },
        },
        relations: { organization: true },
      });

      return this.formatOutputData(
        { key: 'GET_SKILLS_BY_USER' },
        {
          data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('GET_SKILLS_BY_USER', e);
    }
  }

  async updateById(id: string, { description, name }: { description: string; name: string }) {
    try {
      await this.crmSkillsRepository.update(id, { description, name });
      return this.formatOutputData(
        { key: 'UPDATE_SKILL_BY_ID' },
        {
          data: {},
        }
      );
    } catch (e) {
      return this.throwCommonMessage('UPDATE_SKILL_BY_ID', e);
    }
  }

  async deleteByIds(ids: string) {
    try {
      const standardIds = ids ? ids.split(',') : [];
      if (standardIds.length) {
        await this.crmSkillsRepository.delete({ id: In(standardIds) });
      }
      return this.formatOutputData(
        { key: 'DELETE_SKILL_BY_IDS' },
        {
          data: {},
        }
      );
    } catch (e) {
      return this.throwCommonMessage('DELETE_SKILL_BY_IDS', e);
    }
  }
}

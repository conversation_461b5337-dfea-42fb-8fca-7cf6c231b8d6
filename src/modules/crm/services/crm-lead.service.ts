import { CrmLeadRepository } from '../repositories/crm-lead.repository';
import { CrmCompanyRepository } from './../repositories/crm-company.repository';
import { CrmSkillsRepository } from '../repositories/crm-skill.repository';
/* eslint-disable camelcase */
import { Injectable, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In, Repository } from 'typeorm';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmIndustryRepository } from '../repositories/crm-industry.repository';
import { CrmIndustryEntity } from '../entities/crm-industry.entity';
import { createCompanyDto, getCompanyQuery } from '../dto/crm-company/create-company.dto';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { CreateLeadDto, getLeadsQuery } from '../dto/crm-lead/create-lead.dto';
import { EditLeadDto } from '../dto/crm-lead/edit-lead.dto';
import { CrmTagRepository } from '../repositories/crm-tag.repository';

@Injectable()
@UseGuards(AuthenticationGuard)
export class CrmLeadService extends BaseAbstractService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly crmCompanyRepository: CrmCompanyRepository,
    private readonly crmIndustryRepository: CrmIndustryRepository,
    private readonly userRepository: UserRepository,
    private readonly crmLeadRepository: CrmLeadRepository,
    private readonly crmTagRepository: CrmTagRepository,
    private readonly crmSkillsRepository: CrmSkillsRepository
  ) {
    super(i18nService);
  }

  async handleCreateLead(body: CreateLeadDto, userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('CREATE_LEAD', new NotFoundException('User not found'));
      }

      const company = await this.crmCompanyRepository.findOne({ where: { id: body.companyId } });

      if (!company) {
        return this.throwCommonMessage('CREATE CONTACT', new NotFoundException('Company not found'));
      }
      const orgId = user.organizationId;
      let industries = [];
      let skills = [];
      let tags = []
      if (body?.industries?.length > 0) {
        industries = await this.crmIndustryRepository.findByIds(body.industries);
      }
      if (body?.skills?.length > 0) {
        skills = await this.crmSkillsRepository.findByIds(body.skills);
      }
      if (body?.tags?.length > 0) {
        tags = await this.crmTagRepository.findByIds(body.tags);
      }

      const payload: any = {
        ...body,
        creator: user,
        industries: industries,
        organizationId: orgId,
        skills: skills,
        company,
        tags: tags,
        jobType: body?.jobType?.toUpperCase(),
      };

      console.log('payload', payload);

      const data = await this.crmLeadRepository.save(payload);

      return this.formatOutputData(
        { key: 'CREATE_LEAD' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('CREATE_LEAD', e);
    }
  }

  async getAllLeads(userId: string, pageOptions: getLeadsQuery) {
    const {
      companyIds,
      keyword,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      industries,
      jobType,
      tags
    } = pageOptions;

    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      return this.throwCommonMessage('GET LEADS', new NotFoundException('User not found'));
    }
    const orgId = user.organizationId;
    const industryToSearch = typeof industries == 'string' ? [industries] : industries;
    const companyToSearch = typeof companyIds == 'string' ? [companyIds] : companyIds;
    const jobTypeToSearch = typeof jobType == 'string' ? [jobType] : jobType;
    const tagsToSearch = typeof tags == "string" ? [tags] : tags

    const queryBuilder = this.crmLeadRepository
      .createQueryBuilder('leads')
      .leftJoinAndSelect('leads.industries', 'industries')
      .leftJoinAndSelect('leads.skills', 'skills')
      .leftJoinAndSelect('leads.creator', 'creator')
      .leftJoinAndSelect('leads.company', 'companyId')
      .leftJoinAndSelect('leads.tags', 'tags')
      .where('leads.organization_id = :organization_id', { organization_id: orgId });

    if (companyIds && companyIds.length > 0) {
      queryBuilder.andWhere('companyId.id = ANY(:companyToSearch)', { companyToSearch: companyToSearch });
    }

    if (keyword) {
      queryBuilder.andWhere('(leads.jobTitle LIKE :keyword OR leads.description LIKE :keyword)', {
        keyword: `%${keyword}%`,
      });
    }

    if (industries && industries.length > 0) {
      queryBuilder.andWhere('industries.id = ANY(:industryIds)', { industryIds: industryToSearch });
    }

    if (jobTypeToSearch && jobTypeToSearch.length > 0) {
      queryBuilder.andWhere('leads.job_type = ANY(:jobTypeToSearch)', { jobTypeToSearch: jobTypeToSearch });
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere('tags.id = ANY(:tagIds)', { tagIds: tagsToSearch });
    }

    queryBuilder.orderBy(`leads.${sortBy}`, sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC');

    // Apply pagination
    queryBuilder.skip((page - 1) * limit).take(limit);

    const [companies, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    const data = {
      data: companies,
      pagination: {
        total,
        page,
        limit,
        totalPages,
      },
    };

    return this.formatOutputData(
      { key: 'GET_LEADS' },
      {
        data,
      }
    );
  }

  async deleteLead(userId: string, leadId: string) {
    try {
      const company = await this.crmLeadRepository.findOne({ where: { id: leadId, creator: { id: userId } } });
      if (!company) {
        return this.throwCommonMessage('DELETE_LEAD', new NotFoundException('No lead available'));
      }

      await this.crmLeadRepository.delete({ id: leadId });
      return this.formatOutputData(
        { key: 'DELETE_LEAD' },
        {
          data: null,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('DELETE_LEAD', e);
    }
  }

  async getLeadDetail(userId: string, companyId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      return this.throwCommonMessage('GET_LEAD_DETAIL', new NotFoundException('User not found'));
    }
    const leadDetail = await this.crmLeadRepository.findOne({
      where: { id: companyId, organizationId: user.organizationId },
      relations: {
        industries: true,
        skills: true,
        company: true,
        tags: true,
        files: true
      },
    });

    return this.formatOutputData(
      { key: 'GET_LEAD_DETAIL' },
      {
        data: { ...leadDetail, creator: user },
      }
    );
  }

  async handleEditLead(body: EditLeadDto, userId: string, leadId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('UPDATE_LEAD', new NotFoundException('User not found'));
      }
      const lead = await this.crmLeadRepository.findOne({
        where: { id: leadId },
        relations: { industries: true, company: true, skills: true },
      });
      const company = await this.crmCompanyRepository.findOne({ where: { id: body.companyId } });

      if (!company) {
        return this.throwCommonMessage('UPDATE_LEAD', new NotFoundException('Company not found'));
      }
      let industries = [];
      let tags = []
      if (body?.industries?.length > 0) {
        industries = await this.crmIndustryRepository.findByIds(body.industries);
      }

      if (body?.tags?.length > 0) {
        tags = await this.crmTagRepository.findByIds(body.tags);
      }

      let skills = [];

      if (body?.skills?.length > 0) {
        skills = await this.crmSkillsRepository.findByIds(body.skills);
      }

      const dataUpdate = {
        ...lead,
        ...body,
        industries: industries?.length > 0 ? industries : lead.industries,
        company,
        tags: tags?.length > 0 ? tags : lead.tags,
        skills: skills?.length > 0 ? skills : lead?.skills,
      };

      const data = await this.crmLeadRepository.save(dataUpdate);

      return this.formatOutputData(
        { key: 'UPDATE_LEAD' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('UPDATE_LEAD', e);
    }
  }
}

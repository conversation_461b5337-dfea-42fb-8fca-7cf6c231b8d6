import { CrmCompanyRepository } from '../repositories/crm-company.repository';
/* eslint-disable camelcase */
import { Injectable, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource, In, Repository } from 'typeorm';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmIndustryRepository } from '../repositories/crm-industry.repository';
import { CrmIndustryEntity } from '../entities/crm-industry.entity';
import { createCompanyDto, getCompanyQuery } from '../dto/crm-company/create-company.dto';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { CrmNoteRepository } from '../repositories/crm-note.repository';
import { CreateNoteDto } from '../dto/crm-note/create-note.dto';
import { CrmNoteResourceName } from '../entities/crm-note.entity';
import { EditNoteDto } from '../dto/crm-note/edit-note.dto';
import { GetNoteDto } from '../dto/crm-note/get-note.dto';

@Injectable()
@UseGuards(AuthenticationGuard)
export class CrmNoteService extends BaseAbstractService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly crmCompanyRepository: CrmCompanyRepository,
    private readonly crmIndustryRepository: CrmIndustryRepository,
    private readonly userRepository: UserRepository,
    private readonly crmNoteRepository: CrmNoteRepository
  ) {
    super(i18nService);
  }

  async handleCreateNote(body: CreateNoteDto, userId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('CREATE_NOTE', new NotFoundException('User not found'));
      }

      const payload: any = {
        ...body,
        creator: user,
        company: body?.resourceName === CrmNoteResourceName.COMPANY ? body?.resourceId : null,
        contact: body?.resourceName === CrmNoteResourceName.CONTACT ? body?.resourceId : null,
        lead: body?.resourceName === CrmNoteResourceName.LEAD ? body?.resourceId : null,
      };

      const data = await this.crmNoteRepository.insert(payload);

      return this.formatOutputData(
        { key: 'CREATE_NOTE' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('CREATE_NOTE', e);
    }
  }

  async deleteNote(userId: string, noteId: string) {
    try {
      const company = await this.crmNoteRepository.findOne({ where: { id: noteId, creator: { id: userId } } });
      if (!company) {
        return this.throwCommonMessage('DELETE_NOTE', new NotFoundException('No note available'));
      }

      await this.crmNoteRepository.delete({ id: noteId });
      return this.formatOutputData(
        { key: 'DELETE_NOTE' },
        {
          data: null,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('DELETE_NOTE', e);
    }
  }

  async handleEditNote(body: EditNoteDto, userId: string, noteId: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('UPDATE_LEAD', new NotFoundException('User not found'));
      }
      const note = await this.crmNoteRepository.findOne({
        where: { id: noteId },
      });

      const payload: any = {
        ...note,
        ...body,
        company: body?.resourceName === CrmNoteResourceName.COMPANY ? body?.resourceId : null,
        contact: body?.resourceName === CrmNoteResourceName.CONTACT ? body?.resourceId : null,
        lead: body?.resourceName === CrmNoteResourceName.LEAD ? body?.resourceId : null,
      };

      const data = await this.crmNoteRepository.update(note.id, payload);

      return this.formatOutputData(
        { key: 'UPDATE_NOTE' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('UPDATE_NOTE', e);
    }
  }

  async handleGetListNotes(userId: string, query: GetNoteDto) {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        return this.throwCommonMessage('GET_LIST_NOTE', new NotFoundException('User not found'));
      }
      const { resourceName, resourceId, page = 1, limit = 10 } = query;
      const skip = (page - 1) * limit;
      const [notes, total] = await this.crmNoteRepository.findAndCount({
        where: {
          resourceName,
          resourceId,
        },
        skip,
        take: limit,
        order: {
          createdAt: 'DESC',
        },
      });

      const data = {
        notes,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };

      return this.formatOutputData(
        { key: 'GET_LIST_NOTE' },
        {
          data: data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('GET_LIST_NOTE', e);
    }
  }
}

/* eslint-disable camelcase */
import { Injectable, InternalServerErrorException, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource, ILike, In, Repository } from 'typeorm';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmIndustryRepository } from '../repositories/crm-industry.repository';
import { CrmIndustryEntity } from '../entities/crm-industry.entity';
import { CreateIndustryDto } from '../dto/crm-industry/create-industry.dto';
import { EditIndustrtDto } from '../dto/crm-industry/edit-industry.dto';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { GetAdminIndustryParams } from '../dto/crm-industry/get-industry.dto';

@Injectable()
@UseGuards(AuthenticationGuard)
export class CrmIndustryService extends BaseAbstractService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly crmIndustryRepository: CrmIndustryRepository,
    private readonly userRepository: UserRepository
  ) {
    super(i18nService);
  }

  async createIndustry(body: CreateIndustryDto) {
    try {
      // TODO: UPDATE PERMISSION
      const industry = await this.crmIndustryRepository.find({
        where: {
          name: ILike(body.name),
          organization: { id: body.organization },
        },
      });
      if (industry.length > 0) {
        return this.throwCommonMessage(
          'CREATE_INDUSTRY',
          new InternalServerErrorException('Industry already exists in the system')
        );
      }

      const org = await this.dataSource
        .createQueryBuilder(OrganizationEntity, 'o')
        .where({ id: body.organization })
        .getOne();

      if (!org) {
        return this.throwCommonMessage('CREATE_INDUSTRY', new NotFoundException('Organization not found'));
      }

      const payload = {
        name: body.name,
        description: body?.description,
        organization: org,
      };

      const data = await this.crmIndustryRepository.insert(payload);
      return this.formatOutputData(
        { key: 'CREATE_INDUSTRY' },
        {
          data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('CREATE_INDUSTRY', e);
    }
  }

  async editIndustry(body: EditIndustrtDto, industryId: string) {
    try {
      // TODO: UPDATE PERMISSION

      const currentIndustry = await this.crmIndustryRepository.find({
        where: { id: industryId },
      });

      if (!currentIndustry) {
        return this.throwCommonMessage('EDIT_INDUSTRY', new NotFoundException('Industry not exits !!!'));
      }
      const checkIndustry = await this.crmIndustryRepository.find({
        where: { name: ILike(body.name) },
      });

      if (checkIndustry.length > 0) {
        return this.throwCommonMessage(
          'EDIT_INDUSTRY',
          new InternalServerErrorException('Industry already exists in the system')
        );
      }

      const payload = {
        name: body.name,
        description: body?.description,
      };

      const data = await this.crmIndustryRepository.update(industryId, payload);
      return this.formatOutputData(
        { key: 'EDIT_INDUSTRY' },
        {
          data,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('EDIT_INDUSTRY', e);
    }
  }

  async getIndustryList(params: GetAdminIndustryParams) {
    // TODO:  UPDATE PERMISSION
    const query = this.crmIndustryRepository
      .createQueryBuilder('crmIndustry')
      .leftJoinAndSelect('crmIndustry.organization', 'organization');

    if (params.organizationId) {
      query.where('organization.id = :orgId', { orgId: params.organizationId });
    }

    const data = await query.getMany();

    return this.formatOutputData(
      { key: 'GET_INDUSTRY_LIST' },
      {
        data: data,
      }
    );
  }

  async getIndustryListByUser(userId: string) {
    // TODO:  UPDATE PERMISSION

    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user) {
      return this.throwCommonMessage('GET_INDUSTRY_BY_USER', new NotFoundException('User not found'));
    }

    const data = await this.crmIndustryRepository.find({
      where: {
        organization: { id: user.organizationId },
      },
      relations: { organization: true },
    });

    return this.formatOutputData(
      { key: 'GET_INDUSTRY_BY_USER' },
      {
        data: data,
      }
    );
  }

  async deleteIndustry(industryId: string) {
    try {
      // TODO: UPDATE PERMISSION
      const currentIndustry = await this.crmIndustryRepository.find({
        where: { id: industryId },
      });

      if (!currentIndustry) {
        return this.throwCommonMessage('DELETE_INDUSTRY', new NotFoundException('Industry not found !!!'));
      }

      await this.crmIndustryRepository.delete(industryId);
      return this.formatOutputData(
        { key: 'DELETE_INDUSTRY' },
        {
          data: null,
        }
      );
    } catch (e) {
      return this.throwCommonMessage('DELETE_INDUSTRY', e);
    }
  }

  async deleteByIds(ids: string) {
    const standardIds = ids ? ids.split(',') : [];
    if (standardIds.length) {
      await this.crmIndustryRepository.delete({ id: In(standardIds) });
    }
    return this.formatOutputData(
      { key: 'DELETE_INDUSTRY_BY_IDS' },
      {
        data: {},
      }
    );
  }
}

import { Entity, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { CrmContactSequenceStepEntity } from './crm-contact-sequence-step.entity';

export enum CrmReplyType {
  REPLIED = 'REPLIED',
  AUTO_REPLIED = 'AUTO_REPLIED',
  BOUNCED = 'BOUNCED',
  FORWARDED = 'FORWARDED'
}

@Entity('crm_contact_sequence_step_replies')
export class CrmContactSequenceStepReplyEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @ManyToOne(() => CrmContactSequenceStepEntity, sequenceStep => sequenceStep.replies, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sequence_step_id' })
  sequenceStep: CrmContactSequenceStepEntity;

  @Column({ name: 'content', type: 'text', nullable: true })
  content: string;

  @Column({ name: 'subject', nullable: true })
  subject: string;

  @Column({ name: 'from_email', nullable: true })
  fromEmail: string;

  @Column({ name: 'to_email', nullable: true })
  toEmail: string;

  @Index()
  @Column({ name: 'replied_at', type: 'timestamptz', nullable: true })
  repliedAt: Date;

  @Index()
  @Column({ name: 'type', type: 'enum', enum: CrmReplyType, default: CrmReplyType.REPLIED })
  type: CrmReplyType;

  @Column({ name: 'status', nullable: true })
  status: string;

  @Index()
  @Column({ name: 'reference_id', nullable: true })
  referenceId: string;

  @Index()
  @Column({ name: 'reply_reference_id', nullable: true })
  replyReferenceId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

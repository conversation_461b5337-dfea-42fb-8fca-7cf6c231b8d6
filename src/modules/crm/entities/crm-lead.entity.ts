import { Entity, PrimaryGeneratedColumn, Column, BaseEntity, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable, ManyToOne, JoinColumn, OneToMany } from 'typeorm';
import { CrmIndustryEntity } from './crm-industry.entity';
import { CrmCompanyEntity } from './crm-company.entity';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { CrmSkillsEntity } from './crm-skill.entity';
import { CrmNoteEntity } from './crm-note.entity';
import { CrmTagEntity } from './crm-tag.entity';
import { CrmFileEntity } from './crm-file.entity';
import { CrmContactEntity } from './crm-contact.entity';
import { CrmContactSequenceEntity } from './crm-contact-sequence.entity';
import { CrmContactSequenceStepEntity } from './crm-contact-sequence-step.entity';

export enum CrmLeadType {
  PERMANENT = 'PERMANENT',
  CONTACT = 'CONTACT',
  TEMPORARY = 'TEMPORARY',
  FIXED_TERM = 'FIXED_TERM',
}

@Entity('crm_leads')
export class CrmLeadEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar',name: "job_title" ,length: 100, nullable: false })
  jobTitle: string;

  @Column({ name: 'company_id', type: 'uuid', nullable: true })
  companyId: string;

  @ManyToOne(() => CrmCompanyEntity, (company) => company.leads)
  @JoinColumn({ name: 'company_id' })
  company: CrmCompanyEntity;

  @Column({ type: 'varchar', nullable: true, name: 'description' })
  description: string;

  @Column({ type: 'varchar', nullable: true, name: 'website' })
  website: string;

  @Column({ name: 'address', type: 'jsonb', nullable: true })
  address: any;

  @Column({ name: 'job_type', enum: CrmLeadType })
  jobType: CrmLeadType;

  @Column({ name: 'salary', type: 'varchar', nullable: true })
  salary: string;

  @Column({ name: 'currency_unit', type: 'varchar', nullable: true })
  currencyUnit: string;

  @Column({ name: 'pay_rate', type: 'varchar', nullable: true })
  payRate: string;

  @Column({ name: 'addition_field', type: 'jsonb', nullable: true })
  additionField: any;

  @OneToMany(() => CrmContactEntity, (contact) => contact.lead)
  contacts: CrmContactEntity[];

  @OneToMany(() => CrmNoteEntity, (note) => note.lead)
  notes: CrmNoteEntity[];

  @OneToMany(() => CrmFileEntity, (file) => file.lead)
  files: CrmFileEntity[];

  @OneToMany(() => CrmContactSequenceEntity, (contactSequence) => contactSequence.lead)
  contactSequences: CrmContactSequenceEntity[];

  @OneToMany(() => CrmContactSequenceStepEntity, (contactSequenceStep) => contactSequenceStep.lead)
  contactSequenceSteps: CrmContactSequenceStepEntity[];

  @ManyToOne(() => UserEntity, (user) => user.crmContacts)
  @JoinColumn({ name: 'creator' })
  creator: UserEntity;

  @Column({ name: 'organization_id', nullable: true })
  organizationId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @ManyToMany(() => CrmIndustryEntity, (industry) => industry.leads, {
    cascade: ['remove'],
  })
  @JoinTable({
    name: 'crm_lead_industry',
    joinColumn: {
      name: 'lead_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'industry_id',
      referencedColumnName: 'id',
    },
  })
  industries: CrmIndustryEntity[];

  @ManyToMany(() => CrmSkillsEntity, (skills) => skills.leads, {
    cascade: ['remove'],
  })
  @JoinTable({
    name: 'crm_lead_skill',
    joinColumn: {
      name: 'lead_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'skill_id',
      referencedColumnName: 'id',
    },
  })
  skills: CrmSkillsEntity[];

  @ManyToMany(() => CrmTagEntity, (tags) => tags.leads, {
    cascade: ['remove'],
  })
  @JoinTable({
    name: 'crm_lead_tag',
    joinColumn: {
      name: 'lead_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'tag_id',
      referencedColumnName: 'id',
    },
  })
  tags: CrmTagEntity[];
}

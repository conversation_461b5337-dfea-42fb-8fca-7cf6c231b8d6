import { <PERSON>ti<PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON>in<PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn, OneToMany, Unique, Index } from 'typeorm';
import { CrmContactEntity } from './crm-contact.entity';
import { SequenceEntity } from '../../mail/entities/sequence.entity';
import { CrmCompanyEntity } from './crm-company.entity';
import { CrmLeadEntity } from './crm-lead.entity';

@Entity('crm_contact_sequences')
@Unique(['contact', 'sequence'])
export class CrmContactSequenceEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @ManyToOne(() => CrmContactEntity, (contact) => contact.contactSequences, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contact_id' })
  contact: CrmContactEntity;

  @Index()
  @ManyToOne(() => SequenceEntity, (sequence) => sequence.contactSequences, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'sequence_id' })
  sequence: SequenceEntity;

  @Index()
  @ManyToOne(() => CrmCompanyEntity, (company) => company.contactSequences, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'company_id' })
  company: CrmCompanyEntity;

  @Index()
  @ManyToOne(() => CrmLeadEntity, (lead) => lead.contactSequences, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'lead_id' })
  lead: CrmLeadEntity;

  @Column({ name: 'status', nullable: true })
  status: string;

  @Column({ name: 'from_email', nullable: true })
  fromEmail: string;

  @Column({ name: 'to_email', nullable: true })
  toEmail: string;

  @Index()
  @Column({ name: 'sent_at', type: 'timestamptz', nullable: true })
  sentAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}
import { <PERSON>tity, PrimaryGeneratedColumn, Column, BaseEntity, ManyToMany, JoinTable, ManyToOne, JoinColumn } from 'typeorm';
import { CrmCompanyEntity } from './crm-company.entity';
import { CrmLeadEntity } from './crm-lead.entity';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';

@Entity('crm_skills')
export class CrmSkillsEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  name: string;

  @Column({ type: 'varchar', nullable: true, name: 'description' })
  description: string;

  @ManyToMany(() => CrmLeadEntity, (leads) => leads.skills)
  leads: CrmLeadEntity[];
  
  @ManyToOne(() => OrganizationEntity, (org) => org.skills, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'organization' })
  organization: OrganizationEntity;
}

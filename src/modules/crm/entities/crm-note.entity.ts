import { Entity, PrimaryGeneratedColumn, Column, BaseEntity, CreateDateColumn, UpdateDateColumn, JoinTable, ManyToOne, JoinColumn } from 'typeorm';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { CrmCompanyEntity } from './crm-company.entity';
import { CrmContactEntity } from './crm-contact.entity';
import { CrmLeadEntity } from './crm-lead.entity';

export enum CrmNoteResourceName {
  COMPANY = 'COMPANY',
  CONTACT = 'CONTACT',
  LEAD = 'LEAD',
}

export enum CrmNoteStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVE = 'ARCHIVE',
}

export enum CrmNoteAction {
  CALL = 'Call',
  EMAIL = 'Email',
  LINKEDIN = 'LinkedIn',
  MESSAGE = 'Message',
  RESEARCH = 'Research',
  QUALIFY = 'Qualify',
  PROSPECT = 'Prospect',
  OTHER = 'Other'
}

@Entity('crm_notes')
export class CrmNoteEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  title: string;

  @Column({ type: 'varchar', nullable: true, name: 'content' })
  content: string;

  @Column({ name: 'resource_name', nullable: true, enum: CrmNoteResourceName })
  resourceName: CrmNoteResourceName;

  @Column({ name: 'resource_id', nullable: true})
  resourceId: string;

  @Column({ name: 'status', nullable: true, enum: CrmNoteStatus })
  status: CrmNoteStatus;

  @Column({ name: 'action', nullable: true, enum: CrmNoteAction })
  action: CrmNoteAction;

  @ManyToOne(() => UserEntity, (user) => user.crmNotes)
  @JoinColumn({ name: 'creator' })
  creator: UserEntity;

  @Column({ name: 'addition_field', type: 'jsonb', nullable: true })
  additionField: any;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @ManyToOne(() => CrmCompanyEntity, (company) => company.notes)
  @JoinColumn({ name: 'company' })
  company: CrmCompanyEntity;

  @ManyToOne(() => CrmContactEntity, (contact) => contact.notes)
  @JoinColumn({ name: 'contact' })
  contact: CrmContactEntity;

  @ManyToOne(() => CrmLeadEntity, (lead) => lead.notes)
  @JoinColumn({ name: 'lead' })
  lead: CrmLeadEntity;
}

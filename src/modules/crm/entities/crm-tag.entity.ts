import { Entity, PrimaryGeneratedColumn, Column, BaseEntity, ManyToMany, JoinTable, ManyToOne, JoinColumn } from 'typeorm';
import { CrmCompanyEntity } from './crm-company.entity';
import { CrmLeadEntity } from './crm-lead.entity';
import { CrmContactEntity } from './crm-contact.entity';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';

@Entity('crm_tags')
export class CrmTagEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  name: string;

  @Column({ type: 'varchar', nullable: true, name: 'description' })
  description: string;

  @ManyToMany(() => CrmLeadEntity, (leads) => leads.tags)
  leads: CrmLeadEntity[];

  @ManyToMany(() => CrmContactEntity, (contact) => contact.tags)
  contacts: CrmContactEntity[];

  @ManyToMany(() => CrmCompanyEntity, (company) => company.tags)
  companies: CrmCompanyEntity[];

  @ManyToOne(() => OrganizationEntity, (user) => user.industry, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'organization' })
  organization: OrganizationEntity;
}

import { Entity, PrimaryGeneratedColumn, Column, BaseEntity, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable, OneToMany, BeforeInsert, BeforeUpdate } from 'typeorm';
import { CrmIndustryEntity } from './crm-industry.entity';
import { CrmContactEntity } from './crm-contact.entity';
import { CrmLeadEntity } from './crm-lead.entity';
import { CrmNoteEntity } from './crm-note.entity';
import { CrmTagEntity } from './crm-tag.entity';
import { CrmFileEntity } from './crm-file.entity';
import { CrmContactSequenceEntity } from './crm-contact-sequence.entity';
import { CrmContactSequenceStepEntity } from './crm-contact-sequence-step.entity';
import { v5 as uuidv5 } from 'uuid';

@Entity('crm_companies')
export class CrmCompanyEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  name: string;

  @Column({ type: 'varchar', nullable: true, name: 'description' })
  description: string;

  @Column({ type: 'varchar', nullable: true, name: 'website' })
  website: string;

  @Column({ type: 'varchar', nullable: true, name: 'telephone' })
  telephone: string;

  @Column({ name: 'address', type: 'jsonb', nullable: true })
  address: any;

  @Column({ name: 'addition_field', type: 'jsonb', nullable: true })
  additionField: any;

  @Column({ name: 'creator', nullable: true })
  creator: string;

  @Column({ name: 'organization_id', nullable: true })
  organizationId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @OneToMany(() => CrmContactEntity, (contact) => contact.company)
  contacts: CrmContactEntity[];

  @OneToMany(() => CrmLeadEntity, (lead) => lead.company)
  leads: CrmLeadEntity[];

  @OneToMany(() => CrmNoteEntity, (note) => note.company)
  notes: CrmNoteEntity[];

  @OneToMany(() => CrmFileEntity, (note) => note.company)
  files: CrmFileEntity[];

  @OneToMany(() => CrmContactSequenceEntity, (contactSequence) => contactSequence.company)
  contactSequences: CrmContactSequenceEntity[];

  @OneToMany(() => CrmContactSequenceStepEntity, (contactSequenceStep) => contactSequenceStep.company)
  contactSequenceSteps: CrmContactSequenceStepEntity[];

  @ManyToMany(() => CrmIndustryEntity, (industry) => industry.companies, {
    cascade: ['remove'],
  })
  @JoinTable({
    name: 'crm_company_industry',
    joinColumn: {
      name: 'company_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'industry_id',
      referencedColumnName: 'id',
    },
  })
  industries: CrmIndustryEntity[];

  @ManyToMany(() => CrmTagEntity, (tags) => tags.companies, {
    cascade: ['remove'],
  })
  @JoinTable({
    name: 'crm_company_tag',
    joinColumn: {
      name: 'company_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'tag_id',
      referencedColumnName: 'id',
    },
  })
  tags: CrmTagEntity[];

  @Column({ name: 'bullhorn_id', nullable: true })
  bullhornId: string;

  // Prevent duplicate bullhorn id in the same organization
  private generateId() {
    if (this.bullhornId && this.organizationId) {
      const combinedValue = `COMPANY:${this.organizationId}:${this.bullhornId}`;
      this.id = uuidv5(combinedValue, uuidv5.URL);
    }
  }

  @BeforeInsert()
  beforeInsert() {
    this.generateId();
  }
}

import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, BaseEntity, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable, ManyToOne, JoinColumn, OneToMany, BeforeInsert } from 'typeorm';
import { CrmIndustryEntity } from './crm-industry.entity';
import { CrmCompanyEntity } from './crm-company.entity';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { CrmNoteEntity } from './crm-note.entity';
import { CrmTagEntity } from './crm-tag.entity';
import { CrmFileEntity } from './crm-file.entity';
import { CrmContactSequenceEntity } from './crm-contact-sequence.entity';
import { CrmContactSequenceStepEntity } from './crm-contact-sequence-step.entity';
import { CrmLeadEntity } from './crm-lead.entity';
import { v5 as uuidv5 } from 'uuid';

@Entity('crm_contacts')
export class CrmContactEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar',name: "first_name" ,length: 100, nullable: false })
  firstName: string;

  @Column({ type: 'varchar',name: "middle_name" ,length: 100, nullable: true })
  middleName: string;

  @Column({ type: 'varchar',name: "sur_name" ,length: 100, nullable: true })
  surName: string;

  @Column({ name: 'company_id', type: 'uuid', nullable: true })
  companyId: string;

  @ManyToOne(() => CrmCompanyEntity, (company) => company.contacts)
  @JoinColumn({ name: 'company_id' })
  company: CrmCompanyEntity;

  @Column({ type: 'varchar', nullable: true, name: 'description' })
  description: string;

  @Column({ type: 'varchar', nullable: true, name: 'telephone' })
  telephone: string;

  @Column({ name: 'address', type: 'jsonb', nullable: true })
  address: any;

  @Column({ name: 'email', type: 'varchar', nullable: true })
  email: any;

  @Column({ name: 'addition_field', type: 'jsonb', nullable: true })
  additionField: any;

  @Column({ type: 'varchar', nullable: true, name: 'consultant_id' })
  consultantId: string;

  @Column({ type: 'varchar', nullable: true, name: 'job_title' })
  jobTitle: string;

  @ManyToOne(() => UserEntity, (user) => user.crmContacts)
  @JoinColumn({ name: 'creator' })
  creator: UserEntity;

  @Column({ name: 'organization_id', nullable: true })
  organizationId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @OneToMany(() => CrmNoteEntity, (note) => note.contact)
  notes: CrmNoteEntity[];

  @OneToMany(() => CrmFileEntity, (note) => note.contact)
  files: CrmFileEntity[];

  @ManyToMany(() => CrmIndustryEntity, (industry) => industry.contacts, {
    cascade: ['remove'],
  })
  @JoinTable({
    name: 'crm_contact_industry',
    joinColumn: {
      name: 'contact_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'industry_id',
      referencedColumnName: 'id',
    },
  })
  industries: CrmIndustryEntity[];

  @ManyToMany(() => CrmTagEntity, (tags) => tags.contacts, {
    cascade: ['remove'],
  })
  @JoinTable({
    name: 'crm_contact_tag',
    joinColumn: {
      name: 'contact_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'tag_id',
      referencedColumnName: 'id',
    },
  })
  tags: CrmTagEntity[];

  @Column({ name: 'bullhorn_id', nullable: true })
  bullhornId: string;

  @Column({ name: 'linkedin_profile_url', nullable: true })
  linkedinProfileUrl: string;

  @OneToMany(() => CrmContactSequenceEntity, (contactSequence) => contactSequence.contact)
  contactSequences: CrmContactSequenceEntity[];

  @OneToMany(() => CrmContactSequenceStepEntity, (contactSequenceStep) => contactSequenceStep.contact)
  contactSequenceSteps: CrmContactSequenceStepEntity[];

  @ManyToOne(() => CrmLeadEntity, (lead) => lead.contacts, { nullable: true })
  @JoinColumn({ name: 'lead_id' })
  lead: CrmLeadEntity;

  // Prevent duplicate bullhorn id in the same organization
  private generateId() {
    if (this.bullhornId && this.organizationId) {
      const combinedValue = `CONTACT:${this.organizationId}:${this.bullhornId}`;
      this.id = uuidv5(combinedValue, uuidv5.URL);
    }
  }

  @BeforeInsert()
  beforeInsert() {
    this.generateId();
  }
}

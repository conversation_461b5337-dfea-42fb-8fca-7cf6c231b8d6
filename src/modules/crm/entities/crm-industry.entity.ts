import { Entity, PrimaryGeneratedColumn, Column, BaseEntity, ManyToMany, JoinTable, ManyToOne, JoinColumn } from 'typeorm';
import { CrmCompanyEntity } from './crm-company.entity';
import { CrmContactEntity } from './crm-contact.entity';
import { CrmSkillsEntity } from './crm-skill.entity';
import { CrmLeadEntity } from './crm-lead.entity';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';

@Entity('crm_industries')
export class CrmIndustryEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  name: string;

  @Column({ type: 'varchar', nullable: true, name: 'description' })
  description: string;

  @ManyToMany(() => CrmCompanyEntity, (company) => company.industries)
  companies: CrmCompanyEntity[];

  @ManyToMany(() => CrmContactEntity, (contact) => contact.industries)
  contacts: CrmContactEntity[];

  @ManyToMany(() => CrmLeadEntity, (lead) => lead.industries)
  leads: CrmLeadEntity[];

  @ManyToOne(() => OrganizationEntity, (user) => user.industry, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
  @JoinColumn({ name: 'organization' })
  organization: OrganizationEntity;
}

import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, Join<PERSON><PERSON>um<PERSON>, CreateDateColumn, UpdateDateColumn, OneToMany, Index } from 'typeorm';
import { CrmContactEntity } from './crm-contact.entity';
import { SequenceStepEntity } from '../../mail/entities/sequence-step.entity';
import { CrmCompanyEntity } from './crm-company.entity';
import { CrmLeadEntity } from './crm-lead.entity';
import { CrmContactSequenceStepReplyEntity } from './crm-contact-sequence-step-reply.entity';

@Entity('crm_contact_sequence_steps')
export class CrmContactSequenceStepEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @ManyToOne(() => CrmContactEntity, (contact) => contact.contactSequenceSteps, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contact_id' })
  contact: CrmContactEntity;

  @Index()
  @ManyToOne(() => SequenceStepEntity, (sequenceStep) => sequenceStep.contactSequenceSteps, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'sequence_step_id' })
  sequenceStep: SequenceStepEntity;

  @Index()
  @ManyToOne(() => CrmCompanyEntity, (company) => company.contactSequenceSteps, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'company_id' })
  company: CrmCompanyEntity;

  @Index()
  @ManyToOne(() => CrmLeadEntity, (lead) => lead.contactSequenceSteps, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'lead_id' })
  lead: CrmLeadEntity;

  @Index()
  @Column({ name: 'sequence_id', nullable: true })
  sequenceId: string;

  @Column({ name: 'status', nullable: true })
  status: string;

  @Column({ name: 'from_email', nullable: true })
  fromEmail: string;

  @Column({ name: 'to_email', nullable: true })
  toEmail: string;

  @Column({ name: 'subject', nullable: true })
  subject: string;

  @Column({ name: 'content', type: 'text', nullable: true })
  content: string;

  @Column({ name: 'signature_id', nullable: true })
  signatureId: string;

  @Column({ name: 'signature_content', type: 'text', nullable: true })
  signatureContent: string;

  @Column({ name: 'signature_images', type: 'jsonb', nullable: true })
  signatureImages: any[];

  @Column({ name: 'attachments', type: 'jsonb', nullable: true })
  attachments: any[];

  @Index()
  @Column({ name: 'reference_id', nullable: true })
  referenceId: string;

  @Index()
  @Column({ name: 'sent_at', type: 'timestamptz', nullable: true })
  sentAt: Date;

  @Index()
  @Column({ name: 'replied_at', type: 'timestamptz', nullable: true })
  repliedAt: Date;

  @OneToMany(() => CrmContactSequenceStepReplyEntity, reply => reply.sequenceStep)
  replies: CrmContactSequenceStepReplyEntity[];

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}
import { Entity, PrimaryGeneratedColumn, Column, BaseEntity, CreateDateColumn, UpdateDateColumn, JoinTable, ManyToOne, JoinColumn } from 'typeorm';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { CrmCompanyEntity } from './crm-company.entity';
import { CrmContactEntity } from './crm-contact.entity';
import { CrmLeadEntity } from './crm-lead.entity';

export enum CrmNoteResourceName {
  COMPANY = 'COMPANY',
  CONTACT = 'CONTACT',
  LEAD = 'LEAD',
}

@Entity('crm_files')
export class CrmFileEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', nullable: true, name: 'note' })
  note: string;

  @Column({ name: 'resource_name', nullable: true, enum: CrmNoteResourceName })
  resourceName: CrmNoteResourceName;

  @Column({ name: 'resource_id', nullable: true})
  resourceId: string;

  @Column({ name: 'file_id', nullable: true})
  fileId: string;

  @ManyToOne(() => UserEntity, (user) => user.crmNotes)
  @JoinColumn({ name: 'creator' })
  creator: UserEntity;

  @Column({ name: 'addition_field', type: 'jsonb', nullable: true })
  additionField: any;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @ManyToOne(() => CrmCompanyEntity, (company) => company.files)
  @JoinColumn({ name: 'company' })
  company: CrmCompanyEntity;

  @ManyToOne(() => CrmContactEntity, (contact) => contact.files)
  @JoinColumn({ name: 'contact' })
  contact: CrmContactEntity;

  @ManyToOne(() => CrmLeadEntity, (lead) => lead.files)
  @JoinColumn({ name: 'lead' })
  lead: CrmLeadEntity;
}

import { CrmContactService } from '../services/crm-contact.service';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { createContactDto, getContactQuery } from '../dto/crm-contact/create-contact.dto';
import { EditContactDto } from '../dto/crm-contact/edit-contact.dto';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@Controller('crm/contact')
@ApiTags('CRM Contact')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CrmContactController {
  constructor(private readonly crmContactService: CrmContactService) { }

  @Post()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async createContact(@Req() req: any, @Query() pageOptions: getContactQuery) {
    const userId = req.viewAsUser.id;
    return await this.crmContactService.getAllContacts(userId, pageOptions);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getAllContacts(@Req() req: any, @Query() pageOptions: getContactQuery) {
    const userId = req.viewAsUser.id;
    return await this.crmContactService.getAllContacts(userId, pageOptions);
  }

  @Delete(":contactId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteContact(@Req() req: any, @Param('contactId') contactId: string) {
    const userId = req.viewAsUser.id;
    return await this.crmContactService.deleteContact(userId, contactId);
  }

  @Get(":contactId")
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getDetailContact(@Req() req: any, @Param('contactId') contactId: string) {
    const userId = req.viewAsUser.id;
    return await this.crmContactService.getDetailContact(userId, contactId);
  }

  @Patch(":contactId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async editContact(@Req() req: any, @Param('contactId') contactId: string, @Body() body: EditContactDto) {
    const userId = req.viewAsUser.id;
    return await this.crmContactService.handleEditContact(body, userId, contactId);
  }
}

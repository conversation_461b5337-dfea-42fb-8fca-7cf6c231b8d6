import { CrmFileService } from './../services/crm-file.service';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CreateFileDto } from '../dto/crm-file/create-file.dto';
import { EditFileDto } from '../dto/crm-file/edit-file.dto';
import { getFilesDto } from '../dto/crm-file/get-file.dto';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@Controller('crm/file')
@ApiTags('CRM File')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CrmFileController {
  constructor(private readonly crmFileService: CrmFileService) { }

  @Get()
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getListFiles(@Req() req: any, @Query() query: getFilesDto ) {
    const userId = req.viewAsUser.id;
    return await this.crmFileService.handleGetListFile(userId, query);
  }

  @Post()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async createFile(@Body() body: CreateFileDto, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return await this.crmFileService.handleCreateFile(body, userId);
  }

  @Delete(":fileId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteFile(@Req() req: any, @Param('fileId') fileId: string) {
    const userId = req.viewAsUser.id;
    return await this.crmFileService.deleteFile(userId, fileId);
  }

  @Patch(":fileId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async editFile(@Body() body: EditFileDto, @Req() req: any, @Param('fileId') fileId: string) {
    const userId = req.viewAsUser.id;
    return await this.crmFileService.handleEditFile(body, userId, fileId);
  }
}

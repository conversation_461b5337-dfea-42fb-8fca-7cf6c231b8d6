import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CreateNoteDto } from '../dto/crm-note/create-note.dto';
import { CrmNoteService } from '../services/crm-note.service';
import { EditNoteDto } from '../dto/crm-note/edit-note.dto';
import { GetNoteDto } from '../dto/crm-note/get-note.dto';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@Controller('crm/note')
@ApiTags('CRM Note')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CrmNoteController {
  constructor(private readonly crmNoteService: CrmNoteService) { }

  @Post()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async createNote(@Body() body: CreateNoteDto, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return await this.crmNoteService.handleCreateNote(body, userId);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getAllNotes(@Req() req: any, @Query() query: GetNoteDto) {
    const userId = req.viewAsUser.id;
    return await this.crmNoteService.handleGetListNotes(userId, query);
  }

  @Patch(":noteId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async editNote(@Body() body: EditNoteDto, @Req() req: any, @Param('noteId') noteId: string) {
    const userId = req.viewAsUser.id;
    return await this.crmNoteService.handleEditNote(body, userId, noteId);
  }

  @Delete(":noteId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteNote(@Req() req: any, @Param('noteId') noteId: string) {
    const userId = req.viewAsUser.id;
    return await this.crmNoteService.deleteNote(userId, noteId);
  }
}

import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmSkillService } from '../services/crm-skill.service';
import { CreateSkillsDto } from '../dto/crm-skill/crm-skill.dto';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@Controller('crm/skill')
@ApiTags('CRM Skill')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CrmSkillController {
  constructor(private readonly crmSkillService: CrmSkillService) {}

  @Post()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async createSkills(@Body() bodyDto: CreateSkillsDto) {
    await this.crmSkillService.createSkills(bodyDto);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getSkills() {
    return await this.crmSkillService.getSkills({});
  }

  @Delete()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteSkillsByIds(@Query('ids') ids: string) {
    return this.crmSkillService.deleteByIds(ids);
  }

  @Put(':id')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async updateSkillById(@Param('id') id: string, @Body('description') description: string, @Body('name') name: string) {
    return this.crmSkillService.updateById(id, { description, name });
  }
}

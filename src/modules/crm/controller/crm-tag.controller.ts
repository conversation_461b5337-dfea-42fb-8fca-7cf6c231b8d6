import { CrmTagService } from './../services/crm-tag.service';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CreateTagDto } from '../dto/crm-tag/create-tag.dto';
import { EditTagDto } from '../dto/crm-tag/edit-tag.dto';
import { GetAdminTagParams, GetUserTagParams } from '../dto/crm-tag/get-tag.dto';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@Controller('crm/tag')
@ApiTags('CRM Tag')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CrmTagController {
  constructor(private readonly crmTagService: CrmTagService) { }

  @Post()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async createTag(@Body() body: CreateTagDto) {
    return this.crmTagService.createTag(body)
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getTagList(@Query() query: GetAdminTagParams) {
    return this.crmTagService.getTagList(query)
  }

  @Patch("/:tagId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async editTag(@Body() body: EditTagDto, @Param("tagId") tagId: string) {
    return this.crmTagService.editTag(body, tagId)
  }

  @Delete("/:tagId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteTag(@Param("tagId") tagId: string) {
    return this.crmTagService.deleteTag(tagId)
  }
}

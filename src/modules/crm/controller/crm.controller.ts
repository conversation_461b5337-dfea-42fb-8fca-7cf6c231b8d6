import { CrmService } from './../services/crm.service';
import { Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@Controller('crm')
@ApiTags('CRM')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CrmController {
    constructor(private readonly crmService: CrmService) { }

    @Get("/stats")
    @Permission(PermissionResource[ResourceEnum.CRM].Read)
    async getSkills(@Req() req: any) {
      const userId = req.viewAsUser.id;
      return await this.crmService.getCrmStats(userId)
    }

    @Get(":entityType(contact|company|lead)/:id/sequences")
    @Permission(PermissionResource[ResourceEnum.CRM].Read)
    async getSequence(
      @Req() req: any,
      @Param('entityType') entityType: 'contact' | 'company' | 'lead',
      @Param('id') id: string,
      @Query('page') page: number,
      @Query('limit') limit: number
    ) {
      const userId = req.viewAsUser.id;
      return await this.crmService.getCrmSequence(userId, entityType, id, { page, limit });
    }

    @Get(":entityType(contact|company|lead)/:id/emails")
    @Permission(PermissionResource[ResourceEnum.CRM].Read)
    async getSequenceStep(
      @Req() req: any,
      @Param('entityType') entityType: 'contact' | 'company' | 'lead',
      @Param('id') id: string,
      @Query('page') page: number,
      @Query('limit') limit: number
    ) {
      const userId = req.viewAsUser.id;
      return await this.crmService.getCrmSequenceStep(userId, entityType, id, { page, limit });
    }

    @Get("email/:emailId/replies")
    @Permission(PermissionResource[ResourceEnum.CRM].Read)
    async getSequenceStepReplies(
      @Req() req: any,
      @Param('emailId') emailId: string,
      @Query('page') page: number,
      @Query('limit') limit: number
    ) {
      const userId = req.viewAsUser.id;
      return await this.crmService.getCrmSequenceStepReplies(userId, emailId, { page, limit });
    }
}

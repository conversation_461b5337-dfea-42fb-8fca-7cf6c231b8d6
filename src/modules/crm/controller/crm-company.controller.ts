import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmCompanyService } from '../services/crm-company.service';
import { createCompanyDto, getCompanyQuery } from '../dto/crm-company/create-company.dto';
import { EditCompanyDto } from '../dto/crm-company/edit-company.dto';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@Controller('crm/company')
@ApiTags('CRM Company')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CrmCompanyController {
  constructor(private readonly crmCompanyService: CrmCompanyService) { }

  @Post()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async createCompany(@Body() body: createCompanyDto, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.crmCompanyService.handleCreateCompany(body, userId);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getAllCompanies(@Req() req: any, @Query() pageOptions: getCompanyQuery) {
    const userId = req.viewAsUser.id;
    return this.crmCompanyService.getAllCompanies(userId, pageOptions);
  }

  @Delete(":companyId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteCompany(@Req() req: any, @Param('companyId') companyId: string) {
    const userId = req.viewAsUser.id;
    return this.crmCompanyService.deleteCompany(userId, companyId);
  }

  @Patch(":companyId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async editCompany(@Body() body: EditCompanyDto, @Req() req: any, @Param('companyId') companyId: string) {
    const userId = req.viewAsUser.id;
    return this.crmCompanyService.handleEditCompany(body, userId, companyId);
  }
  @Get(":companyId")
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getCompanyDetail(@Req() req: any, @Param('companyId') companyId: string) {
    const userId = req.viewAsUser.id;
    return this.crmCompanyService.getDetailCompany(userId, companyId);
  }

  @Get(":companyId/stats")
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getCompanyStats(@Req() req: any, @Param('companyId') companyId: string) {
    const userId = req.viewAsUser.id;
    return this.crmCompanyService.getCompanyStats(userId, companyId);
  }
}

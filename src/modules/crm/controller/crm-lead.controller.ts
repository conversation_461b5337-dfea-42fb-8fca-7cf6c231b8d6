import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmLeadService } from '../services/crm-lead.service';
import { CreateLeadDto, getLeadsQuery } from '../dto/crm-lead/create-lead.dto';
import { EditLeadDto } from '../dto/crm-lead/edit-lead.dto';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@Controller('crm/lead')
@ApiTags('CRM Lead')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CrmLeadController {
  constructor(private readonly crmLeadService: CrmLeadService) { }

  @Post()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async createLead(@Body() body: CreateLeadDto, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return await this.crmLeadService.handleCreateLead(body, userId);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getAllLeads(@Req() req: any, @Query() pageOptions: getLeadsQuery) {
    const userId = req.viewAsUser.id;
    return await this.crmLeadService.getAllLeads(userId, pageOptions);
  }

  @Get(":leadId")
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getLeadDetail(@Req() req: any, @Param('leadId') leadId: string) {
    const userId = req.viewAsUser.id;
    return await this.crmLeadService.getLeadDetail(userId, leadId);
  }

  @Patch(":leadId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async editLead(@Body() body: EditLeadDto, @Req() req: any, @Param('leadId') leadId: string) {
    const userId = req.viewAsUser.id;
    return await this.crmLeadService.handleEditLead(body, userId, leadId);
  }

  @Delete(":leadId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteLead(@Req() req: any, @Param('leadId') leadId: string) {
    const userId = req.viewAsUser.id;
    return await this.crmLeadService.deleteLead(userId, leadId);
  }
}

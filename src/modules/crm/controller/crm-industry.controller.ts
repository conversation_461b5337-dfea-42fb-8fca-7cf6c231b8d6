import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CrmIndustryService } from '../services/crm-industry.service';
import { CreateIndustryDto } from '../dto/crm-industry/create-industry.dto';
import { EditIndustrtDto } from '../dto/crm-industry/edit-industry.dto';
import { GetAdminIndustryParams } from '../dto/crm-industry/get-industry.dto';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@Controller('crm/industry')
@ApiTags('CRM Industry')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CrmIndustryController {
  constructor(private readonly crmIndustryService: CrmIndustryService) { }

  @Post()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async createIndustry(@Body() body: CreateIndustryDto) {
    return this.crmIndustryService.createIndustry(body)
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getIndustryListByAdmin(@Query() query: GetAdminIndustryParams) {
    return this.crmIndustryService.getIndustryList(query)
  }

  @Delete()
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteIndustryByIds(@Query("ids") ids: string) {
    return this.crmIndustryService.deleteByIds(ids)
  }

  @Get("/view-as/:userId")
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getIndustryListByUser(@Param("userId") userId: string) {
    return this.crmIndustryService.getIndustryListByUser(userId)
  }

  @Patch("/:industryId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async editIndustry(@Body() body: EditIndustrtDto, @Param("industryId") industryId: string) {
    return this.crmIndustryService.editIndustry(body, industryId)
  }

  @Delete("/:industryId")
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteIndustry(@Param("industryId") industryId: string) {
    return this.crmIndustryService.deleteIndustry(industryId)
  }
}

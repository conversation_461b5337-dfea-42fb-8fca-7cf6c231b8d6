import { Injectable } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { ExtractJwt, Strategy } from "passport-jwt";
import { CookieConfig, JwtConfig } from "../../../configs/configs.constants";
import { IJwtRefreshToken } from "../payloads/jwt-payload.payload";
import { UserService } from "../../user/user.service";
import { SECURITY_VERIFY_REFRESH_TOKEN } from "../auth.constants";
import { Request } from "express";

@Injectable()
export class VerifyRefreshTokenStrategy extends PassportStrategy(Strategy, SECURITY_VERIFY_REFRESH_TOKEN) {
  constructor(private readonly userService: UserService) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([(request: Request) => {
        const data = request.cookies[CookieConfig.ACCESS_TOKEN_CODE];
        const accessTokenFromAuthHeader = request?.headers?.authorization?.split(" ")?.[1];
        return data ? data.refreshToken : accessTokenFromAuthHeader;
      }]),
      ignoreExpiration: false,
      secretOrKey: JwtConfig.COMMON_API_JWT_REFRESH_TOKEN_SECRET,
      passReqToCallback: true,
    });
  }

  async validate(req: Request, payload: IJwtRefreshToken): Promise<IJwtRefreshToken> {
    return payload;
  }
}

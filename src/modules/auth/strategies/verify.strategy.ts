import { UserEntity } from './../../user/entities/user.entity';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { IJwtPayload } from '../payloads/jwt-payload.payload';
import { CookieConfig, JwtConfig } from '../../../configs/configs.constants';
import { UserService } from '../../user/user.service';
import { StatusCode, LanguageCode } from '../../../common/constants/common.constant';
import { Request } from 'express';
import { SECURITY_VERIFY } from '../auth.constants';

@Injectable()
export class VerifyStrategy extends PassportStrategy(Strategy, SECURITY_VERIFY) {
  constructor(private readonly usersService: UserService) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        (request: Request) => {
          const data = request.cookies[CookieConfig.ACCESS_TOKEN_CODE];
          const accessTokenFromAuthHeader = request?.headers?.authorization?.split(' ')?.[1];
          return data ? data.accessToken : accessTokenFromAuthHeader;
        },
      ]),
      ignoreExpiration: false,
      secretOrKey: JwtConfig.COMMON_API_JWT_SECRET,
      passReqToCallback: true,
    });
  }

  async checkNotExistUser(user: UserEntity, lang: LanguageCode): Promise<boolean> {
    if (!user) {
      throw new UnauthorizedException(
        await this.usersService.formatOutputData(
          {
            lang,
            key: 'translate.UNAUTHORIZED',
          },
          { data: null, statusCode: StatusCode.UNAUTHORIZED }
        )
      );
    }
    return true;
  }

  async validate(req: Request, payload: IJwtPayload): Promise<IJwtPayload> {
    const langHeader = (req.headers['lang'] as LanguageCode) || LanguageCode.United_States;
    const { id } = payload;

    const user = await this.usersService.findOne({ where: { id }, select: { id: true } });

    await this.checkNotExistUser(user, langHeader);

    let updatedLastActivityUserId = id;
    if (req.originalUrl?.includes('view-as') && Object.keys(req.params).includes('userId')) {
      updatedLastActivityUserId = req.params.userId;
    }

    await this.usersService.updateByConditions({ id: updatedLastActivityUserId }, { lastActivity: new Date() });

    return payload;
  }
}

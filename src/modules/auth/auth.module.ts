import { forwardRef, MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { JwtConfig } from '../../configs/configs.constants';
import { UserModule } from '../user/user.module';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { VerifyRefreshTokenStrategy } from './strategies/verify-refresh-token.strategy';
import { VerifyStrategy } from './strategies/verify.strategy';
import { TokenService } from './token.service';
import { JobsModule } from '../jobs/jobs.module';

@Module({
  imports: [
    forwardRef(() => UserModule),
    PassportModule,
    JwtModule.register({
      secret: JwtConfig.COMMON_API_JWT_SECRET,
      signOptions: {
        expiresIn: JwtConfig.COMMON_API_JWT_EXPIRES_IN,
      },
    }),
    JobsModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, TokenService, VerifyStrategy, VerifyRefreshTokenStrategy],
  exports: [AuthService, TokenService],
})
export class AuthModule {}

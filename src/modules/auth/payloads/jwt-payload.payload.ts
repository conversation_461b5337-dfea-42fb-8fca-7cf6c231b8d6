import { RoleEnum } from "src/modules/user/entities/role.entity";
import { FeatureEnum } from "src/common/constants/feature-permission.constant";

export interface IJwtPayload {
  id: string;
  email: string;
  permissions: string[];
  features: FeatureEnum[];
  role?: RoleEnum;
  organizationId?: string;
}

export interface ISimpleUser {
  id: string;
}

export interface IJwtRefreshToken {
  id: string;
  language?: string;
}

export interface IJwtVerifyCodePayload {
  id: string;
  phone?: string;
  countryCode?: string;
  countryCallingCode?: string;
  email?: string;
  code?: string;
  language?: string;
}

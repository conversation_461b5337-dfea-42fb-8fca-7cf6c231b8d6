import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { JwtService, JwtSignOptions } from '@nestjs/jwt';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from '../../base/base.abstract.service';
import { CookieConfig, JwtConfig } from '../../configs/configs.constants';
import { UserService } from '../user/user.service';
import { IJwtPayload, IJwtRefreshToken } from './payloads/jwt-payload.payload';
import { UserInfoDto } from './dto/response-login.dto';
import { FileUploadService } from '../files-upload/file-upload.service';
import { Response } from 'express';
import { addTimeToDate, calculateUserFeatures } from 'src/common/utils/helpers.util';

@Injectable()
export class TokenService extends BaseAbstractService {
  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    i18nService: I18nService,
    private fileUploadService: FileUploadService
  ) {
    super(i18nService);
  }

  async createTokenLogin(userId: string, res: Response): Promise<UserInfoDto> {
    const userInfo = await this.userService.findOne({
      where: { id: userId },
      relations: ['role', 'role.rolePermissions', 'role.rolePermissions.permission', 'organization'],
    });

    // Ensure userInfo has userPermissions for backward compatibility
    if (!userInfo.userPermissions) {
      userInfo.userPermissions = [];
    }

    const { id, email, role, organizationId } = userInfo;

    // Get permissions from user permissions - Decrecated
    // const userSpecificPermissions = this.userService.getPermissionsOfUser(userPermissions);

    // Get permissions from role.permissions
    let rolePermissions = [];
    if (role && role.permissions) {
      // Format role permissions
      rolePermissions = role.permissions.flatMap((permission) => {
        const result = [];
        if (permission.allowRead) {
          result.push(`${permission.keyCode}.Read`);
        }
        if (permission.allowWrite) {
          result.push(`${permission.keyCode}.Write`);
        }
        return result;
      });
    }

    // Combine user-specific permissions and role permissions
    // User-specific permissions take precedence over role permissions
    const permissions = [...new Set([...rolePermissions])];

    // Calculate feature permissions directly
    const features = calculateUserFeatures(role?.keyCode, permissions);

    const accessTokenPayload: IJwtPayload = { id, permissions, features, email, role: role?.keyCode, organizationId };
    const refreshTokenPayload: IJwtRefreshToken = { id };

    const accessTokenOptions: JwtSignOptions = {
      expiresIn: JwtConfig.COMMON_API_JWT_EXPIRES_IN,
      secret: JwtConfig.COMMON_API_JWT_SECRET,
    };
    const refreshTokenOptions: JwtSignOptions = {
      expiresIn: JwtConfig.COMMON_API_JWT_REFRESH_TOKEN_EXPIRES_IN,
      secret: JwtConfig.COMMON_API_JWT_REFRESH_TOKEN_SECRET,
    };
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(accessTokenPayload, accessTokenOptions),
      this.jwtService.signAsync(refreshTokenPayload, refreshTokenOptions),
    ]);

    this.makeCookie(res, accessToken, refreshToken);

    const expiredRefreshTokenDate = addTimeToDate(String(refreshTokenOptions.expiresIn));
    await this.userService.updateByConditions({ id: userId }, { expiredRefreshTokenDate, lastActivity: new Date() });
    userInfo.expiredRefreshTokenDate = expiredRefreshTokenDate;

    // Check if the user has an initialPassword set
    // If so, add a flag to indicate that password change is required
    const passwordChangeRequired = !!(userInfo as any).initialPassword;

    // Remove sensitive fields from user object
    if (userInfo.password !== undefined) {
      delete userInfo.password;
    }

    if (userInfo.initialPassword !== undefined) {
      delete userInfo.initialPassword;
    }

    return {
      user: userInfo,
      accessToken,
      refreshToken,
      passwordChangeRequired,
    };
  }

  async refreshToken(payload: IJwtRefreshToken, res: Response) {
    const user = await this.userService.findOneById(payload.id);
    const tokenInfo = await this.createTokenLogin(user.id, res);

    return tokenInfo;
  }

  async clearCookie(res: Response) {
    res.cookie(CookieConfig.ACCESS_TOKEN_CODE, 'none', {
      expires: new Date(Date.now() + 5 * 1000),
      httpOnly: true,
    });
  }

  private makeCookie(res: Response, accessToken: string, refreshToken: string) {
    res.cookie(
      CookieConfig.ACCESS_TOKEN_CODE,
      { accessToken, refreshToken },
      {
        httpOnly: true,
        expires: new Date(new Date().getTime() + CookieConfig.REFRESH_TOKEN_EXPIRES_IN),
      }
    );
  }
}

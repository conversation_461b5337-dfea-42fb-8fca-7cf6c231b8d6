import { JobSearchService } from './../jobs/service/job-search.service';
import { BadRequestException, forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { UserService } from '../user/user.service';
import { JwtService } from '@nestjs/jwt';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from '../../base/base.abstract.service';
import { TokenService } from './token.service';
import { StatusCode } from '../../common/constants/common.constant';
import { IJwtRefreshToken } from './payloads/jwt-payload.payload';
import { DataSource } from 'typeorm';
import { LoginDto, RegisterDto } from './dto/login-user.dto';
import { RoleEntity, RoleEnum } from '../user/entities/role.entity';
import { UserEntity } from '../user/entities/user.entity';
import { Response } from 'express';

@Injectable()
export class AuthService extends BaseAbstractService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    private readonly tokenService: TokenService,
    private readonly i18nService: I18nService,
    private dataSource: DataSource,
    private readonly jobSearchService: JobSearchService
  ) {
    super(i18nService);
  }

  async login(loginDto: LoginDto, res: Response) {
    const user = await this.userService.checkValidCredential(loginDto);

    const data = await this.tokenService.createTokenLogin(user.id, res);

    //async handle activate inactive searches
    this.jobSearchService.activateInactiveSearchesBySystem(user.id);

    return this.formatOutputData(
      {
        key: `translate.GET_ACCESS_TOKEN_SUCCESSFULLY`,
      },
      {
        data,
        statusCode: StatusCode.GET_ACCESS_TOKEN_SUCCESSFULLY,
      }
    );
  }

  async refreshToken(payload: IJwtRefreshToken, res: Response) {
    const data = await this.tokenService.refreshToken(payload, res);
    return this.formatOutputData(
      {
        key: `translate.GET_ACCESS_REFRESH_TOKEN_SUCCESSFULLY`,
      },
      {
        data,
        statusCode: StatusCode.GET_ACCESS_REFRESH_TOKEN_SUCCESSFULLY,
      }
    );
  }

  async register(registerDto: RegisterDto) {
    const {email, username, fullName} = registerDto

    // Check for existing email (case insensitive)
    const user = await this.dataSource
      .createQueryBuilder(UserEntity, 'u')
      .where('(LOWER(u.email) = LOWER(:email) OR LOWER(u.username) = LOWER(:username)) AND u.is_deleted = false', { email, username })
      .getOne();


    if (user) {
      throw new BadRequestException(
        await this.formatOutputData(
          {
            key: `translate.USER_EXIST`,
          },
          {
            data: null,
            statusCode: StatusCode.USER_EXIST,
          }
        )
      );
    }
    const roleId =
      user?.roleId ??
      (await this.dataSource.createQueryBuilder(RoleEntity, 'r').where({ keyCode: RoleEnum.BASIC_USER }).getOne())?.id;

    const { hashPassword: password } = await this.userService.hashPassword(registerDto.password);
    const registeredUser = await this.userService.create({ id: user?.id, email, username, password, fullName, roleId });

    return this.formatOutputData(
      {
        key: `translate.REGISTER_SUCCESSFULLY`,
      },
      {
        data: {},
        statusCode: StatusCode.REGISTER_SUCCESSFULLY,
      }
    );
  }

  async logOut(res: Response) {
    await this.tokenService.clearCookie(res);
    return this.formatOutputData({ key: 'LOG_OUT' }, { data: {} });
  }
}

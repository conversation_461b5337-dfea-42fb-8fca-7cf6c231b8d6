import { ApiT<PERSON><PERSON>, ApiOkResponse } from '@nestjs/swagger';
import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Req, Res, UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { IJwtRefreshToken } from '../auth/payloads/jwt-payload.payload';
import { SkipThrottle } from '@nestjs/throttler';
import { ResponseLoginDto, ResponseRegisterDto } from './dto/response-login.dto';
import { SECURITY_VERIFY_REFRESH_TOKEN } from './auth.constants';
import { ResponseMessage } from '../../common/constants/common.constant';
import { LoginDto, RegisterDto } from './dto/login-user.dto';
import { UserService } from '../user/user.service';

@Controller('auth')
@ApiTags('Auth')
@SkipThrottle()
export class AuthController {
  constructor(private readonly authService: AuthService, private readonly userService: UserService) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: ResponseMessage.User.LOGIN_SUCCESSFULLY,
    type: ResponseLoginDto,
  })
  login(@Body() loginDto: LoginDto, @Res({ passthrough: true }) res) {
    return this.authService.login(loginDto, res);
  }

  @Post('refresh-token')
  @UseGuards(AuthGuard(SECURITY_VERIFY_REFRESH_TOKEN))
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: ResponseMessage.User.REFRESH_TOKEN,
    type: ResponseLoginDto,
  })
  refreshToken(@Req() req: Request, @Res({ passthrough: true }) res) {
    return this.authService.refreshToken(<IJwtRefreshToken>req.user, res);
  }

  @Post('register')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: ResponseMessage.User.REGISTER_SUCCESSFULLY,
    type: ResponseRegisterDto,
  })
  register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('log-out')
  @HttpCode(HttpStatus.OK)
  logOut(@Res({ passthrough: true }) res) {
    return this.authService.logOut(res);
  }

  @Get('get-user-by-email/:email')
  async getUserByEmail(@Param('email') email: string) {
    return this.userService.findUserByEmail(email);
  }
}

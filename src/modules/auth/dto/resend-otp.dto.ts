import { ApiProperty } from '@nestjs/swagger';
import { BaseResponseDto } from '../../../common/dto/common.dto';

export class UpdateUserResendOtpDto {
  otpCode: string;
  expiredOtpDate: Date;
  numberResendOtp: number;
  lastResendOtpDate: Date;
}

export class CreateTokenVerifyResendOtpDto {
  @ApiProperty()
  token: string;
}

export class ResendOtpResponseDto extends BaseResponseDto<UpdateUserResendOtpDto> {}

import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class VerifyOtpDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  otpCode: string;

  @ApiProperty()
  //TODO
  // @IsNotEmpty()
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty()
  //TODO
  // @IsNotEmpty()
  @IsOptional()
  @IsString()
  fcmToken?: string;
}

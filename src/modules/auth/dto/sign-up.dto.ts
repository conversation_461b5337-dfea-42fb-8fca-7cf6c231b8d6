import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { Matches, IsOptional, IsString, MaxLength } from 'class-validator';
import { ResponseDto } from '../../../common/dto/common.dto';

export class SignUpDto {
  @IsOptional()
  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsString()
  @Transform((data) => data.value?.trim().toLowerCase())
  @MaxLength(41)
  @Matches(
    /^([^@\s\."'<>\(\)\[\]\{\}\\/,:;]+\.)*[^@\s\."'<>\(\)\[\]\{\}\\/,:;]+@[^@\s\._"'<>\(\)\[\]\{\}\\/,:;]+(\.[^@\s\."'<>\(\)\[\]\{\}\\/,:;]+)+$/m,
    {
      message: 'This must be an email',
    },
  )
  email: string;

  @IsOptional()
  @ApiProperty({
    example: '0123456789',
  })
  @IsString()
  @Transform((data) => data.value?.trim())
  phone: string;

  @ApiProperty()
  @IsString()
  password: string;
}

export class ResponseSignUpDto extends ResponseDto {}

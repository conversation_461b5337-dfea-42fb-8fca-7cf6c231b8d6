import { IsString, IsE<PERSON>, <PERSON> } from "class-validator";
import { Transform } from "class-transformer";
import { USERNAME_REGEX } from "../../../common/constants/common.constant";


//TODO: change to username
export class LoginDto {
  @IsString()
  emailOrUsername: string;

  @IsString()
  password: string;
}

export class RegisterDto {
  @IsString()
  @Transform((data) => data.value?.trim().toLowerCase())
  email: string;

  @IsString()
  @Matches(USERNAME_REGEX, {
    message: 'Username can only contain letters, numbers, underscores, and periods',
  })
  username: string;

  @IsString()
  password: string;

  @IsString()
  fullName: string;
}

import { BaseResponseDto } from "./../../../common/dto/common.dto";
import { ApiProperty } from "@nestjs/swagger";
import { IsString } from "class-validator";
import { UserResponseDto } from "../../../modules/user/dto/user-response.dto";

export class UserInfoDto {
  @ApiProperty({ type: UserResponseDto })
  user: UserResponseDto;

  @ApiProperty()
  accessToken: string;

  @ApiProperty()
  refreshToken: string;

  @ApiProperty({ required: false, description: 'Indicates if the user needs to change their initial password' })
  passwordChangeRequired?: boolean;
}

export class ResponseLoginDto extends BaseResponseDto<UserInfoDto> {
  @ApiProperty({ type: UserInfoDto })
  result: UserInfoDto;
}

export class RefreshTokenDto {
  @ApiProperty()
  @IsString()
  accessToken: string;
}

export class UserInfoOtpDto {
  @ApiProperty()
  @IsString()
  otpCode: string;
}

export class ResponseRegisterDto extends BaseResponseDto<object> {
  @ApiProperty()
  result: object;
}


import { Injectable, NotFoundException } from "@nestjs/common";
import { I18nService } from "nestjs-i18n";
import { BaseAbstractService } from "src/base/base.abstract.service";
import { WebhookRepository } from "../repositories/webhook.repository";
import { CreateWebhookDto } from "../dto/webhook.dto";
import { WebHookNameEnum } from "../enums/webhook.enum";
import { HttpService } from "@nestjs/axios";

@Injectable()
export class WebhookService extends BaseAbstractService {
  constructor(
    private readonly i18nService: I18nService,
    private readonly webhookRepository: WebhookRepository,
    private readonly httpService: HttpService,
  ) {
    super(i18nService);
  }

  async create(createWebhookDto: CreateWebhookDto) {
    return this.webhookRepository.save(createWebhookDto);
  }

  async getByName(name: <PERSON>HookNameEnum) {
    const webhook = await this.webhookRepository.find({
      where: { name },
    });

    if (!webhook) {
      throw new NotFoundException('Webhook not found');
    }

    return webhook;
  }

  async triggerWebhookByName(name: WebHookNameEnum, additionalQueryParams?: Record<string, any>) {
    console.log(`Triggering webhook: ${name}`, additionalQueryParams);
    const webhooks = await this.getByName(name);

    return Promise.allSettled(
      webhooks.map(async (webhook) => {
        const { queryParams } = webhook;
        const query = { ...queryParams, ...additionalQueryParams };
        const url = `${webhook.url}${query ? '?' + new URLSearchParams(query).toString() : ''}`;
        return this.httpService.axiosRef.request({
          url,
          method: webhook.method,
          headers: webhook.headers,
          data: webhook.payload,
          params: webhook.queryParams,
        });
      }),
    );
  }

  async getAll() {
    return this.webhookRepository.find();
  }

  async deleteById(id: string) {
    const webhook = await this.webhookRepository.findOne({ where: { id } });
    if (!webhook) {
      throw new NotFoundException('Webhook not found');
    }
    return this.webhookRepository.remove(webhook);
  }
}
import { Modu<PERSON> } from "@nestjs/common";
import { WebhookController } from "./controllers/webhook.controller";
import { WebhookService } from "./services/webhook.service";
import { WebhookRepository } from "./repositories/webhook.repository";
import { HttpModule } from "@nestjs/axios";

@Module({
  imports: [HttpModule],
  controllers: [WebhookController],
  providers: [WebhookService, WebhookRepository],
  exports: [WebhookService],
})
export class WebhookModule { }
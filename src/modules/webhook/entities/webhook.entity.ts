import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { HttpMethod, WebHookNameEnum } from "../enums/webhook.enum";

@Entity({ name: "webhooks" })
export class WebhookEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'name', nullable: false })
  name: WebHookNameEnum;

  @Column({ name: 'url', nullable: false })
  url: string;

  @Column({ name: 'method', nullable: false, default: HttpMethod.GET })
  method: HttpMethod;

  @Column({ name: 'headers', type: 'jsonb', nullable: true })
  headers: Record<string, string>;

  @Column({ name: 'payload', type: 'jsonb', nullable: true })
  payload: Record<string, any>;

  @Column({ name: 'query_params', type: 'jsonb', nullable: true })
  queryParams: Record<string, string>;

  @Column({ name: 'params', type: 'jsonb', nullable: true })
  params: Record<string, string>;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}
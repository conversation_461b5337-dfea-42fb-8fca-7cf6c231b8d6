import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { HttpMethod, WebHookNameEnum } from "../enums/webhook.enum";
import { IsEnum, IsNotEmpty, IsOptional } from "class-validator";

export class CreateWebhookDto {
  @ApiProperty()
  @IsEnum(WebHookNameEnum)
  name: WebHookNameEnum;

  @ApiProperty()
  @IsNotEmpty()
  url: string;

  @ApiProperty()
  @IsNotEmpty()
  method: HttpMethod;

  @ApiPropertyOptional()
  @IsOptional()
  headers?: Record<string, string>;

  @ApiPropertyOptional()
  @IsOptional()
  payload?: Record<string, any>;

  @ApiPropertyOptional()
  @IsOptional()
  queryParams?: Record<string, string>;

  @ApiPropertyOptional()
  @IsOptional()
  params?: Record<string, string>;  
}
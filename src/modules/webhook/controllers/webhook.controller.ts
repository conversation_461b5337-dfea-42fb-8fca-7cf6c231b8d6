import { Body, Controller, Delete, Get, Param, Post } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { SkipThrottle } from "@nestjs/throttler";
import { WebhookService } from "../services/webhook.service";
import { CreateWebhookDto } from "../dto/webhook.dto";

@ApiTags("Webhooks")
@Controller("webhook")
@SkipThrottle()
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) { }

  @Post()
  create(@Body() createWebhookDto: CreateWebhookDto) {
    return this.webhookService.create(createWebhookDto);
  }

  @Get()
  getAll() {
    return this.webhookService.getAll();
  }

  @Delete(":id")
  delete(@Param("id") id: string) {
    return this.webhookService.deleteById(id);
  }
}
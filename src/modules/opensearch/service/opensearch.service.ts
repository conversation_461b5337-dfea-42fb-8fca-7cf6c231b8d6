// opensearch.service.ts

import { Injectable, OnModuleInit } from '@nestjs/common';
import { Client, RequestParams } from '@opensearch-project/opensearch';
import { writeFileSync } from 'fs';
import { delayTime } from 'src/common/utils/helpers.util';
import { JOBS_INDEX } from 'src/configs/configs.constants';
import { v4 as uuid } from 'uuid';

@Injectable()
export class OpensearchService implements OnModuleInit {
  private readonly client: Client;

  constructor() {
    // Create Elasticsearch client with basic auth
    this.client = new Client({
      node: process.env.ELASTICSEARCH_HOST,
      auth: {
        username: process.env.ELASTICSEARCH_USER,
        password: process.env.ELASTICSEARCH_PASSWORD,
      },
      ssl: {
        rejectUnauthorized: false,
      },
    });
  }

  async onModuleInit() {
    await this.initMappingAndIndexes();
  }

  getClient(): Client {
    return this.client;
  }

  async initMappingAndIndexes() {
    const mappingIndexes = {
      jobs: {
        settings: {
          analysis: {
            analyzer: {
              job_keyword_analyzer: {
                tokenizer: 'whitespace',
                min_gram: 1,
                max_gram: 2,
                filter: ['lowercase'],
              },
            },
          },
        },
        mappings: {
          properties: {
            _meta: {
              type: 'object',
            },
            admin_level_one_area: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            company: {
              type: 'text',
              analyzer: 'job_keyword_analyzer',
              search_analyzer: 'job_keyword_analyzer',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            companyrating: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            country: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            createdAt: {
              type: 'date',
            },
            created_at: {
              type: 'date',
            },
            description: {
              type: 'text',
              analyzer: 'job_keyword_analyzer',
              search_analyzer: 'job_keyword_analyzer',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            extracteddate: {
              type: 'date',
            },
            insert_from_app: {
              type: 'boolean',
            },
            job_id: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            joblocationcity: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            joblocationinput: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            jobtitle: {
              type: 'text',
              analyzer: 'job_keyword_analyzer',
              search_analyzer: 'job_keyword_analyzer',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            jobtype: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            link: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            logoCompany: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            logo_company: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            max_salary: {
              type: 'float',
            },
            min_salary: {
              type: 'float',
            },
            posted: {
              type: 'date',
            },
            raw_salary: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            salary: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            sendToBullHornByUserIds: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            send_to_bullhorn_by_user_ids: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            source: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            updatedAt: {
              type: 'date',
            },
            updated_at: {
              type: 'date',
            },
          },
        },
      },
      reported_agency_country: {
        mappings: {
          properties: {
            _meta: {
              properties: {
                reported_agency: {
                  properties: {
                    id: {
                      type: 'text',
                      fields: {
                        keyword: {
                          type: 'keyword',
                          ignore_above: 256,
                        },
                      },
                    },
                  },
                },
              },
            },
            country: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            reported_agencies: {
              properties: {
                company_name: {
                  type: 'text',
                  fields: {
                    keyword: {
                      type: 'keyword',
                      ignore_above: 256,
                    },
                  },
                },
                country: {
                  type: 'text',
                  fields: {
                    keyword: {
                      type: 'keyword',
                      ignore_above: 256,
                    },
                  },
                },
                id: {
                  type: 'text',
                  fields: {
                    keyword: {
                      type: 'keyword',
                      ignore_above: 256,
                    },
                  },
                },
              },
            },
          },
        },
      },
      location_mappings: {
        mappings: {
          properties: {
            location: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            address: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            country: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            state: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            city: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            countryCode: {
              type: 'keyword',
            },
          },
        },
      },
      unknown_location_jobs: {
        mappings: {
          properties: {
            location: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            job_id: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256,
                },
              },
            },
            last_retrieval_at: {
              type: 'date',
            },
          },
        },
      },
      cached_locations: {
        mappings: {
          properties: {
            key_search: {
              type: 'text',
            },
            data: {
              type: 'object',
            },
          },
        },
      },
    };

    await Promise.allSettled(
      Object.keys(mappingIndexes).map(async (index) => {
        try {
          const existsIndex = await this.client.indices.exists({ index });
          if (existsIndex.body) {
            console.log(`The index ${index} is already exist.`);
            return;
          }
          await this.client.indices.create({
            index,
            body: mappingIndexes[index],
          });
          console.log(`The index ${index} is created successfully.`);
        } catch (error) {
          console.log(`Initial index ${index} was failed with error: `, error);
        }
      })
    );
  }

  async getById(index: string, id: string) {
    try {
      const data = await this.client.get({
        index,
        id,
      });

      return data.body._source;
    } catch (error) {
      return null;
    }
  }

  async searchCachedLocationsByKey(keySearch: string) {
    try {
      const result = await this.client.search({
        index: 'cached_locations',
        body: {
          query: {
            match: {
              key_search: keySearch,
            },
          },
        },
      });

      return result.body.hits.hits.map((hit) => hit._source);
    } catch (error) {
      console.error('Error searching cached_locations by key_search:', error);
      throw error;
    }
  }

  async insertCachedLocation(location: string, data: any) {
    try {
      await this.client.index({
        index: 'cached_locations',
        body: {
          key_search: location,
          data: data,
        },
      });
    } catch (error) {
      console.error('Error insert cached_locations by key_search:', error);
      throw error;
    }
  }

  async getJobsByIds(ids: string[], userId: string) {
    const data = await this.client.search({
      index: JOBS_INDEX,
      body: {
        query: {
          bool: {
            must: [
              {
                ids: {
                  values: ids,
                },
              },
            ],
            must_not: [
              {
                terms: {
                  'company.keyword': {
                    index: 'reported_agency_country',
                    id: 'Global',
                    path: 'reported_agencies.company_name',
                  },
                },
              },
              {
                match_phrase: {
                  send_to_bullhorn_by_user_ids: userId,
                },
              }
            ],
          },
        },
      },
    });
    return data.body.hits.hits;
  }

  async updateSendJobIdsBy(index: string, jobId: string, sendToBullHornByUserIds: string) {
    try {
      if (!jobId) {
        return null;
      }
      return this.client.updateByQuery({
        index,
        body: {
          script: {
            source: `
            if (ctx._source.job_id == params.jobId) {
              ctx._source.send_to_bullhorn_by_user_ids = ctx._source.send_to_bullhorn_by_user_ids + "," + params.sendToBullHornByUserIds;
              ctx._source.updated_at = params.updatedAt;
            }
          `,
            lang: 'painless',
            params: {
              jobId,
              sendToBullHornByUserIds,
              updatedAt: new Date(),
            },
          },
          query: {
            bool: {
              must: [
                {
                  match_phrase: {
                    job_id: jobId,
                  },
                },
              ],
            },
          },
        },
      });
    } catch (error) {
      console.log('Error in updateSendJobIdsBy error', error);
      console.log('Error in updateSendJobIdsBy jobId', jobId);
      console.log('Error in updateSendJobIdsBy sendToBullHornByUserIds', sendToBullHornByUserIds);
      throw error;
    }
  }

  async insertOrUpdate(index: string, documentId: string, partialEntity: any, { logging = true } = {}) {
    const standardPartialEntity = {
      ...partialEntity,
      updated_at: new Date(),
    };
    try {
      const response = await this.client.index({
        index,
        id: documentId,
        body: standardPartialEntity,
      });

      if (logging) {
        console.log(`Item inserted or updated: ${response.body.result}`, { partialEntity, documentId });
      }
    } catch (error) {
      console.log('Error in OpensearchService.insertOrUpdate', error, { partialEntity, documentId });
      throw error;
    }
  }

  async updateDocument(index: string, documentId: string, partialEntity: any) {
    try {
      const response = await this.client.update({
        index,
        id: documentId,
        body: {
          doc: {
            ...partialEntity,
            updated_at: new Date(),
          },
        },
      });

      console.log(`Item was updated: ${response.body.result}`, { partialEntity, documentId });
    } catch (error) {
      console.log('Error in OpensearchService.updateDocument', error, { partialEntity, documentId });
      throw error;
    }
  }

  async deleteDocuments(index: string, documentIds: string[]) {
    try {
      const body = documentIds.flatMap((id) => [{ delete: { _index: index, _id: id } }]);

      const response = await this.client.bulk({ body });

      console.log(`Deleted documents: ${response.body.items.length}`);
    } catch (error) {
      console.log('Error in OpensearchService.deleteDocuments', error, { documentIds });
    }
  }

  async pushNewAgenciesToReportedAgencies(companyNames: string[]) {
    console.log('Start pushNewAgenciesToReportedAgencies', { companyNames });
    try {
      const companyItems = companyNames.map((item) => ({
        id: uuid(),
        company_name: item,
      }));

      const query: RequestParams.UpdateByQuery<any> = {
        index: 'reported_agency_country',
        body: {
          query: {
            term: {
              'country.keyword': {
                value: 'Global',
              },
            },
          },
          script: {
            source: `if (ctx._source.containsKey('reported_agencies') && ctx._source.reported_agencies != null) {
              ctx._source.reported_agencies.addAll(params.new_items);
            } else {
              ctx._source.reported_agencies = params.new_items;
            }`,
            params: {
              new_items: companyItems,
            },
          },
        },
      };

      return this.client.updateByQuery(query);
    } catch (error) {
      console.log('Error in pushNewAgenciesToReportedAgencies', error);
      throw error;
    }
  }

  async removeAgenciesFromReportedAgencies(companyNames: string[]) {
    console.log('Start removeAgenciesFromReportedAgencies', { companyNames });
    try {
      const query = {
        index: 'reported_agency_country',
        body: {
          query: {
            bool: {
              must: [
                {
                  term: {
                    'country.keyword': 'Global',
                  },
                },
                // {
                //   nested: {
                //     path: 'reported_agencies',
                //     query: {
                //       term: {
                //         'reported_agencies.country.keyword': country,
                //       },
                //     },
                //   },
                // },
              ],
            },
          },
          script: {
            source:
              'ctx._source.reported_agencies.removeIf(item -> params.targetCompanies.contains(item.company_name))',
            params: {
              targetCompanies: companyNames,
              // targetCountry: country,
            },
          },
        },
      };

      return this.client.updateByQuery(query);
    } catch (error) {
      console.log('Error in removeAgenciesFromReportedAgencies', error);
      throw error;
    }
  }

  async updateJobLog(index: string, jobId: string, jobLog: string, retry = 0) {
    try {
      if (!jobId) {
        return null;
      }

      return this.client.updateByQuery({
        index,
        body: {
          script: {
            source: `
            if (ctx._source.job_id == params.jobId) {
              // Update the updated_at field
              ctx._source.updated_at = params.updatedAt;

              // Handle jobLogs field
              if (ctx._source.containsKey('jobLogs') && ctx._source.jobLogs != null && ctx._source.jobLogs != '') {
                // Use Painless-compatible string splitting
                def logs = /,/.split(ctx._source.jobLogs);
                def newLogs = new ArrayList();

                // Add all logs except the one we're trying to add (to avoid duplicates)
                for (def log : logs) {
                  def trimmedLog = log.trim();
                  if (trimmedLog != params.jobLog.trim()) {
                    newLogs.add(trimmedLog);
                  }
                }

                // Add the new log
                newLogs.add(params.jobLog.trim());

                // Join back into a string
                ctx._source.jobLogs = String.join(',', newLogs);
              } else {
                // If no logs exist yet, just set it directly
                ctx._source.jobLogs = params.jobLog;
              }
            }
          `,
            lang: 'painless',
            params: {
              jobId,
              jobLog,
              updatedAt: new Date().toISOString(), // Use ISO string for date
            },
          },
          query: {
            bool: {
              must: [
                {
                  match_phrase: {
                    job_id: jobId,
                  },
                },
              ],
            },
          },
        },
      });
    } catch (error) {
      console.log('Error in updateJobLog: ', error, jobId, jobLog, retry);
      if (retry > 0) {
        await delayTime(1000);
        return this.updateJobLog(index, jobId, jobLog, retry - 1);
      }
      throw error;
    }
  }

  async removeJobLog(index: string, jobId: string, jobLog: string) {
    try {
      if (!jobId) {
        return null;
      }

      return this.client.updateByQuery({
        index,
        body: {
          script: {
            source: `
            if (ctx._source.job_id == params.jobId) {
              // Update the updated_at field
              ctx._source.updated_at = params.updatedAt;

              // Handle jobLogs field
              if (ctx._source.containsKey('jobLogs') && ctx._source.jobLogs != null && ctx._source.jobLogs != '') {
                // Use Painless-compatible string splitting
                def logs = /,/.split(ctx._source.jobLogs);
                def newLogs = new ArrayList();

                // Add all logs except the one we're trying to remove
                for (def log : logs) {
                  def trimmedLog = log.trim();
                  if (trimmedLog != params.jobLog.trim()) {
                    newLogs.add(trimmedLog);
                  }
                }

                // Join back into a string
                ctx._source.jobLogs = String.join(',', newLogs);
              }
            }
          `,
            lang: 'painless',
            params: {
              jobId,
              jobLog,
              updatedAt: new Date().toISOString(), // Use ISO string for date
            },
          },
          query: {
            bool: {
              must: [
                {
                  match_phrase: {
                    job_id: jobId,
                  },
                },
              ],
            },
          },
        },
      });
    } catch (error) {
      console.log('Error in removeJobLog: ', error, jobId, jobLog);
      throw error;
    }
  }

  async setOSReportedAgencies(companyNames: string[]) {
    console.log('Start setOSReportedAgencies', { companyNames });
    try {
      const companyItems = companyNames.map((item) => ({
        id: uuid(),
        company_name: item,
      }));

      const query: RequestParams.UpdateByQuery<any> = {
        index: 'reported_agency_country',
        body: {
          query: {
            term: {
              'country.keyword': {
                value: 'Global',
              },
            },
          },
          script: {
            source: `ctx._source.reported_agencies = params.reportedAgencies;`,
            params: {
              reportedAgencies: companyItems,
            },
          },
        },
      };

      return this.client.updateByQuery(query);
    } catch (error) {
      console.log('Error in pushNewAgenciesToReportedAgencies', error);
      throw error;
    }
  }

  async addLocationMapping(location: string, data: any) {
    try {
      return this.client.index({
        index: 'location_mappings',
        id: location.toLowerCase().replace(/\s+/g, '-').trim(),
        body: {
          ...data,
          location,
        },
      });
    } catch (error) {
      console.log('Error in addLocationMapping', error);
      throw error;
    }
  }

  async searchLocationMappings(query: string, size: number = 10) {
    try {
      const data = await this.client.search({
        index: 'location_mappings',
        body: {
          query: {
            term: {
              'location.keyword': query,
            }
          },
          size,
        },
      });

      return data.body.hits.hits;
    } catch (error) {
      console.log('Error in searchLocationMappings', error);
      throw error;
    }
  }

  async createUnknownLocationJob({
    job_id,
    location,
    retry = 5,
  }: {
    job_id: string;
    location: string;
    retry?: number;
  }) {
    try {
      const id = location.toLowerCase().replace(/\s+/g, '-').trim();
      if (!id) {
        return null;
      }
      if (retry === 0) {
        console.error('Error at createUnknownLocationJob', { job_id, location });
        return null;
      }

      const result = await this.client.index({
        index: 'unknown_location_jobs',
        id,
        body: {
          location,
          job_id,
          last_retrieval_at: new Date(),
        },
      });

      return result;
    } catch (error) {
      console.error('Error at createUnknownLocationJob', { job_id, location, retry, error });
      return this.createUnknownLocationJob({ job_id, location, retry: retry - 1 });
    }
  }

  async bulkUpdateRefinedJobs(docIds: string[], keywords: string[], searchId: string) {
    if (!docIds.length) {
      return { success: true, updated: 0, errors: [], total: 0 };
    }

    // Input validation
    const validKeywords = keywords.filter(Boolean);
    if (!validKeywords.length || !searchId) {
      console.warn('[bulkUpdateRefinedJobs] Invalid input: empty keywords or searchId');
      return {
        success: false,
        updated: 0,
        errors: ['Invalid keywords or searchId'],
        total: docIds.length,
      };
    }

    try {
      const bulkOps = docIds.flatMap((id) => [
        { update: { _index: 'jobs', _id: id } },
        {
          script: {
            source: `
            // Initialize arrays if they don't exist
            if (ctx._source.refinedKeywords == null) {
              ctx._source.refinedKeywords = [];
            }
            if (ctx._source.refinedSearches == null) {
              ctx._source.refinedSearches = [];
            }

            // Add keywords only if they don't already exist (prevent duplicates)
            for (String keyword : params.keywords) {
              if (!ctx._source.refinedKeywords.contains(keyword)) {
                ctx._source.refinedKeywords.add(keyword);
              }
            }

            // Add searchId only if it doesn't already exist (prevent duplicates)
            if (!ctx._source.refinedSearches.contains(params.searchId)) {
              ctx._source.refinedSearches.add(params.searchId);
            }

            // Update timestamp
            ctx._source.updated_at = params.updatedAt;
          `,
            lang: 'painless',
            params: {
              keywords: validKeywords,
              searchId: searchId,
              updatedAt: new Date().toISOString(),
            },
          },
        },
      ]);

      const response = await this.client.bulk({ refresh: true, body: bulkOps });

      // Enhanced error handling
      const errors = response.body.items
        .map((item, index) => ({ item, index }))
        .filter(({ item }) => item.update?.error)
        .map(({ item, index }) => ({
          docId: docIds[index],
          error: item.update.error,
        }));

      const successCount = response.body.items.length - errors.length;

      if (errors.length > 0) {
        console.error(
          `[bulkUpdateRefinedJobs] ${errors.length}/${docIds.length} operations failed:`,
          errors,
        );
      }

      console.log(
        `[bulkUpdateRefinedJobs] Successfully updated ${successCount}/${docIds.length} jobs with keywords for search ${searchId}`,
      );

      return {
        success: errors.length === 0,
        updated: successCount,
        errors,
        total: docIds.length,
      };
    } catch (error) {
      console.error('Error in bulkUpdateRefinedJobs', error);
      return {
        success: false,
        updated: 0,
        errors: [error.message],
        total: docIds.length,
      };
    }
  }


  async markedJobAsRefined(docIds: string[], searchId: string) {
    if (!docIds.length) {
      return { success: true, updated: 0, errors: [], total: 0 };
    }

    // Input validation
    if (!searchId) {
      console.warn('[markedJobAsRefined] Invalid input: empty searchId');
      return { success: false, updated: 0, errors: ['Invalid searchId'], total: docIds.length };
    }

    try {
      const bulkOps = docIds.flatMap((id) => [
        { update: { _index: 'jobs', _id: id } },
        {
          script: {
            source: `
            // Initialize array if it doesn't exist
            if (ctx._source.refinedSearches == null) {
              ctx._source.refinedSearches = [];
            }

            // Add searchId only if it doesn't already exist (prevent duplicates)
            if (!ctx._source.refinedSearches.contains(params.searchId)) {
              ctx._source.refinedSearches.add(params.searchId);
            }

            // Update timestamp
            ctx._source.updated_at = params.updatedAt;
          `,
            lang: 'painless',
            params: {
              searchId: searchId,
              updatedAt: new Date().toISOString(),
            },
          },
        },
      ]);

      const response = await this.client.bulk({ refresh: true, body: bulkOps });

      // Enhanced error handling
      const errors = response.body.items
        .map((item: any, index: any) => ({ item, index }))
        .filter(({ item }: any) => item.update?.error)
        .map(({ item, index }: any) => ({
          docId: docIds[index],
          error: item.update.error,
        }));

      const successCount = response.body.items.length - errors.length;

      if (errors.length > 0) {
        console.error(
          `[markedJobAsRefined] ${errors.length}/${docIds.length} operations failed:`,
          errors,
        );
      }

      console.log(
        `[markedJobAsRefined] Successfully marked ${successCount}/${docIds.length} jobs as refined for search ${searchId}`,
      );

      return {
        success: errors.length === 0,
        updated: successCount,
        errors,
        total: docIds.length,
      };
    } catch (error) {
      console.error('Error in markedJobAsRefined', error);
      return {
        success: false,
        updated: 0,
        errors: [error.message],
        total: docIds.length,
      };
    }
  }
}

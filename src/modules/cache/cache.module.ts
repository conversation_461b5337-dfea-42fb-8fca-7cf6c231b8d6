import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { CacheModule } from '@nestjs/cache-manager';
import { CacheService } from "./cache.service";
import * as redisStore from 'cache-manager-ioredis';
import { redisConfig, redisConnection } from "src/configs/configs.constants";
import { CacheController } from "./cache.controller";

@Module({
  controllers: [CacheController],
  imports: [CacheModule.register({
    store: redisStore,
    ...redisConnection,
  })],
  providers: [CacheService],
  exports: [CacheService],
})
export class MyCacheModule { }

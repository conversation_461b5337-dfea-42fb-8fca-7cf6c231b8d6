import Redis from 'ioredis';
import { Inject, Injectable } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

import { Cache, Store } from 'cache-manager';
import { RemoveCacheDto } from './cache.dto';
import { redisConnection } from "src/configs/configs.constants";
import { result } from 'lodash';

const memoryKeysCache = new Map<string, { data: string[]; expiry: number }>();
const cacheTTL = 10 * 60 * 1000; // 10 minutes in milliseconds

@Injectable()
export class CacheService {
  private _redisClient;
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) { }

  async redisClient() {
    if (this._redisClient) {
      return this._redisClient;
    }

    const redisClient = new Redis({
      ...redisConnection,
      connectTimeout: 3000,
      commandTimeout: 3000,
    });

    redisClient.on('connect', () => {
      console.log('✅ Connected to Redis');
    });

    redisClient.on('error', (err) => {
      console.error('❌ Redis connection error:', err);
    });

    await redisClient.ping(); // Ensure connection is active
    this._redisClient = redisClient;

    return redisClient;
  }


  async get(key: string): Promise<any> {
    return this.cacheManager.get(key);
  }

  async getJson<T>(key: string): Promise<T> {
    try {
      const rawData: string = await this.cacheManager.get(key);
      return JSON.parse(rawData) ?? {};
    } catch (error) {
      return {} as T;
    }
  }

  async set(key: string, value: any, ttl?: number) {
    return this.cacheManager.set(key, value, { ttl } as any); // ttl: seconds
  }

  async remove(key: string) {
    return this.cacheManager.del(key);
  }

  getClient(): Store {
    return this.cacheManager.store;
  }

  async _keys(pattern: string = '*') {
    try {
      const keys = await this.cacheManager.store.keys(pattern) || [];
      return keys;
    } catch (error) {
      return null;
    }
  }

  async keys(pattern: string = '*') {
    try {
      // Still use keys if enabled
      const _keys = await this._keys(pattern);
      if (_keys) {
        return _keys;
      }

      const now = Date.now();
      const cacheEntry = memoryKeysCache.get(pattern);

      if (cacheEntry && cacheEntry.expiry > now) {
        console.log('✅ Using memory cache');
        return cacheEntry.data;
      }

      const client = await this.redisClient();
      const keys: string[] = [];
      let cursor = '0';
      do {
        const [nextCursor, foundKeys] = await client.scan(cursor, 'MATCH', pattern, 'COUNT', 1000);
        cursor = nextCursor;
        keys.push(...foundKeys);
      } while (cursor !== '0');

      // Store in memory cache
      memoryKeysCache.set(pattern, { data: keys, expiry: now + cacheTTL });

      return keys;
    } catch (error) {
      return [];
    }
  }

  async removeCache(removeCacheDto: RemoveCacheDto) {
    if (removeCacheDto.isAll) {
      await this.cacheManager.reset();
      return { message: 'All cache removed' };
    }

    if (removeCacheDto.pattern) {
      const keys: string[] = await this.keys(removeCacheDto.pattern);

      if (keys.length > 0) {
        await Promise.all(keys.map((key) => this.cacheManager.del(key)));
      }

      return { message: 'Cache keys removed' };
    }

    if (removeCacheDto.key) {
      await this.cacheManager.del(removeCacheDto.key);
      return { message: 'Cache key removed' };
    }

    return { message: 'No cache key found' };
  }
}

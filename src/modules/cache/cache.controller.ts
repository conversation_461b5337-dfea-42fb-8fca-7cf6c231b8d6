import { Body, Controller, Delete, UseGuards } from '@nestjs/common';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { ApiTags } from '@nestjs/swagger';
import { CacheService } from './cache.service';
import { RemoveCacheDto } from './cache.dto';

@ApiTags('Cache')
@Controller('cache')
@SkipThrottle()
@UseGuards(AuthenticationGuard)
export class CacheController {
  constructor(private readonly cacheService: CacheService) {}

  @Delete()
  async removeCache(@Body() removeCacheDto: RemoveCacheDto) {
    return this.cacheService.removeCache(removeCacheDto);
  }
}

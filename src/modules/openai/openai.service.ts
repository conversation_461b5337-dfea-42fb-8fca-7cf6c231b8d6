import { Injectable, Logger } from '@nestjs/common';
import { JOBS_INDEX, OpenAIConfig } from 'src/configs/configs.constants';
import { OpenAI } from 'openai';
import { OPENAI_MAX_TOKENS, OPENAI_MODEL, OPENAI_MODEL_4, OPENAI_NUMBER_OF_REPOSNE } from './openai.constant';
import { GenerateJobSkillsDto, ParaphraseJobSkillsDto, PromptDto, SuggestionType, PromptJobTypeEnum } from './dto/prompt.dto';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { CacheService } from '../cache/cache.service';
import { getCacheConclusion, getCacheIntroduction } from './cache';
import { JobService } from '../jobs/service/job.service';
import { JobBoardsRepository } from '../jobs/repository/job-boards.repository';
import { OpensearchService } from '../opensearch/service/opensearch.service';

@Injectable()
export class OpenAIService extends BaseAbstractService {
  private readonly logger = new Logger(OpenAIService.name);

  private openai: OpenAI;

  constructor(
    readonly i18nService: I18nService,
    private readonly cacheService: CacheService,
    private readonly jobService: JobService,
    private readonly jobRepository: JobBoardsRepository,
    private readonly opensearchService: OpensearchService
  ) {
    super(i18nService);

    this.openai = new OpenAI({ apiKey: OpenAIConfig.apiKey });
  }

  private formatChoice(choice: any): string[] {
    return choice?.message.content.split('\n').map((line: string) => line.replace('-', '').trim()) || [];
  }

  private getJobType(type: string, location: string): string {
    if (
      type?.toLowerCase().includes(PromptJobTypeEnum.REMOTE)
      || location?.toLowerCase().includes(PromptJobTypeEnum.REMOTE)
    ) {
      return PromptJobTypeEnum.REMOTE;
    }

    return PromptJobTypeEnum.ONSITE;
  }

  private formatContent(generatedFreeContent: string, fullName): string {
    return generatedFreeContent
      .concat(
        ` <div>
            <p style="color:blue"> <strong>${fullName}</strong></p>
          </div>`
      )
      .replace(/\n/g, '')
      .trim();
  }

  async generateKeyPointFromJobDescription(jobDescription: string): Promise<{ [key: string]: string[] }> {
    const prompt = `Given the job description provided, please generate 8 bullet points highlighting 
      my key strengths and qualifications for applying to this company. 
      Note: max word of each point is 10 words. Job description: ${jobDescription}`;

    const completion = await this.openai.chat.completions.create({
      messages: [{ role: 'system', content: prompt }],
      model: OPENAI_MODEL,
      n: OPENAI_NUMBER_OF_REPOSNE,
      max_tokens: OPENAI_MAX_TOKENS,
    });

    const result = {
      firstChoice: this.formatChoice(completion.choices[0]),
      secondChoice: this.formatChoice(completion.choices[1]),
      thirdChoice: this.formatChoice(completion.choices[2]),
    };

    return result;
  }

  async generatedSubject(jobTitle) {
    const prompt = `I need the email subject. The subject needs to come from resource provider to recruiter or companies who are recruiting so we have candidates to to fill this vacancy that the company has posted on the job platform and related to the field: ${jobTitle}. It should be professional, succinct, attractive but still humble.
    The field's description may contain role. You have to analyze the field's description and do not focus on roles. Let focus on the position
    NOTE: raw subject without the word subject at the beginning  and double quotes or special character`;

    const completion = await this.openai.chat.completions.create({
      messages: [{ role: 'system', content: prompt }],
      model: OPENAI_MODEL,
      n: OPENAI_NUMBER_OF_REPOSNE,
      max_tokens: OPENAI_MAX_TOKENS,
    });

    return this.formatChoice(completion.choices[0])[0];
  }

  async generateKeyPointFromUnfinishedContent(
    jobDescription: string,
    unfinishedContent: string,
    location: string,
    jobTitle: string
  ) {
    const startedEmail = this.getJobType('', location) === PromptJobTypeEnum.REMOTE
      ? `Should be an introduction. It will tell the recruiter that "I am currently collaborating with a highly skilled ${jobTitle} who is eager to explore new opportunities.
     Candidate has expressed a preference for a remote work setup, and I am actively searching for a position that aligns with the candidate expertise and the flexibility of working remotely". Paraphrase a sentence`
      : `Should be an introduction. It will tell the recruiter that "I am currently working with a ${jobTitle} who is located around 30 minutes away from your office in ${location} and has asked me to find a suitable position which is local". Paraphrase a sentence`;

    const prompt = `I have a template:\n
      [Start of Template]
      {<div>
        <p>Hi $first name$,</p>
        <p>$startedEmail</p>

        <p><strong>A few main points on this candidate's skill set:</strong></p>
        <ul>
            $keyStrengthsList
        </ul>
      </div>}
      \n
      [End of Template]
      \n
      Here is job description:\n
      ${jobDescription}
      \n\n
      Here is unfinished content:\n
      ${unfinishedContent}
      \n\n
      Here are requirements:\n
      $keyStrengthsList: must be 8 bullet points highlighting my key strengths and qualifications for applying to this company, and make it more professional than the unfinished content.
      Note: The maximum word count for each point is 10 words and ignore if the unfinished content is empty. \n
      $startedEmail: ${startedEmail}\n
      Replace $keyStrengthsList and $startedEmail with generated results, only return the fulfilled template with out {}`;

    const completion = await this.openai.chat.completions.create({
      messages: [{ role: 'system', content: prompt }],
      model: OPENAI_MODEL,
      n: OPENAI_NUMBER_OF_REPOSNE,
      max_tokens: OPENAI_MAX_TOKENS,
    });

    return completion?.choices?.[0]?.message?.content;
  }

  async generateFreeSubject(jobInfo) {
    const prompt = `Re: I need the email subject. The subject needs to come from resource provider to recruiter or companies who are recruiting for ${jobInfo.type} job so we have candidates to to fill this vacancy that the company has posted on the job platform.\n
      Here is job description: ${jobInfo.description}.\n
      It should be professional, succinct, attractive but still humble.\n
      NOTE: raw subject without the word subject at the beginning  and double quotes or special character. No Yapping`;

    const completion = await this.openai.chat.completions.create({
      messages: [{ role: 'system', content: prompt }],
      model: OPENAI_MODEL,
      n: 3,
      max_tokens: OPENAI_MAX_TOKENS,
    });

    return completion.choices.map((choice, index) =>
      index === 0 ? `${choice?.message?.content}` : `Re: ${choice?.message?.content}`
    );
  }

  async generateFreeContent(generateEmailInfo, userName) {
    const { description, type, location, position, unfinishedContent } = generateEmailInfo;
    const startedEmail = this.getJobType(type, location) === PromptJobTypeEnum.ONSITE
        ? `Should be an introduction. Rely on the description: ${description}, It will tell the recruiter that "I am currently working with a ${position} who is located around 30 minutes away from your office in ${location} and has asked me to find a suitable position which is local".`
        : `Should be an introduction. Rely on the description: ${description}, It will tell the recruiter that "I am currently collaborating with a highly skilled in this field (as the description) who is eager to explore new opportunities.
     Candidate has expressed a preference for a remote work setup, and I am actively searching for a position that aligns with the candidate expertise and the flexibility of working remotely`;

    const prompt = `I need to write an email follow this template included html tag. \n
    This email needs to come from resource provider to recruiter or companies who are recruiting so we have candidates to to fill this vacancy that the company has posted on the job platform and related to the field.
    I have a template:\n
      [Start of Template]
      {<div>
        <p>Hey $first name$,</p>
        <p>$startedEmail</p>

        <p>
            $invitation
        </p>

        <p>Best</p>
        <p>{{SENDER_NAME}}</p>
      </div>}
      \n
      [End of Template]
      \n
      Here is job description:\n
      ${description}
      \n\n
      Here is unfinished content:\n
      ${unfinishedContent}
      \n\n
      Here are requirements:\n
      $invitation: generate a short invitation for the position: ${position}, location: ${location}, type of work (remote, office...): ${type}\n
      $startedEmail: ${startedEmail}\n$
      $activeLocation: generate this part which tell the recruiter about candidate is actively interviewing in ${location} and extremely suitable for ${position} position which they looking for. express by your own way\n
      $arrangement: : generate this part which politely tell the recruiter that you are so happy if they can arrange time to discuss about this candidate if possible. Use formal and professional intonation\n
      Replace $keyStrengthsList, $startedEmail, $activeLocation, $arrangement with generated results, only return the fulfilled template with out {} and do not include "\n" character to my html email
      No yapping!`;

    const completion = await this.openai.chat.completions.create({
      messages: [{ role: 'system', content: prompt }],
      model: OPENAI_MODEL,
      n: 3,
      max_tokens: OPENAI_MAX_TOKENS,
    });

    return completion.choices.map((choice) => this.formatContent(choice?.message?.content, userName));
  }

  async generateGeneralContent({ messages, numOfChoice }: PromptDto) {
    if (!numOfChoice || numOfChoice === 0) {
      return {};
    }

    return this.openai.chat.completions.create({
      messages,
      model: OPENAI_MODEL,
      n: numOfChoice,
      max_tokens: OPENAI_MAX_TOKENS,
    });
  }

  async generateSkillByDescription(jobDescription: string) {
    const prompt = `Given the job description provided, please generate 8 concise bullet points highlighting my key strengths and qualifications for applying to this company. Each point should be no more than 10 words.
Do not use list symbols or numbers in the response. Job Description: ${jobDescription}`;

    const completion = await this.openai.chat.completions.create({
      messages: [{ role: 'system', content: prompt }],
      model: OPENAI_MODEL,
      n: 1,
      max_tokens: OPENAI_MAX_TOKENS,
    });

    return this.formatChoice(completion.choices[0]);
  }

  async generateJobSkills({ jobId, regenerate = false }: GenerateJobSkillsDto) {
    const jobDetail = await this.opensearchService.getById(JOBS_INDEX, jobId);
    if (!jobDetail) {
      return this.formatOutputData({ key: 'GENERATE_JOB_SKILLS' }, { data: [] });
    }

    if (!jobDetail.skills || regenerate) {
      jobDetail.skills = await this.generateSkillByDescription(jobDetail.description);

      await this.opensearchService.updateDocument(JOBS_INDEX, jobId, {
        skills: jobDetail.skills,
      });
    }

    return this.formatOutputData({ key: 'GENERATE_JOB_SKILLS' }, { data: jobDetail.skills });
  }

  async generateVarietyOfSkills({ skills, numOfChoice }: { skills: string[]; numOfChoice: number }) {
    if (!numOfChoice || numOfChoice === 0) {
      return [];
    }
    const prompt = `Please rephrase the following bulleted list to highlight my key strengths and qualifications for applying to this company.
    Each point should remain concise and no more than 10 words.
    Original Bullet Points: ${skills.join('\n')}
    Noted: The result only contain text, no ordered characters, no yapping only bulleted list!
`;

    const completions = await this.openai.chat.completions.create({
      messages: [{ role: 'system', content: prompt }],
      model: OPENAI_MODEL,
      n: numOfChoice,
      max_tokens: OPENAI_MAX_TOKENS,
    });

    return completions.choices.map((choice) => this.formatChoice(choice));
  }

  async paraphraseJobSkills({ skills, jobtype, location, numOfChoice = 1 }: ParaphraseJobSkillsDto) {
    if (!numOfChoice || numOfChoice === 0) {
      return this.formatOutputData({ key: 'PARAPHRASE_JOB_SKILLS' }, { data: [] });
    }

    const skillLists = skills?.length
      ? await this.generateVarietyOfSkills({
        skills,
        numOfChoice,
      })
      : Array(numOfChoice).fill(['{job_required_skills}']);

    const results = skillLists
      .filter((skill) => skill)
      .map((skillList) => ({
        skills: skillList.filter((skill) => skill),
        introduction: getCacheIntroduction(jobtype, location),
        conclusion: getCacheConclusion(jobtype, location),
      }));

    return this.formatOutputData({ key: 'GENERATE_CHOICE_SKILLS' }, { data: results });
  }

  async generateSuggestions(query: string, type: SuggestionType) {
    try {
      if (!query) {
        return this.formatOutputData({ key: 'GET_SUGGESTIONS' }, { data: [] });
      }

      const cachedKey = `suggestions.${type}.${query}`;

      const standardResponseJSON: string = await this.cacheService.get(cachedKey);
      if (standardResponseJSON) {
        return this.formatOutputData({ key: 'GET_SUGGESTIONS' }, { data: JSON.parse(standardResponseJSON) });
      }

      const contentQuery = {
        keywords: `Please list 15 keywords related to ${query} keyword. I don't need description just keywords. \nNote: response must be in array of string type.\nNote: response must be in array of string type.`,
        title: `I'm searching job board for ${query} jobs which is the best keywords to use. \nNote: response must be in array of string type.`,
      };

      const { choices } = await this.openai.chat.completions.create({
        messages: [{ role: 'system', content: contentQuery[type] }],
        model: OPENAI_MODEL,
        n: 1,
        max_tokens: OPENAI_MAX_TOKENS,
      });

      if (choices?.length === 0) {
        return this.formatOutputData({ key: 'GET_SUGGESTIONS' }, { data: [] });
      }

      const processedResult = JSON.parse(choices[0]?.message?.content || '');

      const result = processedResult?.map((item: string) => ({
        id: item,
        displayName: item,
      }));

      await this.cacheService.set(cachedKey, JSON.stringify(result), 60 * 60 * 168);

      return this.formatOutputData({ key: 'GET_SUGGESTIONS' }, { data: result });
    } catch (error) {
      this.logger.error(error);
      return this.throwCommonMessage('GET_SUGGESTIONS', error);
    }
  }

  async isCompanyRecruiter(companies: string[]) {
    const prompt = `Please tell me which of the following are recruiters and which are not in Yes or No answer: ${companies.join(
      ', '
    )}`;
    const completion = await this.openai.chat.completions.create({
      messages: [{ role: 'system', content: prompt }],
      model: OPENAI_MODEL_4,
      n: 1,
      max_tokens: OPENAI_MAX_TOKENS,
    });
    const isYesNo = /(Yes|No)$/i;
    const isYes = /(Yes)$/i;
    const answers = (
      completion.choices[0]?.message?.content ? completion.choices[0].message.content.split(/\,|\n/) : []
    ).filter((answer) => isYesNo.test(answer));
    if (answers.length !== companies.length) {
      return [];
    }

    return companies.filter((company, i) => isYes.test(answers[i]));
  }
}

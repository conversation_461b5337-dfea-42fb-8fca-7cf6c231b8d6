import { OpenAIService } from './openai.service';
import {
  Body, Controller, Get, Post, Query, UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse, ApiNotFoundResponse, ApiTags, ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { ResponseMessage } from 'src/common/constants/common.constant';
import { BaseErrorResponseDto } from 'src/common/dto/common.dto';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import {
  GenerateJobSkillsDto, ParaphraseJobSkillsDto, PromptDto, SuggestionType,
} from './dto/prompt.dto';

@ApiTags('OpenAI')
@Controller('open-ai')
@SkipThrottle()
@ApiBadRequestResponse({
  description: ResponseMessage.Common.BAD_REQUEST,
  type: BaseErrorResponseDto,
})
@ApiNotFoundResponse({
  description: ResponseMessage.Common.NOT_FOUND,
  type: BaseErrorResponseDto,
})
@ApiUnauthorizedResponse({
  description: ResponseMessage.Common.UNAUTHORIZED,
  type: BaseErrorResponseDto,
})
@UseGuards(AuthenticationGuard, PermissionGuard)
export class OpenAIController {
  constructor(private readonly openAIService: OpenAIService) {}

  @Post('generate')
  @Permission(PermissionResource[ResourceEnum.SETTINGS].Read)
  async generateChoices(@Body() generateRequestDto: PromptDto) {
    return this.openAIService.generateGeneralContent(generateRequestDto);
  }

  @Post('generate-job-skills')
  @Permission(PermissionResource[ResourceEnum.SETTINGS].Read)
  async generateJobSkills(@Body() generateRequestDto: GenerateJobSkillsDto) {
    return this.openAIService.generateJobSkills(generateRequestDto);
  }

  @Post('paraphrase-job-skills')
  @Permission(PermissionResource[ResourceEnum.SETTINGS].Read)
  async paraphraseJobSkills(@Body() generateRequestDto: ParaphraseJobSkillsDto) {
    return this.openAIService.paraphraseJobSkills(generateRequestDto);
  }

  @Get('suggestion/keywords')
  @Permission(PermissionResource[ResourceEnum.SETTINGS].Read)
  async generateKeywordsSuggestion(@Query('query') query: string) {
    return this.openAIService.generateSuggestions(query, SuggestionType.KEYWORD);
  }

  @Get('suggestion/title')
  @Permission(PermissionResource[ResourceEnum.SETTINGS].Read)
  async generateTitleSuggestion(@Query('query') query: string) {
    return this.openAIService.generateSuggestions(query, SuggestionType.TITLE);
  }
}

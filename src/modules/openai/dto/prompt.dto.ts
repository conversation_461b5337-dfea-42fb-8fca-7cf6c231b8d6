import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsOptional, ValidateNested, IsArray, ArrayNotEmpty } from 'class-validator';

export enum PromptRoleEnum {
  USER = 'user',
  SYSTEM = 'system',
  assistant = 'assistant',
}

export enum SuggestionType {
  KEYWORD = 'keywords',
  TITLE = 'title',
}

export enum PromptJobTypeEnum {
  REMOTE = 'remote',
  ONSITE = 'onsite',
}

export class MessageDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(PromptRoleEnum)
  role: PromptRoleEnum;

  @ApiProperty()
  @IsNotEmpty()
  content: string;
}

export class PromptDto {
  @ApiProperty({ type: [MessageDto] })
  @Type(() => MessageDto)
  @ValidateNested({ each: true })
  messages: MessageDto[];

  @ApiPropertyOptional()
  @IsOptional()
  numOfChoice?: number;
}

export class GenerateJobSkillsDto {
  @ApiProperty()
  @IsNotEmpty()
  jobId: string;

  @ApiPropertyOptional()
  @IsOptional()
  regenerate?: boolean;
}

export class ParaphraseJobSkillsDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  skills?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  jobtype?: string;

  @ApiPropertyOptional()
  @IsOptional()
  location?: string;

  @ApiPropertyOptional()
  @IsOptional()
  numOfChoice?: number;
}
import { <PERSON>du<PERSON> } from '@nestjs/common';
import { OpenAIService } from './openai.service';
import { OpenAIController } from './openai.controller';
import { MyCacheModule } from '../cache/cache.module';
import { JobsModule } from '../jobs/jobs.module';
import { JobBoardsRepository } from '../jobs/repository/job-boards.repository';
import { OpensearchService } from '../opensearch/service/opensearch.service';

@Module({
  imports: [MyCacheModule, JobsModule],
  controllers: [OpenAIController],
  providers: [
    OpenAIService,
    JobBoardsRepository,
    OpensearchService,
  ],
  exports: [OpenAIService],
})
export class OpenaiModule {}

/* eslint-disable camelcase */
import { Promise as BBPromise } from 'bluebird';
import {
  Injectable,
  Logger,
} from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import * as sgMail from '@sendgrid/mail';
import * as sgMailClient from '@sendgrid/client';
import {
  DataSource,
  In,
  Raw,
} from 'typeorm';
import <PERSON><PERSON><PERSON>, { MessageFields } from 'nylas';
import axios from 'axios';

import * as nylasClient from '../jobs/utils/nylas.util';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import {
  APP_CONFIG,
  NYLAS_CONFIG,
  SendGridConfig,
  BullHornConfig,
  JOBS_INDEX,
} from 'src/configs/configs.constants';
import { SequenceRepository } from 'src/modules/mail/repositories/sequence.repostiory';
import {
  SequenceStepEntity,
  SequenceStepStatus,
  SequenceStepType,
} from 'src/modules/mail/entities/sequence-step.entity';
import { SequenceEntity } from 'src/modules/mail/entities/sequence.entity';
import { SequenceActivityLogEntity, SequenceActivityType } from 'src/modules/mail/entities/sequence-activity-log.entity';
import { SequenceActivityLogRepository } from 'src/modules/mail/repositories/sequence-activity-log.repository';
import { SequenceInstanceRepository } from 'src/modules/mail/repositories/sequence-instance.repository';
import { SequenceStepTaskRepository } from 'src/modules/mail/repositories/sequence-step-task.repository';
import { SequenceStepRepository } from 'src/modules/mail/repositories/sequence-step.repostiory';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';
import { BullHornService } from 'src/middlewares/bullhorn/bullhorn.service';
import { chunkArray, getMergeTagsContent, getValueByPath, processingContent, removeDuplicatesByKey, safeParseJSON } from 'src/common/utils/helpers.util';
import { SequenceStepTaskEntity } from '../mail/entities/sequence-step-task.entity';
import { SequenceInstanceEntity } from '../mail/entities/sequence-instance.entity';
import { ContactRepository } from '../user/repositories/contact.repository';
import { getJobVacancy, queryClientContacts, queryGetListShortList, sendNoteToBullhorn } from '../jobs/utils/bullhorn-service.util';
import { MailInfo, RecipientInfo } from '../mail/dto/send-email.dto';
import { JobLeadsRepository } from '../jobs/repository/job-leads.repository';
import { JOB_BOARDS } from '../jobs/constants/job.const';
import { JobBoardsRepository } from '../jobs/repository/job-boards.repository';
import { OpensearchService } from '../opensearch/service/opensearch.service';
import { UserSignatureRepository } from '../user/repositories/user-signature.repository';
import { UserEntity } from '../user/entities/user.entity';
import { CoreJobLead } from '../jobs/dto/job-lead/job-lead.dto';
import { UserSignature } from '../user/entities/user-signature.entity';
import { cloneDeep, set } from 'lodash';
import { MERGE_TAGS_PLACE_HOLDERS, MergeTagMappingEnum } from '../mail/dto/send-email-process.dto';
import { UserSignatureServices } from '../user/user-signature.service';
import { FileUploadService } from '../files-upload/file-upload.service';
import { WebhookService } from '../webhook/services/webhook.service';
import { WebHookNameEnum } from '../webhook/enums/webhook.enum';

@Injectable()
export class SharedMailService extends BaseAbstractService {
  private appEnv: string;
  private readonly logger = new Logger(SharedMailService.name);
  public readonly nylas = new Nylas({
    apiKey: NYLAS_CONFIG.apiKey,
    apiUri: NYLAS_CONFIG.apiUri, // "https://api.us.nylas.com" or "https://api.eu.nylas.com"
    timeout: 10, // seconds
  });
  readonly DELAY_UNIT_MULTIPLE = {
    DAY: 24 * 60 * 60 * 1000,
    HOUR: 60 * 60 * 1000,
  };

  constructor(
    // DONT USE OTHER SERVICES HERE; ONLY ALLOWS REPOSITORY
    readonly i18nService: I18nService,
    private readonly dataSource: DataSource,
    private readonly bullhornService: BullHornService,
    private readonly opensearchService: OpensearchService,
    private readonly userSignatureServices: UserSignatureServices,
    private readonly fileUploadService: FileUploadService,
    private readonly contactRepository: ContactRepository,
    private readonly jobLeadRepository: JobLeadsRepository,
    private readonly sequenceRepository: SequenceRepository,
    private readonly sequenceStepRepository: SequenceStepRepository,
    private readonly sequenceInstanceRepository: SequenceInstanceRepository,
    private readonly sequenceStepTaskRepository: SequenceStepTaskRepository,
    private readonly sequenceActivityLogRepository: SequenceActivityLogRepository,
    private readonly jobBoardRepository: JobBoardsRepository,
    private readonly userSignatureRepository: UserSignatureRepository,
    private readonly webhookService: WebhookService,

  ) {
    super(i18nService);
    sgMail.setApiKey(SendGridConfig.apiKey);
    sgMailClient.setApiKey(SendGridConfig.apiKeyEmailValid);
    this.appEnv = process.env.APP_ENV;

  }

  public async getBhToken(organizationId) {
    // get access token which use to call to bullhorn
    const org = await this.dataSource
      .createQueryBuilder(OrganizationEntity, 'o')
      .where({ id: organizationId })
      .getOne();

    let { access_token, expires_at, refresh_token, bhRestToken, corporateRestUrl } = org?.bhToken ?? {};

    if (!expires_at || expires_at < Date.now() - 60 * 1000) {
      const { bhClientId, bhUsername, bhPassword, bhClientSecret } = org ?? {
        bhClientId: BullHornConfig.clientId,
        bhUsername: BullHornConfig.username,
        bhPassword: BullHornConfig.password,
        bhClientSecret: BullHornConfig.clientSecret,
      };

      if (!bhClientId || !bhUsername || !bhPassword || !bhClientSecret) {
        return null;
      }

      const { oauthUrl, restUrl } = await this.bullhornService.getDataCenter(bhUsername);
      const {
        access_token: accessToken,
        expires_in,
        refresh_token: refreshToken,
      } = await this.bullhornService.getAccessTokenFromScratch(refresh_token, {
        bhClientId,
        bhUsername,
        bhPassword,
        bhClientSecret,
        rootOauthUrl: oauthUrl,
      });

      access_token = accessToken;
      expires_at = Date.now() + expires_in * 1000 - 60 * 1000;
      refresh_token = refreshToken;

      const { BhRestToken: newBhRestToken, restUrl: newCorporateRestUrl } =
        await this.bullhornService.getBhRestTokenAndCorporateRestEndpoint(accessToken, restUrl);

      bhRestToken = newBhRestToken;
      corporateRestUrl = newCorporateRestUrl;

      const bhToken = {
        access_token,
        expires_at,
        refresh_token,
        bhRestToken,
        corporateRestUrl,
      };

      if (org) {
        await this.dataSource.getRepository(OrganizationEntity).update(org.id, {
          bhToken,
        });
      }

      return { ...bhToken, organizationId: organizationId ?? 0 };
    }

    return { ...org.bhToken, organizationId: organizationId ?? 0 };
  }

  async updateCorrespondingField({ field, mail, emailSeq, eventTime, recipient, recipientSend, url, reason }) {
    const mappingEvents = {
      open: 'opened',
      click: 'link_clicked',
    };
    const mappingFields = {
      open: 'opened',
      click: 'linkClicked',
      bounce: 'bounced',
    };
    const mappingRawFields = {
      open: 'opened',
      click: 'link_clicked',
      bounce: 'bounced',
    };

    const eventEvent = mappingEvents[field] || field;
    const eventField = mappingFields[field] || field;
    const dbRawField = mappingRawFields[field] || field;
    const activityLogEntity = this.sequenceActivityLogRepository.create({
      //TODO: to check if this includes null as type
      type: eventEvent,
      sequence: { id: mail?.sequence.id },
      occurredAt: new Date().toISOString(),
      sequenceStep: { id: mail.sequenceStep?.id },
      content: {
        email: { content: recipient },
        urlClick: url,
        contact: recipientSend,
        sequenceInstance: mail.id,
        recipient,
        ...(reason && { reason }),
      },
    });

    await Promise.all([
      this.sequenceRepository
        .createQueryBuilder()
        .update(SequenceEntity)
        .set({ [eventField]: () => `"${dbRawField}" + 1` })
        .where('id = :id', { id: mail?.sequence.id })
        .execute(),

      this.sequenceActivityLogRepository.insert(activityLogEntity),
    ]);
  }

  async findMailByRawId(rawId) {
    if (!rawId) {
      return null;
    }
    const regex = /<([^@>]+)@/;
    const messageId = rawId.match(regex)?.[1] || rawId;
    try {
      const q = this.sequenceInstanceRepository.createQueryBuilder()
        .where('sent_ids @> :sentId', { sentId: JSON.stringify([messageId]) })
        .andWhere('status = :status', { status: SequenceStepStatus.SENT })
        .select([
          'id',
          'sequence_id',
          'sequence_step_id',
          'user_id',
        ]);

      const rawInstance = await q.getRawOne();

      if (!rawInstance) {
        return null;
      }

      const [instance, sequence, sequenceStep, user] = await Promise.all([
        this.sequenceInstanceRepository.findOne({ where: { id: rawInstance.id }, select: { sentIds: false }}),
        this.sequenceRepository.findOne({ where: { id: rawInstance.sequence_id }}),
        this.sequenceStepRepository.findOne({ where: { id: rawInstance.sequence_step_id }}),
        this.dataSource.createQueryBuilder(UserEntity, 'u').where({ id: rawInstance.user_id }).getOne(),
      ]);
      instance.sequence = sequence;
      instance.sequenceStep = sequenceStep;
      instance.user = user;

      return instance;
    } catch (error) {
      console.log(`[ERROR] Find instance by messageId ${messageId}: `, error);

      return null;
    }
  }

  async findRecipientByEmail(instanceId: string, email: string) {
    try {
      const result = await this.sequenceStepTaskRepository.findOne({
        where: {
          recipients: Raw((alias) => `${alias} @> :email`, {
            email: JSON.stringify([{ email }]),
          }),
          sequenceInstance: {
            id: instanceId,
          }
        },
      });

      return result?.recipients?.[0];
    } catch (error) {
      console.log(`[ERROR] Find recipient ${email}: `, error);

      return null;
    }
  }

  async isLastSequenceStep(sequenceId: string, sequenceStepId: string) {
    // Create the subquery to get the maximum stepIndex for the sequence
    const subQuery = this.sequenceStepRepository
      .createQueryBuilder('innerStep')
      .select('MAX(innerStep.step_index)', 'maxStepIndex')
      .where('innerStep.sequence_id = :sequenceId', { sequenceId })
      .andWhere('innerStep.type NOT IN (:...type)', { type: [SequenceStepType.TASK] })
      .getQuery();

    // Use the subquery in the main query's WHERE clause
    const result = await this.sequenceStepRepository
      .createQueryBuilder('step')
      .select('step.id')
      .where('step.id = :sequenceStepId', { sequenceStepId })
      .andWhere('step.sequence_id = :sequenceId', { sequenceId })
      .andWhere(`step.step_index = (${subQuery})`, { sequenceId, type: [SequenceStepType.TASK] })
      .getOne();

    return !!result;
  }

  async getSequenceAncestorsIds(id: string, onlyRoot = false) {
    const results = await this.dataSource.query(`
      WITH RECURSIVE ancestors AS (
          -- Start with the given child ID
          SELECT
              id AS child_id,
              parent_id AS ancestor_id
          FROM sequences
          WHERE id = $1

          UNION ALL

          -- Recursively find ancestors
          SELECT
              a.child_id,
              s.parent_id AS ancestor_id
          FROM ancestors a
          JOIN sequences s ON s.id::text = a.ancestor_id::text
      )
      SELECT ancestor_id
      FROM ancestors
      WHERE ancestor_id IS NOT NULL;
    `, [id]);

    const ids = results?.map((item) => item.ancestor_id) || [];

    return onlyRoot ? ids.at(-1) : ids;
  }

  async sendEmailSequenceStop(user: any, sequence: any, isCompleted: boolean = false) {
    const name = user.fullName || user.username;
    const status = isCompleted ? 'completed' : 'stopped';
    const content = isCompleted ? 'Your sequence has now completed. Please click the link to view the Completed Sequence.' : 'Your sequence has now stopped. Please click the link to view the Stopped Sequence.';

    const msg = {
      to: user.email,
      from: {
        name: 'Zileo',
        email: SendGridConfig.fromEmail,
      },
      subject: `Sequence Updated: Your sequence ${sequence.name} is ${status}`,
      html: `<!doctypehtml><meta charset=utf-8><meta content="ie=edge"http-equiv=x-ua-compatible><title>Your sequence is ${status}</title><meta content="width=device-width,initial-scale=1"name=viewport><style>@media screen{@font-face{font-family:'Source Sans Pro';font-style:normal;font-weight:400;src:local('Source Sans Pro Regular'),local('SourceSansPro-Regular'),url(https://fonts.gstatic.com/s/sourcesanspro/v10/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff')}@font-face{font-family:'Source Sans Pro';font-style:normal;font-weight:700;src:local('Source Sans Pro Bold'),local('SourceSansPro-Bold'),url(https://fonts.gstatic.com/s/sourcesanspro/v10/toadOcfmlt9b38dHJxOBGFkQc6VGVFSmCnC_l7QZG60.woff) format('woff')}}a,body,table,td{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}table,td{mso-table-rspace:0;mso-table-lspace:0}img{-ms-interpolation-mode:bicubic}a[x-apple-data-detectors]{font-family:inherit!important;font-size:inherit!important;font-weight:inherit!important;line-height:inherit!important;color:inherit!important;text-decoration:none!important}div[style*="margin: 16px 0;"]{margin:0!important}body{width:100%!important;height:100%!important;padding:0!important;margin:0!important}table{border-collapse:collapse!important}a{color:#1a82e2}img{height:auto;line-height:100%;text-decoration:none;border:0;outline:0}</style><body style=background-color:#e9ecef><div class=preheader style=display:none;max-width:0;max-height:0;overflow:hidden;font-size:1px;line-height:1px;color:#fff;opacity:0>Your sequence ${sequence.name} is ${status}.</div><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td align=center bgcolor=#e9ecef><!--[if (gte mso 9)|(IE)]><table border=0 cellpadding=0 cellspacing=0 width=600 align=center><tr><td align=center valign=top width=600><![endif]--><table border=0 cellpadding=0 cellspacing=0 width=100% style=max-width:600px><tr><td align=left bgcolor=#ffffff style="padding:36px 24px 0;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;border-top:3px solid #d4dadf"><h1 style=margin:0;font-size:32px;font-weight:700;letter-spacing:-1px;line-height:48px>Your sequence is ${status}</h1></table><!--[if (gte mso 9)|(IE)]><![endif]--><tr><td align=center bgcolor=#e9ecef><!--[if (gte mso 9)|(IE)]><table border=0 cellpadding=0 cellspacing=0 width=600 align=center><tr><td align=center valign=top width=600><![endif]--><table border=0 cellpadding=0 cellspacing=0 width=100% style=max-width:600px><tr><td align=left bgcolor=#ffffff style="padding:24px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;line-height:24px"><p style=margin:0>Hi ${name},</p><br><p style=margin:0>${content}</p><tr><td align=left bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td align=center bgcolor=#ffffff style=padding:12px><table border=0 cellpadding=0 cellspacing=0><tr><td align=center bgcolor=#1a82e2 style=border-radius:6px><a clicktracking="off" href="${APP_CONFIG.CLIENT_URL}/sequence?seqId=${sequence.id}" target=_blank style="display:inline-block;padding:16px 36px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;color:#fff;text-decoration:none;border-radius:6px">View Sequence</a></table></table><tr><td align=left bgcolor=#ffffff style="padding:24px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;line-height:24px;border-bottom:3px solid #d4dadf"><p style=margin:0>Best regards,</p><p style=margin:0>Zileo Team</p></table><!--[if (gte mso 9)|(IE)]><![endif]--></table>`,
    };

    await sgMail.send(msg).catch((warn) => {
      console.warn('Error in sending email sequence stop', warn?.response?.body?.errors);
    });


    const jobBoardId = sequence?.jobBoardId || '';
    if (jobBoardId) {
      this.webhookService.triggerWebhookByName(
        isCompleted ? WebHookNameEnum['sequence.completed'] : WebHookNameEnum['sequence.stopped'],
        {
          jobBoardId,
          type: isCompleted ? 'COMPLETED': 'STOPPED'
        }
      );
    }
  }


  async findContactFromContactList(contactListIds: string|string[]) {
    const ids = Array.isArray(contactListIds) ? contactListIds : [contactListIds];
    const dataContacts = await this.contactRepository.find({ where: { contactList: { id: In(ids) } } });

    return dataContacts;
  }

  async findHotListContact(tearSheetIds: string[]|number[], user: any = {}, email = '') {
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(user?.organizationId)) || {};
    const entityName = 'ClientContact';

    return (
      await BBPromise.all(
        tearSheetIds.map(async (tearSheetId) => {
          let data = await queryClientContacts(
            {
              entityName,
              tearSheetId,
              start: '0',
              query: '',
              email,
              count: '10000',
            },
            entityName,
            bhRestToken,
            corporateRestUrl
          );

          if (!data?.length) {
            const entity = 'Candidate';
            data = await queryClientContacts(
              {
                entityName: entity,
                tearSheetId,
                start: '0',
                query: '',
                email,
                count: '10000',
              },
              entity,
              bhRestToken,
              corporateRestUrl
            );
          }

          return data;
        }),
        { concurrency: 10 }
      )
    ).flat();
  }

  async findShortListCandidate(shortListIds: string[] | number[], user: any = {}) {
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(user?.organizationId)) || {};

    return (
        await BBPromise.all(
            shortListIds.map(async (shortListId) => {
                const response = await queryGetListShortList(
                    bhRestToken,
                    corporateRestUrl,
                    0,
                    shortListId,
                    null
                );

                return response?.data?.flatMap((item) =>
                    Array.isArray(item.candidate) ? item.candidate : [item.candidate]
                );
            }),
            { concurrency: 10 },
        )
    )
        .flat()
        .filter(Boolean);
  }

  async bulkUpsertTasks(tasks: any[]) {
    const chunkRecords = chunkArray(tasks, 1000);

    await BBPromise.all(
      chunkRecords.map(async (items) => {
        await this.dataSource
          .createQueryBuilder()
          .insert()
          .into(SequenceStepTaskEntity)
          .values(items)
          .orUpdate(
            ['updated_at', 'extra_information'],
            ['id'],
          )
          .execute();

        return true;
      }),
      { concurrency: 1 },
    );
  }

  async createSequenceTasks(instance: SequenceInstanceEntity) {
    const { recipients, hotlistIds, contactListIds } = instance;
    const clientContacts: RecipientInfo[] = recipients.filter(({ id }) => id);

    if (!clientContacts.length) {
      return {
        id: instance.id,
        emailSeq: instance?.sequence,
        seqStep: instance?.sequenceStep,
        reason: "Don't have any client contact to create task",
        type: SequenceStepType.TASK,
      };
    }

    return {
      sentIds: [],
      id: instance.id,
      emailSeq: instance?.sequence,
      seqStep: instance?.sequenceStep,
      type: SequenceStepType.TASK,
    };
  }

  async updateFollowUpEmails(data: any, userId: string, seqId: string, instanceVersion?: number) {
    await this.sequenceInstanceRepository.update(
      {
        user: { id: userId },
        sequence: { id: seqId },
        status: SequenceStepStatus.PENDING,
        ...(typeof instanceVersion === 'number' ? { version: instanceVersion } : {}),
      },
      data
    );
  }

  async getJobFromSequence(sequence: SequenceEntity): Promise<{
    jobtitle: string;
    description: string;
    jobtype: string;
    company: string;
    logoCompany: string;
    salary: string | number;
    posted: Date;
    joblocationcity: string;
    source: string;
    address_city: string;
    address_country: string;
    address_line_1: string;
    address_line_2: string;
    employment_type: string;
    companyBhId?: string
  }> {
    const { jobBoardId, externalJobId } = sequence;
    // Get original jobs
    if (jobBoardId && !externalJobId) {
      const jobBoardFromOpenSearch = this.opensearchService.getById(JOBS_INDEX, jobBoardId);
      const jobBoardFromDb = this.jobBoardRepository.findOneBy({ job_id: jobBoardId });
      const data = await Promise.all([jobBoardFromOpenSearch, jobBoardFromDb]);

      let osJob: any;
      let dbJob: any;
      if (data[0]) {
        osJob = {
          jobtitle: data[0].jobtitle,
          description: data[0].description,
          jobtype: data[0].jobtype,
          company: data[0].company,
          logoCompany: data[0].logoCompany,
          salary: data[0].salary,
          posted: data[0].posted,
          joblocationcity: data[0].joblocationcity,
          source: data[0].source,
          address_city: data[0].joblocationcity,
          address_country: data[0].country,
          address_line_1: '',
          address_line_2: '',
          employment_type: data[0].jobtype,
        };
      }

      if (data[1]) {
        dbJob = {
          jobtitle: data[1].jobtitle,
          description: data[1].description,
          jobtype: data[1].jobtype,
          company: data[1].company,
          logoCompany: data[1].logoCompany,
          salary: data[1].salary,
          posted: data[1].posted,
          joblocationcity: data[1].joblocationcity,
          source: data[1].source,
          address_city: data[1].joblocationcity,
          address_country: data[1].country,
          address_line_1: '',
          address_line_2: '',
          employment_type: data[1].jobtype,
        };
      }

      return osJob || dbJob || {};
    }

    // Get from job leads
    if (jobBoardId && !externalJobId) {
      const jobLeadData = await this.jobLeadRepository.findOne({
        where: [{ job_lead_external_id: externalJobId }, { job_board_id: jobBoardId }],
      });

      if (jobLeadData) {
        return {
          jobtitle: jobLeadData.title,
          description: jobLeadData.description,
          jobtype: jobLeadData.employment_type,
          company: jobLeadData.company_name,
          logoCompany: jobLeadData.logoCompany,
          salary: jobLeadData.salary,
          posted: jobLeadData.date_added,
          joblocationcity: [jobLeadData.address_city, jobLeadData.address_country].filter(Boolean).join(', '),
          source: jobBoardId ? JOB_BOARDS.find((board) => jobBoardId.startsWith(board)) : '',
          address_city: jobLeadData.address_city,
          address_country: jobLeadData.address_country,
          address_line_1: jobLeadData.address_line_1,
          address_line_2: jobLeadData.address_line_2,
          employment_type: jobLeadData.employment_type,
        };
      }
    }

    // Get from BH
    if (externalJobId) {
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(sequence.user?.organizationId)) || {};
      const jobVacancyData = await getJobVacancy(bhRestToken, corporateRestUrl, externalJobId);
      if (jobVacancyData) {
        return {
          jobtitle: jobVacancyData.title,
          description:
            jobVacancyData.description?.split('<br> Source: Zileo<br>')?.at(-1) || jobVacancyData.description,
          jobtype: jobVacancyData.employmentType,
          company: jobVacancyData.clientCorporation?.name,
          companyBhId: jobVacancyData.clientCorporation?.id,
          logoCompany: jobVacancyData.logoCompany,
          salary: jobVacancyData.salary,
          posted: jobVacancyData.dateAdded,
          joblocationcity: jobVacancyData.address?.city,
          source: '',
          address_city: jobVacancyData.address?.city,
          address_country: jobVacancyData.address?.countryName,
          address_line_1: jobVacancyData.address?.address1,
          address_line_2: jobVacancyData.address?.address2,
          employment_type: jobVacancyData.employmentType,
        };
      }
    }

    return null;
  }

  async addBHNoteForEmailSent({ clientContacts, subject, body }: { clientContacts: any[], subject: string, body: string }, user: any) {
    try {
      if (!clientContacts.length) {
        return;
      }

      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(user?.organizationId)) || {};
      const payload = {
        comments: `<b>Subject:</b> ${subject}
        <br/><hr/>
        <b>Body:</b> ${body}
        `,
        personReference: {
          id: clientContacts[0]?.id,
          searchEntity: 'ClientContact',
        },
        action: 'Email',
        clientContacts: clientContacts,
        commentingPerson: { id: user?.consultantId || 1 },
      };

      const result = await sendNoteToBullhorn(bhRestToken, corporateRestUrl, payload);

      return result;
    } catch (error) {
      this.logger.error(`Error addBHNoteForEmailSent: ${error.message}`);

      return;
    }
  }

  async getSignatureAsset(signatureId, useSendGrid = false) {
    const signature = signatureId
      ? await this.userSignatureRepository.findOne({
          where: {
            id: signatureId,
          },
        })
      : null;

    const images: any = signature
      ? signature.sigContent
          .replace(new RegExp('\r', 'g'), '')
          .replace(new RegExp('\n', 'g'), '')
          .match(/<img[^>]+src="([^">]+)"/gm)
          ?.map((x) => x.replace(/.*src="([^"]*)".*/, '$1'))
          ?.map((content, index) => {
            const imgId = `SIGNATURE-${index}`;
            // Only replace the src with cid if the image is actually in the content
            const originalSrc = content;
            signature.sigContent = signature.sigContent
              .replace(new RegExp('\r', 'g'), '')
              .replace(new RegExp('\n', 'g'), '')
              .replace(content, `cid:${imgId}`);
            const splittedContent = content.split(',');
            const imageValue = splittedContent?.length > 0 ? splittedContent[1] : '';
            return {
              filename: `${imgId}.jpeg`,
              contentType: `image/jpeg`,
              content: imageValue,
              originalSrc,
              ...(useSendGrid ? { disposition: 'inline', content_id: imgId } : { isInline: true, contentId: imgId }),
            };
          })
          ?.filter((img: any) => img.content) || []
      : [];
    return { signature, images };
  }

  private convertUtcToTimeZone = (utc: string) => {
    if (!utc) return null;
    const offset = parseInt(utc.replace('UTC', ''), 10);
    return `Etc/GMT${-offset}`;
  };

  standardizeSentEmail({
    recipient,
    detailEmail,
    currentUser,
    job,
    signature,
    fromHotList,
    lastSent,
    timeZone = null,
    utc = null,
    sourceType = false,
    sourceId = ""
  }: {
    recipient?: RecipientInfo;
    detailEmail: MailInfo;
    currentUser: UserEntity;
    job: CoreJobLead;
    signature: UserSignature;
    fromHotList: boolean;
    lastSent?: any;
    timeZone?: string;
    utc?: string;
    sourceType?: any;
    sourceId?: string;
  }) {
    if (currentUser.fullName === null || currentUser.fullName === undefined) {
      currentUser.fullName = currentUser.username;
    }
    const cloneDetailEmail = cloneDeep(detailEmail);
    const finalTimeZone =
      utc && utc !== 'UTC-12:00' ? this.convertUtcToTimeZone(utc) : timeZone || this.convertUtcToTimeZone('UTC+0');

    const emailInfo: {
      sender: object;
      recipient: RecipientInfo;
      job: CoreJobLead;
      sentDateTime: string;
      currentSentDateTime: string;
    } = {
      sender: {
        name: currentUser.fullName,
        email: currentUser.email,
      },
      recipient,
      job: {
        ...job,
        owner: {
          firstName: currentUser.fullName.split(' ')[0] ?? '',
          fullName: currentUser.fullName,
          email: currentUser.email,
        },
      },
      currentSentDateTime: new Intl.DateTimeFormat('en-GB', {
        dateStyle: 'full',
        timeStyle: 'long',
        timeZone: finalTimeZone,
      }).format(),
      sentDateTime: lastSent,
    };

    MERGE_TAGS_PLACE_HOLDERS?.forEach((rawPlaceHolder) => {
      const purePlaceHolder = rawPlaceHolder.split('{{').join('').split('}}').join('');
      const replacedContent =
        fromHotList && rawPlaceHolder.includes('RECIPIENT') // from hot list, do not specify recipient's info
          ? ''
          : getValueByPath(emailInfo, MergeTagMappingEnum[purePlaceHolder], '');

      cloneDetailEmail.content = cloneDetailEmail?.content
        ?.split(rawPlaceHolder)
        ?.join(Array.isArray(replacedContent) ? replacedContent.join(', ') : replacedContent);
    });
    cloneDetailEmail.subject = getMergeTagsContent(cloneDetailEmail.subject, emailInfo);

    cloneDetailEmail.content = processingContent({
      emailContent: cloneDetailEmail?.content?.replace(
        /\$(first_name|first name)\$/g,
        fromHotList ? '' : recipient?.name?.split(' ')[0]
      ),
      organizationId: currentUser.organizationId || 'null',
      signature: signature?.sigContent,
      email: recipient.email,
      sourceType,
      sourceId,
    });

    return cloneDetailEmail;
  }

  async sendEmail({
    recipients,
    detailEmail,
    currentUser,
    signatureId = '',
    job,
    fromHotList,
    sequenceId,
    threadId,
    timeZone = null,
    utc = null,
    useSendGrid = false,
    sourceType = false,
  }: {
    recipients: RecipientInfo[];
    detailEmail: MailInfo;
    currentUser: UserEntity;
    signatureId?: string;
    job?: CoreJobLead;
    fromHotList: boolean;
    sequenceId?: string;
    threadId?: string;
    timeZone?: string;
    utc?: string;
    useSendGrid?: boolean;
    sourceType?: any
  }) {
    const sigItem: any = await this.userSignatureServices.getSignatureDefault(currentUser.id);
    const { images, signature } = await this.getSignatureAsset(sigItem?.result?.id, useSendGrid);
    const lastSentItem =
      sequenceId && threadId
        ? await this.sequenceActivityLogRepository.findOne({
            where: {
              sequence: { id: sequenceId },
              sequenceStep: { id: threadId },
              type: SequenceActivityType.SENT,
            },
            order: {
              createdAt: 'DESC',
            },
          })
        : null;

    const finalTimeZone =
      utc && utc !== 'UTC-12:00' ? this.convertUtcToTimeZone(utc) : timeZone || this.convertUtcToTimeZone('UTC+0');

    const lastSent = lastSentItem?.createdAt
      ? new Intl.DateTimeFormat('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          timeZoneName: 'short',
          timeZone: finalTimeZone,
        }).format(lastSentItem.createdAt)
      : null;
    return Promise.all(
      recipients.map(async (recipient: any) => {
        const standardEmail = this.standardizeSentEmail({
          detailEmail,
          recipient: {
            ...recipient,
            firstName: recipient?.firstName?.trim() ? recipient?.firstName : recipient?.name?.split(' ')?.[0],
            clientCorporation: {name: recipient?.clientCorporation?.name || recipient?.companyName},
            occupation: recipient?.occupation || recipient?.contactTitle
          },
          currentUser,
          signature,
          job,
          fromHotList,
          lastSent,
          timeZone,
          sourceType,
          sourceId: recipient?.id
        });
        let result: any;

        if (useSendGrid) {
          const msg = {
            to: {
              email: recipient.email,
              name: recipient.name,
            },
            from: {
              email: currentUser.email,
              name: currentUser.fullName,
            },
            subject: standardEmail?.subject,
            html: standardEmail?.content,
            replyTo: {
              email: currentUser.grantEmail || currentUser.email,
              name: currentUser.fullName,
            },
            trackingSettings: {
              clickTracking: {
                // Click tracking need to update Brand Links (SendGrid)
                // https://app.sendgrid.com/settings/sender_auth/links
                enable: true,
              },
              openTracking: {
                enable: true,
              },
            },
            headers: {
              'Return-Path': currentUser.grantEmail || currentUser.email,
            }
          };
          if (images.length > 0) {
            // Only include images that have corresponding CID references in the content
            const usedImages = images.filter(img => {
              const cidRef = `cid:${useSendGrid ? img.content_id : img.contentId}`;
              return standardEmail?.content?.includes(cidRef);
            });

            let attachments = [...usedImages];

            if (standardEmail?.attachments?.length > 0) {
              const mailAttachments = await Promise.all(
                standardEmail?.attachments?.map(async ({ name, type, fileId, uid }) => {
                  try {
                    const link = await this.fileUploadService.getSignedUrl(fileId);
                    if (!link) {
                      return null;
                    }

                    const image = await axios.get(link, { responseType: 'arraybuffer' });
                    const imageValue = Buffer.from(image.data).toString('base64');
                    return {
                      contentId: uid,
                      filename: name,
                      contentType: type,
                      content: imageValue,
                      // isInline: false,
                    };
                  } catch(err) {
                    console.log("Error getting attachment:", fileId, err);

                    return null;
                  }
                })
              );

              attachments = [...attachments, ...(mailAttachments || []).filter(Boolean)];
            }
            set(msg, 'attachments', attachments);
          }

          sgMail.setApiKey(SendGridConfig.apiKeyEmailExplorerCandiate);
          result = await sgMail.send(msg);

          return {
            data: {
              messageId: result[0].headers['x-message-id'],
              subject: standardEmail?.subject,
              html: standardEmail?.content,
              attachments: standardEmail?.attachments,
              signatureId: sigItem?.result?.id,
              signature: sigItem?.result?.sigContent,
              originalImages: sigItem?.result?.originalImages
            },
          };
        } else {
          const msg = {
            to: [
              {
                email: recipient.email,
                name: recipient.name,
              },
            ],
            from: [
              {
                email: currentUser.grantEmail || currentUser.email,
                name: currentUser.fullName,
              },
            ],
            subject: standardEmail?.subject,
            body: standardEmail?.content,
            replyTo: [
              {
                email: currentUser.grantEmail || currentUser.email,
                name: currentUser.fullName,
              },
            ],
            trackingOptions: {
              links: true,
              opens: true,
              thread_replies: true,
            },
            customHeaders: [
              {
                name: 'List-Unsubscribe-Post',
                value: 'List-Unsubscribe=One-Click',
              },
              {
                name: 'List-Unsubscribe',
                value:
                  '<mailto: <EMAIL>?subject=unsubscribe>,  <http://mailinglist.example.com/unsubscribe.html>',
              },
            ],
          };
          let nylasMessageClient: any = this.nylas.messages;
          if (images.length > 0) {
            // Only include images that have corresponding CID references in the content
            const usedImages = images.filter(img => {
              const cidRef = `cid:${img.contentId}`;
              return standardEmail?.content?.includes(cidRef);
            });

            let attachments = [...usedImages];

            if (standardEmail?.attachments?.length > 0) {
              const mailAttachments = await Promise.all(
                standardEmail?.attachments?.map(async ({ name, type, fileId, uid }) => {
                  const link = await this.fileUploadService.getSignedUrl(fileId);
                  const image = await axios.get(link, { responseType: 'arraybuffer' });
                  const imageValue = Buffer.from(image.data);
                  return {
                    // contentId: uid,
                    filename: name,
                    contentType: type,
                    content: imageValue,
                    // isInline: false,
                  };
                })
              );
              attachments = [...attachments, ...(mailAttachments || [])];
              nylasMessageClient = nylasClient;
            }

            set(msg, 'attachments', attachments);
          }

          result = await nylasMessageClient.send({
            identifier: currentUser.grantId,
            requestBody: msg,
          });

          if (result.data?.id) {
            const emailWithHeader = await this.nylas.messages.find({
              identifier: currentUser.grantId,
              messageId: result.data.id,
              queryParams: {
                fields: MessageFields.INCLUDE_HEADERS,
              },
            });
            const rawId = (emailWithHeader?.data?.headers || []).find(
              (item) => item?.name?.toLowerCase() === 'message-id'
            )?.value;
            const regex = /<([^@>]+)@/;
            const messageId = `${rawId}`.match(regex)[1];
            result.data.messageId = messageId;
            result.data.subject = standardEmail?.subject;
            result.data.body = standardEmail?.content;
            result.data.attachments = standardEmail?.attachments;
            result.data.signatureId = sigItem?.result?.id;
            result.data.signature = sigItem?.result?.sigContent;
            result.data.originalImages = sigItem?.result?.originalImages;
          }

          return result;
        }
      })
    );
  }
}

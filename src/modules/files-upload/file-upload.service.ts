import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { getCommonMessageError, getSignedUrl, logError, logInfo, S3 } from '../../common/utils/helpers.util';
import { SesConfig } from '../../configs/configs.constants';
import * as fs from 'fs';
import { HttpService } from '@nestjs/axios';
import * as sharp from 'sharp';
import { LanguageCode, StatusCode } from '../../common/constants/common.constant';
import { HeaderDto } from '../../common/dto/common.dto';
import { BaseAbstractService } from '../../base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { v4 as uuid } from 'uuid';

@Injectable()
export class FileUploadService extends BaseAbstractService {
  constructor(private readonly httpService: HttpService, private readonly i18nService: I18nService) {
    super(i18nService);
  }
  async resizeImage(dataBuffer: Buffer, height?: number, width?: number) {
    try {
      const targetSizeInBytes = 64 * 1024;
      const defaultQuality = 80;

      let currentQuality = defaultQuality;
      let resizedImageBuffer = dataBuffer;
      let currentSize = resizedImageBuffer.length;
      while (currentSize > targetSizeInBytes && currentQuality >= 10) {
        currentQuality -= 10;
        if (height && width) {
          resizedImageBuffer = await sharp(dataBuffer).resize({ height, width }).toBuffer();
        }
        resizedImageBuffer = await sharp(dataBuffer).resize({ height: 400, width: 400 }).toBuffer();
        currentSize = resizedImageBuffer.length;
      }
      return resizedImageBuffer;
    } catch (error) {
      logError('resizeImage error ', error);
      return '';
    }
  }

  async uploadPublicFile(dataBuffer: Buffer, fileName: string, isReturnSignUrl = false) {
    try {
      const uploadResult = await S3.upload({
        Bucket: SesConfig.COMMON_API_S3_BUCKET_NAME,
        ACL: 'private',
        Body: dataBuffer,
        Key: `${fileName}`,
      }).promise();

      const key = uploadResult.Key;

      if (isReturnSignUrl) {
        return this.getSignedViewUrl(key);
      }

      return key;
    } catch (err) {
      logError('uploadPublicFile error ', err);
      return '';
    }
  }

  async uploadResizeFile(
    dataBuffer: Buffer,
    fileName: string,
    isReturnSignUrl = false,
    height?: number,
    width?: number
  ) {
    try {
      const resizedImageBuffer = await this.resizeImage(dataBuffer, height, width);
      const uploadResult = await S3.upload({
        Bucket: SesConfig.COMMON_API_S3_BUCKET_NAME,
        ACL: 'private',
        Body: resizedImageBuffer,
        Key: `${fileName}`,
      }).promise();

      const key = uploadResult.Key;

      if (isReturnSignUrl) {
        return this.getSignedUrl(key);
      }

      return key;
    } catch (err) {
      logError('uploadPublicFile error ', err);
      return '';
    }
  }

  async getSignedUrl(Key: string) {
    if (!Key) {
      return '';
    } else {
      try {
        const params: Record<string, unknown> = {
          Bucket: SesConfig.COMMON_API_S3_BUCKET_NAME,
          Key,
          Expires: Number(SesConfig.COMMON_API_S3_BUCKET_EXPIRES) * 24 * 60 * 60,
        };
        const preSignedUrl = await getSignedUrl('getObject', params);

        return preSignedUrl;
      } catch (error) {
        logError('getSignedUrl', error);
        return '';
      }
    }
  }

  async getSignedViewUrl(Key: string, isImage = true) {
    if (!Key) {
      return '';
    } else {
      try {
        const params: Record<string, unknown> = {
          Bucket: SesConfig.COMMON_API_S3_BUCKET_NAME,
          Key,
          Expires: Number(SesConfig.COMMON_API_S3_BUCKET_EXPIRES) * 24 * 60 * 60,
          ...(isImage && { ResponseContentType: 'image/webp' }),
        };
        const preSignedUrl = await getSignedUrl('getObject', params);

        return preSignedUrl;
      } catch (error) {
        logError('getSignedUrl', error);
        return '';
      }
    }
  }

  async getDownSizeSignedUrl(Key: string) {
    if (!Key) {
      return '';
    } else {
      try {
        const params: Record<string, unknown> = {
          Bucket: SesConfig.COMMON_API_S3_BUCKET_NAME,
          Key,
          Expires: Number(SesConfig.COMMON_API_S3_BUCKET_EXPIRES) * 24 * 60 * 60,
        };
        const preSignedUrl = await getSignedUrl('getObject', params);

        return preSignedUrl;
      } catch (error) {
        logError('getSignedUrl', error);
        return '';
      }
    }
  }

  async downloadUrl(url: string, directory: string, fileName: string) {
    !fs.existsSync(directory) && fs.mkdirSync(directory);

    const path = `${directory}/${fileName}`;
    const writer = fs.createWriteStream(path);

    const response = await this.httpService.axiosRef({
      url: url,
      method: 'GET',
      responseType: 'stream',
    });

    response.data.pipe(writer);

    await new Promise((resolve, reject) => {
      writer.on('finish', (...args) => resolve(args));
      writer.on('error', reject);
    });

    return path;
  }

  async uploadFileByUrl(url: string, directory: string, fileName: string, isReturnSignUrl = false) {
    try {
      const path = await this.downloadUrl(url, directory, fileName);
      const fileBuffer = fs.readFileSync(path);

      const key = await this.uploadPublicFile(fileBuffer, fileName, isReturnSignUrl);

      fs.rmSync(path);
      return key;
    } catch (error) {
      logError(`[${this.uploadFileByUrl.name}]`, error);

      return '';
    }
  }

  async uploadResizeFileByUrl(url: string, directory: string, fileName: string, height?: number, width?: number) {
    try {
      const path = await this.downloadUrl(url, directory, fileName);

      const fileBuffer = fs.readFileSync(path);

      const key = await this.uploadResizeFile(fileBuffer, fileName, false, height, width);

      fs.rmSync(path);
      return key;
    } catch (error) {
      logError(`[${this.uploadFileByUrl.name}]`, error);

      return '';
    }
  }

  async deleteFileByKey(key: string) {
    try {
      await S3.deleteObject({
        Bucket: SesConfig.COMMON_API_S3_BUCKET_NAME,
        Key: key,
      }).promise();

      return true;
    } catch (e) {
      logError('deleteFileByKey', e);
      return null;
    }
  }

  async getSignedUrlAvatar(avatarKey: string = null) {
    if (!avatarKey) {
      return null;
    }

    if (avatarKey.includes('https')) {
      return avatarKey;
    }

    return this.getSignedUrl(avatarKey);
  }

  async uploadImageFile(headerDto: HeaderDto, file: Express.Multer.File) {
    const { lang } = headerDto;
    try {
      const extension = file.mimetype.split('/')[1];
      const data = await this.uploadPublicFile(file.buffer, `${uuid()}.${extension}`);
      return this.formatOutputData(
        {
          key: 'translate.UPLOAD_IMAGE_SUCCESSFULLY',
          lang: LanguageCode.United_States,
        },
        {
          data,
          statusCode: StatusCode.UPLOAD_IMAGE_SUCCESSFULLY,
        }
      );
    } catch (error) {
      logError('UPLOAD_IMAGE_UNSUCCESSFULLY', error);
      const message = getCommonMessageError(error);
      const result = await this.formatOutputData(
        {
          key: 'translate.COMMON_MESSAGE',
          lang,
          args: { message },
        },
        {
          data: null,
          statusCode: StatusCode.UPLOAD_IMAGE_UNSUCCESSFULLY,
        }
      );

      throw new HttpException(result, error.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async bulkDeleteFiles(keys: string[]) {
    return S3.deleteObjects({
      Bucket: SesConfig.COMMON_API_S3_BUCKET_NAME,
      Delete: {
        Objects: keys.map((key) => ({ Key: key })),
      },
    }).promise();
  }
}

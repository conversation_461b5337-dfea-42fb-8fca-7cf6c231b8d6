import { Controller, Get, Query, UseGuards, Req, Post, Body, Param } from '@nestjs/common';
import { LinkedInFinderService } from './linkedin-finder.service';
import { SkipThrottle } from "@nestjs/throttler";
import { Permission } from "../../common/decorators/permissions.decorator";
import { PermissionResource } from "../../common/constants/permission.constant";
import { ResourceEnum } from "../user/entities/permission.entity";
import { LinkedInQueryDto, LinkedInFinderQueryDto,LinkedInPagingDto, LinkedInParameterType, LinkedInSearchPeopleType, LinkedInSearchCompaniesQuery, LinkedInProfileRetrieveDto, LinkedInManualRawSearchDto} from './dto/get-data-finder.dto';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('LinkedIn')
@SkipThrottle()
@Controller('linkedin-finder')

export class LinkedInFinderController {
  constructor(private readonly linkedinFinderService: LinkedInFinderService) { }

  @Get('/query-parameter')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async searchParamter(
    @Query() query: LinkedInFinderQueryDto,
    @Req() req: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.linkedinFinderService.getParamsValue(query, userId);
  }

  @Post('/get-contact-linkedin')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async getDataLinkedInByParameter(
    @Body() linkedInQueryDto: LinkedInQueryDto,
    @Query() query: LinkedInPagingDto,
    @Req() req: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.linkedinFinderService.getContactLinkedinData(linkedInQueryDto, userId, query);
  }
  
  @Post('/search-parameters')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async searchParameter(
    @Body() linkedInQueryDto: LinkedInParameterType,
    @Req() req: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.linkedinFinderService.getParameters(linkedInQueryDto, userId);
  }
  
  @Post('/search-people')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  @UseGuards(AuthenticationGuard, PermissionGuard)
  async searchPeople(
    @Body() linkedInQueryDto: LinkedInSearchPeopleType,
    @Req() req: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.linkedinFinderService.getPeople(linkedInQueryDto, userId);
  }
  
  @Post('/search-companies')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async searchCompanies(
    @Body() linkedInQueryDto: LinkedInSearchCompaniesQuery,
    @Req() req: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.linkedinFinderService.getCompanies(linkedInQueryDto, userId);
  }
  
  @Post('/retrieve-profile')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async retrieveProfile(
    @Body() body: LinkedInProfileRetrieveDto,
    @Req() req: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.linkedinFinderService.retrieveProfile(userId, body);
  }

  @Post('manual-raw-search')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async manualRawSearch(
    @Body() body: LinkedInManualRawSearchDto

  ) {
    return await this.linkedinFinderService.manualRawSearch(body);
  }
  
  @Get('/company-detail/:companyId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async retrievePeople(
    @Param('companyId') companyId: string,
    @Req() req: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.linkedinFinderService.getCompanyDetail(userId, companyId);
  }


  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Get('/view-as/:userId/query-parameter')
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  async searchParamterLegacy(
    @Query() query: LinkedInFinderQueryDto,
    @Param('userId') userId: string
  ) {
    return await this.linkedinFinderService.getParamsValue(query, userId);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  @Post('/view-as/:userId/get-contact-linkedin')
  async getDataLinkedInByParameterLegacy(
    @Param('userId') userId: string,
    @Body() linkedInQueryDto: LinkedInQueryDto,
    @Query() query: LinkedInPagingDto,
  ) {
    return await this.linkedinFinderService.getContactLinkedinData(linkedInQueryDto, userId, query);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  @Post('/view-as/:userId/search-parameters')
  async searchParameterLegacy(
    @Body() linkedInQueryDto: LinkedInParameterType,
    @Param('userId') userId: string
  ) {
    // TODO: Remove this after client migration is complete
    return await this.linkedinFinderService.getParameters(linkedInQueryDto, userId);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  @Post('/view-as/:userId/search-people')
  async searchPeopleLegacy(
    @Body() linkedInQueryDto: LinkedInSearchPeopleType,
    @Param('userId') userId: string
  ) {
    return await this.linkedinFinderService.getPeople(linkedInQueryDto, userId);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  @Post('/view-as/:userId/search-companies')
  async searchCompaniesLegacy(
    @Body() linkedInQueryDto: LinkedInSearchCompaniesQuery,
    @Param('userId') userId: string
  ) {
    return await this.linkedinFinderService.getCompanies(linkedInQueryDto, userId);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  @Get('/view-as/:userId/company-detail/:companyId')
  async retrievePeopleLegacy(
    @Param('userId') userId: string,
    @Param('companyId') companyId: string
  ) {
    return await this.linkedinFinderService.getCompanyDetail(userId, companyId);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.EMAIL_FINDER].Read)
  @Post('/view-as/:userId/retrieve-profile')
  async retrieveProfileLegacy(
    @Param('userId') userId: string,
    @Body() body: LinkedInProfileRetrieveDto
  ) {
    return await this.linkedinFinderService.retrieveProfile(userId, body);
  }
}

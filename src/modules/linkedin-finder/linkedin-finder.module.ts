import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { Keyv } from 'keyv';
import KeyvRedis from '@keyv/redis';
import { LinkedInFinderController } from './linkedin-finder.controller';
import { LinkedInFinderService } from './linkedin-finder.service';
import { HttpModule } from '@nestjs/axios';
import { UserRepository } from '../user/repositories/user.repository';
import { CacheService } from '../cache/cache.service';
import { CacheModule } from '@nestjs/cache-manager';
import * as redisStore from 'cache-manager-redis-store';
import { EmployeeFinderModule } from '../employee-finder/employee-finder.module';

@Module({
  imports: [
    HttpModule,
    CacheModule.registerAsync({
      useFactory: async () => {
        return new Keyv({
          store: new KeyvRedis(process.env.REDIS_CONNECTION),
          ttl: 24 * 60 * 60,
        });
      },
    }),
    EmployeeFinderModule
  ],
  controllers: [LinkedInFinderController],
  providers: [LinkedInFinderService, UserRepository, CacheService],
})
export class LinkedInFinderModule {}

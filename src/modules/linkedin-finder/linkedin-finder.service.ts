import { Promise as BBPromise } from 'bluebird';
import { ApolloService } from './../employee-finder/services/apollo.service';
import { Injectable, NotFoundException, UseGuards } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import axios from 'axios';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import {
  LinkedInFinderQueryDto,
  LinkedInQueryDto,
  LinkedInPagingDto,
  LinkedInParameterType,
  LinkedInSearchPeopleType,
  LinkedInSearchCompaniesQuery,
  LinkedInProfileRetrieveDto,
  LinkedInManualRawSearchDto,
} from './dto/get-data-finder.dto';
import { CONTACT_FINDER_LINKEDIN_API, ProspeoConfig } from 'src/configs/configs.constants';
import { UserRepository } from '../user/repositories/user.repository';
import { unipileConfig } from 'src/configs/configs.constants';
import * as unipileClient from '../jobs/utils/unipile-service.utils';
import { CacheService } from '../cache/cache.service';
import { CACHE_REDIS_UNIPILE_ACCOUNT_NAME } from 'src/common/constants/common.constant';
import { FinderProviderEnum } from './constants/finder.const';
import { convertDateStringToObject } from 'src/common/utils/helpers.util';

interface LinkedinSearchPayload {
  api: string;
  category: string;
  keywords?: string;
  current_company?: { id: string }[];
  industry?: { id: string }[];
  location?: { id: string }[];
}

@Injectable()
@UseGuards(AuthenticationGuard)
export class LinkedInFinderService extends BaseAbstractService {
  constructor(
    private readonly httpService: HttpService,
    private readonly i18nService: I18nService,
    private readonly userRepository: UserRepository,
    private readonly cacheService: CacheService,
    private readonly apolloService: ApolloService
  ) {
    super(i18nService);
  }

  async getParamsValue(dataDto: LinkedInFinderQueryDto, userId: string) {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const accountId = await this.handleGetAccountUnipile();
    try {
      const data = await unipileClient.getParameters(accountId, dataDto.limit || 20, dataDto.keywords, dataDto.type);
      return this.formatOutputData({ key: 'GET_LINKEDIN_PARAMETER' }, { data: { data } });
    } catch (error) {
      console.error(error);
      return await this.throwCommonMessage('GET_LINKEDIN_PARAMETER_FAIL', error);
    }
  }

  async getContactLinkedinData(dataDto: LinkedInQueryDto, userId: string, query: LinkedInPagingDto) {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const unipileAccountId = user.unipileAccountId;
    try {
      // Call api post to get data from Unipile
      const data = await unipileClient.getLinkedInDataPeople(dataDto, unipileAccountId, query);
      return this.formatOutputData({ key: 'GET_LINKEDIN_DATA' }, { data: { data } });
    } catch (error) {
      console.error(error);
      return await this.throwCommonMessage('GET_LINKEDIN_PARAMETER_FAIL', error);
    }
  }

  async getParameters(body: LinkedInParameterType, userId: string) {
    try {
      const accountId = await this.handleGetAccountUnipile();
      const payload: any = {
        api: body.type || 'classic',
        category: body.category,
      };
      const limit = body.limit;

      if (body.keywords) {
        payload.keywords = body.keywords;
      }

      if (body.category === 'companies') {
        if (body.industry && body.industry.length > 0) {
          payload.industry = body.industry.map((industryId) => industryId);
        }

        if (body.location && body.location.length > 0) {
          payload.location = body.location.map((locationId) => locationId);
        }

        if (body.companySize) {
          payload.headcount = [
            {
              min: body.companySize.min,
              max: body.companySize.max,
            },
          ];
        }
      }
      const cursor = body?.cursor;
      const data = await unipileClient.getParametersData(payload, accountId, limit, cursor);
      return this.formatOutputData({ key: 'GET_LINKEDIN_DATA' }, { data: { data } });
    } catch (error) {
      console.error(error);
      return await this.throwCommonMessage('GET_LINKEDIN_PARAMETER_FAIL', error);
    }
  }

  getProviderToSearch(payload: any) {
    const keys = Object.keys(payload);
    const apolloFilters = [
      'technologyIds',
      'revenueStatusItem',
      'website',
      'minFunding',
      'maxFunding',
      'organizationLatestFundingStageCd',
      'funding',
      'website',
    ];
    const unipileFilters = [
      'keywords',
      'companyIds',
      'location_within_area',
      'location',
      'industry',
      'skills',
      'schools',
      'graduationYear',
      'yearExperiences',
      'seniority',
      'first_name',
      'last_name',
      'departmentIds',
      'jobTitleIds',
      'companySize'
    ];

    const includesApollo = apolloFilters.some((key) => keys.includes(key) && payload[key]);
    const includesUnipile = unipileFilters.some((key) => keys.includes(key) && payload[key]);

    if (includesApollo && includesUnipile) {
      return null;
    } else if (includesApollo) {
      return FinderProviderEnum.APOLLO;
    }
    return FinderProviderEnum.UNIPILE;
  }

  //TODO: need to be updated, current version is just for testing

  parseToUnipilePeople(apolloPeople: any) {
    const res = {
      object: 'LinkedinSearch',
      items: apolloPeople.people?.map((person) => ({
        type: 'PEOPLE',
        id: person.id,
        headline: person.headline,
        location: person.country ? person.country : null,
        member_urn: null,
        network_distance: null,
        can_send_inmail: null,
        recruiter_candidate_id: null,
        hiddenCandidate: null,
        interests: null,
        interestLikelihood: null,
        summary: null,
        name: person.name,
        first_name: person.first_name,
        last_name: person.last_name,
        industry: null,
        public_identifier: null,
        public_profile_url: person?.linkedin_url,
        profile_url: null,
        profile_picture_url: person.photo_url,
        profile_picture_url_large: person.photo_url,
        connections_count: null,
        current_company_website: person.organization.primary_domain,
        work_experience: person.employment_history?.map((history, index) => ({
          company: history.organization_name,
          company_id: history.organization_id,
          item_type: FinderProviderEnum.APOLLO,
          industry: null,
          location: null,
          role: history.title,
          start: history?.start_date && convertDateStringToObject(history.start_date),
          end: history?.end_date && convertDateStringToObject(history.end_date),
          description: null,
          logo: index === 0 ? person.organization?.logo_url : null,
        })),
        education: null,
        privacySettings: { allowConnectionsBrowse: null, showPremiumSubscriberIcon: null },
        item_type: FinderProviderEnum.APOLLO,
      })),
      config: {
        params: {
          api: 'recruiter',
          category: 'people',
          keywords: 'a',
          current_company: [{ id: apolloPeople.people?.[0]?.organization_id }], // Assuming at least one person exists
        },
      },
      paging: {
        start: 0,
        page_count: apolloPeople?.people?.length,
        total_count: apolloPeople?.pagination?.total_entries,
      },
      cursor: apolloPeople.cursor || '',
    };

    return res;
  }

  parseToUnipileCompanies(apolloCompany: any) {
    const res = {
      object: 'LinkedinSearch',
      items: apolloCompany?.organizations?.map((person) => ({
        type: 'COMPANY',
        id: person.id,
        followers_count: null,
        industry: person?.industry,
        job_offers_count: null,
        location: person?.raw_address,
        name: person?.name,
        profile_url: person.linkedin_url,
        logo_url: person?.logo_url,
        summary: person.industry,
        item_type: FinderProviderEnum.APOLLO,
      })),
      paging: {
        start: 0,
        page_count: apolloCompany?.organizations?.length,
        total_count: apolloCompany?.pagination?.total_entries,
      },
      cursor: apolloCompany?.cursor || '',
    };

    return res;
  }

  async getPeople(body: LinkedInSearchPeopleType, userId: string) {
    try {
      let decodedCursor = null
      if (body.cursor) {
        decodedCursor = JSON.parse(Buffer.from(body.cursor, 'base64').toString('utf-8'));
      }
      const providerToSearch = this.getProviderToSearch(body);
      if (providerToSearch === FinderProviderEnum.APOLLO) {
        const searchPayload: any = {};
        if (body.technologyIds?.length > 0) {
          searchPayload.currentlyUsingAnyOfTechnologyUids = body.technologyIds;
        }

        if (body.revenueStatusItem) {
          searchPayload.organizationTradingStatus = body.technologyIds;
        }

        if (body.organizationLatestFundingStageCd) {
          searchPayload.organizationLatestFundingStageCd = body.organizationLatestFundingStageCd;
        }

        if (body.funding) {
          searchPayload.totalFundingRangeMin = body.funding?.min || null;
          searchPayload.totalFundingRangeMax = body.funding?.max || null;
        }

        const data = await this.apolloService.getPeople(searchPayload);

        //TODO: parse data to the same as Unipile
        return this.formatOutputData(
          { key: 'GET_LINKEDIN_PEOPLE_DATA' },
          { data: { data: this.parseToUnipilePeople(data) } }
        );
      }

      let accountId = ""
      if (!decodedCursor) {
        accountId = await this.handleGetAccountUnipile();
      } else {
        accountId = decodedCursor?.account_id;
      }
      let payload: any = {
        api: CONTACT_FINDER_LINKEDIN_API,
        category: 'people',
      };

      if (body.keywords) {
        payload.keywords = body.keywords
      } else if (!(body.first_name || body?.last_name)) {
        payload.keywords = 'a';
      }


      if (payload.api === 'recruiter') {
        if (body.companyIds && body.companyIds.length > 0) {
          if (body.isCurrentCompany) {
            payload.current_company = body.companyIds.map((companyId) => ({ id: companyId }));
          } else {
            payload.company = body.companyIds.map((companyId) => ({ id: companyId }));
          }
        }

        if (body.first_name) {
          payload.first_name = [body.first_name];
        }

        if (body.last_name) {
          payload.last_name = [body.last_name];
        }
        
        if (body.location && body.location.length > 0) {
          payload.location = body.location.map((locationId) => ({ id: locationId }));
        }

        if (body.location_within_area) {
          payload.location_within_area = body.location_within_area;
        }
        
        if (body.industry && body.industry.length > 0) {
          payload.industry = {
            include: body.industry,
          };
        }
        
        if (body.jobTitleIds) {
          payload.role = body.jobTitleIds.map((jobTitle) => ({is_selection: true, id: jobTitle}));
        }

        if (body.schools && body.schools.length > 0) {
          payload.school = body.schools.map((schoolsId) => ({ id: schoolsId }));
        }
        
        if (body.departmentIds && body.departmentIds.length > 0) {
          payload.function = body.departmentIds;
        }
      } else if (payload.api === 'sales_navigator') {
        if (body.companyIds && body.companyIds.length > 0) {
          if (body.isCurrentCompany) {
            payload.company = {
              include: body.companyIds,
            };
          } else {
            payload.past_company = {
              include: body.companyIds,
            };
          }
        }
        
        if (body.first_name) {
          payload.first_name = body.first_name;
        }

        if (body.last_name) {
          payload.last_name = body.last_name;
        }

        if (body.location && body.location.length > 0) {
          payload.location = {
            include: body.location,
          }
        }
        
        if (body.industry && body.industry.length > 0) {
          payload.industry = {
            include: body.industry,
          };
        }

        if (body.jobTitleIds) {
          payload.role = {
            include: body.jobTitleIds,
          }
        }

        if (body.schools && body.schools.length > 0) {
          payload.school = {
            include: body.schools,
          }
        }
          
        if (body.departmentIds && body.departmentIds.length > 0) {
          payload.function = {
            include: body.departmentIds,
          }
        }
      } else {
        if (body.companyIds && body.companyIds.length > 0) {
          if (body.isCurrentCompany) {
            payload.company = body.companyIds;
          } else {
            payload.past_company = body.companyIds;
          }
        }

        if (body.first_name) {
          payload.advanced_keywords = {
            ...payload.advanced_keywords,
            first_name: body.first_name,
          }
        }

        if (body.last_name) {
          payload.advanced_keywords = {
            ...payload.advanced_keywords,
            last_name: body.last_name,
          }
        }
        
        if (body.location && body.location.length > 0) {
          payload.location = body.location;
        }

        if (body.industry && body.industry.length > 0) {
          payload.industry = body.industry;
        }
        
        if (body.schools && body.schools.length > 0) {
          payload.school = body.schools;
        }
      }

      if (body.skills && body.skills.length > 0) {
        payload.skills = body.skills.map((skillsId) => ({ id: skillsId }));
      }

      if (payload.graduationYear) {
        payload.graduation_year = {
          min: body.graduationYear?.min || 1000,
          to: body.graduationYear?.max || 2000,
        };
      }

      if (payload.yearExperiences) {
        payload.tenure = {
          min: body.yearExperiences?.min || 1000,
          to: body.yearExperiences?.max || 2000,
        };
      }

      if (payload.seniority) {
        payload.seniority = {
          include: [body.seniority?.include],
          exclude: [body.seniority?.exclude],
        };
      }

      if (body.companySize) {
        payload.company_headcount = [
          {
            min: body.companySize.min,
            max: body.companySize.max,
          },
        ];
      }

      const limit = body.limit || "10";
      const pageSize = body.pageSize || 10
      const currentPage = body.currentPage || 1
      const cursorPayload = {
        account_id: accountId,
        limit: pageSize,
        start: (currentPage - 1) * pageSize,
        params: payload
      }

      const encodedCursor = Buffer.from(JSON.stringify(cursorPayload)).toString('base64'); 
      
      const data = await unipileClient.getParametersData(payload, accountId, limit, encodedCursor);
      if (CONTACT_FINDER_LINKEDIN_API !== 'recruiter' && data?.items?.length) {
        data.items = await BBPromise.map(
          data.items,
          async (record) => {
            const profile = await this.getRetrieveProfile(record.id);

            return {
              skills: profile.skills,
              work_experience: profile.work_experience,
              education: profile.education,
              ...record,
            }
          },
          { concurrency: 20 },
        );
      }

      return this.formatOutputData({ key: 'GET_LINKEDIN_PEOPLE_DATA' }, { data: { data } });
    } catch (error) {
      console.error(error);
      return await this.throwCommonMessage('GET_LINKEDIN_PEOPLE_DATA', error);
    }
  }

  async getCompanyDetail(userId: string, companyId: string) {
    try {
      const accountId = await this.handleGetAccountUnipile();
      const data = await unipileClient.getCompanyDetail(accountId, companyId);
      return this.formatOutputData({ key: 'GET_LINKEDIN_COMPANY_DETAIL' }, { data: { data } });
    } catch (error) {
      console.error(error);
      return await this.throwCommonMessage('GET_LINKEDIN_PEOPLE_DATA', error);
    }
  }

  async createAccountUnipileRedis() {
    try {
      const data = await unipileClient.getListAccounts();
      const items = data?.items
        ?.filter(
          (item) =>
            item?.connection_params?.im?.premiumFeatures?.includes(CONTACT_FINDER_LINKEDIN_API) && item?.sources?.[0]?.status === 'OK'
        )
        ?.map((item) => ({ value: item?.id, used: 0 }));

      // Cache lifetime is from creation to end of day

      await this.actionUpdateCacheData(items);

      return items;
    } catch (error) {
      console.error(error);
      return await this.throwCommonMessage('CREATE_ACCOUNT_UNIPILE_REDIS_FAIL', error);
    }
  }

  async actionUpdateCacheData(data) {
    const now = new Date();
    const nextHour = new Date(now);
    nextHour.setMinutes(60, 0, 0);
    const ttl = Math.floor((nextHour.getTime() - now.getTime()) / 1000);

    await this.cacheService.set(`${CACHE_REDIS_UNIPILE_ACCOUNT_NAME}:${CONTACT_FINDER_LINKEDIN_API}`, data, ttl);
  }

  async getRetrieveProfile(identifier: string) {
    try {
      const cacheKey = `unipile:cache:profile:${identifier}`;
      let data = await this.cacheService.get(cacheKey);
      if (!data) {
        const accountId = await this.handleGetAccountUnipile();
        data = await unipileClient.retrieveProfile(accountId, identifier);
        await this.cacheService.set(cacheKey, data, 24 * 60 * 60);
      }

      return data;
    } catch (error) {
      return null;
    }
  }


  private getRandomAccount(data) {
    if (data.length === 0) {
      return null;
    }
    // Only allow to call 60% of 2500 requests
    const threshold = 2500 * 0.6;
    let selectedItem;

    while (true) {
      const randomIndex = Math.floor(Math.random() * data.length);
      selectedItem = data[randomIndex];
      if (selectedItem.used < threshold) {
        return selectedItem.value;
      }
    }
  }

  async handleGetAccountUnipile() {
    try {
      let data = await this.cacheService.get(`${CACHE_REDIS_UNIPILE_ACCOUNT_NAME}:${CONTACT_FINDER_LINKEDIN_API}`);
      if (!data || data.length === 0) {
        data = await this.createAccountUnipileRedis();
      }
      const selectedItem = this.getRandomAccount(data);
      const dataUpdate = data?.map((item) => {
        if (item.value === selectedItem) {
          item.used += 1;
        }
        return item;
      });
      await this.actionUpdateCacheData(dataUpdate);
      return selectedItem;
    } catch (error) {
      return await this.throwCommonMessage('GET_ACCOUNT_UNIPILE_STATUS', error);
    }
  }

  async getCompanies(body: LinkedInSearchCompaniesQuery, userId: string) {
    try {
      let decodedCursor = null
      if (body.cursor) {
        decodedCursor = JSON.parse(Buffer.from(body.cursor, 'base64').toString('utf-8'));
      }
      const providerToSearch = this.getProviderToSearch(body);
      if (providerToSearch === FinderProviderEnum.APOLLO) {
        const searchPayload: any = {};
        if (body.technologyIds?.length > 0) {
          searchPayload.currentlyUsingAnyOfTechnologyUids = body.technologyIds;
        }

        if (body.revenueStatusItem) {
          searchPayload.organizationTradingStatus = body.technologyIds;
        }

        if (body.organizationLatestFundingStageCd) {
          searchPayload.organizationLatestFundingStageCd = body.organizationLatestFundingStageCd;
        }

        if (body.funding) {
          searchPayload.totalFundingRangeMin = body.funding?.min || null;
          searchPayload.totalFundingRangeMax = body.funding?.max || null;
        }

        if (body.website) {
          searchPayload.searchText = body.website;
        }

        const data = await this.apolloService.getCompaniesWithExploreMode(searchPayload);

        //TODO: parse data to the same as Unipile
        return this.formatOutputData(
          { key: 'GET_LINKEDIN_PEOPLE_DATA' },
          { data: { data: this.parseToUnipileCompanies(data) } }
        );
      }
      let accountId = ""
      if (!decodedCursor) {
        accountId = await this.handleGetAccountUnipile();
      } else {
        accountId = decodedCursor?.account_id;
      }
      const payload: any = {
        api: CONTACT_FINDER_LINKEDIN_API === 'sales_navigator' ? CONTACT_FINDER_LINKEDIN_API : 'classic',
        category: 'companies',
        annual_revenue: {
          min: 0,
          max: 1001,
          currency: 'USD',
        },
      };
      const limit = body.limit || "10";

      if (body.keywords) {
        payload.keywords = body.keywords;
      }
      if (body.industry && body.industry.length > 0) {
        payload.industry = body.industry.map((industryId) => industryId);
      }

      if (body.location && body.location.length > 0) {
        payload.location = body.location.map((locationId) => locationId);
      }

      if (body.companySize) {
        payload.headcount = [
          {
            min: body.companySize.min,
            max: body.companySize.max,
          },
        ];
      }
      // Specific cases
      if (payload.api === 'recruiter') {
        // 
      } else if (payload.api === 'sales_navigator') {
        if (body.industry && body.industry.length > 0) {
          payload.industry = {
            include: body.industry.map((industryId) => industryId),
          }
        }
        
        if (body.location && body.location.length > 0) {
          payload.location = {
            include: body.location.map((locationId) => locationId),
          }
        }
      }
      
      const cursor = body?.cursor;
      const pageSize = body.pageSize || 10
      const currentPage = body.currentPage || 1
      const cursorPayload = {
        account_id: accountId,
        limit: pageSize,
        start: (currentPage - 1) * pageSize,
        params: payload
      }

      const encodedCursor = Buffer.from(JSON.stringify(cursorPayload)).toString('base64'); 
      const data = await unipileClient.getParametersData(payload, accountId, limit, encodedCursor);

      return this.formatOutputData({ key: 'GET_LINKEDIN_DATA' }, { data: { data } });
    } catch (error) {
      console.error(error.response?.data || error);
      return await this.throwCommonMessage('GET_LINKEDIN_PARAMETER_FAIL', error);
    }
  }

  async retrieveProfile(userId: string, body: LinkedInProfileRetrieveDto) {
    try {
      const accountId = await this.handleGetAccountUnipile();
      const data = await unipileClient.retrieveProfile(accountId, body.identifier);
      return this.formatOutputData({ key: 'GET_LINKEDIN_PEOPLE_DATA' }, { data: { data } });
    } catch (error) {
      return await this.throwCommonMessage('GET_ACCOUNT_UNIPILE_STATUS', error);
    }
  }

  async manualRawSearch(body: LinkedInManualRawSearchDto) {
    try {
      const accountId = await this.handleGetAccountUnipile();
      const rawData = await unipileClient.getLinkedinRawSearch(accountId, body);
      const data = rawData?.data?.data?.searchDashTypeaheadByGlobalTypeahead?.elements?.map((item, index) => ({
        name: item?.entityLockupView?.title?.text,
        image: item?.entityLockupView?.image?.attributes?.[0]?.detailData?.nonEntityProfilePicture?.vectorImage?.artifacts?.[0]?.fileIdentifyingUrlPathSegment || item?.entityLockupView?.image?.attributes?.[0]?.detailData?.nonEntityCompanyLogo?.vectorImage?.artifacts?.[0]?.fileIdentifyingUrlPathSegment,
        subTitle: item?.entityLockupView?.subtitle?.text,
        id: index + 1,
        type: item?.entityLockupView?.image?.attributes?.[0]?.detailData?.nonEntityCompanyLogo ? "COMPANY" : "USER"
      }))
      return this.formatOutputData({ key: 'GET_RAW_LINKEDIN_SEARCH' }, { data: {data} });
    } catch (error) {
      return await this.throwCommonMessage('GET_RAW_LINKEDIN_SEARCH', error);
    }
  }
}

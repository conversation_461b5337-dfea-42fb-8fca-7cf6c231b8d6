import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsIn } from 'class-validator';

const TYPE = [
  'LOCATION',
  'PEOPLE',
  'COMPANY',
  'SCHOOL',
  'INDUSTRY',
  'SERVICE',
  'SKILL',
  'EMPLOYMENT_TYPE',
  'JOB_TITLE',
  'JOB_FUNCTION',
  'DEPARTMENT',
];

export class LinkedInFinderQueryDto {
  @ApiPropertyOptional()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional()
  @IsOptional()
  keywords?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsIn(TYPE, { each: true })
  type?: string;
}
export class LinkedInPagingDto {
  @ApiPropertyOptional()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional()
  @IsOptional()
  cursor?: string;
}

export class LinkedInQueryDto {
  @ApiProperty()
  @IsNotEmpty()
  api: string;

  @ApiPropertyOptional()
  @IsOptional()
  category: string;

  @ApiPropertyOptional()
  @IsOptional()
  cursor: string;

  @ApiPropertyOptional()
  @IsOptional()
  company?: [number];

  @ApiPropertyOptional()
  @IsOptional()
  school?: [number];

  @ApiPropertyOptional()
  @IsOptional()
  industry?: [number];

  @ApiPropertyOptional()
  @IsOptional()
  service?: [number];
}

export class LinkedInParameterType {
  @ApiProperty()
  @IsOptional()
  type: string;

  @ApiProperty()
  @IsNotEmpty()
  category: string;

  @ApiProperty()
  @IsOptional()
  keywords: string;

  @ApiProperty()
  @IsOptional()
  limit: string;

  @ApiProperty()
  @IsOptional()
  industry?: string[];

  @ApiProperty()
  @IsOptional()
  location?: string[];

  @ApiProperty()
  @IsOptional()
  companySize?: {
    min: number;
    max: number;
  };

  @ApiProperty()
  @IsOptional()
  cursor?: string;
}

export class LinkedInSearchPeopleType {
  @ApiProperty()
  @IsOptional()
  keywords?: string;

  @ApiProperty()
  @IsOptional()
  limit: string;

  @ApiProperty()
  @IsOptional()
  cursor?: string;

  @ApiProperty()
  @IsOptional()
  companyIds: string[];

  @ApiProperty()
  @IsOptional()
  isCurrentCompany: boolean;


  @ApiProperty()
  @IsOptional()
  location_within_area?: number;

  @ApiProperty()
  @IsOptional()
  location?: string[];

  @ApiProperty()
  @IsOptional()
  industry?: string[];

  @ApiProperty()
  @IsOptional()
  skills?: string[];

  @ApiProperty()
  @IsOptional()
  schools?: string[];

  @ApiProperty()
  @IsOptional()
  graduationYear?: {
    min: number;
    max: number;
  };

  @ApiProperty()
  @IsOptional()
  yearExperiences?: {
    min: number;
    max: number;
  };

  @ApiProperty()
  @IsOptional()
  seniority?: {
    include?: string;
    exclude?: string;
  };

  // [owner, partner, cxo, vp, director, manager, senior, entry, training, unpaid]

  @ApiProperty()
  @IsOptional()
  first_name?: string;

  @ApiProperty()
  @IsOptional()
  last_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  technologyIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  revenueStatusItem?: string;

  @ApiPropertyOptional()
  @IsOptional()
  organizationLatestFundingStageCd?: string[];

  @ApiProperty()
  @IsOptional()
  funding?: {
    max?: string;
    min?: string;
  };

  @ApiProperty()
  @IsOptional()
  companySize?: {
    max?: string;
    min?: string;
  };

  @ApiPropertyOptional()
  @IsOptional()
  website?: string;

  @ApiPropertyOptional()
  @IsOptional()
  minFunding?: string;

  @ApiPropertyOptional()
  @IsOptional()
  maxFunding?: string;

  @ApiPropertyOptional()
  @IsOptional()
  departmentIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  jobTitleIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  savedSearch?: string;

  @ApiPropertyOptional()
  @IsOptional()
  currentPage?: number

  @ApiPropertyOptional()
  @IsOptional()
  pageSize ?: number
}

export class LinkedInSearchCompaniesQuery extends LinkedInParameterType {
  @ApiPropertyOptional()
  @IsOptional()
  technologyIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  revenueStatusItem?: string;

  @ApiPropertyOptional()
  @IsOptional()
  website?: string;

  @ApiPropertyOptional()
  @IsOptional()
  organizationLatestFundingStageCd?: string[];

  @ApiProperty()
  @IsOptional()
  funding?: {
    max?: string;
    min?: string;
  };

  @ApiPropertyOptional()
  @IsOptional()
  currentPage?: number

  @ApiPropertyOptional()
  @IsOptional()
  pageSize ?: number

  @ApiPropertyOptional()
  @IsOptional()
  cursor?: string;
}

export class LinkedInProfileRetrieveDto {
  @ApiProperty()
  @IsNotEmpty()
  identifier?: string;
}

export class LinkedInManualRawSearchDto {
  @ApiProperty()
  @IsNotEmpty()
  keyword?: string;
}

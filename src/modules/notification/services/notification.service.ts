import { Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { NotificationRepository } from '../repositories/notification.repository';
import { UpdateNotificationDto } from '../dto/update-notification.dto';
import { PageOptionsDto } from 'src/common/dto/pagination.dto';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { NotificationEntity, NotificationEnum } from '../entities/notification.entity';
import { Not, Raw } from 'typeorm';

@Injectable()
export class NotificationService extends BaseAbstractService {
  constructor(private i18nService: I18nService, private readonly notificationRepository: NotificationRepository) {
    super(i18nService);
  }

  async updateNotification(id: string, updateDto: UpdateNotificationDto) {
    await this.notificationRepository.update(id, updateDto);

    return this.formatOutputData({ key: 'UPDATE_NOTIFICATION' }, { data: true });
  }

  async markAllReadNotifications(userId: string) {
    await this.notificationRepository.update({ userId }, { isRead: true });

    return this.formatOutputData({ key: 'MARK_ALL_READ' }, { data: {} });
  }

  async getMyNotifications(userId: string, notificationQueryParams: PageOptionsDto) {
    const { limit, skip } = notificationQueryParams;

    const data = await this.notificationRepository
      .createQueryBuilder('n')
      .where({ userId })
      .leftJoinAndSelect(UserEntity, 'u_creator', 'u_creator.id = n.creator_id')
      .innerJoinAndSelect(UserEntity, 'u', 'u.id = n.user_id AND (u.id != u_creator.id OR u_creator.id IS NULL)')
      .limit(limit)
      .offset(skip)
      .select(
        `n.id, 
         n.is_read as "isRead",
         n.updated_at as "updatedAt", 
         n.created_at as "createdAt",
         n.notification_type as "notificationType", 
         n.title as "title",
         n.content as "content",
         n.user_id as "userId", 
         n.lead_id as "leadId", 
         n.target_id as "targetId", 
         u_creator.email as "creatorEmail", 
         u_creator.username as "creatorUsername"`
      )
      .addOrderBy('n.updated_at', 'DESC')
      .getRawMany();
    return this.formatOutputData({ key: 'GET_MY_NOTIFICATION' }, { data });
  }

  async countMyUnreadNotification(userId: string) {
    const count = await this.notificationRepository.countBy({
      userId,
      isRead: false,
      creatorId: Raw((creatorCol) => `(${creatorCol} != :userId OR ${creatorCol} IS NULL)`, { userId }),
    });

    return this.formatOutputData({ key: 'COUNT_MY_UNREAD_NOTIFICATION' }, { data: String(count) });
  }

  async handleCreateAssignLeadNotification(notification: Partial<NotificationEntity>) {
    const { leadId, userId } = notification;

    if (userId === null) {
      return this.notificationRepository.delete({ leadId: notification.leadId });
    }

    const existingLead = await this.notificationRepository.findOneBy({ leadId });
    if (existingLead) {
      return this.notificationRepository.update({ id: existingLead.id }, { userId });
    }

    return this.notificationRepository.insert(this.notificationRepository.create(notification));
  }

  async handleCreateOnboardingRequestNotification(notification: Partial<NotificationEntity>) {
    return this.notificationRepository.insert(this.notificationRepository.create(notification));
  }

  async createNotification(notification: Partial<NotificationEntity>) {
    if (!notification.notificationType) {
      return null;
    }

    switch (notification.notificationType) {
      case NotificationEnum.ASSIGNED_LEAD:
        await this.handleCreateAssignLeadNotification(notification);
        break;
      case NotificationEnum.SENT_JOB:
        await this.handleCreateAssignLeadNotification(notification);
        break;
      case NotificationEnum.ONBOARDING_COMPANY_REQUEST:
        await this.handleCreateOnboardingRequestNotification(notification);
        break;
      default:
        break;
    }

    return true;
  }
}

import { Body, Controller, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { NotificationService } from './services/notification.service';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { PageOptionsDto } from 'src/common/dto/pagination.dto';
import { IJwtPayload } from '../auth/payloads/jwt-payload.payload';

@Controller('notification')
@ApiTags('Notification')
@SkipThrottle()
@UseGuards(AuthenticationGuard)
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get('view-as/:userId')
  async getMyNotificationsLegacy(@Query() notificationQueryParams: PageOptionsDto, @Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.notificationService.getMyNotifications(userId, notificationQueryParams);
  }

  @Get()
  async getMyNotifications(@Query() notificationQueryParams: PageOptionsDto, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.notificationService.getMyNotifications(userId, notificationQueryParams);
  }

  @Get('view-as/:userId/count-unread')
  async countMyUnreadNotificationLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.notificationService.countMyUnreadNotification(userId);
  }

  @Get('count-unread')
  async countMyUnreadNotification(@Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.notificationService.countMyUnreadNotification(userId);
  }

  @Patch(':id/update')
  async updateNotification(@Param('id') id: string, @Body() updateNotificationDto: UpdateNotificationDto) {
    return this.notificationService.updateNotification(id, updateNotificationDto);
  }

  @Post('mark-all-read')
  async markAllReadNotifications(@Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.notificationService.markAllReadNotifications(userId);
  }

  @Post('mark-all-read/view-as/:userId')
  @UseGuards(AuthenticationGuard)
  async markAllReadNotificationsViewAsLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.notificationService.markAllReadNotifications(userId);
  }
}

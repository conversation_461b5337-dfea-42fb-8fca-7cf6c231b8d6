import { UserEntity } from 'src/modules/user/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum NotificationEnum {
  ASSIGNED_LEAD = 'ASSIGNED_LEAD',
  LINKEDIN_DISCONNECTED = 'LINKEDIN_DISCONNECTED',
  SENT_JOB = 'SENT_JOB',
  BULK_ADD_TO_BULLHORN = 'BULK_ADD_TO_BULLHORN',
  ONBOARDING_COMPANY_REQUEST = 'ONBOARDING_COMPANY_REQUEST',
}

@Entity('notifications')
export class NotificationEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'notification_type', type: 'enum', enum: NotificationEnum })
  notificationType: NotificationEnum;

  @Column({ name: 'is_read', default: false })
  isRead: boolean;

  @Column({ name: 'lead_id', nullable: true })
  leadId: string;

  //notifications of user_id
  @Column({ name: 'user_id', nullable: true })
  userId: string;

  @ManyToOne(() => UserEntity, (user) => user.myNotifications, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id', foreignKeyConstraintName: 'user-notification' })
  user: UserEntity;

  @Column({ name: 'creator_id', nullable: true })
  creatorId: string;

  @ManyToOne(() => UserEntity, (user) => user.creatorNotifications)
  @JoinColumn({ name: 'creator_id', foreignKeyConstraintName: 'creator-notification' })
  creator: UserEntity;

  @Column({ name: 'title', nullable: true })
  title: string;

  @Column({ name: 'content', nullable: true })
  content: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ name: 'target_id', nullable: true }) // target object id of the notification
  targetId: string;
}

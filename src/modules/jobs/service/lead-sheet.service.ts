import { LeadStatusesRepository } from './../repository/lead-statuses.repository';
import { JobLeadsRepository } from './../repository/job-leads.repository';
import { UserRepository } from './../../user/repositories/user.repository';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { LeadSheetRepository } from '../repository/lead-sheet.repository';
import { CreateLeadSheetDto, GetLeadSheetQueryDto, UpdateLeadSheetDto } from '../dto/lead-sheet/lead-sheet.dto';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { DataSource, FindOptionsWhere, In } from 'typeorm';
import { LeadSheetEntity } from '../entities/lead-sheet.entity';
import { LeadStatus } from '../entities/lead-statuses.entity';
import { JobLead } from '../entities/job-leads.entity';

@Injectable()
export class LeadSheetService extends BaseAbstractService {
  constructor(
    private readonly i18nService: I18nService,
    private readonly leadSheetRepository: LeadSheetRepository,
    private readonly dataSource: DataSource,
    private readonly userRepository: UserRepository,
    private readonly jobLeadsRepository: JobLeadsRepository,
    private readonly leadStatusesRepository: LeadStatusesRepository
  ) {
    super(i18nService);
  }

  async isDuplicatedLeadSheet({ name, userId }: { name: string; userId: string }) {
    const existingLeadSheet = await this.leadSheetRepository.findOneBy({ name, userId });

    return !!existingLeadSheet;
  }

  async create(bodyDto: CreateLeadSheetDto) {
    const { name, leadStatusType, updateFor } = bodyDto;
    const isDuplicatedSheet = await this.isDuplicatedLeadSheet({ name, userId: updateFor });
    if (isDuplicatedSheet) {
      return this.throwCommonMessage('CREATE_LEAD_SHEET', new BadRequestException('Lead sheet is already existing'));
    }

    const record = await this.leadSheetRepository.insert({ name, leadStatusType, userId: updateFor });

    return this.formatOutputData(
      { key: 'CREATE_LEAD_SHEET' },
      { data: { ...bodyDto, id: record.identifiers?.[0]?.id } }
    );
  }

  async getMany(queryDto: GetLeadSheetQueryDto) {
    const { leadStatusType, userId } = queryDto;

    const condition: FindOptionsWhere<LeadSheetEntity> = {
      userId,
    };
    if (leadStatusType) {
      condition.leadStatusType = leadStatusType;
    }

    const data = await this.leadSheetRepository.findBy(condition);

    return this.formatOutputData({ key: 'GET_LEAD_SHEETS' }, { data });
  }

  async update(id: string, bodyDto: UpdateLeadSheetDto) {
    const leadSheet = await this.leadSheetRepository.findOneBy({ id });
    if (!leadSheet) {
      return this.throwCommonMessage('UPDATE_LEAD_SHEET', new NotFoundException('Lead sheet not found'));
    }

    const isDuplicatedSheet = await this.isDuplicatedLeadSheet({ name: bodyDto.name, userId: leadSheet.userId });
    if (isDuplicatedSheet && !bodyDto.userId) {
      return this.throwCommonMessage('UPDATE_LEAD_SHEET', new BadRequestException('Lead sheet is already existing'));
    }

    if (bodyDto.userId) {
      const user = await this.userRepository.findOne({ where: { id: bodyDto.userId } });
      if (!user) {
        return this.throwCommonMessage('UPDATE_LEAD_SHEET', new NotFoundException('User not found'));
      }

      const leadStatus = await this.leadStatusesRepository.find({ where: { leadSheetId: id } });
      const leadStatusIds = leadStatus.map((leadStatus) => leadStatus.id);

      await this.leadSheetRepository.update(id, { name: bodyDto.name, userId: user.id });
      if (leadStatusIds.length > 0) {
        await this.jobLeadsRepository.update(
          { lead_status_id: In(leadStatusIds) },
          { creatorId: user.id }
        );
      }
    } else {
      await this.leadSheetRepository.update(id, { name: bodyDto.name });
    }

    return this.formatOutputData({ key: 'UPDATE_LEAD_SHEET' }, { data: { id, ...bodyDto } });
  }

  async delete(id: string) {
    const leadStatuses = await this.dataSource
      .createQueryBuilder(LeadStatus, 'ls')
      .where({ leadSheetId: id })
      .select('id')
      .getRawMany<{ id: string }>();
    const leadStatusIds = leadStatuses.map((item) => item.id);

    if (leadStatusIds.length) {
      await this.dataSource
        .createQueryBuilder(LeadStatus, 'ls')
        .update()
        .where({ leadSheetId: id })
        .set({ isDeleted: true, leadSheetId: null })
        .execute();

      await this.dataSource
        .createQueryBuilder(JobLead, 'jl')
        .softDelete()
        .where({ lead_status_id: In(leadStatusIds) })
        .execute();
    }

    await this.leadSheetRepository.delete(id);

    return this.formatOutputData({ key: 'DELETE_LEAD_SHEET' }, { data: {} });
  }
}

import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { DataSource, EntityManager, FindOptionsWhere, In, IsNull, Not, Repository } from 'typeorm';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { NotificationEnum } from 'src/modules/notification/entities/notification.entity';
import { UserRepository } from 'src/modules/user/repositories/user.repository';
import { SentJobEntity, StatusSentJobEnum } from 'src/modules/jobs/entities/sent-jobs.entity';
import { BullhornIntegrationService } from './bullhorn-integration.service';
import { NotificationService } from '../../notification/services/notification.service';
import { LeadStatusesRepository } from '../repository/lead-statuses.repository';
import { JobLeadsRepository } from '../repository/job-leads.repository';
import {
  ChangeJobLeadStatusDto,
  ChangeLeadStatusByCompanyDto,
} from '../dto/change-job-lead-status-request.dto';
import { BulkCreateJobLeadRequestDto, CreateJobLeadDto } from '../dto/create-job-lead-request.dto';
import { LeadStatus, LeadStatusType } from '../entities/lead-statuses.entity';
import { LeadQueryDto } from '../dto/job-lead/lead-query.dto';
import { StaredJobLeadDto, UpdateLeadDto, UpdatePriorityDto } from '../dto/job-lead/job-lead.dto';
import { PREFIX } from '../constants/bullhorn.constant';
import { CreateJobSentDto } from '../dto/create-job-sent.dto';
import { JobBoardsRepository } from '../repository/job-boards.repository';
import { SentJobRepository } from '../repository/sent-job.repository';
import { DeleteJobLeadsDto } from '../dto/job-lead/delete-leads.dto';
import {
  ChangeStatusLeadsByNameDto,
  ChangeStatusLeadsDto,
} from '../dto/job-lead/change-status-leads.dto';
import { SearchVacanciesDto } from '../dto/job-lead/search-vacancies.dto';
import * as stringSimilarity from 'string-similarity';
import { OpensearchService } from 'src/modules/opensearch/service/opensearch.service';
import { SimilarJobDto } from '../dto/similar-job.dto';
import { JobLead } from '../entities/job-leads.entity';
import { LeadSheetEntity } from '../entities/lead-sheet.entity';
import {
  convertCamelToNormalCase,
  extractJobKeywords,
  standardizeJobType,
} from 'src/common/utils/helpers.util';
import { JobLeadKeywordRepository } from '../repository/job-lead-keywords.repository';
import { JobLeadKeyword } from '../entities/job-lead-keywords.entity';
import { JobLeadKeywordDto, JobLeadStatsDto } from '../dto/job-lead/job-lead-stats.dto';
import { LeadSheetRepository } from '../repository/lead-sheet.repository';
import { StatsEntityDataRepository } from '../repository/stats-entity-data.repository';
import { InjectEntityManager } from '@nestjs/typeorm';
import { JOBS_INDEX } from 'src/configs/configs.constants';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';
import { DEFAULT_LEAD_SHEET_NAME } from '../enums/job-lead.enum';
import { OpenAIService } from 'src/modules/openai/openai.service';
import { SequenceEntity } from 'src/modules/mail/entities/sequence.entity';

@Injectable()
export class JobLeadsService extends BaseAbstractService {
  constructor(
    @InjectEntityManager() private readonly entityManager: EntityManager, // Inject EntityManager
    private readonly leadStatusRepository: LeadStatusesRepository,
    private readonly jobLeadRepository: JobLeadsRepository,
    private readonly jobBoardRepository: JobBoardsRepository,
    private readonly jobLeadSheetRepository: LeadSheetRepository,
    private readonly jobLeadKeywordRepository: JobLeadKeywordRepository,
    private i18nService: I18nService,
    private dataSource: DataSource,
    private notificationService: NotificationService,
    private sentJobRepository: SentJobRepository,
    private readonly userRepository: UserRepository,
    @Inject(forwardRef(() => BullhornIntegrationService))
    private readonly bullhornIntegrationService: BullhornIntegrationService,
    private readonly elasticsearchService: OpensearchService,
    private readonly statsEntityDataRepository: StatsEntityDataRepository,
    private readonly openAIService: OpenAIService,
  ) {
    super(i18nService);
  }

  async getOrganizationId(request) {
    const loginUserId = request.user.id;
    const viewAsUserId = request.headers?.['view-as-user-id'] as string;
    if ((!viewAsUserId || viewAsUserId === loginUserId) && request.user?.organizationId) {
      return request.user?.organizationId || 0;
    }

    const organization = await this.dataSource
      .createQueryBuilder(UserEntity, 'u')
      .where({ id: viewAsUserId })
      .select('"organizationId" as "organizationId"')
      .getRawOne<{ organizationId: string }>();

    const organizationId = organization?.organizationId || 0;

    return organizationId;
  }

  async getAllJobsLeads({
    userFromPayload,
    userId,
  }: {
    userFromPayload?: IJwtPayload;
    userId?: string;
  }) {
    let user = userFromPayload;

    if (userId) {
      const latestUser = await this.dataSource
        .createQueryBuilder(UserEntity, 'u')
        .where('u.id = :userId', { userId })
        .getOne();

      user = {
        ...user,
        id: userId,
        organizationId: latestUser.organizationId,
      };
    }

    const data = await this.leadStatusRepository.find({
      relations: { jobLeads: true },
      where: { organizationId: user.organizationId ?? IsNull() },
    });

    return this.formatOutputData({ key: 'GET_ALL_JOB_LEADS' }, { data });
  }

  async getLeadsByStatusId(statusId: string, queryDto: LeadQueryDto) {
    const { page = 1, limit = 10 } = queryDto;

    const leads = await this.jobLeadRepository.find({
      where: { lead_status_id: statusId },
      take: limit,
      skip: (page - 1) * limit,
      order: {
        date_added: 'DESC',
      },
    });

    return this.formatOutputData(
      { key: 'GET_LEADS_BY_STATUS_ID' },
      { data: { items: leads, currentPage: Number(page), pageSize: Number(limit) } },
    );
  }

  async getLeadsByStatusIdInViewAs(statusId: string, queryDto: LeadQueryDto, userId: string) {
    const { page = 1, limit = 10 } = queryDto;

    const leads = await this.jobLeadRepository.find({
      where: [
        { lead_status_id: statusId, creatorId: userId },
        { lead_status_id: statusId, assigneeId: userId },
      ],
      take: limit,
      skip: (page - 1) * limit,
      order: {
        date_added: 'DESC',
      },
    });

    return this.formatOutputData(
      { key: 'GET_LEADS_BY_STATUS_ID' },
      { data: { items: leads, currentPage: Number(page), pageSize: Number(limit) } },
    );
  }

  async getLeadsCompanyByStatusIdInViewAs(
    statusId: string,
    queryDto: LeadQueryDto,
    userId: string,
  ) {
    const { page = 1, limit = 10, search = '' } = queryDto;
    const queryBuilder = this.jobLeadRepository
      .createQueryBuilder('jl')
      .select([
        'jl.company_name',
        'MAX(jl.date_added) AS max_date_added',
        'MAX(jl.company_id) as company_id',
        'COUNT(*) as total_lead',
        'MAX(jl.logo_company) as "logoCompany"',
      ])
      .where(
        '((jl.lead_status_id = :statusId AND jl.creator_id = :userId) OR (jl.lead_status_id = :statusId AND jl.assignee_id = :userId))',
        { statusId, userId },
      )
      .andWhere('(jl.is_done = true or jl.creator_id = :userId)')
      .groupBy('jl.company_name')
      .orderBy('max_date_added', 'DESC')
      .take(limit)
      .skip((page - 1) * limit);

    if (search) {
      queryBuilder.andWhere('jl.company_name ILIKE :search', { search: `%${search}%` });
    }

    const [statusLead, companies] = await Promise.all([
      this.leadStatusRepository.findOne({ where: { id: statusId } }),
      queryBuilder.getRawMany(),
    ]);
    const companiesOrder = statusLead.orderedCompanies ?? [];

    const sortedCompanies = companies.sort((a, b) => {
      const indexA = companiesOrder.indexOf(a.company_id);
      const indexB = companiesOrder.indexOf(b.company_id);

      if (indexA === -1 && indexB === -1) {
        return 0;
      }
      if (indexA === -1) {
        return 1;
      }
      if (indexB === -1) {
        return -1;
      }
      return indexA - indexB;
    });

    return this.formatOutputData(
      { key: 'GET_LEADS_COMPANY_BY_STATUS_ID' },
      { data: { items: sortedCompanies, currentPage: Number(page), pageSize: Number(limit) } },
    );
  }

  async getMyLeadCompanies(queryDto: LeadQueryDto, userId: string) {
    const { page = 1, limit = 10, search = '' } = queryDto;

    const queryBuilder = this.jobLeadRepository
      .createQueryBuilder('jl')
      .select([
        'jl.company_name',
        'MAX(jl.date_added) AS max_date_added',
        'MAX(jl.company_id) as company_id',
        'MAX(jl.logo_company) as "logoCompany"',
      ])
      .where('(jl.creator_id = :userId OR jl.assignee_id = :userId)', { userId })
      .groupBy('jl.company_name')
      .orderBy('max_date_added', 'DESC')
      .take(limit)
      .skip((page - 1) * limit);

    if (search) {
      queryBuilder.andWhere('jl.company_name ILIKE :search', { search: `%${search}%` });
    }

    const companies = await queryBuilder.getRawMany();

    return this.formatOutputData(
      { key: 'GET_MY_LEAD_COMPANIES' },
      { data: { items: companies, currentPage: Number(page), pageSize: Number(limit) } },
    );
  }

  async getLeadsInMailBox(queryDto: LeadQueryDto, userId: string) {
    const { page = 1, limit = 10 } = queryDto;

    const data = await this.jobLeadRepository.find({
      where: { assigneeId: userId, isDone: false, creatorId: Not(userId) },
      relations: {
        assigner: true,
      },
      select: {
        assigner: {
          id: true,
          fullName: true,
          username: true,
          consultantName: true,
        },
      },

      order: { assignee_at: 'desc' },
      take: limit,
      skip: (page - 1) * limit,
    });

    const dataLeads = data?.map((item: any) => ({
      ...item,
      jobType: convertCamelToNormalCase(
        standardizeJobType(
          item?.jobType?.replace('{', '').replace('}', '').replace(/\"/g, ''),
        ).join(', '),
      ),
    }));

    return this.formatOutputData(
      { key: 'GET_LEADS_MAIL_BOX_BY_BY_USER_ID' },
      { data: { items: dataLeads, currentPage: Number(page), pageSize: Number(limit) } },
    );
  }

  async countLeadsInMailBox(userId: string) {
    const data = await this.jobLeadRepository.count({
      where: { assigneeId: userId, isDone: false, creatorId: Not(userId) },
    });

    const dataSentJob = await this.sentJobRepository.count({
      where: { receiverId: userId, status: StatusSentJobEnum.NOT_SENT },
    });

    return this.formatOutputData(
      { key: 'COUNT_LEADS_IN_MAIL_BOX' },
      { data: String(data + dataSentJob) },
    );
  }

  async updateLeadInMailBox(userId: string, leadId: string, req: any) {
    const data = await this.jobLeadRepository.findOne({
      where: { assigneeId: userId, id: leadId },
    });

    if (!data) {
      throw new NotFoundException('Lead Not Found');
    }

    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User Not Found');
    }

    const leadStatus = await this.leadStatusRepository
      .createQueryBuilder('leadStatus')
      .where('leadStatus.organizationId = :organizationId', { organizationId: user.organizationId })
      .orderBy('leadStatus.orderOfDisplay', 'ASC')
      .getOne();

    await this.jobLeadRepository.update(
      { id: leadId },
      { isDone: true, lead_status_id: leadStatus.id },
    );

    const consultantId = user.consultantId;
    if (consultantId) {
      const bullhornLeadId = data.job_lead_external_id.split(`${PREFIX.JOB_ORDER}-`)[1];
      await this.bullhornIntegrationService.updateBullhorn(
        req,
        {
          entityName: 'JobOrder',
          owner: {
            id: consultantId,
          },
        },
        bullhornLeadId,
      );
    }

    return this.formatOutputData({ key: 'UPDATE_MAIL_BOX_LEADS_BY_ID' }, { data: { items: {} } });
  }

  async createSentJob(jobLeadDto: CreateJobSentDto) {
    const dataJob = await this.elasticsearchService.getById(JOBS_INDEX, jobLeadDto.jobBoardId);

    if (!dataJob) {
      throw new NotFoundException('Job Not Found');
    }

    const userCount = await this.userRepository.count({ where: { id: In(jobLeadDto.sentToUser) } });
    if (userCount == 0) {
      throw new NotFoundException('Users Not Found');
    }

    const checkUserInJob = await this.sentJobRepository.find({
      where: { receiverId: In(jobLeadDto.sentToUser), jobBoardId: jobLeadDto.jobBoardId },
    });

    if (checkUserInJob.length === jobLeadDto.sentToUser.length) {
      throw new BadRequestException('All user have sent this Job');
    }

    const filteredUsers = jobLeadDto.sentToUser.filter(
      (userId) => !checkUserInJob.some((sentJob) => sentJob.receiverId === userId),
    );

    const createData: SentJobEntity[] = filteredUsers?.map((data: string) => ({
      jobBoardId: jobLeadDto.jobBoardId,
      companyName: dataJob.company,
      title: dataJob.jobtitle,
      location: dataJob.address || dataJob.joblocationcity,
      dateAdded: dataJob?.posted,
      receiverId: data,
      jobType: dataJob.jobtype,
      salary: dataJob.salary,
      status: StatusSentJobEnum.NOT_SENT,
    }));
    await this.sentJobRepository.insert(createData);

    filteredUsers?.forEach(
      async (userId: string) =>
        await this.notificationService.createNotification({
          leadId: jobLeadDto.jobBoardId,
          notificationType: NotificationEnum.SENT_JOB,
          userId,
          creatorId: jobLeadDto.senderId,
        }),
    );

    return this.formatOutputData({ key: 'CREATE_LEADS_SENT_JOB' }, { data: { items: {} } });
  }

  async deleteSentJob(id: string) {
    try {
      await this.sentJobRepository.softDelete({ id });
      return this.formatOutputData({ key: 'DELETE_SENT_JOB' }, { data: {} });
    } catch (error) {
      console.error('Error in deleteSentJob', error);
      return this.throwCommonMessage('DELETE_SENT_JOB', { data: {} });
    }
  }

  async getSentJob(userId: string) {
    const user = await this.userRepository.find({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException('User Not Found');
    }

    const dataUser = await this.sentJobRepository.find({
      where: { receiverId: userId, status: StatusSentJobEnum.NOT_SENT },
    });

    const dataSentJob = dataUser.map((item: any) => ({
      ...item,
      jobType: convertCamelToNormalCase(
        standardizeJobType(
          item?.jobType?.replace('{', '').replace('}', '').replace(/\"/g, ''),
        ).join(', '),
      ),
    }));

    return this.formatOutputData({ key: 'GET_LEADS_SENT_JOB' }, { data: { items: dataSentJob } });
  }

  async getJobDetail(jobId: string) {
    const data = await this.elasticsearchService.getById(JOBS_INDEX, jobId);
    if (!data) {
      throw new BadRequestException('Job Not Found');
    }

    const jobSentData = await this.sentJobRepository.findOne({ where: { jobBoardId: jobId } });

    const response = {
      ...jobSentData,
      ...data,
    };

    return this.formatOutputData({ key: 'GET_LEADS_JOB_DETAIL' }, { data: { item: response } });
  }

  async getLeadsJobCompanyByStatusIdInViewAs(
    statusId: string,
    userId: string,
    rawCompanyId: string,
  ) {
    const companyId = rawCompanyId.split(PREFIX.CORPORATE)?.[1]?.split('-')?.[1];
    const companies = await this.jobLeadRepository
      .createQueryBuilder('job')
      .where(
        '((job.lead_status_id = :statusId AND job.creator_id = :userId) OR (job.lead_status_id = :statusId AND job.assignee_id = :userId))',
        { statusId, userId },
      )
      .andWhere('job.company_id = :companyId', { companyId: rawCompanyId })
      .andWhere('(job.is_done = true or job.creator_id = :userId)')
      .orderBy('job.date_added', 'DESC')
      .getMany();

    return this.formatOutputData(
      { key: 'GET_JOB_LEADS_COMPANY_BY_STATUS_ID' },
      { data: { items: companies, companyId } },
    );
  }

  async getLeadsByCompany(userId: string, companyId: string) {
    const companies = await this.jobLeadRepository
      .createQueryBuilder('job')
      .where('( job.creator_id = :userId OR job.assignee_id = :userId)', { userId })
      .andWhere('job.company_id = :companyId', { companyId })
      .orderBy('job.date_added', 'DESC')
      .getMany();

    return this.formatOutputData(
      { key: 'GET_LEADS_BY_COMPANY' },
      { data: { items: companies, companyId } },
    );
  }

  async changeJobLeadStatus(changeJobLeadStatusDto: ChangeJobLeadStatusDto, loginUserId: string) {
    const userId = changeJobLeadStatusDto.updatedFor ?? loginUserId;
    const { jobLeadId, newStatusId } = changeJobLeadStatusDto;
    if (changeJobLeadStatusDto.isUpdateMailBox) {
      await this.jobLeadRepository.update(
        { id: jobLeadId },
        { lead_status_id: newStatusId, updatedBy: userId, isDone: true },
      );

      return this.formatOutputData({ key: 'CHANGE_JOB_LEAD_STATUS' }, { data: {} });
    }
    await this.jobLeadRepository.update(
      { id: jobLeadId },
      { lead_status_id: newStatusId, updatedBy: userId },
    );

    return this.formatOutputData({ key: 'CHANGE_JOB_LEAD_STATUS' }, { data: {} });
  }

  async changeJobLeadStatusByCompany(
    changedLeadStatusByCompanyDto: ChangeLeadStatusByCompanyDto,
    loginUserId: string,
  ) {
    const userId = changedLeadStatusByCompanyDto.updatedFor ?? loginUserId;
    const {
      companyId,
      newStatusId,
      oldStatusId,
      oldStatusCompanyIdsOrder,
      newStatusCompanyIdsOrder,
    } = changedLeadStatusByCompanyDto;

    await this.jobLeadRepository
      .createQueryBuilder('jl')
      .where('(creator_id = :userId OR assignee_id = :userId)', { userId })
      .andWhere('company_id = :companyId', { companyId })
      .andWhere('(is_done = true or creator_id = :userId)')
      .update()
      .set({ lead_status_id: newStatusId, updatedBy: userId })
      .execute();

    if (newStatusId && newStatusCompanyIdsOrder) {
      await this.leadStatusRepository.update(newStatusId, {
        orderedCompanies: newStatusCompanyIdsOrder,
      });
    }

    if (oldStatusId && oldStatusCompanyIdsOrder) {
      await this.leadStatusRepository.update(oldStatusId, {
        orderedCompanies: oldStatusCompanyIdsOrder,
      });
    }

    return this.formatOutputData({ key: 'CHANGE_JOB_LEAD_STATUS' }, { data: {} });
  }

  async deleteJobLead(jobLeadId: string) {
    await this.jobLeadRepository.softDelete({ id: jobLeadId });

    return this.formatOutputData({ key: 'DELETE_JOB_LEAD' }, { data: {} });
  }

  async getUserById(id: string) {
    return this.dataSource.createQueryBuilder(UserEntity, 'u').where({ id }).getOne();
  }

  async insertJobLeadKeyword(
    createJobLeadDtoArr: CreateJobLeadDto[],
    userId: string,
    keywordRepository,
  ) {
    const listKeyWords = createJobLeadDtoArr.map((item) => item.title);
    const date = new Date().toISOString().split('T')[0];

    const listAddressCountry = createJobLeadDtoArr.map((item) => item?.address_country);
    const jobLeadKeywords = await keywordRepository.find({
      where: {
        keyword: In(listKeyWords),
        date,
        userId: userId,
        country: In(listAddressCountry),
      },
    });

    const existingKeywordCountryMap = new Map<string, { country: string; count: number }[]>();
    jobLeadKeywords.forEach((item) => {
      const key = `${item.keyword}`;
      if (!existingKeywordCountryMap.has(key)) {
        existingKeywordCountryMap.set(key, []);
      }
      existingKeywordCountryMap.get(key)!.push({ country: item.country, count: item.count });
    });

    const updateConditions = listKeyWords
      .map((keyword) => {
        const existingEntries = existingKeywordCountryMap.get(keyword) || [];
        const existingCountries = existingEntries.map((entry) => entry.country);
        return existingCountries.length > 0
          ? { keyword, date, userId, country: In(existingCountries) }
          : null;
      })
      .filter(Boolean);

    if (updateConditions.length > 0) {
      await keywordRepository
        .createQueryBuilder()
        .update(JobLeadKeyword)
        .set({ count: () => 'count + 1' })
        .where(updateConditions)
        .execute();
    }

    const insertsKeyword = listKeyWords
      .flatMap((keyword) => {
        const existingEntries = existingKeywordCountryMap.get(keyword) || [];
        const existingCountries = existingEntries.map((entry) => entry.country);
        const countriesToInsert = listAddressCountry.filter(
          (country) => !existingCountries.includes(country),
        );
        return countriesToInsert.map((country) => ({
          keyword,
          date,
          userId: userId,
          country,
          count: 1,
        }));
      })
      .filter((entry) => entry);

    if (insertsKeyword.length > 0) {
      await keywordRepository.insert(insertsKeyword);
    }
  }

  async bulkInsertJobLead(user: UserEntity, createJobLeadDtoArr: CreateJobLeadDto[]) {
    let { organizationId, id: userId } = user;
    const leadSheetIds = new Set(createJobLeadDtoArr.map((dto) => dto.leadSheetId));

    const existingLeadStatuses = leadSheetIds.size
      ? await this.leadStatusRepository.find({
          where: { leadSheetId: In([...leadSheetIds]), organizationId: Not(IsNull()) },
          order: { orderOfDisplay: 'ASC' },
        })
      : [];

    const leadStatusMap = new Map<string, string>(); // { leadSheetId: leadStatusId }
    existingLeadStatuses.forEach((status) => {
      if (!leadStatusMap.has(status.leadSheetId)) {
        leadStatusMap.set(status.leadSheetId, status.id);
      }
    });

    const leadSheetsWithoutStatus = [...leadSheetIds].filter((id) => !leadStatusMap.has(id));

    const leadStatusesToInsert = leadSheetsWithoutStatus.map((leadSheetId) => ({
      name: 'Lead',
      orderOfDisplay: 1,
      organizationId: organizationId ?? null,
      color: '#000000',
      updatedBy: userId,
      leadSheetId,
    }));
    return this.entityManager.transaction(async (transactionalEntityManager) => {
      await transactionalEntityManager.query(
        "SET SESSION idle_in_transaction_session_timeout = '3min';",
      );
      await transactionalEntityManager.query('SET SESSION statement_timeout = 45000;');
      // Set idle_in_transaction_session_timeout/statement_timeout(3min & 45s) for this transaction session
      const leadStatusRepository = transactionalEntityManager.getRepository(LeadStatus);
      const jobLeadRepository = transactionalEntityManager.getRepository(JobLead);
      const jobLeadKeywordRepository = transactionalEntityManager.getRepository(JobLeadKeyword);

      if (leadStatusesToInsert.length > 0) {
        const createdLeadStatuses = await leadStatusRepository.insert(leadStatusesToInsert);
        createdLeadStatuses.raw.forEach((raw, index) => {
          leadStatusMap.set(leadSheetsWithoutStatus[index], raw.id);
        });
      }

      createJobLeadDtoArr.forEach((dto) => {
        if (dto.leadSheetId) {
          dto.lead_status_id = leadStatusMap.get(dto.leadSheetId) || null;
        }
      });

      await this.insertJobLeadKeyword(createJobLeadDtoArr, userId, jobLeadKeywordRepository);

      await jobLeadRepository.insert(createJobLeadDtoArr);
    });
  }

  async createJobLead(
    userJwtPayload: IJwtPayload,
    createJobLeadDto: CreateJobLeadDto,
    transactionalEntityManager?: EntityManager,
  ) {
    if (!transactionalEntityManager) {
      transactionalEntityManager = this.entityManager; // Use the injected EntityManager if not provided
    }
    const leadStatusRepository = transactionalEntityManager.getRepository(LeadStatus);
    const jobLeadRepository = transactionalEntityManager.getRepository(JobLead);
    const jobLeadKeywordRepository = transactionalEntityManager.getRepository(JobLeadKeyword);
    const leadSheetRepository = transactionalEntityManager.getRepository(LeadSheetEntity);
    const userId = userJwtPayload.id;

    await this.insertJobLeadKeyword([createJobLeadDto], userId, jobLeadKeywordRepository);
    await this.updateJobLeadDtoForInserting(
      userJwtPayload,
      createJobLeadDto,
      leadStatusRepository,
      leadSheetRepository,
    );

    return jobLeadRepository.save(jobLeadRepository.create(createJobLeadDto));
  }

  async bulkCreateJobLead(
    userJwtPayload: IJwtPayload,
    bulkCreateJobLeadRequestDto: BulkCreateJobLeadRequestDto,
    transactionalEntityManager?: EntityManager,
  ) {
    if (!transactionalEntityManager) {
      transactionalEntityManager = this.entityManager; // Use the injected EntityManager if not provided
    }

    const leadStatusRepository = transactionalEntityManager.getRepository(LeadStatus);
    const jobLeadRepository = transactionalEntityManager.getRepository(JobLead);
    const jobLeadKeywordRepository = transactionalEntityManager.getRepository(JobLeadKeyword);
    const leadSheetRepository = transactionalEntityManager.getRepository(LeadSheetEntity);
    const userId = userJwtPayload.id;
    const user = await this.getUserById(userId);

    const { data: jobLeadsToCreate } = bulkCreateJobLeadRequestDto;

    await this.insertJobLeadKeyword(jobLeadsToCreate, userId, jobLeadKeywordRepository);

    const { leadSheet, leadStatus } = await this.getDefaultLeadSheetAndStatus(
      user,
      leadStatusRepository,
      leadSheetRepository,
    );
    const createdJobLeads: CreateJobLeadDto[] = await Promise.all(
      jobLeadsToCreate.map(async (jobLead) => {
        const newJobLead: CreateJobLeadDto = {
          ...jobLead,
          lead_status_id: leadStatus.id,
          leadSheetId: leadSheet.id,
          dataKey: userId,
          creatorId: userId, // creator and dataKey are different in the way they user...later on, creator could be updatable
          skills: jobLead.skills?.length
            ? jobLead.skills
            : await this.openAIService.generateSkillByDescription(jobLead.description),
        };
        return newJobLead;
      }),
    );
    await jobLeadRepository.insert(createdJobLeads);

    return this.formatOutputData(
      { key: 'BULK_CREATE_JOB_LEAD' },
      { data: { items: createdJobLeads } },
    );
  }

  async updateJobLeadDtoForInserting(
    userJwtPayload: IJwtPayload,
    createJobLeadDto: CreateJobLeadDto,
    leadStatusRepository: Repository<LeadStatus>,
    leadSheetRepository: Repository<LeadSheetEntity>,
  ) {
    let { organizationId, id: userId } = userJwtPayload;
    if (createJobLeadDto.updatedFor) {
      userId = createJobLeadDto.updatedFor;
      const user = await this.getUserById(userId);
      organizationId = user.organizationId;
    }

    if (!createJobLeadDto.lead_status_id) {
      // First status
      const condition: FindOptionsWhere<LeadStatus> = {
        isDeleted: false,
      };
      if (organizationId) {
        condition.organizationId = organizationId;
      } else {
        condition.organizationId = IsNull();
      }

      if (createJobLeadDto.leadSheetId) {
        condition.leadSheetId = createJobLeadDto.leadSheetId;
      }

      const firstLeadStatus = await leadStatusRepository.findOne({
        order: {
          orderOfDisplay: 'ASC',
        },
        where: condition,
      });

      if (!firstLeadStatus) {
        const leadSheet = createJobLeadDto.leadSheetId
          ? await this.dataSource
              .createQueryBuilder(LeadSheetEntity, 'ls')
              .where({ id: createJobLeadDto.leadSheetId })
              .getOne()
          : await this.getDefaultLeadSheet(userId, leadSheetRepository);

        const createdLeadStatus = await leadStatusRepository.insert({
          name: 'Lead',
          orderOfDisplay: 1,
          organizationId: organizationId ?? null,
          color: '#f8f6e3',
          updatedBy: userId,
          leadSheetId: leadSheet.id,
          leadStatusType: leadSheet.leadStatusType,
        });

        createJobLeadDto.lead_status_id = createdLeadStatus.raw[0].id;
      } else {
        createJobLeadDto.lead_status_id = firstLeadStatus.id;
      }
    }

    createJobLeadDto.dataKey = userId;
    createJobLeadDto.creatorId = userId; // creator and dataKey are different in the way they user...later on, creator could be updatable
  }

  async getDefaultLeadSheet(
    userId: string,
    jobLeadSheetRepository: Repository<LeadSheetEntity>,
  ): Promise<LeadSheetEntity> {
    const defaultLeadSheetName = DEFAULT_LEAD_SHEET_NAME;
    const existingLeadSheet = await this.jobLeadSheetRepository.findOne({
      where: { name: defaultLeadSheetName, userId },
    });
    if (existingLeadSheet) {
      return existingLeadSheet;
    }
    // If no existing lead sheet, create a new one
    const leadSheet = jobLeadSheetRepository.create({
      name: defaultLeadSheetName,
      leadStatusType: LeadStatusType.LEAD,
      userId,
    });
    const createdLeadSheet = await jobLeadSheetRepository.save(leadSheet);
    return createdLeadSheet;
  }

  async getDefaultLeadSheetAndStatus(
    user: UserEntity,
    leadStatusRepository: Repository<LeadStatus>,
    jobLeadSheetRepository: Repository<LeadSheetEntity>,
  ): Promise<{ leadSheet: LeadSheetEntity; leadStatus: LeadStatus }> {
    const leadSheet = await this.getDefaultLeadSheet(user.id, jobLeadSheetRepository);

    const firstLeadStatus = await leadStatusRepository.findOne({
      order: {
        orderOfDisplay: 'ASC',
      },
      where: { leadSheetId: leadSheet.id, isDeleted: false, organizationId: user.organizationId },
    });

    if (firstLeadStatus) {
      return { leadSheet, leadStatus: firstLeadStatus };
    }

    const createdLeadStatus = await leadStatusRepository.save({
      name: 'Lead',
      orderOfDisplay: 1,
      organizationId: user.organizationId ?? null,
      color: '#f8f6e3',
      updatedBy: user.id,
      leadSheetId: leadSheet.id,
      leadStatusType: leadSheet.leadStatusType,
    });

    return { leadSheet, leadStatus: createdLeadStatus };
  }

  async countLeads(id: string, query: JobLeadStatsDto) {
    const { country, fromDate, toDate } = query;
    const whereConditions = [];
    if (country) {
      whereConditions.push(['jl.address_country = :country', { country }]);
    }
    if (fromDate) {
      whereConditions.push(['(jl.date_added >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(jl.date_added <= :toDate)', { toDate }]);
    }

    const selectFields = `
        sum(case when jl.job_board_id is null then 1 else 0 END) as manual_lead_num,
        sum(case when jl.job_board_id is not null then 1 else 0 END) as sync_lead_num,
        sum(case when jl.job_board_id is null and jl.email is not null then 1 else 0 END) as email_manual_lead_num,
        sum(case when jl.job_board_id is not null and jl.email is not null then 1 else 0 END) as email_sync_lead_num,
        sum(case when jl.job_board_id is not null and jl.company_contact_id is not null then 1 else 0 END) as contact_added_num
    `;

    let baseQuery = this.jobLeadRepository.createQueryBuilder('jl').where({ dataKey: id });

    // Apply dynamic where conditions
    whereConditions.forEach((condition) => {
      baseQuery = baseQuery.andWhere(condition[0], condition[1]);
    });

    const totalCounts = await baseQuery.select(selectFields).getRawOne<{
      manual_lead_num: number;
      sync_lead_num: number;
      email_manual_lead_num: number;
      email_sync_lead_num: number;
      contact_added_num: number;
    }>();

    const monthlyCounts = await baseQuery
      .select([`DATE_TRUNC('month', jl.updated_at) as month`, selectFields])
      .andWhere("jl.updated_at >= NOW() - INTERVAL '7 MONTHS'")
      .groupBy("DATE_TRUNC('month', jl.updated_at)")
      .orderBy("DATE_TRUNC('month', jl.updated_at)", 'ASC')
      .getRawMany<{
        month: string;
        manual_lead_num: number;
        sync_lead_num: number;
        email_manual_lead_num: number;
        email_sync_lead_num: number;
        contact_added_num: number;
      }>();

    const totalData = {
      manualLeadCount: totalCounts.manual_lead_num,
      syncedLeadCount: totalCounts.sync_lead_num,
      newEmailFoundCount: totalCounts.email_manual_lead_num,
      emailSentsCount: totalCounts.email_sync_lead_num,
      contactAddedCount: totalCounts.contact_added_num,
    };

    const dataByMonth = monthlyCounts.map((item) => ({
      month: item.month,
      manualLeadCount: item.manual_lead_num,
      syncedLeadCount: item.sync_lead_num,
      newEmailFoundCount: item.email_manual_lead_num,
      emailSentsCount: item.email_sync_lead_num,
      contactAddedCount: item.contact_added_num,
    }));

    return this.formatOutputData(
      { key: 'COUNT_LEADS' },
      {
        data: {
          totalData: totalData,
          monthData: dataByMonth,
        },
      },
    );
  }

  private getJoinConditions(country?: string, fromDate?: Date, toDate?: Date): string {
    let conditions = 'jl.lead_status_id = ls.id';

    if (country) {
      conditions += ' AND jl.address_country = :country';
    }
    if (fromDate) {
      conditions += ' AND jl.date_added >= :fromDate';
    }
    if (toDate) {
      conditions += ' AND jl.date_added <= :toDate';
    }

    return conditions;
  }

  async countForEachLeadStatus(id: string, query: JobLeadStatsDto) {
    const { country, fromDate, toDate } = query;
    const queryBuilder = this.jobLeadSheetRepository
      .createQueryBuilder('lse')
      .where('lse.user_Id = :userId', { userId: id })
      .leftJoin(LeadStatus, 'ls', 'ls.lead_sheet_id = lse.id and ls.is_deleted = false')
      .leftJoin(JobLead, 'jl', this.getJoinConditions(country, fromDate, toDate), {
        country,
        fromDate,
        toDate,
      });

    const leadStatusCounts = await queryBuilder
      .groupBy('ls.id, ls.name, lse.id, ls.order_of_display, ls.color')
      .select([
        'ls.id AS leadStatusId',
        'COALESCE(COUNT(jl.id), 0) AS count',
        'ls.name AS statusName',
        'ls.order_of_display AS orderOfDisplay',
        'lse.name AS leadSheetName',
        'ls.color AS color',
      ])
      .getRawMany();

    return this.formatOutputData({ key: 'COUNT_FOR_EACH_LEAD_STATUS' }, { data: leadStatusCounts });
  }

  async updateLead(jobLeadId: string, updateLeadDto: UpdateLeadDto, req: any) {
    try {
      const lead = await this.jobLeadRepository.findOne({
        where: { id: jobLeadId },
        select: { id: true, job_lead_external_id: true },
      });

      if (!lead) {
        throw new NotFoundException('This lead is not valid anymore');
      }

      const { assigneeId, updatedBy, creatorId } = updateLeadDto;
      if (!assigneeId && !creatorId) {
        throw new BadRequestException('Assignee or Creator must not be empty');
      }
      let isDone = null;
      let consultantId = null;
      let timeAssign = null;
      if (assigneeId) {
        const assignee = await this.dataSource
          .createQueryBuilder(UserEntity, 'u')
          .where({ id: assigneeId, isDeleted: false })
          .select('consultant_id as "consultantId"')
          .getRawOne<{ consultantId: string }>();

        if (!assignee) {
          console.log('assignee', assignee);
          throw new BadRequestException('This assignee is not valid');
        }

        if (!assignee.consultantId) {
          throw new BadRequestException('This assignee is not linked with a consultant yet');
        }
        isDone = false;
        timeAssign = new Date();
        consultantId = assignee.consultantId;
      }

      await this.notificationService.createNotification({
        leadId: jobLeadId,
        notificationType: NotificationEnum.ASSIGNED_LEAD,
        userId: assigneeId,
        creatorId,
      });
      await this.jobLeadRepository.update(
        { id: jobLeadId },
        {
          assigneeId,
          creatorId,
          updatedBy,
          isDone,
          assignee_at: timeAssign,
          assignerId: updatedBy,
        },
      );
      // Not Update Consultant here
      // if (consultantId) {
      //   const bullhornLeadId = lead.job_lead_external_id.split(`${PREFIX.JOB_ORDER}-`)[1];
      //   await this.bullhornIntegrationService.updateBullhorn(
      //     req,
      //     {
      //       entityName: 'JobOrder',
      //       owner: {
      //         id: consultantId,
      //       },
      //     },
      //     bullhornLeadId
      //   );
      // }

      return this.formatOutputData({ key: 'UPDATE_LEAD' }, { data: {} });
    } catch (err) {
      console.log('Error in updateLead', err);
      return this.throwCommonMessage('UPDATE_LEAD', err);
    }
  }

  async getLeadById(id: string) {
    const data = await this.jobLeadRepository.findOneBy({ id });

    return this.formatOutputData({ key: 'GET_LEAD_BY_ID' }, { data });
  }

  async bulkDeleteJobLeads(data: DeleteJobLeadsDto) {
    const { ids } = data;
    const queryBuilder = this.jobLeadRepository.createQueryBuilder('JobLead');
    await queryBuilder
      .update()
      // Soft Delete
      .set({ deletedAt: new Date() })
      .where('id IN (:...ids)', { ids })
      .execute();

    return this.formatOutputData({ key: 'DELETED_JOB_LEADS' }, { data: ids });
  }

  async bulkChangeStatusJobLeads(req, data: ChangeStatusLeadsDto) {
    const { ids, leadStatusId } = data;
    const queryBuilder = this.jobLeadRepository.createQueryBuilder('JobLead');

    await queryBuilder
      .update()
      .set({ lead_status_id: leadStatusId, updatedBy: req.user.id })
      .where('id IN (:...ids)', { ids })
      .execute();

    return this.formatOutputData({ key: 'CHANGED_STATUS_JOB_LEADS' }, { data: ids });
  }

  //function to get random bright good color
  getRandomColor() {
    let color = '#';

    for (let i = 0; i < 3; i++) {
      const component = Math.floor(Math.random() * 128) + 128; // Ensure bright colors by keeping values in the upper range
      color += component.toString(16).padStart(2, '0'); // Convert to hex and pad if necessary
    }
    return color;
  }

  async bulkChangeStatusLeadsByName(userId: string, data: ChangeStatusLeadsByNameDto) {
    const { leadSheetName } = data;

    const user = await this.getUserById(userId);
    const leadSheet = await this.jobLeadSheetRepository.findOne({
      where: { name: leadSheetName ?? DEFAULT_LEAD_SHEET_NAME },
      select: { id: true },
    });
    let leadSheetId = leadSheet?.id;
    if (!leadSheet) {
      const createdLeadSheet = await this.jobLeadSheetRepository.insert({
        name: leadSheetName ?? DEFAULT_LEAD_SHEET_NAME,
        leadStatusType: LeadStatusType.LEAD,
        userId: user.id,
      });
      leadSheetId = createdLeadSheet.raw[0].id;
    }

    const { ids = [], leadStatusName, jobIds = [], sequenceId } = data;
    if (sequenceId) {
      const sequence = await this.dataSource.getRepository(SequenceEntity).findOne({
        where: { id: sequenceId },
        select: { id: true, jobBoardId: true },
      });
      if (sequence?.jobBoardId) {
        jobIds.push(sequence.jobBoardId);
      }
    }
    const leadStatus = await this.leadStatusRepository.findOne({
      where: { name: leadStatusName },
      select: { id: true },
    });

    let leadStatusId = leadStatus?.id;
    if (!leadStatus) {
      const createdStatus = await this.leadStatusRepository.insert({
        name: leadStatusName,
        orderOfDisplay: 1,
        organizationId: user.organizationId ?? null,
        color: this.getRandomColor(),
        updatedBy: userId,
        leadSheetId,
        leadStatusType: LeadStatusType.LEAD,
      });
      leadStatusId = createdStatus.raw[0].id;
    }

    const queryBuilder = this.jobLeadRepository.createQueryBuilder('JobLead');

    const whereCondition = ids?.length > 0 ? 'id IN (:...ids)' : 'job_board_id IN (:...jobIds)';
    await queryBuilder
      .update()
      .set({ lead_status_id: leadStatusId, updatedBy: userId })
      .where(whereCondition, { ids, jobIds })
      .execute();

    return this.formatOutputData({ key: 'CHANGED_STATUS_JOB_LEADS' }, { data: ids });
  }

  private getStartAndEndOfDay(rawDate: Date): [Date, Date] {
    const date = new Date(rawDate);
    const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

    return [startOfDay, endOfDay];
  }

  async searchVacancies(userId: string, data: SearchVacanciesDto) {
    try {
      const user = await this.getUserById(userId);
      const leadStatuses = await this.leadStatusRepository.findBy({
        organizationId: user.organizationId ?? IsNull(),
      });
      const leadStatusIds = leadStatuses.map(({ id }) => id);

      const {
        companyIds,
        search: optional,
        excluded,
        required,
        start = 0,
        count = 20,
        salary,
        maxSalary,
        minSalary,
        datePostedFrom,
        datePostedTo,
        datePosted,
        inRange,
        outRange,
        statusId,
        source,
        jobType,
      } = data;
      const whereConditions = [];
      const fieldsSearchable = [
        'title',
        'description',
        'consultant_name',
        'company_contact_name',
        'company_name',
      ];

      if (required?.trim()) {
        const requiredQuery = [];
        const requiredParams = {};
        required.split(' ').forEach((keyword, i) => {
          requiredQuery.push(
            `(${fieldsSearchable.map((field) => `${field} ILIKE :required${i}`).join(' OR ')})`,
          );
          requiredParams[`required${i}`] = `%${keyword.trim()}%`;
        });
        whereConditions.push([`(${requiredQuery.join(' AND  ')})`, requiredParams]);
      }

      if (optional?.trim()) {
        const optionalQuery = [];
        const optionalParams = {};
        optional.split(' ').forEach((keyword, i) => {
          optionalQuery.push(
            `(${fieldsSearchable.map((field) => `${field} ILIKE :search${i}`).join(' OR ')})`,
          );
          optionalParams[`search${i}`] = `%${keyword.trim()}%`;
        });
        whereConditions.push([`(${optionalQuery.join(' OR  ')})`, optionalParams]);
      }

      if (excluded?.trim()) {
        const excludedQuery = [];
        const excludedParams = {};
        excluded.split(' ').forEach((keyword, i) => {
          excludedQuery.push(
            `(${fieldsSearchable.map((field) => `${field} ILIKE :excluded${i}`).join(' OR ')})`,
          );
          excludedParams[`excluded${i}`] = `%${keyword.trim()}%`;
        });
        whereConditions.push([` NOT (${excludedQuery.join(' OR  ')})`, excludedParams]);
      }

      if (salary) {
        whereConditions.push(['(salary = :salary )', { salary }]);
      } else {
        if (minSalary) {
          whereConditions.push(['(salary >= :minSalary)', { minSalary }]);
        }
        if (maxSalary) {
          whereConditions.push(['(salary <= :maxSalary)', { maxSalary }]);
        }
      }

      if (datePosted) {
        const [startOfDay, endOfDay] = this.getStartAndEndOfDay(datePosted);
        whereConditions.push([
          '(date_added >= :startOfDay AND date_added < :endOfDay)',
          { startOfDay, endOfDay },
        ]);
      } else {
        if (datePostedFrom) {
          const [startOfDay] = this.getStartAndEndOfDay(datePostedFrom);
          whereConditions.push(['(date_added >= :startOfDay)', { datePostedFrom: startOfDay }]);
        }
        if (datePostedTo) {
          const [_, endOfDay] = this.getStartAndEndOfDay(datePostedTo);
          whereConditions.push(['(date_added <= :datePostedTo)', { datePostedTo: endOfDay }]);
        }
      }

      if (inRange) {
        const [startOfRange] = this.getStartAndEndOfDay(inRange[0]);
        const [_, endOfRange] = this.getStartAndEndOfDay(inRange[1]);
        whereConditions.push([
          '(date_added >= :startOfRange AND date_added < :endOfRange)',
          { startOfRange, endOfRange },
        ]);
      } else if (outRange) {
        const [startOfRange] = this.getStartAndEndOfDay(outRange[1]);
        const [_, endOfRange] = this.getStartAndEndOfDay(outRange[0]);
        whereConditions.push([
          '(date_added >= :startOfRange OR date_added < :endOfRange)',
          { startOfRange, endOfRange },
        ]);
      }

      if (leadStatusIds?.length) {
        whereConditions.push([
          '(lead_status_id IN(:...leadStatusIds))',
          { leadStatusIds: statusId ? [statusId] : leadStatusIds },
        ]);
      }
      if (companyIds?.length) {
        whereConditions.push(['(company_id IN(:...companyIds))', { companyIds }]);
      }

      if (source) {
        whereConditions.push(['(job_board_id ILIKE :source)', { source: `%${source}%` }]);
      }

      if (jobType) {
        whereConditions.push(['(job_type ILIKE :jobType)', { jobType: `%${jobType}%` }]);
      }

      whereConditions.push(['(creator_id = :userId OR assignee_id = :userId)', { userId }]);
      whereConditions.push(['(is_done = true OR creator_id = :userId)', { userId }]);

      const queryBuilder = this.jobLeadRepository
        .createQueryBuilder('job')
        .orderBy('job.date_added', 'DESC')
        .limit(count)
        .offset(start);

      whereConditions.forEach(([condition, variables], i) => {
        if (i === 0) {
          queryBuilder.where(condition, variables);
        } else {
          queryBuilder.andWhere(condition, variables);
        }
      });
      const [items, total] = await queryBuilder.getManyAndCount();

      //TODO: remove after migration
      const standardItems = items.map((item) => ({
        ...item,
        jobType: convertCamelToNormalCase(
          standardizeJobType(
            item?.jobType?.replace('{', '').replace('}', '').replace(/\"/g, ''),
          ).join(', '),
        ),
      }));

      return this.formatOutputData(
        { key: 'SEARCH_VACANCIES' },
        {
          data: {
            items: standardItems,
            total,
            count,
            start,
          },
        },
      );
    } catch (error) {
      console.error('Error in searchVacancies', error);
      return this.throwCommonMessage('SEARCH_VACANCIES', { data: [] });
    }
  }

  async getSimilarJobs(req, similarJobDto: SimilarJobDto) {
    try {
      const MATCH_PERCENT = 60 / 100;
      const organizationId = await this.getOrganizationId(req);
      const { bhRestToken, corporateRestUrl } =
        (await this.bullhornIntegrationService.getBhToken(organizationId)) || {};
      const { companyId, jobTitle, jobDescription } = similarJobDto;
      // const jobData = await this.jobBoardRepository.findOne({ where: { job_id: jobId } });
      // const jobData = await this.elasticsearchService.getById(JOBS_INDEX, jobId);
      // if (!jobData) {
      //   throw new BadRequestException('Job Not Found');
      // }

      const { data: vacancies } = await this.bullhornIntegrationService.getSimilarJobs(companyId, {
        bhRestToken,
        corporateRestUrl,
      });
      const similarVacancies = vacancies.filter(
        (vacancy) =>
          vacancy.title &&
          vacancy.description &&
          stringSimilarity.compareTwoStrings(jobTitle, vacancy.title) > MATCH_PERCENT &&
          stringSimilarity.compareTwoStrings(jobDescription, vacancy.description) > MATCH_PERCENT,
      );

      return this.formatOutputData(
        { key: 'SIMILAR_VACANCIES' },
        {
          data: {
            items: similarVacancies,
            total: similarVacancies.length,
          },
        },
      );
    } catch (error) {
      console.error('Error in getSimilarJobs', error);

      return this.throwCommonMessage('SIMILAR_VACANCIES', error);
    }
  }

  async updateSentJobData() {
    const jobSent = await this.sentJobRepository.find();

    for (const job of jobSent) {
      await this.handleUpdateData(job);
    }
  }

  async handleUpdateData(jobSent: SentJobEntity) {
    try {
      const dataJob = await this.elasticsearchService.getById(JOBS_INDEX, jobSent.jobBoardId);
      if (dataJob) {
        await this.sentJobRepository.update(jobSent.id, {
          companyName: dataJob.company,
          title: dataJob.jobtitle,
          location: dataJob.joblocationcity,
          dateAdded: dataJob?.posted,
          jobType: dataJob.jobtype,
          salary: dataJob.salary,
        });
      }
    } catch (e) {
      console.log('err :', e);
      return true;
    }
  }

  async statsJobKeywords(userId: string, filter: JobLeadKeywordDto) {
    const { country, fromDate, toDate, limit = 10, page = 0 } = filter;
    const whereConditions = [];

    if (userId) {
      whereConditions.push(['(user_id = :userId)', { userId }]);
    }
    if (country) {
      whereConditions.push(['(country = :country)', { country }]);
    }
    if (fromDate) {
      whereConditions.push(['(date >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(date <= :toDate)', { toDate }]);
    }

    const queryBuilder = this.jobLeadKeywordRepository
      .createQueryBuilder('jlk')
      .select(['jlk.keyword as keyword', 'SUM(jlk.count) as count'])
      .limit(limit)
      .offset(page * limit)
      .groupBy('jlk.keyword')
      .orderBy('SUM(jlk.count)', 'DESC');

    whereConditions.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });
    const items = await queryBuilder.getRawMany();

    return this.formatOutputData(
      { key: 'STATS_JOB_KEYWORDS' },
      {
        data: {
          items,
        },
      },
    );
  }

  async statsNewLeads(userId: string, filter: JobLeadStatsDto) {
    const { country, fromDate, toDate } = filter;
    const whereConditions = [];

    if (userId) {
      whereConditions.push(['jl.dataKey = :userId', { userId }]);
    }
    if (country) {
      whereConditions.push(['jl.address_country = :country', { country }]);
    }
    if (fromDate) {
      whereConditions.push(['(jl.date_added >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(jl.date_added <= :toDate)', { toDate }]);
    }

    const newStatusSubQuery = this.leadStatusRepository
      .createQueryBuilder('ls')
      .select('ls.id', 'lead_status_id')
      .where('(ls.is_deleted = false AND ls.order_of_display = 0)');

    let queryBuilder = this.jobLeadRepository
      .createQueryBuilder('jl')
      .select('COUNT(jl.id)', 'count')
      .where(`jl.lead_status_id IN (${newStatusSubQuery.getQuery()}) AND jl.dataKey = :userId`, {
        userId,
      });

    whereConditions.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });
    const data = await queryBuilder.getRawOne();

    const monthlyCounts = await queryBuilder
      .select([`DATE_TRUNC('month', jl.updated_at) as month`, 'COUNT(jl.id) as count'])
      .andWhere("jl.updated_at >= NOW() - INTERVAL '7 MONTHS'")
      .groupBy("DATE_TRUNC('month', jl.updated_at)")
      .orderBy("DATE_TRUNC('month', jl.updated_at)", 'ASC')
      .getRawMany<{
        month: string;
        count: number;
      }>();
    const dataByMonth = monthlyCounts.map((item) => ({
      month: item.month,
      count: item.count,
    }));
    return this.formatOutputData(
      { key: 'STATS_NEW_LEADS' },
      {
        data: {
          totalCount: data.count,
          monthData: dataByMonth,
        },
      },
    );
  }

  async countCompanyAdded(userId: string, filter: any) {
    const { country, fromDate, toDate } = filter;
    const whereConditions = [];

    if (userId) {
      whereConditions.push(['(o.created_by = :userId)', { userId }]);
    }
    if (country) {
      whereConditions.push(['(o.address LIKE :country)', { country: `%${country}%` }]);
    }

    if (fromDate) {
      whereConditions.push(['(o.created_at >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(o.created_at <= :toDate)', { toDate }]);
    }
    const organizationRepository = this.dataSource.getRepository(OrganizationEntity);
    const queryBuilder = organizationRepository
      .createQueryBuilder('o')
      .select('COUNT(id)', 'count');

    whereConditions.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });

    const totalData = await queryBuilder.getRawOne();

    const monthlyCounts = await queryBuilder
      .clone()
      .select([`DATE_TRUNC('month', o.created_at) as month`, 'COUNT(id) as count'])
      .groupBy(`DATE_TRUNC('month', o.created_at)`)
      .orderBy(`DATE_TRUNC('month', o.created_at)`, 'ASC')
      .getRawMany<{
        month: string;
        count: number;
      }>();

    const dataByMonth = monthlyCounts.map((item) => ({
      month: item.month,
      count: item.count,
    }));

    return this.formatOutputData(
      { key: 'STATS_COMPANY_ADDED_SENT' },
      {
        data: {
          totalCount: totalData.count,
          monthData: dataByMonth,
        },
      },
    );
  }

  async statsContactAdded(userId: string, filter: JobLeadStatsDto) {
    const { country, fromDate, toDate } = filter;
    const whereConditions = [];

    const action = filter.action || 'ADDED_CLIENTCONTACT';

    // Adding the required type condition
    whereConditions.push(['(type = :type)', { type: action }]);

    // Adding dynamic conditions for userId, country, fromDate, and toDate
    if (userId) {
      whereConditions.push(['(user_id = :userId)', { userId }]);
    }
    if (country) {
      whereConditions.push(['(country = :country)', { country }]);
    }
    if (fromDate) {
      whereConditions.push(['(date >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(date <= :toDate)', { toDate }]);
    }

    const queryBuilder = this.statsEntityDataRepository
      .createQueryBuilder('sed')
      .select(['SUM(sed.count) as count']);

    whereConditions.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });

    const totalData = await queryBuilder.getRawOne();

    const monthlyCounts = await queryBuilder
      .clone()
      .select([`DATE_TRUNC('month', sed.date) as month`, 'SUM(sed.count) as count'])
      .groupBy(`DATE_TRUNC('month', sed.date)`)
      .orderBy(`DATE_TRUNC('month', sed.date)`, 'ASC')
      .getRawMany<{
        month: string;
        count: number;
      }>();

    const dataByMonth = monthlyCounts.map((item) => ({
      month: item.month,
      count: item.count,
    }));
    return this.formatOutputData(
      { key: 'STATS_CONTACT_ADDED' },
      {
        data: {
          totalCount: totalData.count,
          monthData: dataByMonth,
        },
      },
    );
  }

  async starJobLead(jobLeadId: string, staredJobLeadDto: StaredJobLeadDto) {
    try {
      const lead = await this.jobLeadRepository.findOne({
        where: { id: jobLeadId },
      });

      if (!lead) {
        throw new NotFoundException('Lead not found');
      }

      await this.jobLeadRepository.update(lead.id, { isStared: staredJobLeadDto.star });

      return this.formatOutputData({ key: 'STAR_JOB_LEAD' }, { data: {} });
    } catch (err) {
      console.log('Error in staredJobLeads', err);
      return this.throwCommonMessage('STAR_JOB_LEAD', err);
    }
  }

  async checkExistingJobLeadByExternalIds({ userId, ids = [] }: { userId: string; ids: string[] }) {
    if (!ids?.length) {
      return this.formatOutputData(
        { key: 'CHECK_EXISTING_LEADS' },
        {
          data: {
            existingIds: [],
          },
        },
      );
    }
    // Add Bullhorn prefix to the IDs
    const BH_PREFIX = 'bullhorn-';
    const formattedIds = ids?.map((id) => `${BH_PREFIX}${id.trim()}`);

    const jobLeadData = await this.jobLeadRepository.find({
      where: { job_lead_external_id: In(formattedIds), creatorId: userId },
      select: ['job_lead_external_id'],
    });

    // Extract the existing IDs from the fetched job leads
    const existingIds = jobLeadData.map((jobLead) =>
      jobLead.job_lead_external_id?.includes(BH_PREFIX)
        ? jobLead.job_lead_external_id.split(BH_PREFIX)[1]
        : jobLead.job_lead_external_id,
    );

    return this.formatOutputData(
      { key: 'CHECK_EXISTING_LEADS' },
      {
        data: {
          existingIds,
        },
      },
    );
  }

  async getLeadsByStatus(leadStatusId: string, queryDto: LeadQueryDto): Promise<any> {
    const { limit = 10, companyName } = queryDto;
    const whereConditions: FindOptionsWhere<JobLead> = { lead_status_id: leadStatusId };
    if (companyName) {
      whereConditions.company_name = companyName;
    }
    const leads = await this.jobLeadRepository.find({
      where: whereConditions,
      order: { priorityLevel: 'DESC', date_added: 'DESC' },
      take: limit,
    });

    return this.formatOutputData(
      { key: 'GET_LEADS_BY_STATUS' },
      {
        data: leads,
      },
    );
  }

  async updatePriority(body: UpdatePriorityDto) {
    const { leadIds, priorityLevel, companyNames } = body;

    const condition: FindOptionsWhere<JobLead> = {};
    if (leadIds?.length) {
      condition.id = In(leadIds);
    }
    if (companyNames?.length) {
      condition.company_name = In(companyNames);
    }

    await this.jobLeadRepository.update(condition, { priorityLevel });

    return this.formatOutputData(
      { key: 'UPDATE_PRIORITY' },
      {
        data: {},
      },
    );
  }
}

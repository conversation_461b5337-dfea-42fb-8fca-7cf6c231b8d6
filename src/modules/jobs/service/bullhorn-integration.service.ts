import { BadRequestException, Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { convertToNumber, safeStringify, validateEmail } from 'src/common/utils/helpers.util';
import { OpensearchService } from 'src/modules/opensearch/service/opensearch.service';
import { MailService } from 'src/modules/mail/mail.service';
import { ISendMail } from 'src/modules/mail/dto/send-email-process.dto';
import { DataSource, EntityManager, In } from 'typeorm';
import { LicenseType as UserLicenseType, UserEntity } from 'src/modules/user/entities/user.entity';
import { InjectEntityManager } from '@nestjs/typeorm';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { Request } from 'express';
import { JobBoardsRepository } from '../repository/job-boards.repository';
import {
  createJobOrder,
  getCounties,
  getCountries,
  queryEntity,
  updateEntity,
  insertEntity,
  searchVacancies,
  deleteEntity,
  getTotalDataEntity,
  getFile,
  uploadBHFile,
  deleteFileBH,
  updateMassAdvance,
  insertMassAdvance,
  searchLookupData,
  sendAppointment,
  searchContactDetail,
  queryGetListShortList,
  queryGetListVacancy,
} from '../utils/bullhorn-service.util';
import { GetCorporateUserQueryDto, SearchCommonBullhornDto } from '../dto/get-corporate.dto';
import { CreateJobLeadDto } from '../dto/create-job-lead-request.dto';
import { JobLeadsService } from './job-leads.service';
import { GetCountiesQueryDto, GetCountriesQueryDto } from '../dto/bull-horn/get-counties.dto';
import { JobSearchRepository } from '../repository/job-search.repository';
import { PREFIX } from '../constants/bullhorn.constant';
import { SentJobRepository } from '../repository/sent-job.repository';
import { ClientContactQueryDto } from '../dto/client-contact-query.dto';
import {
  ContactWarningDto,
  DeleteBHEntityDto,
  InsertMassAdvanceDto,
  searchLookupDataDto,
  updateMassAdvanceDto,
} from '../dto/check-contact-warning.dto';
import { SentJobEntity, StatusSentJobEnum } from '../entities/sent-jobs.entity';
import { BHJobDataDto, BulkImportJobDto, ConnectedUserJobDto, SendJobToBullhornDto, StandardUserJobDto } from '../dto/job/job-to-bullhorn.dto';
import { JobSearchEntity } from '../entities/job-search.entity';
import { JobBoard } from '../entities/job-boards.entity';
import { SearchVacanciesDto } from '../dto/job-lead/search-vacancies.dto';
import { JobSearchService } from './job-search.service';
import { BullhornSubmissionTotal } from '../dto/bull-horn/bullhorn-job.dto';
import { BullhornEntityEnum } from '../enums/bullhorn.enum';
import { CacheService } from 'src/modules/cache/cache.service';
import { BHUploadFileDto } from '../dto/bull-horn/update-file.dto';
import { StatsEntityDataRepository } from '../repository/stats-entity-data.repository';
import { StatisticItemEntity } from '../entities/statistic-item.entity';
import { HttpService } from '@nestjs/axios';
import { MassUpdateDto, BullhornGuessDto } from '../dto/bull-horn/bullhorn-entity.dto';
import { IApolloContactDto } from 'src/modules/employee-finder/dto/employee-finder.dto';
import { FoundContactEntity } from 'src/modules/employee-finder/entities/found-contact.entity';
import { invalid } from 'moment';
import { EmailValidationResultRepository } from 'src/modules/email-finder/repositories/email-validation-result.repository';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';
import { BullHornConfig, JOBS_INDEX } from 'src/configs/configs.constants';
import { BullHornService } from 'src/middlewares/bullhorn/bullhorn.service';
import { CrmLeadEntity, CrmLeadType } from 'src/modules/crm/entities/crm-lead.entity';
import { CrmBullhornService } from 'src/modules/crm/services/crm-bullhorn.service';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { CrmIndustryEntity } from 'src/modules/crm/entities/crm-industry.entity';
import { CrmSkillsEntity } from 'src/modules/crm/entities/crm-skill.entity';
import { CrmTagEntity } from 'src/modules/crm/entities/crm-tag.entity';
import { SequenceEntity } from 'src/modules/mail/entities/sequence.entity';
import { CrmContactSequenceEntity } from 'src/modules/crm/entities/crm-contact-sequence.entity';
import { CrmContactSequenceStepEntity } from 'src/modules/crm/entities/crm-contact-sequence-step.entity';
import { SequenceStepEntity } from 'src/modules/mail/entities/sequence-step.entity';
import { FoundCompanyEntity } from 'src/modules/employee-finder/entities/found-company.entity';
import { OpenAIService } from 'src/modules/openai/openai.service';

@Injectable()
export class BullhornIntegrationService extends BaseAbstractService {
  private readonly logger = new Logger(BullhornIntegrationService.name);

  constructor(
    @InjectEntityManager() private readonly entityManager: EntityManager, // Inject EntityManager
    private readonly i18nService: I18nService,
    @Inject(forwardRef(() => JobLeadsService))
    private readonly jobLeadsService: JobLeadsService,
    private readonly jobSearchRepository: JobSearchRepository,
    private readonly jobBoardsRepository: JobBoardsRepository,
    private readonly opensearchService: OpensearchService,
    private readonly sentJobRepository: SentJobRepository,
    private readonly mailService: MailService,
    private readonly dataSource: DataSource,
    private readonly jobSearchService: JobSearchService,
    private readonly statsEntityDataRepository: StatsEntityDataRepository,
    private readonly cacheService: CacheService,
    private readonly httpService: HttpService,
    private readonly emailValidationResultRepository: EmailValidationResultRepository,
    private readonly bullhornService: BullHornService,
    private readonly crmBullhornService: CrmBullhornService,
    private readonly openAIService: OpenAIService,
  ) {
    super(i18nService);
  }

  public async getBullHornTokenFromGuess(body: BullhornGuessDto) {
    const { bhClientId, bhUsername, bhPassword, bhClientSecret } = body;

    if (!bhClientId || !bhUsername || !bhPassword || !bhClientSecret) {
      return null;
    }
    let refresh_token = null;
    let access_token = null;
    let expires_at = null;
    let bhRestToken = null;
    let corporateRestUrl = null;
    const { oauthUrl, restUrl } = await this.bullhornService.getDataCenter(bhUsername);

    const {
      access_token: accessToken,
      expires_in,
      refresh_token: refreshToken,
    } = await this.bullhornService.getAccessTokenFromScratch(refresh_token, {
      bhClientId,
      bhUsername,
      bhPassword,
      bhClientSecret,
      rootOauthUrl: oauthUrl,
    });

    access_token = accessToken;
    expires_at = Date.now() + expires_in * 1000 - 60 * 1000;
    refresh_token = refreshToken;

    const { BhRestToken: newBhRestToken, restUrl: newCorporateRestUrl } =
      await this.bullhornService.getBhRestTokenAndCorporateRestEndpoint(accessToken, restUrl);

    bhRestToken = newBhRestToken;
    corporateRestUrl = newCorporateRestUrl;

    const bhToken = {
      access_token,
      expires_at,
      refresh_token,
      bhRestToken,
      corporateRestUrl,
    };

    return { ...bhToken, organizationId: 0 };
  }

  public async getBhToken(organizationId) {
    // get access token which use to call to bullhorn
    const org = await this.dataSource
      .createQueryBuilder(OrganizationEntity, 'o')
      .where({ id: organizationId })
      .getOne();

    let { access_token, expires_at, refresh_token, bhRestToken, corporateRestUrl } =
      org?.bhToken ?? {};

    if (!expires_at || expires_at < Date.now() - 60 * 1000) {
      const { bhClientId, bhUsername, bhPassword, bhClientSecret } = org ?? {
        bhClientId: BullHornConfig.clientId,
        bhUsername: BullHornConfig.username,
        bhPassword: BullHornConfig.password,
        bhClientSecret: BullHornConfig.clientSecret,
      };

      if (!bhClientId || !bhUsername || !bhPassword || !bhClientSecret) {
        return null;
      }

      const { oauthUrl, restUrl } = await this.bullhornService.getDataCenter(bhUsername);
      const {
        access_token: accessToken,
        expires_in,
        refresh_token: refreshToken,
      } = await this.bullhornService.getAccessTokenFromScratch(refresh_token, {
        bhClientId,
        bhUsername,
        bhPassword,
        bhClientSecret,
        rootOauthUrl: oauthUrl,
      });

      access_token = accessToken;
      expires_at = Date.now() + expires_in * 1000 - 60 * 1000;
      refresh_token = refreshToken;

      const { BhRestToken: newBhRestToken, restUrl: newCorporateRestUrl } =
        await this.bullhornService.getBhRestTokenAndCorporateRestEndpoint(accessToken, restUrl);

      bhRestToken = newBhRestToken;
      corporateRestUrl = newCorporateRestUrl;

      const bhToken = {
        access_token,
        expires_at,
        refresh_token,
        bhRestToken,
        corporateRestUrl,
      };

      if (org) {
        await this.dataSource.getRepository(OrganizationEntity).update(org.id, {
          bhToken,
        });
      }

      return { ...bhToken, organizationId: organizationId ?? 0 };
    }

    return { ...org.bhToken, organizationId: organizationId ?? 0 };
  }

  async getOrganizationId(request) {
    const loginUserId = request.user.id;
    const viewAsUserId = request.headers?.['view-as-user-id'] as string;
    if ((!viewAsUserId || viewAsUserId === loginUserId) && request.user?.organizationId) {
      return request.user?.organizationId || 0;
    }

    const organization = await this.dataSource
      .createQueryBuilder(UserEntity, 'u')
      .where({ id: viewAsUserId })
      .select('"organizationId" as "organizationId"')
      .getRawOne<{ organizationId: string }>();

    const organizationId = organization?.organizationId || 0;

    return organizationId;
  }

  async getCounties(request, queryDto: GetCountiesQueryDto) {
    const organizationId = await this.getOrganizationId(request);
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};
    try {
      const { data } = await getCounties(bhRestToken, corporateRestUrl, queryDto);
      return this.formatOutputData({ key: 'GET_COUNTIES' }, { data });
    } catch (error) {
      return this.throwCommonMessage('GET_COUNTIES', new BadRequestException(error));
    }
  }

  async getTotalData(request, queryDto: BullhornSubmissionTotal) {
    const organizationId = await this.getOrganizationId(request);
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};
    try {
      const data = await getTotalDataEntity(bhRestToken, corporateRestUrl, queryDto);
      return this.formatOutputData({ key: 'GET_TOTAL' }, { data: { total: data } });
    } catch (error) {
      return this.throwCommonMessage('GET_TOTAL', new BadRequestException(error));
    }
  }

  async getCountries(request, queryDto: GetCountriesQueryDto) {
    const organizationId = await this.getOrganizationId(request);
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};
    try {
      const { data } = await getCountries(bhRestToken, corporateRestUrl, queryDto);
      return this.formatOutputData({ key: 'GET_COUNTRIES' }, { data });
    } catch (error) {
      console.log('Error in getCountries', error);
      return this.throwCommonMessage('GET_COUNTRIES', new BadRequestException(error));
    }
  }

  async searchListShortList(req, shortListId: string) {
    try {
      const organizationId = await this.getOrganizationId(req);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};
      const finalResults = [];

      const data = await queryGetListShortList(bhRestToken, corporateRestUrl, 0, shortListId);
      if (data?.data.length > 0) {
        finalResults.push(...data?.data);
        const totalAction = Math.ceil(data.total / 200);
        if (totalAction > 1) {
          for (let i = 1; i <= totalAction; i++) {
            const results = await queryGetListShortList(bhRestToken, corporateRestUrl, 200 * i + 1);
            finalResults.push(...results?.data);
          }
        }
        return this.formatOutputData({ key: `SEARCH_BULLHORN_SHORT_LIST` }, { data: finalResults });
      } else {
        const data = await queryEntity(
          {
            start: 0,
            count: 1,
            queryId: shortListId,
          },
          BullhornEntityEnum.JOB_ORDER,
          bhRestToken,
          corporateRestUrl,
        );

        return this.formatOutputData(
          { key: `SEARCH_BULLHORN_SHORT_LIST` },
          { data: [{ jobOrder: data[0] }] },
        );
      }
    } catch (error) {
      this.logger.error(error.message);
      return await this.throwCommonMessage(
        `SEARCH_BULLHORN_SHORT_LIST`,
        new BadRequestException(error),
      );
    }
  }

  async searchListVacancy(req, query: GetCorporateUserQueryDto) {
    try {
      const organizationId = await this.getOrganizationId(req);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};
      const finalResults = [];

      const data = await queryGetListVacancy(bhRestToken, corporateRestUrl, 0, query.companyId);
      finalResults.push(...data?.data);
      const totalAction = Math.ceil(data.total / 200);
      if (totalAction > 1) {
        for (let i = 1; i <= totalAction; i++) {
          const results = await queryGetListVacancy(bhRestToken, corporateRestUrl, 200 * i + 1);
          finalResults.push(...results?.data);
        }
      }
      return this.formatOutputData({ key: `SEARCH_BULLHORN_SHORT_LIST` }, { data: finalResults });
    } catch (error) {
      this.logger.error(error.message);
      return await this.throwCommonMessage(
        `SEARCH_BULLHORN_SHORT_LIST`,
        new BadRequestException(error),
      );
    }
  }

  async search(req, query: GetCorporateUserQueryDto) {
    try {
      const organizationId = await this.getOrganizationId(req);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};
      if (!corporateRestUrl) {
        return this.formatOutputData(
          { key: `SEARCH_BULLHORN_${query.entityName?.toUpperCase() || 'ENITIY'}` },
          { data: [] },
        );
      }

      //question: why needs this condition?

      const validEntityNames: string[] = [
        BullhornEntityEnum.CLIENT_CONTACT,
        BullhornEntityEnum.TEARSHEET,
        BullhornEntityEnum.JOB_ORDER,
        BullhornEntityEnum.JOB_SUBMISSION,
        BullhornEntityEnum.SENDOUT,
        BullhornEntityEnum.NOTE,
        BullhornEntityEnum.CANDIDATE,
        BullhornEntityEnum.APPOINTMENT,
        BullhornEntityEnum.PLACEMENT,
        BullhornEntityEnum.USERMESSAGE,
      ];

      if (!validEntityNames.includes(query?.entityName)) {
        if (!query.query && !query.queryId) {
          return this.formatOutputData(
            { key: `SEARCH_BULLHORN_${query.entityName.toUpperCase()}` },
            { data: [] },
          );
        }
      }

      const results = await queryEntity(query, query.entityName, bhRestToken, corporateRestUrl);

      const resultAfterFormat = await this.removeDuplicate(results);

      if (query.entityName === 'BusinessSector') {
        // Remove redundant Power Apps
        results.shift();
        return this.formatOutputData(
          { key: `SEARCH_BULLHORN_${query.entityName.toUpperCase()}` },
          { data: resultAfterFormat },
        );
      }
      return this.formatOutputData(
        { key: `SEARCH_BULLHORN_${query.entityName.toUpperCase()}` },
        { data: resultAfterFormat },
      );
    } catch (error) {
      this.logger.error(error.message);
      return await this.throwCommonMessage(
        `SEARCH_BULLHORN_${query.entityName.toUpperCase()}`,
        new BadRequestException(error),
      );
    }
  }

  async searchCommon(
    req,
    {
      entityName,
      fields,
      sort = '-dateAdded',
      where,
      count,
      start,
      orderBy = '',
      meta = '',
      showTotalMatched = false,
    }: SearchCommonBullhornDto,
  ) {
    try {
      const organizationId = await this.getOrganizationId(req);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

      const apiUrl = `${corporateRestUrl}query/${entityName}?BhRestToken=${bhRestToken}&fields=${fields}&sort=${sort}&showTotalMatched=${showTotalMatched}&where=${encodeURIComponent(
        where,
      )}&count=${count}&start=${start}&orderBy=${orderBy}&meta=${meta}`;
      const { data } = await this.httpService.axiosRef.get(apiUrl);

      return this.formatOutputData(
        { key: `SEARCH_BULLHORN_${entityName.toUpperCase()}` },
        { data },
      );
    } catch (error) {
      this.logger.error(error.message);
      console.error(error);
      return await this.throwCommonMessage(
        `SEARCH_BULLHORN_${entityName.toUpperCase()}`,
        new BadRequestException(error),
      );
    }
  }

  async querySearchCommon(
    req,
    {
      entityName,
      fields,
      sort = '-dateAdded',
      where,
      count,
      start,
      orderBy = '',
      meta = '',
      showTotalMatched = false,
    }: SearchCommonBullhornDto,
  ) {
    try {
      const organizationId = await this.getOrganizationId(req);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

      const apiUrl = `${corporateRestUrl}search/${entityName}?BhRestToken=${bhRestToken}&fields=${fields}&sort=${sort}&showTotalMatched=${showTotalMatched}&query=${encodeURIComponent(
        where,
      )}&count=${count}&start=${start}&orderBy=${orderBy}&meta=${meta}`;
      const { data } = await this.httpService.axiosRef.get(apiUrl);

      return this.formatOutputData(
        { key: `QUERY_SEARCH_BULLHORN_${entityName.toUpperCase()}` },
        { data },
      );
    } catch (error) {
      this.logger.error(error.message);
      console.error(error);
      return await this.throwCommonMessage(
        `QUERY_SEARCH_BULLHORN_${entityName.toUpperCase()}`,
        new BadRequestException(error),
      );
    }
  }

  async searchFile(req, query: GetCorporateUserQueryDto) {
    try {
      const organizationId = await this.getOrganizationId(req);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

      const results = await getFile(query, query.entityName, bhRestToken, corporateRestUrl);

      return this.formatOutputData(
        { key: `SEARCH_BULLHORN_FILE_${query.entityName.toUpperCase()}` },
        { data: results },
      );
    } catch (error) {
      this.logger.error(error.message);
      return await this.throwCommonMessage(
        `SEARCH_BULLHORN_${query.entityName.toUpperCase()}`,
        new BadRequestException(error),
      );
    }
  }

  async uploadFile(req, requestBody: BHUploadFileDto, file: Express.Multer.File) {
    try {
      const organizationId = await this.getOrganizationId(req);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

      const results = await uploadBHFile(
        requestBody,
        requestBody.entityName,
        bhRestToken,
        corporateRestUrl,
        file,
      );

      return this.formatOutputData(
        { key: `SEARCH_BULLHORN_UPLOAD_FILE_${requestBody.entityName.toUpperCase()}` },
        { data: results },
      );
    } catch (error) {
      this.logger.error(error.message);
      return await this.throwCommonMessage(
        `SEARCH_BULLHORN_${requestBody.entityName.toUpperCase()}`,
        new BadRequestException(error),
      );
    }
  }

  protected removeDuplicate(arr: any) {
    return arr.reduce((acc: any, current: any) => {
      const x = acc.find((item: any) => item.id === current.id);
      if (!x) {
        return acc.concat([current]);
      }
      return acc;
    }, []);
  }

  protected async createJobLead({
    externalJobId,
    jobToSend,
    userJwtPayload,
    transactionalEntityManager,
  }: {
    externalJobId: string;
    jobToSend: StandardUserJobDto | ConnectedUserJobDto;
    userJwtPayload: IJwtPayload;
    transactionalEntityManager: EntityManager;
  }) {
    const jobLeadDto: CreateJobLeadDto = {
      job_lead_external_id: externalJobId ? `bullhorn-${externalJobId}` : undefined,
      consultant_id:
        'consultantId' in jobToSend && jobToSend.consultantId
          ? `${PREFIX.CONSULTANT}-${jobToSend.consultantId}`
          : '',
      consultant_name:
        'consultant' in jobToSend && jobToSend.consultant ? jobToSend.consultant : ('' as any),
      company_id: jobToSend.companyId ? `${PREFIX.CORPORATE}-${jobToSend.companyId}` : '',
      company_name: jobToSend.company || '',
      company_contact_id:
        'contactId' in jobToSend && jobToSend.contactId
          ? `${PREFIX.CONTACT}-${jobToSend.contactId}`
          : '',
      company_contact_name:
        'contact' in jobToSend && jobToSend.contact ? jobToSend.contact : ('' as any),
      description: jobToSend.description,
      cleanDescription: jobToSend.cleanDescription,
      title: jobToSend.jobtitle,
      salary: convertToNumber(jobToSend.salary),
      status: 'status' in jobToSend ? jobToSend.status : undefined,
      employment_type: 'employmentType' in jobToSend ? jobToSend.employmentType : undefined,
      jobType: Array.isArray(jobToSend.jobtype) ? jobToSend.jobtype.join(', ') : jobToSend.jobtype,
      address_city: jobToSend.city,
      lead_status_id: jobToSend.leadStatusId,
      dataKey: userJwtPayload.id,
      creatorId: userJwtPayload.id,
      job_board_id: jobToSend.job?.job_id,
      minSalary: convertToNumber(jobToSend.min_salary),
      maxSalary: convertToNumber(jobToSend.max_salary) ?? convertToNumber(jobToSend.min_salary),
      email: jobToSend.email,
      updatedFor: jobToSend.updatedFor,
      logoCompany: jobToSend?.job?.logoCompany || null,
      leadSheetId: jobToSend.leadSheetId,
      address_country: 'countryAddress' in jobToSend ? jobToSend.countryAddress : undefined,
      crmLeadId: jobToSend.crmLeadId,
      skills: jobToSend.job?.skills?.length
        ? jobToSend.job.skills
        : await this.openAIService.generateSkillByDescription(jobToSend.job.description),
    };
    await this.jobLeadsService.createJobLead(
      userJwtPayload,
      jobLeadDto,
      transactionalEntityManager,
    );
  }

  protected getBHJobPayload(jobToSend: StandardUserJobDto | ConnectedUserJobDto): BHJobDataDto {
    const SOURCE = 'Zileo';
    return {
      title: jobToSend.jobtitle,
      clientContact: {
        id: 'contactId' in jobToSend && jobToSend.contactId ? jobToSend.contactId : '',
      },
      email: jobToSend?.email,
      clientCorporation: {
        id: jobToSend.companyId,
      },
      skills: {
        replaceAll:
          'skills' in jobToSend
            ? jobToSend.skills?.map((item) => (typeof item === 'string' ? item : item.key)) || []
            : [],
      },
      businessSectors: {
        replaceAll:
          'businessSectors' in jobToSend
            ? jobToSend.businessSectors?.map(({ key }) => key) || []
            : [],
      },
      categories: {
        replaceAll:
          'categories' in jobToSend ? jobToSend.categories?.map(({ key }) => key) || [] : [],
      },
      owner:
        'consultantId' in jobToSend && jobToSend.consultantId
          ? {
              id: jobToSend.consultantId,
            }
          : null,
      address: {
        address1: jobToSend.address1,
        address2: null,
        city: jobToSend.city,
        state: jobToSend.county,
        timezone: null,
        zip: jobToSend.zip,
        countryID: jobToSend.stateId || 2359, // UK
      },
      clientBillRate: jobToSend.clientBillRate,
      markUpPercentage: jobToSend.markUpPercentage,
      payRate: jobToSend.payRate,
      description: jobToSend.description,
      salary: convertToNumber(jobToSend.salary),
      source: SOURCE,
      employmentType:
        'employmentType' in jobToSend
          ? jobToSend.employmentType.charAt(0).toUpperCase() + jobToSend.employmentType.slice(1)
          : undefined,
      status: 'status' in jobToSend ? jobToSend.status : undefined,
      startDate: jobToSend.startDate ? Date.parse(jobToSend.startDate) : null,
      dateEnd: jobToSend.startDate ? Date.parse(jobToSend.scheduledEndDate) : null,
      currencyUnit: jobToSend.currencyUnit,
      feeArrangement: jobToSend.permFee,
    };
  }

  protected getJobLeadRecord({
    externalJobId,
    jobToSend,
    user,
  }: {
    externalJobId: string;
    jobToSend: StandardUserJobDto | ConnectedUserJobDto;
    user: UserEntity;
  }) {
    const jobLeadDto: CreateJobLeadDto = {
      job_lead_external_id: `bullhorn-${externalJobId}`,
      consultant_id:
        'consultantId' in jobToSend && jobToSend.consultantId
          ? `${PREFIX.CONSULTANT}-${jobToSend.consultantId}`
          : '',
      consultant_name:
        'consultant' in jobToSend && jobToSend.consultant ? jobToSend.consultant : ('' as any),
      company_id: jobToSend.companyId ? `${PREFIX.CORPORATE}-${jobToSend.companyId}` : '',
      company_name: jobToSend.company || '',
      company_contact_id:
        'contactId' in jobToSend && jobToSend.contactId
          ? `${PREFIX.CONTACT}-${jobToSend.contactId}`
          : '',
      company_contact_name: 'contact' in jobToSend && jobToSend.contact ? jobToSend.contact : '',
      description: jobToSend.description,
      cleanDescription: jobToSend.cleanDescription,
      title: jobToSend.jobtitle,
      salary: convertToNumber(jobToSend.salary),
      status: 'status' in jobToSend ? jobToSend.status : undefined,
      employment_type: 'employmentType' in jobToSend ? jobToSend.employmentType : undefined,
      jobType: Array.isArray(jobToSend.jobtype) ? jobToSend.jobtype.join(', ') : jobToSend.jobtype,
      address_city: jobToSend.city,
      lead_status_id: jobToSend.leadStatusId,
      dataKey: user.id,
      creatorId: user.id,
      job_board_id: jobToSend.job?.job_id,
      minSalary: convertToNumber(jobToSend.min_salary),
      maxSalary: convertToNumber(jobToSend.max_salary) ?? convertToNumber(jobToSend.min_salary),
      email: jobToSend.email,
      updatedFor: jobToSend.updatedFor,
      logoCompany: jobToSend?.job?.logoCompany || null,
      leadSheetId: jobToSend.leadSheetId,
      address_country: 'countryAddress' in jobToSend ? jobToSend.countryAddress : undefined,
    };
    return jobLeadDto;
  }

  async bulkImportJob(jobToSend: BulkImportJobDto, userId: string) {
    try {
      const user = await this.dataSource
        .createQueryBuilder(UserEntity, 'u')
        .where({ id: userId })
        .getOne();
      const listRecord = jobToSend.data?.map((item) => ({
        ...this.getJobLeadRecord({
          externalJobId: item.bullHornJobId,
          jobToSend: item as StandardUserJobDto | ConnectedUserJobDto,
          user,
        }),
      }));

      await this.jobLeadsService.bulkInsertJobLead(user, listRecord);
      return this.formatOutputData({ key: 'BULK_IMPORT_JOB' }, { data: {} });
    } catch (error) {
      return await this.throwCommonMessage(`BULK_IMPORT_JOB`, new BadRequestException(error));
    }
  }

  async searchCorporateUser(body: BullhornGuessDto) {
    try {
      const { bhRestToken, corporateRestUrl } = (await this.getBullHornTokenFromGuess(body)) || {};
      const currentQuery = {
        query: '',
        entityName: BullhornEntityEnum.CORPORATE_USER,
        start: 0,
      };
      const fetchedItems = await queryEntity(
        currentQuery,
        BullhornEntityEnum.CORPORATE_USER,
        bhRestToken,
        corporateRestUrl,
      );

      return this.formatOutputData({ key: 'SEARCH_CORPORATE_USER' }, { data: fetchedItems });
    } catch (error) {
      return await this.throwCommonMessage(`SEARCH_CORPORATE_USER`, new BadRequestException(error));
    }
  }

  /**
   * Search corporate users with hasMore pagination and organizationId-based token
   * Used by UserService for getting all users
   */
  async searchCorporateUsersWithPagination(organizationId: string, query: any = {}) {
    try {
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

      if (!bhRestToken || !corporateRestUrl) {
        return [];
      }

      let allItems = [];
      let hasMore = true;
      let startIndex = 0;
      let pageSize = query.count || '200';

      while (hasMore) {
        pageSize = String(
          Math.min(
            Number(pageSize),
            query.count ? Number(query.count) - allItems.length : Infinity,
          ),
        );
        const currentQuery = {
          ...query,
          query: query.query || '',
          entityName: BullhornEntityEnum.CORPORATE_USER,
          start: String(startIndex),
          count: pageSize,
        };
        const fetchedItems = await queryEntity(
          currentQuery,
          BullhornEntityEnum.CORPORATE_USER,
          bhRestToken,
          corporateRestUrl,
        );

        allItems = allItems.concat(fetchedItems);

        // Update hasMore logic:
        // 1. If we have a count limit and reached it, stop
        // 2. If no items returned, stop
        // 3. If returned items < pageSize, we've reached the end
        hasMore =
          fetchedItems.length > 0 &&
          fetchedItems.length >= Number(pageSize) &&
          (!query.count || allItems.length < Number(query.count));

        startIndex = allItems.length;

        // Exit loop if no items are fetched or the requested count is reached
        if (
          fetchedItems.length === 0 ||
          fetchedItems.length < Number(pageSize) ||
          (query.count && allItems.length >= Number(query.count))
        ) {
          break;
        }
      }

      return allItems;
    } catch (error) {
      return [];
    }
  }

  async sendJobToBullhorn(
    req,
    jobToSend: StandardUserJobDto | ConnectedUserJobDto,
    userJwtPayload,
  ) {
    const user = await this.dataSource
      .createQueryBuilder(UserEntity, 'u')
      .where({ id: userJwtPayload.id })
      .getOne();

    if (user.licenseType === UserLicenseType.CONNECTED) {
      const jobDto = plainToClass(ConnectedUserJobDto, jobToSend);
      const errors = await validate(jobDto);
      if (errors.length > 0) {
        throw new BadRequestException(errors);
      }
    } else {
      const jobDto = plainToClass(StandardUserJobDto, jobToSend);
      const errors = await validate(jobDto);
      if (errors.length > 0) {
        throw new BadRequestException(errors);
      }
    }

    const organizationId = await this.getOrganizationId(req);
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};
    const jobId = jobToSend?.job?.job_id;

    // validate job if it's a synced job not a manual job
    let jobFromOS = null;
    if (jobId) {
      jobFromOS = await this.opensearchService.getById(JOBS_INDEX, jobId);

      if (!jobFromOS) {
        console.log('Error in sendJobToBullhorn job', jobToSend);
        throw new BadRequestException('Job is not valid. Please contact to the admin');
      }
    }

    let crmSkills = [];
    let crmIndustries = [];
    let crmTags = [];

    if (user.licenseType === UserLicenseType.CONNECTED) {
      const skillNames = 'skills' in jobToSend ? jobToSend.skills.map((item) => item.label) : [];
      const existingSkills = await this.dataSource
        .getRepository(CrmSkillsEntity)
        .findBy({ name: In(skillNames) });
      const existingSkillNames = existingSkills.map((skill) => skill.name);
      const newSkillNames = skillNames.filter((name) => !existingSkillNames.includes(name));

      const newSkills = await Promise.all(
        newSkillNames.map((name) => this.dataSource.getRepository(CrmSkillsEntity).save({ name })),
      );
      crmSkills = [...existingSkills, ...newSkills];

      const industryNames =
        'businessSectors' in jobToSend ? jobToSend.businessSectors.map((item) => item.label) : [];
      const existingIndustries = await this.dataSource
        .getRepository(CrmIndustryEntity)
        .findBy({ name: In(industryNames) });
      const existingIndustryNames = existingIndustries.map((industry) => industry.name);
      const newIndustryNames = industryNames.filter(
        (name) => !existingIndustryNames.includes(name),
      );

      const newIndustries = await Promise.all(
        newIndustryNames.map((name) =>
          this.dataSource.getRepository(CrmIndustryEntity).save({ name }),
        ),
      );
      crmIndustries = [...existingIndustries, ...newIndustries];
    } else {
      crmSkills = await this.dataSource
        .getRepository(CrmSkillsEntity)
        .findBy({ id: In(jobToSend.skills) });
      crmIndustries =
        'industries' in jobToSend
          ? await this.dataSource
              .getRepository(CrmIndustryEntity)
              .findBy({ id: In(jobToSend.industries) })
          : [];
      crmTags =
        'tags' in jobToSend
          ? await this.dataSource.getRepository(CrmTagEntity).findBy({ id: In(jobToSend.tags) })
          : [];
    }

    const jobData = this.getBHJobPayload(jobToSend);
    try {
      const crmCompany = await this.crmBullhornService.syncBullhornCompany(
        jobToSend.companyDetail || { id: jobToSend.companyId, name: jobToSend.company },
        organizationId,
      );

      return this.entityManager.transaction(async (transactionalEntityManager) => {
        // Set idle_in_transaction_session_timeout/statement_timeout(3min & 45s) for this transaction session
        await transactionalEntityManager.query(
          "SET SESSION idle_in_transaction_session_timeout = '3min';",
        );
        await transactionalEntityManager.query('SET SESSION statement_timeout = 45000;');
        const sentJobRepository = transactionalEntityManager.getRepository(SentJobEntity);
        const jobSearchRepository = transactionalEntityManager.getRepository(JobSearchEntity);
        const jobBoardsRepository = transactionalEntityManager.getRepository(JobBoard);

        // TODO: consider whether we should store all users sending job
        if (jobFromOS) {
          let { send_to_bullhorn_by_user_ids: sendToBullHornByUserIds } = jobFromOS;
          sendToBullHornByUserIds?.length
            ? (sendToBullHornByUserIds = sendToBullHornByUserIds.concat(`,${userJwtPayload.id}`))
            : (sendToBullHornByUserIds = userJwtPayload.id);

          // TODO: will remove when migrating all data to OS
          await jobBoardsRepository.update({ job_id: jobId }, { sendToBullHornByUserIds });

          // async handling
          await this.opensearchService.updateSendJobIdsBy(JOBS_INDEX, jobId, userJwtPayload.id);
        }

        let crmLead;
        if (crmCompany) {
          const crmLeadData = {
            jobTitle: jobToSend.jobtitle,
            company: {
              id: crmCompany.id,
            },
            description: jobToSend.description,
            website: 'website' in jobToSend ? jobToSend.website : undefined,
            address: 'address' in jobToSend ? jobToSend.address : undefined,
            jobType:
              (Array.isArray(jobToSend.jobtype) ? jobToSend.jobtype[0] : jobToSend.jobtype) ||
              (CrmLeadType.PERMANENT as CrmLeadType),
            salary: jobToSend.salary,
            currencyUnit: jobToSend.currencyUnit,
            payRate: jobToSend.salaryUnit,
            industries: crmIndustries,
            skills: crmSkills,
            tags: crmTags,
            organizationId,
            creator: {
              id: userJwtPayload.id,
            },
          };

          crmLead = await transactionalEntityManager.getRepository(CrmLeadEntity).save(crmLeadData);
          jobToSend.crmLeadId = crmLead?.id;
        }
        let jobOrderCreationResponse;
        if (user.licenseType === UserLicenseType.CONNECTED) {
          jobOrderCreationResponse = await createJobOrder(jobData, bhRestToken, corporateRestUrl);
        }

        await this.createJobLead({
          externalJobId: jobOrderCreationResponse?.changedEntityId,
          jobToSend,
          userJwtPayload,
          transactionalEntityManager,
        });

        if (jobToSend?.sentJobId) {
          await sentJobRepository.update(jobToSend?.sentJobId, { status: StatusSentJobEnum.SENT });
        }

        // remove job from syncs and search
        const { searchId } = jobToSend;
        if (searchId) {
          const search = await jobSearchRepository.findOneBy({ id: searchId });
          let { deletedJobIds } = search;
          if (deletedJobIds) {
            deletedJobIds = deletedJobIds.concat(',', jobId);
          } else {
            deletedJobIds = jobId;
          }
          await jobSearchRepository.update(
            { id: searchId },
            { deletedJobIds, updatedBy: userJwtPayload.id },
          );
          await this.cacheService.remove(`JOBSEARCH:${searchId}`);
        }

        const jobDetail = await this.opensearchService.getById(JOBS_INDEX, jobId);
        if (jobDetail) {
          this.jobSearchService.removeSearchJobCachedByCompany(jobDetail.company).catch(() => {});
        }
        if (jobToSend.sendEmail?.emailSeqId && crmLead?.id) {
          const seqId = jobToSend.sendEmail.emailSeqId;
          const seqUpdateData: any = {
            crmLeadId: crmLead?.id,
          };
          if (jobToSend.bullHornJobId) {
            seqUpdateData.externalJobId = jobToSend.bullHornJobId;
          }

          // SequenceEntity
          await transactionalEntityManager
            .getRepository(SequenceEntity)
            .update({ id: seqId }, seqUpdateData);
          // SequenceStepEntity
          await transactionalEntityManager
            .getRepository(SequenceStepEntity)
            .update({ id: seqId }, seqUpdateData);
          // CrmContactSequenceEntity
          await transactionalEntityManager
            .getRepository(CrmContactSequenceEntity)
            .update({ sequence: { id: seqId } }, { lead: crmLead ? { id: crmLead.id } : null });
          // CrmContactSequenceStepEntity
          await transactionalEntityManager
            .getRepository(CrmContactSequenceStepEntity)
            .update({ sequenceId: seqId }, { lead: crmLead ? { id: crmLead.id } : null });
        }
        return this.formatOutputData({ key: 'SEND_JOB_TO_BULLHORN' }, { data: {} });
      });
    } catch (error) {
      console.log('Error in sendJobToBullhorn', error);
      throw error;
    }
  }

  validateBHPayload(requestBody: any) {
    const address1 = requestBody?.address?.address1;
    if (address1 && address1.length > 100) {
      throw new BadRequestException('Address should not be longer than 100 characters');
    }
  }

  async upsertStatsEvent(userId, type, country) {
    const date = new Date().toISOString().split('T')[0];
    const statsEntity = await this.statsEntityDataRepository.findOne({
      where: {
        type: type,
        date,
        user_id: userId,
        country: country,
      },
    });

    if (statsEntity?.id) {
      // If exists, increment the count
      await this.statsEntityDataRepository
        .createQueryBuilder()
        .update(StatisticItemEntity)
        .set({ count: () => 'count + 1' })
        .where('id = :id', { id: statsEntity.id })
        .execute();
    } else {
      // If not, create a new entry with count 1
      await this.statsEntityDataRepository.insert({
        type: type,
        date,
        user_id: userId,
        country: country,
        count: 1,
      });
    }
  }

  async insertBullhorn(request, bodyDto) {
    const { rawInformation, ...requestBody } = bodyDto;
    this.validateBHPayload(requestBody);
    try {
      const organizationId = await this.getOrganizationId(request);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

      const loginUserId = request.user.id;
      const viewAsUserId = request.headers?.['view-as-user-id'] as string;

      let payloadInsert = {
        ...requestBody,
      };

      if (requestBody.entityName === BullhornEntityEnum.NOTE) {
        const consultantId = await this.dataSource
          .createQueryBuilder(UserEntity, 'u')
          .where({ id: viewAsUserId || loginUserId })
          .select('"consultant_id" as "consultantId"')
          .getRawOne<{ consultantId: string }>();

        payloadInsert = {
          ...payloadInsert,
          commentingPerson: { id: consultantId.consultantId || 1 },
        };
      }

      const result = await insertEntity(
        payloadInsert,
        requestBody.entityName,
        bhRestToken,
        corporateRestUrl,
      );
      if (result.data?.entityName === 'ClientContact') {
        const statsType = `ADDED_${requestBody.entityName.toUpperCase()}`;
        await this.upsertStatsEvent(
          requestBody?.userId || request.user.id,
          statsType,
          result.data.address?.countryName,
        );

        if (rawInformation) {
          const { id, organization_name } = rawInformation as IApolloContactDto;
          const checkExistingFoundContact = await this.dataSource
            .createQueryBuilder(FoundContactEntity, 'fc')
            .where({ apolloContactId: id })
            .getOne();

          if (!checkExistingFoundContact) {
            await this.dataSource
              .createQueryBuilder(FoundContactEntity, 'fc')
              .insert()
              .values({ apolloContactId: id, company: organization_name, rawInformation })
              .execute();
          }
        }

        await this.crmBullhornService
          .syncBullhornContact(
            {
              ...result.data,
              id: result.changedEntityId,
            },
            organizationId,
          )
          .catch((e) => console.log);
      }
      if (result.data?.entityName === 'ClientCorporation') {
        await this.crmBullhornService
          .syncBullhornCompany(
            {
              ...result.data,
              id: result.changedEntityId,
            },
            organizationId,
          )
          .catch((e) => console.log);
        const statsType = `ADDED_COMPANY_TO_BULLHORN}`;
        await this.upsertStatsEvent(
          requestBody?.userId || request.user.id,
          statsType,
          result.data.address?.countryName,
        );
        const checkExistingFoundContact = await this.dataSource
          .createQueryBuilder(FoundCompanyEntity, 'fc')
          .where({ apolloCompanyId: result.changedEntityId })
          .getOne();

        if (!checkExistingFoundContact) {
          await this.dataSource
            .createQueryBuilder(FoundCompanyEntity, 'fc')
            .insert()
            .values({
              apolloCompanyId: result.changedEntityId,
              rawInformation: bodyDto,
              createdBy: viewAsUserId || loginUserId,
            })
            .execute();
        }
      }

      console.info(
        `[BULLHORN] @RequestId: ${bodyDto?.requestId}, @Status: SUCCESS, ${requestBody.entityName.toUpperCase()}, @Payload: ${safeStringify(bodyDto)}, @Response: ${safeStringify(result)}`,
      );
      return this.formatOutputData(
        { key: `INSERT_BULLHORN_${requestBody.entityName.toUpperCase()}` },
        { data: result },
      );
    } catch (error) {
      console.error(
        'Error in insertBullhorn data.errors',
        error?.response?.data?.errors || error.message,
      );
      console.error(
        `[BULLHORN] @RequestId: ${bodyDto?.requestId}, @Status: FAIL, ${requestBody.entityName.toUpperCase()}, @Payload: ${safeStringify(bodyDto)}, @Response: null`,
      );
      const errorMsg = (error?.response?.data?.errors || [])
        .filter(({ severity }) => severity === 'ERROR')
        .map(({ detailMessage: msg }) => msg.charAt(0).toUpperCase() + msg.slice(1))
        .join('; ');
      return this.throwCommonMessage(
        `INSERT_BULLHORN_${requestBody.entityName.toUpperCase()}`,
        errorMsg ? new BadRequestException(errorMsg) : error,
      );
    }
  }

  async insertBullhornWorkflow(request, requestBody) {
    this.validateBHPayload(requestBody);
    try {
      const organizationId = await this.getOrganizationId(request);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

      const results = await insertEntity(
        requestBody,
        requestBody.entityName,
        bhRestToken,
        corporateRestUrl,
      );
      return this.formatOutputData(
        { key: `INSERT_BULLHORN_${requestBody.entityName.toUpperCase()}` },
        { data: results },
      );
    } catch (error) {
      console.error('Error in insertBullhorn data', error.response.data);
      console.error('Error in insertBullhorn data.errors', error.response.data.errors);

      const errorMsg = (error?.response?.data?.errors || [])
        .filter(({ severity }) => severity === 'ERROR')
        .map(({ detailMessage: msg }) => msg.charAt(0).toUpperCase() + msg.slice(1))
        .join('; ');
      return this.throwCommonMessage(
        `INSERT_BULLHORN_${requestBody.entityName.toUpperCase()}`,
        errorMsg ? new BadRequestException(errorMsg) : error,
      );
    }
  }

  async updateBullhorn(request, requestBody, id) {
    const entityName = requestBody?.entityName;
    try {
      const organizationId = await this.getOrganizationId(request);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

      const results = await updateEntity(
        requestBody,
        id,
        entityName,
        bhRestToken,
        corporateRestUrl,
      );
      return this.formatOutputData(
        { key: `UPDATE_BULLHORN_${entityName.toUpperCase()}` },
        { data: results },
      );
    } catch (error) {
      return await this.throwCommonMessage(
        `UPDATE_BULLHORN_${entityName.toUpperCase()}`,
        new BadRequestException(error),
      );
    }
  }

  async getHotListClientContacts(req: any, tearSheetId: string, query: ClientContactQueryDto = {}) {
    if (!tearSheetId) {
      return [];
    }

    const organizationId = await this.getOrganizationId(req);
    let entityName = 'ClientContact';
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

    let allItems = [];
    let hasMore = true;
    let startIndex = 0;
    let pageSize = query.count || '200';

    while (hasMore) {
      pageSize = String(
        Math.min(Number(pageSize), query.count ? Number(query.count) - allItems.length : Infinity),
      );
      const currentQuery = {
        ...query,
        tearSheetId,
        entityName,
        start: String(startIndex),
        count: pageSize,
      };
      const fetchedItems = await queryEntity(
        currentQuery,
        entityName,
        bhRestToken,
        corporateRestUrl,
      );

      allItems = allItems.concat(fetchedItems);
      hasMore = !query.count || fetchedItems.length < Number(query.count);
      startIndex = allItems.length;

      // Exit loop if no items are fetched or the requested count is reached
      if (fetchedItems.length === 0 && allItems.length === 0 && entityName === 'ClientContact') {
        entityName = 'Candidate';
        continue;
      }

      if (fetchedItems.length === 0 || (query.count && allItems.length >= Number(query.count))) {
        break;
      }
    }

    return allItems;
  }

  async checkContactWarnings(req: Request, data: ContactWarningDto) {
    const { emails = [], tearsheetIds = [] } = data;

    if (!emails.length && !tearsheetIds.length) {
      return { warningNumber: 0, emailWarnings: [], domainWarnings: [] };
    }

    const hotlistContacts = (
      await Promise.all(
        tearsheetIds.map(async (tearsheetId) => this.getHotListClientContacts(req, tearsheetId)),
      )
    )
      .flat()
      .map(({ email }) => email);

    const contacts = [...hotlistContacts, ...emails];
    const domainCounts = {};
    const emailCounts = {};
    const domainEmails = {};
    const domainWarnings = [];
    const emailWarnings = [];

    for (const item of contacts) {
      const email = item.toLowerCase();
      if (email?.includes('@')) {
        const [, domain] = email.split('@');
        domainCounts[domain] = (domainCounts[domain] || 0) + 1;
        emailCounts[email] = (emailCounts[email] || 0) + 1;
        if (!domainEmails[domain]) {
          domainEmails[domain] = [];
        }
        domainEmails[domain].push(email);
      }
    }

    for (const domain in domainCounts) {
      if (domainCounts[domain] > 1) {
        domainWarnings.push({
          domain,
          repeatedTimes: domainCounts[domain],
          emails: domainEmails[domain],
        });
      }
    }

    for (const email in emailCounts) {
      if (emailCounts[email] > 1) {
        emailWarnings.push({ email, repeatedTimes: emailCounts[email] });
      }
    }

    return {
      warningNumber: domainWarnings.length + emailWarnings.length,
      emailWarnings,
      domainWarnings,
    };
  }

  async checkHotListContactsInvalid(req: Request, hotlistId: string) {
    const hotlistContacts = await this.getHotListClientContacts(req, hotlistId);
    const hotlistEmails = hotlistContacts.map(({ email }) => email?.toLowerCase()).filter(Boolean);
    const checkEmailFromDb = await this.emailValidationResultRepository.find({
      where: { email: In(hotlistEmails) },
    });
    // const result = await this.mailService.validListEmail({ emails: hotlistEmails });
    const resultArr = checkEmailFromDb.map((item) => ({ email: item.email, result: item.status }));

    const emailsNotInObjects = hotlistEmails.filter(
      (email) => !resultArr.some((obj) => obj.email === email),
    );

    const chunkSize = 5;
    if (emailsNotInObjects.length > chunkSize) {
      for (let i = 0; i < emailsNotInObjects.length; i += chunkSize) {
        const emailChunk = emailsNotInObjects.slice(i, i + chunkSize);

        const result = await this.mailService.validListEmail({ emails: emailChunk });
        resultArr.push(result.result);

        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    } else {
      const result = await this.mailService.validListEmail({ emails: emailsNotInObjects });
      resultArr.push(result.result);
    }

    // const result = await this.mailService.validListEmail({ emails: emailsNotInObjects });
    // resultArr.push(result.result)

    return resultArr.reduce(
      (acc, data) => {
        if (data.result?.toLowerCase() === 'invalid') {
          acc.invalidEmails.push(data.email);
          acc.invalidCount += 1;
        }

        return acc;
      },
      {
        invalidEmails: [],
        invalidCount: 0,
      },
    );
  }

  private delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private formatVacanciesDateTime(dateTime: Date, endOfDay: boolean = true) {
    const year = dateTime.getFullYear().toString().padStart(4, '0');
    const month = (dateTime.getMonth() + 1).toString().padStart(2, '0');
    const day = dateTime.getDate().toString().padStart(2, '0');
    let hour = '00';
    let minute = '00';
    let second = '00';
    if (endOfDay) {
      hour = '23';
      minute = '59';
      second = '59';
    }

    return `${year}${month}${day}${hour}${minute}${second}`;
  }

  async getSimilarJobs(
    companyId: string,
    { bhRestToken, corporateRestUrl }: { bhRestToken: string; corporateRestUrl: string },
  ) {
    try {
      const currentDate = new Date();
      const last30Days = new Date(new Date().setDate(currentDate.getDate() - 30));
      const query = [
        'isDeleted:0',
        'NOT status:Archive',
        `clientCorporation.id:${companyId}`,
        `startDate:[${this.formatVacanciesDateTime(last30Days)} TO ${this.formatVacanciesDateTime(currentDate, true)}]`,
      ];
      const params = {
        fields: 'id,title,clientCorporation(id,name),employmentType,isOpen,description,dateAdded',
        sort: '-id',
        query: query.join(' AND '),
        showTotalMatched: true,
        showLabels: true,
        showTrackSubtype: true,
        BhRestToken: bhRestToken,
        start: 0,
        count: 100,
      };

      return searchVacancies(bhRestToken, corporateRestUrl, params);
    } catch (error) {
      console.error(error);
      throw new BadRequestException('Get similar jobs failed.');
    }
  }

  private async getBHConfig(request: Request) {
    const organizationId = await this.getOrganizationId(request);
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

    return { bhRestToken, corporateRestUrl };
  }

  async deleteBullhornEntity(req: Request, deleteEntityDto: DeleteBHEntityDto) {
    try {
      const { bhRestToken, corporateRestUrl } = await this.getBHConfig(req);
      await deleteEntity({
        ...deleteEntityDto,
        bhpToken: bhRestToken,
        corporateRestEndpoint: corporateRestUrl,
      });

      return this.formatOutputData({ key: 'DELETE_BULLHORN_ENTITY' }, { data: {} });
    } catch (error) {
      console.error('Error in deleteBullhornEntity', error);
      return this.throwCommonMessage('DELETE_BULLHORN_ENTITY', error);
    }
  }

  async deleteBullhornFile(req: Request, deleteEntityDto: DeleteBHEntityDto) {
    try {
      const { bhRestToken, corporateRestUrl } = await this.getBHConfig(req);
      await deleteFileBH({
        ...deleteEntityDto,
        parentId: deleteEntityDto.parentId,
        bhpToken: bhRestToken,
        corporateRestEndpoint: corporateRestUrl,
      });

      return this.formatOutputData({ key: 'DELETE_BULLHORN_FILE' }, { data: {} });
    } catch (error) {
      console.error('Error in deleteBullhornEntity', error);
      return this.throwCommonMessage('DELETE_BULLHORN_ENTITY', error);
    }
  }

  async updateMassAdvance(req: Request, dto: updateMassAdvanceDto) {
    try {
      const { bhRestToken, corporateRestUrl } = await this.getBHConfig(req);
      const data = await updateMassAdvance({
        entity: dto.entity,
        body: dto,
        bhpToken: bhRestToken,
        corporateRestEndpoint: corporateRestUrl,
      });

      return this.formatOutputData({ key: 'UPDATE_MASS_ADVANCE' }, { data: data });
    } catch (error) {
      console.error('Error in updateMassAdvance', error);
      return this.throwCommonMessage('UPDATE_MASS_ADVANCE', error);
    }
  }

  async insertMassAdvance(req: Request, dto: InsertMassAdvanceDto) {
    try {
      const { bhRestToken, corporateRestUrl } = await this.getBHConfig(req);
      const data = await insertMassAdvance({
        entity: dto.entity,
        body: dto.data,
        bhpToken: bhRestToken,
        corporateRestEndpoint: corporateRestUrl,
      });

      return this.formatOutputData({ key: 'INSERT_MASS_ADVANCE' }, { data: data });
    } catch (error) {
      console.error('Error in updateMassAdvance', error);
      return this.throwCommonMessage('INSERT_MASS_ADVANCE', error);
    }
  }

  async sendAppointment(req: Request, dto: InsertMassAdvanceDto) {
    try {
      const { bhRestToken, corporateRestUrl } = await this.getBHConfig(req);
      const data = await sendAppointment({
        entity: dto.entity,
        body: dto.data,
        bhpToken: bhRestToken,
        corporateRestEndpoint: corporateRestUrl,
      });

      return this.formatOutputData({ key: 'SEND_APPOINTMENT' }, { data: data });
    } catch (error) {
      console.error('Error in sendAppointment', error);
      return this.throwCommonMessage('SEND_APPOINTMENT', error);
    }
  }

  async searchLookup(req: Request, dto: searchLookupDataDto) {
    try {
      const { bhRestToken, corporateRestUrl } = await this.getBHConfig(req);
      const data = await searchLookupData({
        entity: dto.entity,
        body: dto,
        bhpToken: bhRestToken,
        corporateRestEndpoint: corporateRestUrl,
      });

      return this.formatOutputData({ key: 'SEARCH_LOOKUP' }, { data: data });
    } catch (error) {
      console.error('Error in searchLookup', error);
      return this.throwCommonMessage('SEARCH_LOOKUP', error);
    }
  }

  async searchDataContact(req: Request, id: string) {
    try {
      const { bhRestToken, corporateRestUrl } = await this.getBHConfig(req);
      const data = await searchContactDetail({
        bhpToken: bhRestToken,
        corporateRestEndpoint: corporateRestUrl,
        id: id,
      });

      return this.formatOutputData({ key: 'SEARCH_LOOKUP' }, { data: data });
    } catch (error) {
      console.error('Error in searchLookup', error);
      return this.throwCommonMessage('SEARCH_LOOKUP', error);
    }
  }

  async massUpdate(req: Request, massUpdateDto: MassUpdateDto) {
    const { entityName, updatedPayload } = massUpdateDto;
    try {
      const organizationId = await this.getOrganizationId(req);
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationId)) || {};

      const apiUrl = `${corporateRestUrl}massUpdate/${entityName}?BhRestToken=${bhRestToken}`;
      const { data } = await this.httpService.axiosRef.post(apiUrl, updatedPayload);

      return this.formatOutputData({ key: `MASS_UPDATE_${entityName.toUpperCase()}` }, { data });
    } catch (error) {
      this.logger.error(error.message);
      console.error(error);
      return await this.throwCommonMessage(
        `MASS_UPDATE_${entityName.toUpperCase()}`,
        new BadRequestException(error),
      );
    }
  }
}

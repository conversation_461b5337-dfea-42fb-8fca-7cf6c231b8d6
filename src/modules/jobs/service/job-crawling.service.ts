import { JobSearchService } from './job-search.service';
import { Injectable } from '@nestjs/common';
import { DataSource, In, IsNull, Not } from 'typeorm';
import { JobSearchEntity } from '../entities/job-search.entity';
import { EC2 } from 'aws-sdk';
import { CrawlingJobEntity } from '../entities/crawling-job.entity';

@Injectable()
export class JobScrawlingService {
  constructor(private readonly dataSource: DataSource, private readonly jobSearchService: JobSearchService) {}
  async restartDeadCrawlingProcess() {
    const ec2 = new EC2({
      region: 'eu-west-2',
      accessKeyId: '********************',
      secretAccessKey: 'bcPKHuRtKw3gnLWf4hRmnGk+U0b8LAo3z3VpDxbl',
    });
    ec2.describeInstances(async (err, data) => {
      console.log('\nIn describe instances:\n');
      if (err) console.log(err, err.stack); // an error occurred
      const activeEc2InstanceIds: string[] = [];
      data?.Reservations.forEach((item) => {
        const instances = item.Instances;
        //16 = running
        activeEc2InstanceIds.push(
          ...instances.filter((item) => item.State.Name === 'running').map((item) => item.InstanceId)
        );
      });

      console.log(activeEc2InstanceIds, 'activeEc2InstanceIds');
      const crawlingJobs = await this.dataSource
        .createQueryBuilder(CrawlingJobEntity, 'cj')
        .where([{ ec2InstanceId: Not(In(activeEc2InstanceIds)) }, { ec2InstanceId: IsNull() }])
        .getMany();
      console.log(crawlingJobs, 'crawlingJobs');

      const deadSearchIds = crawlingJobs.map((item) => item.searchId);
      const MAX_SEARCH_RESTART = 3;
      const deadSearches = await this.dataSource
        .createQueryBuilder(JobSearchEntity, 'js')
        .where({ id: In(deadSearchIds), isDeleted: false, stopScapingAt: IsNull() })
        .take(MAX_SEARCH_RESTART)
        .getMany();

      console.log(deadSearches, deadSearches.length, 'deadSearches');
      await Promise.all(deadSearches.map((item) => this.jobSearchService.createCrawlingProcess(item)));
      return data;
    });

    return true;
  }
}

import { Injectable, Logger, NotFoundException, StreamableFile } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { Promise } from 'bluebird';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { ReportedAgencyRepository } from '../repository/reported-agency.repository';
import { JobBoardsRepository } from '../repository/job-boards.repository';
import { JobBoardResponseDto } from '../dto/job-boards-response.dto';
import { plainToInstance } from 'class-transformer';
import { ReportedAgencyResponseDto } from '../dto/reported-agency-response.dto';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { BulkCreateReportedAgencyDto, CreateReportedAgencyDto } from '../dto/create-reported-agency.dto';
import { UserEntity } from '../../../modules/user/entities/user.entity';
import { GetReportedAgencyQuery } from '../dto/get-reported-agency.dto';
import { OpensearchService } from 'src/modules/opensearch/service/opensearch.service';
import { BulkUpdateReportedAgencyDto } from '../dto/update-reported-agency.dto';
import { In } from 'typeorm';
import { chunkArray, convertArrayToObject, queryAllByChunk } from 'src/common/utils/helpers.util';
import { getLogoCompany } from '../constants/job.const';
import { OnEvent } from '@nestjs/event-emitter';
import { OpenAIService } from 'src/modules/openai/openai.service';
import { RedisService } from 'src/modules/redis/redis.service';
import { EMITTER_EVENTS, JOBS_INDEX, REDIS_KEYS } from 'src/configs/configs.constants';
import { AgencyType, CSVDelimiter, FilterExportReportedAgencyDto } from '../dto/export-reported-agency.dto';
import * as fastCsv from 'fast-csv';
import { VerifiedCompanyRepository } from '../repository/verified_company.repository';
import { ReportedAgency } from '../entities/reported_agency.entity';
import { JobSearchService } from './job-search.service';
import { RoleEnum } from 'src/modules/user/entities/role.entity';

export const MAX_SIZE_OPEN_SEARCH = 65535;

const levenshtein = (a: string, b: string): number => {
  const an = a ? a.length : 0;
  const bn = b ? b.length : 0;
  if (an === 0) {
    return bn;
  }
  if (bn === 0) {
    return an;
  }
  const matrix = new Array<number[]>(bn + 1);
  for (let i = 0; i <= bn; ++i) {
    let row = (matrix[i] = new Array<number>(an + 1));
    row[0] = i;
  }
  const firstRow = matrix[0];
  for (let j = 1; j <= an; ++j) {
    firstRow[j] = j;
  }
  for (let i = 1; i <= bn; ++i) {
    for (let j = 1; j <= an; ++j) {
      if (b.charAt(i - 1) === a.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] =
          Math.min(
            matrix[i - 1][j - 1], // substitution
            matrix[i][j - 1], // insertion
            matrix[i - 1][j] // deletion
          ) + 1;
      }
    }
  }
  return matrix[bn][an];
};

const REPORTED_AGENCIES = {};

@Injectable()
export class ReportedAgencyService extends BaseAbstractService {
  private readonly logger = new Logger(ReportedAgencyService.name);
  constructor(
    readonly i18nService: I18nService,
    private readonly reportedAgencyRepository: ReportedAgencyRepository,
    private readonly jobBoardsRepository: JobBoardsRepository,
    private readonly verifiedCompanyRepository: VerifiedCompanyRepository,
    private readonly openSearchService: OpensearchService,
    private readonly openAI: OpenAIService,
    private readonly redis: RedisService,
    private readonly jobSearchService: JobSearchService,
  ) {
    super(i18nService);
  }

  async createReportedAgency(
    { companyName, aliasCompanyNames = [] }: CreateReportedAgencyDto,
    user: IJwtPayload
  ) {
    const newReportedAgency = this.reportedAgencyRepository.create({
      companyName,
      dataKey: user.id,
      aliasCompanyNames,
      status: 'PENDING',
    });
    const savedReportedAgencyEntity = await this.reportedAgencyRepository.insert(newReportedAgency);
    const companyNames = [
      ...new Set([companyName, ...aliasCompanyNames]),
    ];
    await this.openSearchService.pushNewAgenciesToReportedAgencies(companyNames);
    await this.jobSearchService.removeSearchJobCachedByCompanies(companyNames);

    return plainToInstance(ReportedAgencyResponseDto, savedReportedAgencyEntity);
  }

  async getAllReportedAgencies(queryParams: GetReportedAgencyQuery, user: IJwtPayload) {
    try {
      const { page = 1, limit = 10, searchName = '', sortOrder = 'DESC', status = '' } = queryParams;
      const offset = ((page ?? 1) - 1) * limit;

      const reportedAgencyQueryBuilder = this.reportedAgencyRepository.createQueryBuilder('reported_agency');
      reportedAgencyQueryBuilder
        .leftJoin(UserEntity, 'u', 'u.id = reported_agency.dataKey')
        .select(
          `reported_agency.id as "reported_agency_id", 
          reported_agency.id as "id",
          reported_agency.company_name as  "companyName",
          reported_agency.alias_company_names as "aliasCompanyNames" ,
          reported_agency.updated_at as "updatedAt",
          reported_agency.status as "status",
          u.email as "userEmail"`
        )
        .orderBy({ 'reported_agency.updated_at': sortOrder })
        .offset(offset)
        .limit(limit);

      if (searchName !== '' && searchName) {
        reportedAgencyQueryBuilder.where(
          `(reported_agency.company_name ILIKE :searchName OR ARRAY_TO_STRING(reported_agency.alias_company_names, ', ') ILIKE :searchName)`,
          { searchName: `%${searchName}%` }
        );
      }
      if (status) {
        reportedAgencyQueryBuilder.where(
          `(reported_agency.status = :status)`,
          { status }
        );
      }

      const reportedAgenciesResult = await reportedAgencyQueryBuilder.getRawMany();

      const reportedAgencyTotalCount = await reportedAgencyQueryBuilder.getCount();
      return this.formatOutputData(
        { key: 'REPORTED_AGENCY_FETCHED' },
        {
          data: {
            data: reportedAgenciesResult,
            currentPage: page,
            totalCount: reportedAgencyTotalCount,
            pageSize: limit,
          },
        }
      );
    } catch (error) {
      console.error(`Error fetching reported agencies`, error);
      return this.throwCommonMessage('REPORTED_AGENCY_FETCHED', error);
    }
  }

  async getJobBoardByReportedAgencyId(reportedAgencyId: string, page: number, user: IJwtPayload) {
    try {
      const limit = 10;
      const reportedAgencyById = await this.reportedAgencyRepository.findOne({
        where: {
          id: reportedAgencyId,
          // dataKey: user.id
        },
      });

      if (!reportedAgencyById) {
        throw new NotFoundException('Reported Agency Not Found');
      }

      const { companyName, aliasCompanyNames } = reportedAgencyById;

      const queryCompanies = [...new Set([companyName, ...aliasCompanyNames])];

      const response = await this.openSearchService.getClient().search({
        index: JOBS_INDEX,
        body: {
          query: {
            terms: {
              'company.keyword': [...queryCompanies],
            },
          },
          from: (page - 1) * limit, // Assuming page size is 10
          size: limit, // Adjust the size as needed
          sort: [
            {
              posted: {
                order: 'desc',
              },
            },
          ],
        },
        _source: [
          'jobtitle',
          'extracteddate',
          'joblocationcity',
          'link',
          'description',
          'created_at',
          'source',
          'salary',
          'jobtype',
          'posted',
          'companyrating',
          'max_salary',
          'updated_at',
          'job_id',
          'min_salary',
          'company',
          'joblocationinput',
          'logo_company',
          'send_to_bullhorn_by_user_ids',
          'logoCompany',
          'raw_salary',
          'jobLogs',
        ],
      });
      const data = response.body.hits.hits.map((e: { _source: any }) => ({
        ...e._source,
        logoCompany:
          e._source.logo_company ?? e._source.logoCompany ?? getLogoCompany(e._source?.company, e._source?.source),
        sendToBullHornByUserIds: e._source.send_to_bullhorn_by_user_ids,
        salary: e._source?.raw_salary ?? e._source?.salary,
      }));

      const jobBoardTotalCount = response.body.hits.total.value;
      const jobBoardResponseDtos = plainToInstance(JobBoardResponseDto, data);

      return this.formatOutputData(
        { key: 'JOB_SEARCH_FETCHED' },
        {
          data: {
            data: jobBoardResponseDtos,
            currentPage: page,
            totalCount: jobBoardTotalCount,
            pageSize: limit,
          },
        }
      );
    } catch (error) {
      console.error(`Error fetching job-boards by reported agency id ${reportedAgencyId}`, error);
      return this.throwCommonMessage('REPORTED_AGENCY_FETCHED', error);
    }
  }

  async getFuzzySearch(companyName: string) {
    // const data = await this.jobBoardsRepository
    //   .createQueryBuilder()
    //   // .where('company % :companyName', { companyName })
    //   .where(`SIMILARITY(company, '${companyName}') > :rate`, { rate: 0.5 })
    //   .groupBy('company')
    //   .select('company')
    //   .getRawMany<{ company: string }>();

    // const fuzzyResults = data.map((item) => item.company);

    let sumOtherDocCount = 1; // not equal 0
    let companies = [];
    let from = 0;
    do {
      const response = await this.openSearchService.getClient().search({
        index: JOBS_INDEX,
        body: {
          from,
          query: {
            bool: {
              must: [
                {
                  match: {
                    company: {
                      query: companyName.trim(),
                      minimum_should_match: '90%',
                    },
                  },
                },
                {
                  match: {
                    company: companyName.trim(),
                  },
                },
              ],
            },
          },
          _source: 'false',
          aggs: {
            companies: {
              terms: {
                field: 'company.keyword',
                size: MAX_SIZE_OPEN_SEARCH,
              },
            },
          },
        },
      });
      const bucketsFromOpenSearch = response?.body?.aggregations?.companies?.buckets || [];
      const companiesTemp = bucketsFromOpenSearch?.map((bucket) => bucket?.key);
      companies = [...companies, ...companiesTemp];
      from += MAX_SIZE_OPEN_SEARCH;
      sumOtherDocCount = response?.body?.aggregations?.companies?.sum_other_doc_count || 0;
    } while (sumOtherDocCount !== 0);

    const fuzzyResults = [...new Set([...companies])];
    const fuzzyResultsWithScore = fuzzyResults.map((company: string) => {
      const score = levenshtein(company.toLowerCase(), companyName.trim().toLowerCase());
      return { score, company };
    });
    const sortedResults = fuzzyResultsWithScore
      .sort((a, b) => a.score - b.score)
      .map((item) => item.company)
      .slice(0, 10);
    return this.formatOutputData({ key: 'GET_FUZZY_SEARCH' }, { data: sortedResults });
  }

  async deleteReportedAgency(id: string, user: IJwtPayload) {
    try {
      const reportedAgency = await this.reportedAgencyRepository.findOneBy({ id });
      if (!reportedAgency) {
        return this.formatOutputData({ key: 'DELETE_REPORTED_AGENCY' }, { data: true });
      }
      const isSuccess = await this.openSearchService.removeAgenciesFromReportedAgencies([
        ...new Set([reportedAgency.companyName, ...(reportedAgency.aliasCompanyNames ?? [])]),
      ]);

      console.log('isSuccess', isSuccess);
      await this.reportedAgencyRepository.delete(id);
      if (!reportedAgency.dataKey) {
        // GPT only
        await this.verifiedCompanyRepository.insert({
          ...reportedAgency,
          userId: user?.id,
        });
      }

      return this.formatOutputData({ key: 'DELETE_REPORTED_AGENCY' }, { data: true });
    } catch (error) {
      console.error('Error while deleting reported agency', error);

      return this.throwCommonMessage('DELETE_REPORTED_AGENCY', error);
    }
  }

  async reviewReportedAgency(id: string, status: 'APPROVED'|'REJECTED', user: IJwtPayload) {
    try {
      if (!user.role || user.role === RoleEnum.BASIC_USER) {
        return this.formatOutputData({ key: 'REVIEW_REPORTED_AGENCY' }, { data: false });
      }

      const reportedAgency = await this.reportedAgencyRepository.findOneBy({ id });
      if (!reportedAgency) {
        return this.formatOutputData({ key: 'REVIEW_REPORTED_AGENCY' }, { data: true });
      }

      const result = await this.reportedAgencyRepository.update({ id }, { status });
      if (status === 'APPROVED') {
        if (reportedAgency.status !== 'PENDING') {
          const companyNames = [
            ...new Set([reportedAgency.companyName, ...(reportedAgency.aliasCompanyNames || [])]),
          ];
          await this.openSearchService.pushNewAgenciesToReportedAgencies(companyNames);
          await this.jobSearchService.removeSearchJobCachedByCompanies(companyNames);
        }
        
        return this.formatOutputData({ key: 'REVIEW_REPORTED_AGENCY' }, { data: true });
      }

      await this.openSearchService.removeAgenciesFromReportedAgencies([
        ...new Set([reportedAgency.companyName, ...(reportedAgency.aliasCompanyNames ?? [])]),
      ]);

      if (!reportedAgency.dataKey) {
        // GPT only
        await this.verifiedCompanyRepository.insert({
          ...reportedAgency,
          userId: user?.id,
        });
      }

      return this.formatOutputData({ key: 'REVIEW_REPORTED_AGENCY' }, { data: true });
    } catch (error) {
      console.error('Error while reviewing reported agency', error);

      return this.throwCommonMessage('REVIEW_REPORTED_AGENCY', error);
    }
  }

  async bulkUpdateReportedAgency({ values = [] }: BulkUpdateReportedAgencyDto, user: IJwtPayload) {
    if (!values.length) {
      return this.formatOutputData({ key: 'UPDATE_REPORTED_AGENCY' }, { data: true });
    }

    const agencyCompanies: string[] = [];
    const agencyIds = values.map((item) => item.id);
    const reportedAgencies = await this.reportedAgencyRepository.findBy({ id: In(agencyIds) });
    const reportedAgenciesMapping = convertArrayToObject(reportedAgencies, 'id');
    try {
      await Promise.map(
        values,
        async ({ id, aliasCompanyNames }) => {
          const result = await this.reportedAgencyRepository.update({ id }, { aliasCompanyNames, dataKey: user?.id });
          if (result.affected && reportedAgenciesMapping?.[id]) {
            const reportedAgency = reportedAgenciesMapping[id];
            agencyCompanies.push(reportedAgency.companyName, ...aliasCompanyNames);
          }
        },
        {
          concurrency: 20,
        }
      );

      await this.openSearchService.pushNewAgenciesToReportedAgencies([
        ...new Set(agencyCompanies),
      ]);

      return this.formatOutputData({ key: 'UPDATE_REPORTED_AGENCY' }, { data: true });
    } catch (error) {
      console.error('Error while updating reported agency', error);

      return this.throwCommonMessage('UPDATE_REPORTED_AGENCY', error);
    }
  }

  @OnEvent(EMITTER_EVENTS.REPORT_AGENCY_COMPANY_ADDED)
  async handleCompanyAdded(event: { company: string }) {
    await this.redis
      .getClient()
      .set(`${REDIS_KEYS.UNVERIFIED_COMPANY}:${event.company}`, '1', { EX: 1 * 60 * 60 });
  }

  async checkAgency(companies: string[]) {
    const result = await this.openAI.isCompanyRecruiter(companies);

    return this.formatOutputData({ key: 'CHECK_AGENCY_COMPANY' }, { data: result });
  }

  async detectCurrentAgencies() {
    try {
      const VERIFIED_COMPANIES = {};

      const query = {
        bool: {
          must_not: [
            {
              terms: {
                'company.keyword': {
                  index: 'reported_agency_country',
                  id: 'Global',
                  path: 'reported_agencies.company_name',
                },
              },
            },
          ],
        },
      };

      const { body } = await this.openSearchService.getClient().search({
        index: JOBS_INDEX,
        body: {
          size: 0,
          query,
          aggs: {
            countries: {
              terms: {
                field: 'country.keyword',
                size: 100,
              },
              aggs: {
                companies: {
                  terms: {
                    field: 'company.keyword',
                    size: 1000,
                  },
                },
              },
            },
          },
        },
      });

      const verifiedCompanies = await this.redis.keys(`${REDIS_KEYS.VERIFIED_COMPANY}:*`);
      verifiedCompanies.forEach((key: string) => {
        const [_, companyName] = key.split(':');
        VERIFIED_COMPANIES[companyName] = true;
      });

      const unverifiedCompanies: string[] = body.aggregations?.countries?.buckets?.reduce(
        (acc: string[], bucket: any) => {
          const {
            companies: { buckets: companies },
          } = bucket;
          companies.forEach(({ key: company }) => {
            if (VERIFIED_COMPANIES[company]) {
              return;
            }

            acc.push(company);
          });

          return acc;
        },
        [],
      );

      try {
        const countryCompanies = [...new Set(unverifiedCompanies)];
        const chunks = chunkArray(countryCompanies, 25);
        await Promise.map(
          chunks,
          async (chunkCompanies: string[]) => {
            const agencyCompanies = await this.openAI.isCompanyRecruiter(chunkCompanies);
            if (!agencyCompanies.length) {
              return;
            }
            const reportedAgencies = agencyCompanies.map((companyName: string) =>
              this.reportedAgencyRepository.create({
                companyName,
                aliasCompanyNames: [companyName],
              })
            );

            await this.reportedAgencyRepository.insert(reportedAgencies);
            await this.openSearchService.pushNewAgenciesToReportedAgencies(agencyCompanies);
            await Promise.map(chunkCompanies, async (company: string) => {
              if (!agencyCompanies.includes(company)) {
                await this.redis.set(`${REDIS_KEYS.VERIFIED_COMPANY}:${company}`, '1', {
                  EX: 180 * 24 * 60 * 60,
                });
              }
            });
          },
          {
            concurrency: 20,
          }
        );
      } catch (error) {
        console.error('Error while detecting reported agency', error);
      }

      return this.formatOutputData(
        { key: 'DETECT_CURRENT_AGENCIES' },
        { data: Object.values(unverifiedCompanies).reduce((sum, items) => sum + items.length, 0) }
      );
    } catch (error) {
      return this.throwCommonMessage('DETECT_CURRENT_AGENCIES', error);
    }
  }

  async exportAgencies(res, filter: FilterExportReportedAgencyDto) {
    const { agencyType, fromDate, toDate, delimiter = CSVDelimiter.COMMA } = filter;
    const numPerPage = 500;
    const whereConditions = [];

    if (agencyType) {
      whereConditions.push(agencyType === AgencyType.GPT ? ['(data_key IS NULL)', {}] : ['(data_key IS NOT NULL)', {}]);
    }
    if (fromDate) {
      whereConditions.push(['(updated_at >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(updated_at <= :toDate)', { toDate }]);
    }

    const queryBuilder = this.reportedAgencyRepository
      .createQueryBuilder('reported_agency')
      .limit(numPerPage)
      .orderBy('reported_agency.updated_at', 'DESC');

    whereConditions.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=agencies.csv');
    // Create writable CSV stream
    const csvWritableStream = fastCsv.format({
      headers: ['id', 'companyName', 'aliasCompanyNames', 'updatedAt'],
      delimiter: delimiter === CSVDelimiter.COMMA ? ',' : ';',
      quote: true,
      writeHeaders: true,
    });
    csvWritableStream.pipe(res);

    const fetchAndWrite = async (page: number) => {
      const data = await queryBuilder.offset(page * numPerPage).getMany();
      if (data.length === 0) {
        csvWritableStream.end();
      } else {
        data.forEach((item) => csvWritableStream.write(item));
        fetchAndWrite(page + 1);
      }
    };

    fetchAndWrite(0);
  }

  async bulkInsert(bulkInsertDto: BulkCreateReportedAgencyDto, user: IJwtPayload) {
    const reportedAgencyEntities: Partial<ReportedAgency>[] = [];

    const agencyCompanies: string[] = [];
    bulkInsertDto.agencies.forEach((item) => {
      const { companyName, aliasCompanyNames = [] } = item;
      reportedAgencyEntities.push({ companyName, aliasCompanyNames, dataKey: user.id });
      const agencies = [...new Set([companyName, ...aliasCompanyNames])];
      agencyCompanies.push(...agencies);
    });

    await this.reportedAgencyRepository.insert(reportedAgencyEntities);
    await this.openSearchService.pushNewAgenciesToReportedAgencies([...new Set(agencyCompanies)]);

    return this.formatOutputData({ key: 'BULK_INSERT' }, { data: {} });
  }

  async syncRdsAgencies() {
    try {
      const agencyCompanies: string[] = [];

      const queryBuilder = this.reportedAgencyRepository
        .createQueryBuilder('reported_agency')
        .select('id, company_name, alias_company_names')
        .orderBy('reported_agency.updated_at', 'DESC');

      const reportedAgencies = await queryAllByChunk(queryBuilder);
      reportedAgencies.forEach((record) => {
        agencyCompanies.push(
          record.company_name,
          ...(Array.isArray(record.alias_company_names) ? record.alias_company_names : []),
        );
      });

      /* eslint-disable no-restricted-syntax */
      await this.openSearchService.setOSReportedAgencies(
        [...new Set(agencyCompanies)],
      );

      return this.formatOutputData({ key: 'SYNC_RDS_REPORTED_AGENCY' }, { data: true });
    } catch (error) {
      console.error('Error while synchronizing RDS reported agency', error);

      return this.throwCommonMessage('SYNC_RDS_REPORTED_AGENCY', error);
    }
  }
}

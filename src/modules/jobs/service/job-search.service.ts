import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Promise } from 'bluebird';
import { spawn } from 'child_process';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { JobSearchRepository } from '../repository/job-search.repository';
import { CreateJobSearchDto } from '../dto/job-request.dto';
import { JobBoardsRepository } from '../repository/job-boards.repository';
import { JobBoardResponseDto } from '../dto/job-boards-response.dto';
import { plainToInstance } from 'class-transformer';
import { JobSearchResponseDto } from '../dto/job-search-response.dto';
import { ReportedAgencyRepository } from '../repository/reported-agency.repository';
import { SetGroupByCompanyEnum, SetGroupByCompanyRequestDto, UpdateJobSearchDto } from '../dto/update-job-search.dto';
import { IJwtPayload, ISimpleUser } from 'src/modules/auth/payloads/jwt-payload.payload';
import { jobBoardsQueryBuild } from '../utils/job-board-query.util';
import { DataSource, FindOptionsWhere, In, IsNull, Not } from 'typeorm';
import { IncludeSentJobEnum, JobBySearchIdQuery, SortJobEnum } from '../dto/job-by-search-id.dto';
import { JobSyncRepository } from '../repository/job-sync.repository';
import { PREFIX_SYNC_REFERENCE } from '../constants/sync.const';
import { getReferenceNameBySearchesName } from '../utils/sync.util';
import { UserTrackingEntity } from '../../../modules/user-tracking/entities/user-tracking.entity';
import { JobSearchEntity } from '../entities/job-search.entity';
import { ReportedAgency } from '../entities/reported_agency.entity';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { NotificationEntity } from '../../../modules/notification/entities/notification.entity';
import { CacheService } from 'src/modules/cache/cache.service';
import { CheckoutNewJobQueryDto } from '../dto/job-search/checkout-new-job-query.dto';
import { NotificationQueryDto } from '../dto/job-search/notification.dto';
import * as path from 'path';
import * as fs from 'fs';
import {
  convertArrayToObject,
  convertCamelToNormalCase,
  getCommonItems,
  splitArray,
  standardizeJobType,
  uniqueArray,
} from 'src/common/utils/helpers.util';
import { CrawlingJobEntity } from '../entities/crawling-job.entity';
import { HttpService } from '@nestjs/axios';
import { CrawlingConfig, JOBS_INDEX, REFINE_API } from 'src/configs/configs.constants';
import { GetJobSearchListQuery } from '../dto/get-job-search-list.dto';
import { AwsService } from 'src/modules/aws/services/aws-db.service';
import { CrawlingRequestDto } from '../dto/job/crawling-request.dto';
import { STOP_BY_ACTOR } from 'src/common/constants/common.constant';
import { JobLead } from '../entities/job-leads.entity';
import { OpensearchService } from '../../opensearch/service/opensearch.service';
import { JOB_BOARDS, getLogoCompany } from '../constants/job.const';
import { DeleteBasicJobCompanyDto } from '../dto/job/job.dto';
import { UserRepository } from '../../user/repositories/user.repository';
import { JobLogEnum } from '../entities/job-log.entity';
import { HuggingfacePipeline } from '../utils/huggingface-transformers';
import { IResponseCommon } from 'src/common/interfaces/common.interface';

@Injectable()
export class JobSearchService extends BaseAbstractService {
  private readonly logger = new Logger(JobSearchService.name);
  constructor(
    readonly i18nService: I18nService,
    private readonly jobSearchRepository: JobSearchRepository,
    private readonly jobBoardsRepository: JobBoardsRepository,
    private readonly jobSyncRepository: JobSyncRepository,
    private readonly dataSource: DataSource,
    private readonly elasticsearchService: OpensearchService,
    private readonly userRepository: UserRepository,
    private readonly cacheService: CacheService,
    private readonly httpService: HttpService,
    private readonly awsService: AwsService
  ) {
    super(i18nService);
  }

  // async countForNewJobSearchHelper(userId?: string, checkedJobSearches?: JobSearchEntity[]) {
  //   const jobSearches =
  //     checkedJobSearches ??
  //     (await this.jobSearchRepository.find({
  //       where: { dataKey: userId, isDeleted: false },
  //       relations: { userTrackings: true },
  //     }));

  //   const countData = await Promise.all(
  //     jobSearches.map(async (search) => {
  //       const userTracking = search?.userTrackings?.[0];
  //       const jobBoardQueryBuilder = await this.getFullyQueryOfJobBoards({
  //         search,
  //         queryParams: { limit: 10 },
  //         newJobOnly: true,
  //         lastVisitedAt: userTracking?.lastVisitedAt,
  //       });

  //       const jobs = await jobBoardQueryBuilder
  //         .select(
  //           'job_id as id, company,source,posted,jobtitle,description,joblocationcity,min_salary,max_salary,updated_at'
  //         )
  //         .getRawMany();

  //       if (jobs.length === 0) {
  //         return null;
  //       }

  //       return {
  //         count: jobs.length,
  //         type: 'NEW_JOB',
  //         jobSearch: {
  //           id: search.id,
  //           searchName: search.searchName,
  //           userId: search.dataKey,
  //         },
  //         jobIds: jobs.map((job) => job.id),
  //       };
  //     })
  //   );

  //   const responseData = countData.filter(Boolean);

  //   return responseData;
  // }

  async removeSearchJobCachedByCompany(company: string) {
    try {
      const pattern = `job-search.${company}.*`;
      const keyCaches = await this.cacheService.keys(pattern);

      Promise.map(
        keyCaches,
        async (key: string) => {
          await this.cacheService.remove(key);
        },
        { concurrency: 50 }
      ).catch(() => { });
    } catch (error) {
      console.error('Error in removeSearchJobCachedByCompany', error);
      return null;
    }
  }

  async removeSearchJobCachedByCompanies(companys: string[]) {
    Promise.map(
      companys,
      async (company: string) => {
        await this.removeSearchJobCachedByCompany(company);
      },
      { concurrency: 50 }
    ).catch(() => { });
  }

  async getSearchesByCondition(condition: FindOptionsWhere<JobSearchEntity>) {
    return this.jobSearchRepository.find({
      where: condition,
      relations: { userTrackings: true },
    });
  }

  async checkoutNewJobs(queryDto: CheckoutNewJobQueryDto) {
    // return this.notificationQueue.add('check-new-notification', queryDto, {
    //   delay: 5000,
    // });

    return true;
  }

  async countForNewJobSearch(user: { id: string }, queryDto?: NotificationQueryDto) {
    const userId = user.id;
    const { page = 1, pageSize = 20 } = queryDto ?? {};

    const notReadCount = await this.dataSource
      .createQueryBuilder(NotificationEntity, 'n')
      .where('user_id = :userId and is_read = :isRead', {
        userId,
        isRead: false,
      })
      .getCount();

    const notifications = await this.dataSource
      .createQueryBuilder(NotificationEntity, 'n')
      .where('user_id = :userId', { userId })
      .orderBy('created_at', 'DESC')
      .select(
        `
        id,
        user_id as "userId",
        job_search_id as "jobSearchId",
        job_search_name as "jobSearchName",
        notification_type as "notificationType",
        job_count as "jobCount",
        is_read as "isRead",
        created_at as "createdAt"
        `
      )
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getRawMany<{
        id: string;
        userId: string;
        jobSearchId: string;
        jobSearchName: string;
        notificationType: string;
        jobCount: string;
        isRead: string;
        createdAt: string;
      }>();
    return this.formatOutputData(
      { key: 'COUNT_FOR_NEW_JOB_SEARCH' },
      { data: { items: notifications, count: { notReadCount } } }
    );
  }

  async countNewSearch(user: IJwtPayload) {
    const jobSearches = await this.jobSearchRepository.find({
      where: { dataKey: user.id, isDeleted: false },
      relations: { userTrackings: true },
    });

    const countData = await Promise.all(
      jobSearches.map(async (search) => {
        const userTracking = search?.userTrackings?.[0];
        // const jobBoardQueryBuilder = await this.getFullyQueryOfJobBoards({
        //   search,
        //   queryParams: { limit: 10 },
        //   newJobOnly: true,
        //   lastVisitedAt: userTracking?.lastVisitedAt,
        // });

        // return jobBoardQueryBuilder.getCount();
      })
    );

    const count = countData.filter(Boolean).length;

    return this.formatOutputData({ key: 'COUNT_NEW_SEARCH' }, { data: { count } });
  }

  async createSearch(createJobSearchDto: CreateJobSearchDto, user: IJwtPayload) {
    let {
      searchName,
      keywords,
      location,
      postedWithin,
      minSalary,
      maxSalary,
      jobBoards,
      updatedFor,
      adminLevelOneArea,
      country,
      jobTitles,
      datePosted,
    } = createJobSearchDto;

    const userId = updatedFor ?? user.id;

    let postedStartDate = (postedWithin || []).length == 2 ? postedWithin[0] : null;
    let postedEndDate = (postedWithin || []).length == 2 ? postedWithin[1] : null;
    try {
      //start the data crawler
      let savedJob = {
        searchName,
        location,
        keywords: (keywords || []).length > 0 ? keywords.join(',') : null,
        postedStartDate: postedStartDate ? new Date(postedStartDate) : null,
        postedEndDate: postedEndDate ? new Date(postedEndDate) : null,
        minSalary: minSalary ? Number(minSalary) : null,
        maxSalary: maxSalary ? Number(maxSalary) : null,
        jobBoards: (jobBoards || []).length > 0 ? jobBoards.join(',') : null,
        dataKey: userId,
        activeSubprocessIds: [],
        adminLevelOneArea,
        country: country?.trim() ? country : 'United Kingdom',
        jobTitles: jobTitles,
        datePosted,
      };
      //STEP 1: create search
      const savedJobSearch = await this.jobSearchRepository.insert(
        this.jobSearchRepository.create({ ...savedJob, updatedBy: user.id })
      );

      //STEP 2: create user-tracking
      await this.dataSource
        .createQueryBuilder()
        .insert()
        .into(UserTrackingEntity)
        .values({
          userId,
          jobSearchId: savedJobSearch?.raw?.[0]?.id,
          lastVisitedAt: new Date(),
        })
        .execute();

      //STEP 3: create crawling process, async
      this.createCrawlingProcess({ id: savedJobSearch.raw?.[0]?.id, ...savedJob } as JobSearchEntity);

      return this.formatOutputData({ key: 'JOB_SEARCH_CREATED' }, { data: savedJobSearch.identifiers[0] });
    } catch (error) {
      console.error('Error inserting job-search search:', error);
      return await this.throwCommonMessage('JOB_SEARCH_CREATED', error);
    }
  }

  //TODO: change to object param
  createCrawlingRequestDto(
    keywords: string[],
    jobBoards: string[],
    adminLevelOneArea: string,
    country: string
  ): CrawlingRequestDto[] {
    const standardJobBoards = jobBoards && jobBoards.length ? jobBoards : JOB_BOARDS;

    return keywords.flatMap((keyword) => {
      return standardJobBoards.map(
        (jobBoard) =>
        ({
          keyword,
          adminLevelOneArea: adminLevelOneArea ?? '',
          jobBoard,
          country: country ?? '',
        } as CrawlingRequestDto)
      );
    });
  }

  async createCrawlingProcess(search: JobSearchEntity) {
    console.log('createCrawlingProcess is running');
    try {
      const jobIds = [Math.ceil(Math.random() * 10).toString()];

      await this.jobSearchRepository.update(
        { id: search.id },
        { activeSubprocessIds: jobIds.filter(Boolean), stopScapingAt: null, stopBy: null }
      );

      await this.cacheService.remove(`JOBSEARCH:${search.id}`);
    } catch (error) {
      console.log('CREATE_CRAWLING_PROCESS----------', error);
      return true;
    }
  }

  async callToCrawlerServer(
    bodyRequest: { keyword: string; adminLevelOneArea: string; jobBoard: string; country: string }[]
  ) {
    try {
      //TODO: split the number of this request
      // allocate more instances
      // ==>  more than 1 instance per a job searching request
      const data = await this.httpService.axiosRef.post(CrawlingConfig.startCrawlingApi, bodyRequest, {
        timeout: 30000,
      });
      const response: { jobId: string; ec2InstanceId: string }[] = data.data.map(
        ({ job_id: jobId, ec2_instance_id: ec2InstanceId }) => ({
          jobId,
          ec2InstanceId,
        })
      );

      return response;
    } catch (error) {
      console.log('Error in callToCrawlerServer', error);
      return null;
    }
  }

  async createOrUpdateCrawlingJob(searchId: string, { ec2InstanceId }) {
    try {
      const isExisted = await this.dataSource.createQueryBuilder(CrawlingJobEntity, 'cl').where({ searchId }).getOne();

      if (isExisted) {
        return this.dataSource
          .createQueryBuilder(CrawlingJobEntity, 'cj')
          .update()
          .where({ searchId })
          .set({ ec2InstanceId })
          .execute();
      }
      return this.dataSource
        .createQueryBuilder()
        .insert()
        .into(CrawlingJobEntity)
        .values({
          searchId,
          ec2InstanceId,
        })
        .execute();
    } catch (error) {
      console.log('CREATE_CRAWLING_JOBS: *************************', error);
      return true;
    }
  }

  async updateJobSearchById(searchId: string, payload: UpdateJobSearchDto, user: { id: string }) {
    try {
      const loginUserId = user.id;
      const currentSearch: JobSearchEntity = await this.jobSearchRepository.findOneBy({ id: searchId });
      if (!currentSearch) {
        throw new NotFoundException('Search is deleted or not found');
      }

      const {
        searchName,
        keywords,
        location,
        postedWithin,
        minSalary,
        maxSalary,
        jobBoards,
        country,
        adminLevelOneArea,
        jobTitles,
        datePosted,
      } = payload;
      const postedStartDate = (postedWithin || []).length == 2 ? postedWithin[0] : null;
      const postedEndDate = (postedWithin || []).length == 2 ? postedWithin[1] : null;
      const keywordsString = (keywords || []).length > 0 ? keywords.join(',') : null;
      const jobBoardsString = (jobBoards || []).length > 0 ? jobBoards.join(',') : null;
      const startDate = postedStartDate ? new Date(postedStartDate) : null;
      const endDate = postedEndDate ? new Date(postedEndDate) : null;

      await this.jobSearchRepository.update(
        { id: searchId }, // Specify the condition for the update
        {
          searchName,
          keywords: keywordsString,
          location,
          postedStartDate: startDate,
          postedEndDate: endDate,
          minSalary,
          maxSalary,
          jobBoards: jobBoardsString,
          updatedBy: loginUserId,
          country,
          adminLevelOneArea,
          datePosted,
          jobTitles,
        }
      );

      const updatedSearch: any = {
        ...currentSearch,
        searchName,
        keywords: keywordsString,
        location: location || [],
        postedStartDate: startDate,
        postedEndDate: endDate,
        minSalary,
        maxSalary,
        jobBoards: jobBoardsString,
        updatedBy: loginUserId,
        country,
        adminLevelOneArea,
        datePosted,
        jobTitles,
      };

      const queryParams: JobBySearchIdQuery = { page: 1, limit: 1 };

      const condition = await this.getJobsConditionByOpenSearch(updatedSearch, queryParams, user);

      const response = await this.elasticsearchService.getClient().count({
        index: JOBS_INDEX, // Replace with your actual index name
        body: {
          query: condition,
        },
      });

      const totalCount = response.body.count;
      if (totalCount !== currentSearch.counts) {
        await this.jobSearchRepository.update({ id: currentSearch.id }, { counts: totalCount });
      }

      // const {
      //   keywords: oldKeyWords,
      //   location: oldLocation,
      //   postedEndDate: oldPostedEndDate,
      //   postedStartDate: oldPostedStartDate,
      //   minSalary: oldMinSalary,
      //   maxSalary: oldMaxSalary,
      //   jobBoards: oldJobBoards,
      //   country: oldCountry,
      //   adminLevelOneArea: oldAdminLevelOneArea,
      // } = currentSearch;
      // const isRestart = currentSearch.stopScapingAt
      //   ? false
      //   : keywordsString !== oldKeyWords ||
      //     location !== oldLocation ||
      //     startDate !== oldPostedStartDate ||
      //     endDate !== oldPostedEndDate ||
      //     minSalary !== oldMinSalary ||
      //     maxSalary !== oldMaxSalary ||
      //     country !== oldCountry ||
      //     adminLevelOneArea !== oldAdminLevelOneArea ||
      //     oldJobBoards !== jobBoardsString;
      // if (isRestart) {
      //   // await this.killProcessJob(searchId, loginUserId);
      //   await this.startProcessJob(searchId, loginUserId);
      // }
      await this.cacheService.remove(`JOBSEARCH:${searchId}`);

      return this.formatOutputData({ key: 'UPDATE_SEARCH' }, { data: {} });
    } catch (error) {
      console.log('Error in UpdateSearch', error);
      return this.throwCommonMessage('UPDATE_SEARCH', error);
    }
  }

  async getFullyQueryOfJobBoards({
    search,
    queryParams,
    newJobOnly,
    lastVisitedAt,
    reportedAgencies,
    notification,
    sentJobIds,
  }: {
    search: JobSearchEntity;
    queryParams: JobBySearchIdQuery;
    newJobOnly?: boolean;
    lastVisitedAt?: Date;
    reportedAgencies?: ReportedAgency[];
    notification?: { id: string; jobIds: string };
    sentJobIds: string[];
  }) {
    let jobBoardQueryBuilder = this.jobBoardsRepository.createQueryBuilder('job_boards');

    if (notification) {
      jobBoardQueryBuilder.andWhere('job_id in (:...jobIds)', {
        jobIds: notification.jobIds,
      });
    } else {
      jobBoardQueryBuilder = await jobBoardsQueryBuild(jobBoardQueryBuilder, search);
      //Add further filter based on query params
      //TODO: make this smarter
      if (
        queryParams.searchText !== '' ||
        queryParams.jobBoards !== '' ||
        queryParams.keywords !== '' ||
        queryParams.location !== '' ||
        queryParams.maxSalary !== '' ||
        queryParams.minSalary !== '' ||
        queryParams.postedStartDate !== '' ||
        queryParams.postedEndDate !== '' ||
        queryParams.companies?.length
      ) {
        jobBoardQueryBuilder = await jobBoardsQueryBuild(
          jobBoardQueryBuilder,
          queryParams,
          newJobOnly ? lastVisitedAt : null,
          true
        );
      }
    }

    const reportedAgencyCountry = search.country ?? 'United Kingdom';
    const subQuery = this.dataSource
      .createQueryBuilder(ReportedAgency, 'ra')
      .select('ra.company_name')
      .where('ra.company_name = job_boards.company')
      .andWhere(`ra.country = '${reportedAgencyCountry}'`);
    jobBoardQueryBuilder.andWhere(`NOT EXISTS (${subQuery.getQuery()})`);

    const excludedJobIds = [...sentJobIds];
    if (search?.pinnedJobIds) {
      excludedJobIds.push(...(search.pinnedJobIds?.split(',') ?? []));
    }
    if (search?.deletedJobIds) {
      excludedJobIds.push(...(search.deletedJobIds?.split(',') ?? []));
    }

    if (excludedJobIds.length > 0) {
      jobBoardQueryBuilder.andWhere('job_boards.job_id NOT IN (:...excludedJobIds)', { excludedJobIds });
    }

    return jobBoardQueryBuilder;
  }

  async getJobSearchById(searchId: string, userId: string): Promise<JobSearchEntity> {
    const cacheKey = `JOBSEARCH:${searchId}`;

    let data = null;
    // let data = await this.cacheService.get(cacheKey);
    if (!data) {
      data = await this.jobSearchRepository.findOne({
        where: { id: searchId, dataKey: userId, isDeleted: false },
        // relations: { userTrackings: true },
        select: {
          id: true,
          keywords: true,
          location: true,
          postedStartDate: true,
          postedEndDate: true,
          minSalary: true,
          maxSalary: true,
          jobBoards: true,
          isDeleted: true,
          dataKey: true,
          pinnedJobIds: true,
          deletedJobIds: true,
          stopScapingAt: true,
          adminLevelOneArea: true,
          country: true,
          isGroupByCompany: true,
          jobTitles: true,
          datePosted: true,
          excludeCompanies: true,
          excludeKeywords: true,
          excludeTitles: true,
          isRefineBySearch: true,
          latestRefinedJobDate: true,
          oldestRefinedJobDate: true,
          isScanOldest: true,
        },
      });

      if (data) {
        await this.cacheService.set(cacheKey, data, 60 * 5);
      }
    }

    return data?.dataKey === userId ? data : null;
  }

  async getJobSearchByIdHelper(searchId: string, queryParams: JobBySearchIdQuery, user: { id: string }) {
    const { newJobOnly, notificationId } = queryParams;
    const notification = notificationId
      ? await this.dataSource
        .createQueryBuilder(NotificationEntity, 'n')
        .where('id = :notificationId', { notificationId })
        .select('id, job_ids as "jobIds"')
        .getRawOne<{ id: string; jobIds: string }>()
      : null;

    let jobSearchById = await this.getJobSearchById(searchId, user.id);
    const lastVisitedAt = jobSearchById?.userTrackings?.[0]?.lastVisitedAt;

    const myJobLeads = await this.dataSource
      .createQueryBuilder(JobLead, 'jl')
      .where({ dataKey: user.id })
      .select('jl.job_board_id as "jobBoardId"')
      .getRawMany<{ jobBoardId: string }>();
    const sentJobIds = myJobLeads.map((item) => item.jobBoardId).filter(Boolean);

    const jobBoardQueryBuilder = await this.getFullyQueryOfJobBoards({
      search: jobSearchById,
      queryParams,
      newJobOnly,
      lastVisitedAt,
      notification,
      sentJobIds,
    });

    if (!jobSearchById?.keywords?.length) {
      jobBoardQueryBuilder.andWhere('job_id = :destroy', { destroy: '0' });
    }

    return { jobBoardQueryBuilder, search: jobSearchById };
  }

  async getJobBoardBySearchId(searchId: string, queryParams: JobBySearchIdQuery, user: ISimpleUser) {
    try {
      const { sort } = queryParams;
      const standardQueryParams = {
        page: 1,
        ...queryParams,
      };
      const { jobBoardQueryBuilder, search } = await this.getJobSearchByIdHelper(searchId, standardQueryParams, user);
      const limit = queryParams.limit || 10;
      const currentPage = isNaN(standardQueryParams.page) ? 1 : standardQueryParams.page;
      const offset = (currentPage - 1) * limit;
      jobBoardQueryBuilder
        .skip(offset)
        .take(limit)
        .addOrderBy('posted', sort || 'DESC');

      // if (queryParams.searchText) {
      //   jobBoardQueryBuilder.addOrderBy(`SIMILARITY(job_boards.jobtitle, '${queryParams.searchText}')`, 'DESC');
      // }

      const jobBoardResult = await jobBoardQueryBuilder.getMany();

      const jobBoardResponseDtos = plainToInstance(JobBoardResponseDto, jobBoardResult);

      // await this.dataSource
      //   .createQueryBuilder()
      //   .update(UserTrackingEntity)
      //   .where('job_search_id = :searchId', { searchId })
      //   .set({ lastVisitedAt: new Date() })
      //   .execute();

      const refinedJobs = jobBoardResponseDtos.map((item) => ({
        //TODO: update this when logoCompany is stable (is not null)
        ...item,
        logoCompany: item.logoCompany ?? `${item.source}-${item.company}.png`,
        filteredKeyword: item.jobtitle.concat(' ', item.description).toLowerCase(),
      }));

      let returnedJobs = [];
      for (const item of refinedJobs) {
        const keywords = search.keywords?.split(',');
        const isContainKeywords = keywords.some((keyword) => item.filteredKeyword.includes(keyword.toLowerCase()));

        if (isContainKeywords && returnedJobs.length < limit) {
          returnedJobs.push(item);
        }

        if (returnedJobs.length === limit) {
          break;
        }
      }

      // if (!returnedJobs || !returnedJobs.length) {
      //   //TODO: to update here
      //   const nextPageData = await this.getJobBoardBySearchId(
      //     searchId,
      //     { ...queryParams, page: queryParams.page + 1 },
      //     user
      //   );
      //   returnedJobs = nextPageData.result?.data;
      // }
      return this.formatOutputData(
        { key: 'JOB_SEARCH_FETCHED' },
        {
          data: {
            data: returnedJobs,
            search,
            totalCount: returnedJobs?.length, //TODO: remove later
            pageSize: limit, //TODO: remove later
            currentPage: queryParams.page, //TODO: remove later
          },
        }
      );
    } catch (error) {
      console.error(`Error fetching job-boards by search id ${searchId}`, error);
      // return await this.throwCommonMessage('JOB_SEARCH_FETCHED', error);
      return this.formatOutputData(
        { key: 'JOB_SEARCH_FETCHED' },
        {
          data: {
            data: [],
            totalCount: 10, //TODO: remove later
            pageSize: queryParams.limit || 10, //TODO: remove later
            currentPage: 1, //TODO: remove later
          },
        }
      );
    }
  }

  async getCompaniesOfSearch(searchId: string, queryParams: JobBySearchIdQuery, user: ISimpleUser) {
    try {
      const standardQueryParams = {
        page: 1,
        ...queryParams,
      };
      const limit = queryParams.limit || 10;
      const currentPage = Number(standardQueryParams.page);
      const offset = (currentPage - 1) * limit;
      const { jobBoardQueryBuilder } = await this.getJobSearchByIdHelper(searchId, standardQueryParams, user);
      jobBoardQueryBuilder
        .groupBy('job_boards.company, job_boards.posted')
        .select('job_boards.company as company, job_boards.posted as posted')
        .addOrderBy('job_boards.posted', 'DESC');

      if (!queryParams.getAll) {
        jobBoardQueryBuilder.take(limit).skip(offset);
      }

      const jobBoardResult = await jobBoardQueryBuilder.getRawMany<{ company: string }>();

      const companies = jobBoardResult.map((item) => item.company);
      return this.formatOutputData(
        { key: 'GET_COMPANIES' },
        {
          data: {
            data: [...new Set(companies)],
            currentPage,
          },
        }
      );
    } catch (error) {
      console.log('Error in getCompaniesOfSearch', error);
      return this.throwCommonMessage('GET_COMPANIES', error);
    }
  }

  async getCompaniesOfSearchByOpenSearch(searchId: string, queryParams: JobBySearchIdQuery, user: ISimpleUser) {
    try {
      const page = queryParams.page || 1;
      const limit = queryParams.limit || 10000;
      const sort: any = queryParams?.sort?.toLowerCase() || 'desc';

      const search: JobSearchEntity = await this.getJobSearchById(searchId, user.id);
      if (!search) {
        return this.formatOutputData(
          { key: 'GET_COMPANIES_BY_OPENSEARCH' },
          {
            data: {
              data: [],
              search,
            },
          }
        );
      }
      const condition = await this.getJobsConditionByOpenSearch(search, queryParams, user);
      // console.log('condition of getJobsConditionByOpenSearch', condition);
      let response = await this.elasticsearchService.getClient().search({
        index: JOBS_INDEX, // Replace with your actual index name
        from: (page - 1) * limit,
        size: limit, // Adjust the size as needed
        body: {
          sort: [
            {
              posted: {
                order: sort,
              },
            },
          ],
          query: condition,
          aggs: {
            group_by_field: {
              terms: {
                field: 'company.keyword',
                size: 10 * page,
              },
            },
          },
        },
      });

      //Refine AI company

      // Refine AI
      let refinedJobs = response.body.hits.hits;
      if (search.isRefineBySearch) {
        refinedJobs = await this.refineJobsByAI({ keywords: search.keywords.split(","), opensearchResponse: response });
      }

      const resultCompanies = [...new Set(refinedJobs.map((item) => item._source.company))]
      return this.formatOutputData(
        { key: 'GET_COMPANIES_BY_OPENSEARCH' },
        {
          data: {
            data: resultCompanies,
          },
        }
      );
    } catch (error) {
      console.log('Error in getCompaniesOfSearchByOpenSearch', error);
      return this.throwCommonMessage('GET_COMPANIES_BY_OPENSEARCH', error);
    }
  }

  private isUpdateCount(queryParams: JobBySearchIdQuery, search: JobSearchEntity) {
    const filterHasValues = Object.keys(queryParams).filter((key) => queryParams[key]);
    const filterCriteria = [
      'searchText',
      'jobBoards',
      'keywords',
      'location',
      'maxSalary',
      'minSalary',
      'postedStartDate',
      'postedEndDate',
      'company',
      'includeSentJob',
      'jobTitles',
    ];
    // if one of the above filter has value, it should not update count
    const isUpdatedByFilter = !filterHasValues.some((key) => filterCriteria.includes(key));

    return queryParams.isSavedExclude || isUpdatedByFilter;
  }

  async getPaginationOfSearchDetail(searchId: string, queryParams: JobBySearchIdQuery, user: { id: string }) {
    const standardQueryParams = {
      page: 1,
      ...queryParams,
    };
    const { jobBoardQueryBuilder } = await this.getJobSearchByIdHelper(searchId, standardQueryParams, user);
    const jobBoardTotalCount = await jobBoardQueryBuilder.getCount();
    if (this.isUpdateCount) {
      await this.jobSearchRepository.update({ id: searchId }, { counts: jobBoardTotalCount });
      await this.cacheService.remove(`JOBSEARCH:${searchId}`);
    }

    return this.formatOutputData(
      { key: 'GET_PAGINATION_SEARCH_DETAIL' },
      {
        data: {
          totalCount: jobBoardTotalCount,
          pageSize: queryParams.limit || 10, //for now, 10 is allowed only,
          currentPage: standardQueryParams.page,
        },
      }
    );
  }

  async deleteCrawlingJob(searchId: string) {
    await this.dataSource.createQueryBuilder(CrawlingJobEntity, 'cj').delete().where({ searchId }).execute();
  }
  async killProcessJob(searchId: string, loginUserId: string) {
    try {
      const search = await this.jobSearchRepository.findOneBy({ id: searchId });
      if (!search) {
        return this.throwCommonMessage('KILL_PROCESS_JOB', new NotFoundException());
      }

      const activeSubprocessIds = search.activeSubprocessIds;
      if (!activeSubprocessIds) {
        return this.throwCommonMessage(
          'KILL_PROCESS_JOB',
          new BadRequestException('Scrapping process is stopped already')
        );
      }
      if (activeSubprocessIds.length) {
        //async
        this.callToKillCrawlProcess(search.activeSubprocessIds);
      }
      await this.jobSearchRepository.update(
        { id: searchId },
        {
          activeSubprocessIds: null,
          stopScapingAt: new Date(),
          updatedBy: loginUserId,
          stopBy: loginUserId,
        }
      );

      await this.deleteCrawlingJob(searchId);
      await this.cacheService.remove(`JOBSEARCH:${searchId}`);

      return this.formatOutputData({ key: 'KILL_PROCESS_JOB' }, { data: {} });
    } catch (error) {
      this.logger.error(error.message);
      //TODO: throw error instead => handle with FE
      return this.formatOutputData({ key: 'KILL_PROCESS_JOB' }, { data: { error } });
    }
  }

  async callToKillCrawlProcess(jobIds: string[]) {
    try {
      // await this.httpService.axiosRef.post(CrawlingConfig.deleteCrawlingProcess, { jobIds });
      await this.awsService.createEvent({
        message: {
          type: 'kill',
          jobIds: jobIds,
        },
        subject: 'KILL_PROCESS',
      });
      return true;
    } catch (error) {
      console.log('Error in callToKillCrawlProcess', error);
      return null;
    }
  }

  private crawlJobsByPython({
    keywords,
    location,
    searchName,
    jobBoards,
  }: {
    keywords: string[];
    location: string;
    searchName: string;
    jobBoards: string;
  }): number[] {
    try {
      const pyFile = 'jobsScraper/scraper_all.py';
      const pids: number[] = [];
      if (keywords) {
        for (const keyword of keywords) {
          const args = [
            `keywords=${keyword || ''}`,
            `location=${location || ''}`,
            `jobTitleQuery=${keyword || ''}`,
            `jobBoards=${jobBoards || ''}`,
          ];
          args.unshift(pyFile);
          args.unshift('-u');
          const child = spawn('python3', args);
          child.stdout.on('data', (data) => {
            console.log(data.toString());
          });
          child.stderr.on('data', (data) => {
            console.error(data.toString());
          });
          child.on('close', () => { });
          pids.push(child.pid);
        }
      }
      return pids;
    } catch (error) {
      this.logger.error('Error in crawl data by python');
      throw error;
    }
  }

  async getJobSearches(queryParams: GetJobSearchListQuery, user: { id: string }) {
    const limit = queryParams.limit || 10;
    const page = queryParams.page || 1;
    const offset = ((queryParams.page ?? 1) - 1) * limit;
    const searchText = queryParams.searchText || null;

    const sortField = queryParams.sortField || 'searchName';
    let sortOrder: 'ASC' | 'DESC' = 'ASC';
    if (queryParams.sortOrder && ['ASC', 'DESC'].includes(queryParams.sortOrder.toUpperCase())) {
      sortOrder = queryParams.sortOrder.toUpperCase() as 'ASC' | 'DESC';
    }

    const findQuery = this.jobSearchRepository
      .createQueryBuilder('job_search')
      .where({ isDeleted: false, dataKey: user.id })
      .andWhere(
        searchText
          ? '(LOWER(job_search.search_name) LIKE LOWER(:searchText) OR LOWER(job_search.keywords) LIKE LOWER(:searchText))'
          : '1=1',
        { searchText: `%${searchText?.toLowerCase()}%` }
      );

    if (queryParams.isGettingActiveSearch) {
      findQuery.andWhere({ stopScapingAt: IsNull() })
    }

    findQuery
      .select(
        `job_search.search_name AS "searchName",
        job_search.id AS id,
        job_search.keywords AS "keywords",
        job_search.location AS "location",
        job_search.posted_start_date AS "postedStartDate",
        job_search.posted_end_date AS "postedEndDate",
        job_search.min_salary AS "minSalary",
        job_search.max_salary AS "maxSalary",
        job_search.counts AS "counts",
        job_search.active_subprocess_id AS "activeSubprocessId",
        job_search.dataKey AS "dataKey",
        job_search.admin_level_one_area as "adminLevelOneArea",
        job_search.country,
        job_search.job_boards as "jobBoards",
        job_search.job_titles as "jobTitles",
        job_search.date_posted as "datePosted",
        job_search.excluded_companies as "excludeCompanies",
        job_search.excluded_keywords as "excludeKeywords",
        job_search.excluded_titles as "excludeTitles"
      `
      );

    findQuery.orderBy(`job_search.${sortField}`, sortOrder)
      .skip(offset)
      .take(limit);

    const jobSearchCount = await findQuery.getCount();
    const jobSearches = await findQuery.getRawMany();

    return this.formatOutputData(
      { key: 'GET_JOB_SEARCHES' },
      {
        data: {
          data: plainToInstance(JobSearchResponseDto, jobSearches),
          currentPage: page,
          totalCount: jobSearchCount,
          pageSize: limit,
        },
      }
    );
  }

  async startProcessJob(searchId: string, loginUserId: string) {
    console.log('startProcessJob is running');
    try {
      const jobSearch = await this.jobSearchRepository.findOneBy({
        id: searchId,
      });
      if (!jobSearch) {
        return this.throwCommonMessage('START_PROCESS_JOB', new NotFoundException());
      }
      // const activeSubprocessIds = this.crawlJobsByPython({
      //   ...jobSearch,
      //   keywords: jobSearch?.keywords?.split(','),
      // });
      // await this.jobSearchRepository.update(
      //   { id: searchId },
      //   {
      //     // activeSubprocessIds: activeSubprocessIds,
      //     stopScapingAt: null,
      //     updatedBy: loginUserId,
      //   }
      // );
      //async
      this.createCrawlingProcess(jobSearch);
      return this.formatOutputData({ key: 'START_PROCESS_JOB' }, { data: {} });
    } catch (error) {
      this.logger.error(error.message);
      return this.throwCommonMessage('START_PROCESS_JOB', new InternalServerErrorException('Something went wrong'));
    }
  }

  //TODO: update transaction here
  async deleteJobSearchById(searchId: string, loginUserId: string) {
    try {
      const search = await this.jobSearchRepository.findOneBy({ id: searchId });
      if (!search) {
        throw new NotFoundException('Search not found');
      }

      await this.jobSearchRepository.update(
        { id: searchId },
        {
          isDeleted: true,
          updatedBy: loginUserId,
        }
      );

      const { searchName: deletedSearchName } = search;

      const affectedSyncs = await this.jobSyncRepository.findBy({
        jobSearches: { id: searchId },
      });

      await Promise.all(
        affectedSyncs.map((sync) => {
          const { referenceName } = sync;
          const searchesName = referenceName.split(PREFIX_SYNC_REFERENCE)[1].split(', ');
          const updatedSearchesName = searchesName.filter((item) => item !== deletedSearchName);
          if (updatedSearchesName.length === 0) {
            return this.jobSyncRepository.update({ id: sync.id }, { isDeleted: true });
          }

          const updatedReferenceName = getReferenceNameBySearchesName(updatedSearchesName);
          return this.jobSyncRepository.update({ id: sync.id }, { referenceName: updatedReferenceName });
        })
      );
      if (search.activeSubprocessIds) {
        //async
        this.callToKillCrawlProcess(search.activeSubprocessIds);
      }

      // await this.deleteCrawlingJob(searchId);
      await this.cacheService.remove(`JOBSEARCH:${searchId}`);

      return this.formatOutputData({ key: 'DELETE_JOB_SEARCH' }, { data: {} });
    } catch (error) {
      this.logger.error(error);
      return this.throwCommonMessage('DELETE_JOB_SEARCH', error);
    }
  }

  //TODO: update transaction here
  async deleteBulkJobSearches(searchIds: string[], loginUserId: string) {
    try {
      await this.jobSearchRepository
        .createQueryBuilder()
        .update()
        .set({
          isDeleted: true,
          updatedBy: loginUserId,
        })
        .where('id IN (:...ids)', { ids: searchIds })
        .execute();

      const affectedSyncs = await this.jobSyncRepository.findBy({
        jobSearches: { id: In(searchIds) },
      });

      const listSyncDelete = affectedSyncs.map((item) => item.id);

      if (listSyncDelete.length > 0) {
        await this.jobSyncRepository
          .createQueryBuilder()
          .update()
          .set({
            isDeleted: true,
          })
          .where('id IN (:...ids)', { ids: listSyncDelete })
          .execute();
      }

      await Promise.allSettled(listSyncDelete.map((syncId) => this.cacheService.remove(`JOBSEARCH:${syncId}`)));

      return this.formatOutputData({ key: 'DELETE_BULK_JOB_SEARCH' }, { data: {} });
    } catch (error) {
      this.logger.error(error);
      return this.throwCommonMessage('DELETE_BULK_JOB_SEARCH', error);
    }
  }

  async pinJobToJobSearch(searchId: string, jobId: string, user: IJwtPayload) {
    const jobSearch = await this.jobSearchRepository.findOneBy({
      id: searchId,
    });
    const newPinnedList = [...(jobSearch.pinnedJobIds ?? '').split(','), jobId];
    await this.jobSearchRepository.update(
      { id: searchId },
      {
        pinnedJobIds: newPinnedList.join(','),
        updatedBy: user.id,
      }
    );

    await this.cacheService.remove(`JOBSEARCH:${searchId}`);

    return this.formatOutputData({ key: 'PIN_TO_JOB_SEARCH' }, { data: {} });
  }

  async getPinnedJobsBySearchId(searchId: string, user: ISimpleUser) {
    const jobSearch = await this.jobSearchRepository.findOneBy({
      id: searchId,
    });
    const pinnedJobIds = (jobSearch.pinnedJobIds ?? '').split(',').filter((job) => Object.keys(job).length !== 0);
    const pinnedJobsTemp = await this.elasticsearchService.getJobsByIds(pinnedJobIds, user.id);
    const pinnedJobs = pinnedJobsTemp.map((job) => ({ ...job?._source, isPinned: true }));

    return this.formatOutputData(
      { key: 'GET_JOB_SEARCH_PINNED_JOBS' },
      { data: { data: pinnedJobs, search: jobSearch } }
    );
  }

  async unpinJobToJobSearch(searchId: string, jobId: string, user: IJwtPayload) {
    const jobSearch = await this.jobSearchRepository.findOneBy({
      id: searchId,
    });
    const newPinnedList = [...(jobSearch.pinnedJobIds ?? '').split(',')].filter((pinnedId) => pinnedId != jobId);
    await this.jobSearchRepository.update(
      { id: searchId },
      {
        pinnedJobIds: newPinnedList.join(','),
        updatedBy: user.id,
      }
    );
    await this.cacheService.remove(`JOBSEARCH:${searchId}`);

    return this.formatOutputData({ key: 'UNPIN_FROM_JOB_SEARCH' }, { data: {} });
  }

  async removeJobFromSearch(searchId: string, jobId: string, loginUserId: string) {
    const search = await this.jobSearchRepository.findOneBy({ id: searchId });
    if (!search) {
      return this.throwCommonMessage('REMOVE_JOB_FROM_SEARCH', new NotFoundException('Search not found'));
    }

    let { deletedJobIds } = search;
    if (deletedJobIds) {
      deletedJobIds = deletedJobIds.concat(',', jobId);
    } else {
      deletedJobIds = jobId;
    }

    const oldPinnedJob = search.pinnedJobIds;
    let updatedPinnedJob = oldPinnedJob;
    if (oldPinnedJob) {
      let array = oldPinnedJob.split(',')?.filter(item => item !== '') ?? [];
      array = array.filter(item => item !== jobId);
      updatedPinnedJob = ',' + array.join(',');
    }


    await this.jobSearchRepository.update({ id: searchId }, { deletedJobIds, updatedBy: loginUserId, pinnedJobIds: updatedPinnedJob });
    await this.cacheService.remove(`JOBSEARCH:${searchId}`);

    const jobDetail = await this.elasticsearchService.getById(JOBS_INDEX, jobId);
    if (jobDetail) {
      this.removeSearchJobCachedByCompany(jobDetail.company).catch(() => { });
    }

    return this.formatOutputData({ key: 'REMOVE_JOB_FROM_SEARCH' }, { data: {} });
  }

  async removeJobsFromSearch(searchId: string, jobIds: string[], loginUserId: string) {
    const search = await this.jobSearchRepository.findOneBy({ id: searchId });
    if (!search) {
      return this.throwCommonMessage('REMOVE_JOBS_FROM_SEARCH', new NotFoundException('Search not found'));
    }
    let { deletedJobIds, pinnedJobIds } = search;

    deletedJobIds = deletedJobIds ? `${deletedJobIds},${jobIds.join(',')}` : jobIds.join(',');

    if (pinnedJobIds) {
      pinnedJobIds = pinnedJobIds
        .split(',')
        .filter((pinnedId) => !jobIds.includes(pinnedId))
        .join(',');
    }
    await this.jobSearchRepository.update(
      { id: searchId },
      { deletedJobIds, updatedBy: loginUserId, pinnedJobIds }
    );
    await this.cacheService.remove(`JOBSEARCH:${searchId}`);
    return this.formatOutputData({ key: 'REMOVE_JOBS_FROM_SEARCH' }, { data: {} });
  }

  async removePinnedJobGroupByCompany(searchId: string, jobIds: string[], loginUserId: string) {
    const search = await this.jobSearchRepository.findOneBy({ id: searchId });
    if (!search) {
      return this.throwCommonMessage('REMOVE_PINNED_JOB_GROUP_BY_COMPANY', new NotFoundException('Search not found'));
    }

    let { deletedJobIds, pinnedJobIds } = search;
    jobIds.forEach((jobId) => {
      if (deletedJobIds) {
        deletedJobIds = deletedJobIds.concat(',', jobId);
      } else {
        deletedJobIds = jobId;
      }

      if (pinnedJobIds) {
        const newPinnedList = [...pinnedJobIds.split(',')].filter((pinnedId) => pinnedId != jobId).join(',');
        pinnedJobIds = newPinnedList;
      } else {
        pinnedJobIds = jobId;
      }
    });

    await this.jobSearchRepository.update({ id: searchId }, { deletedJobIds, updatedBy: loginUserId, pinnedJobIds });
    await this.cacheService.remove(`JOBSEARCH:${searchId}`);

    return this.formatOutputData({ key: 'REMOVE_PINNED_JOB_GROUP_BY_COMPANY' }, { data: {} });
  }

  async removeJobByCompanyName(searchId: string, dto: DeleteBasicJobCompanyDto, loginUserId: string) {
    const search = await this.jobSearchRepository.findOneBy({ id: searchId });
    if (!search) {
      return this.throwCommonMessage('REMOVE_JOB_FROM_SEARCH', new NotFoundException('Search not found'));
    }

    const queryParams: JobBySearchIdQuery = {
      companies: [dto.companyName],
      limit: 1000,
    };

    const dataSearch = await this.getAllJobByCompanyName(searchId, queryParams, { id: dto.userViewAs });
    const listIds = dataSearch?.map((item: any) => item.job_id);
    console.log(listIds, 'deletedJobIds');

    let { deletedJobIds } = search;
    if (deletedJobIds) {
      deletedJobIds = deletedJobIds.concat(',', listIds.join(','));
    } else {
      deletedJobIds = listIds.join(',');
    }

    await this.jobSearchRepository.update({ id: searchId }, { deletedJobIds, updatedBy: loginUserId });

    this.removeSearchJobCachedByCompany(dto.companyName).catch(() => { });
    await this.cacheService.remove(`JOBSEARCH:${searchId}`);

    return this.formatOutputData({ key: 'REMOVE_JOB_LIST_FROM_SEARCH' }, { data: {} });
  }

  async getKeywordJobSearch() {
    const jsonFilePath = path.resolve('json', 'keywordSearch.json');
    const data = fs.readFileSync(jsonFilePath, 'utf8');
    const jsonData = JSON.parse(data);
    return jsonData;
  }

  async updateKeywordJobSearch(query: any) {
    const jsonFilePath = path.resolve('json', 'keywordSearch.json');
    const data = fs.readFileSync(jsonFilePath, 'utf8');
    const jsonData = JSON.parse(data);
    const isDataExists = jsonData.data.includes(query?.name);
    if (!isDataExists && query?.name) {
      jsonData.data.push(query?.name);
      const jsonString = JSON.stringify(jsonData, null, 2);
      fs.writeFileSync(jsonFilePath, jsonString, 'utf8');
    }
  }

  async updateByIds(ids: string[], partialEntity: Partial<JobSearchEntity>) {
    if (!ids.length) {
      return null;
    }
    return this.jobSearchRepository.update({ id: In(ids) }, partialEntity);
  }

  async updateByCondition(condition: FindOptionsWhere<JobSearchEntity>, partialEntity: Partial<JobSearchEntity>) {
    return this.jobSearchRepository.update(condition, partialEntity);
  }

  async activateInactiveSearchesBySystem(userId: string) {
    return this.jobSearchRepository.update(
      {
        dataKey: userId,
        stopBy: STOP_BY_ACTOR.SYSTEM,
        isDeleted: false,
        stopScapingAt: Not(IsNull()),
      },
      { activeSubprocessIds: ['temp'], stopScapingAt: null, stopBy: null }
    );
  }

  public tranferDateAdded(datePosted: string) {
    let dataAfter = 0;
    let hourAfter = 0;
    switch (datePosted) {
      case 'last_1_hour':
        dataAfter = 0;
        hourAfter = 1;
        break;
      case 'last_one_days':
        dataAfter = 1;
        break;
      case 'last_seven_days':
        dataAfter = 7;
        break;
      case 'last_fourteen_days':
        dataAfter = 14;
        break;
      case 'last_one_month':
        dataAfter = 30;
        break;
      default:
        dataAfter = 0;
    }
    return { dataAfter, hourAfter };
  }

  async getEmbeddingVector(queryString) {
    const huggingfacePipeline = await HuggingfacePipeline.getInstance();
    const response = await huggingfacePipeline(queryString, { pooling: 'mean', normalize: true });

    return Array.from(response.data);
  }

  async getJobsConditionByOpenSearch(search: Partial<JobSearchEntity>, queryParams: JobBySearchIdQuery, user: { id: string }) {
    const {
      companies,
      location,
      keywords: additionalKeyWords,
      searchText,
      jobBoards: additionalJobBoards,
      jobTitles,
      postedStartDate: additionalPostedStartDate,
      postedEndDate: additionalPostedEndDate,
      minSalary: additionalMinSalary,
      maxSalary: additionMaxSalary,
      includeSentJob,
      limit = 10,
      getRefinedJobs,
    } = queryParams;

    let { excludeCompanies = [], excludeTitles = [], excludeKeywords = [] } = queryParams as any;
    if (typeof excludeCompanies === 'string') {
      excludeCompanies = excludeCompanies.split(',').filter(Boolean);
    }
    if (typeof excludeTitles === 'string') {
      excludeTitles = excludeTitles.split(',').filter(Boolean);
    }
    if (typeof excludeKeywords === 'string') {
      excludeKeywords = excludeKeywords.split(',').filter(Boolean);
    }

    const arrCompany = companies ? companies.map((com) => decodeURIComponent(com)) : [];
    const keywords = search?.keywords ? search?.keywords?.split(',') : [];
    const additionalKeywordsArray = additionalKeyWords ? additionalKeyWords.split(',') : [];
    if (searchText) {
      additionalKeywordsArray.push(searchText);
    }

    let {
      // location, //not using this location to filter
      jobBoards,
      maxSalary,
      minSalary,
      postedStartDate,
      postedEndDate,
      stopScapingAt,
      country,
      adminLevelOneArea,
      jobTitles: jobTitlesEntity,
      datePosted,
      isRefineBySearch
    } = search;

    // let listJobTitle = jobTitlesEntity ?? []
    // if(jobTitles) {
    //   listJobTitle = listJobTitle?.concat(jobTitles)
    // }
    const uniqueElements = jobTitles?.filter((item) => !jobTitlesEntity?.includes(item)) ?? [];
    const { dataAfter, hourAfter } = this.tranferDateAdded(datePosted);

    const postedStartDateByAdded = datePosted ? new Date() : null;
    if (datePosted) {
      postedStartDateByAdded.setHours(postedStartDateByAdded.getHours() - hourAfter);
      postedStartDateByAdded.setDate(postedStartDateByAdded.getDate() - dataAfter);
    }
    const postedEndDateByAdded = datePosted ? new Date() : null;

    const maxStartDateToSet: any = [
      postedStartDateByAdded,
      postedStartDate ? new Date(postedStartDate) : null,
      additionalPostedStartDate ? new Date(additionalPostedStartDate) : null,
    ];
    const validMaxStartDateToSet = maxStartDateToSet.filter((date: Date) => date !== null);
    const maxStartDate = validMaxStartDateToSet.length > 0 ? new Date(Math.max(...validMaxStartDateToSet)) : null;

    const minEndDateToSet: any = [
      postedEndDateByAdded,
      postedEndDate ? new Date(postedEndDate) : null,
      additionalPostedEndDate ? new Date(additionalPostedEndDate) : null,
    ];

    const validMinEndDateToSet = minEndDateToSet.filter((date: Date) => date !== null);
    const minEndDate = validMinEndDateToSet.length > 0 ? new Date(Math.min(...validMinEndDateToSet)) : null;

    const standardMinSalary =
      additionalMinSalary && (!minSalary || minSalary < Number(additionalMinSalary)) ? additionalMinSalary : minSalary;
    const standardMaxSalary =
      additionMaxSalary && (!maxSalary || maxSalary > Number(additionMaxSalary)) ? additionMaxSalary : maxSalary;

    const standardStartDate = maxStartDate ? maxStartDate.toISOString() : null;
    let standardEndDate = null;
    if (minEndDate) {
      minEndDate.setUTCHours(23, 59, 59, 999);
      standardEndDate = minEndDate.toISOString();
    }

    const filterJobBoards = getCommonItems(
      jobBoards ? jobBoards.split(',') : JOB_BOARDS,
      additionalJobBoards ? additionalJobBoards.split(',') : JOB_BOARDS
    );

    if (!filterJobBoards.length) {
      throw new BadRequestException(`This search is working only in ${jobBoards}`);
    }

    const getKeywordCondition = (keyword: string, field: string, endingChars?: string[]) => {
      const SPACE_CHARS_REGEX = /[\s+]/;
      if (!SPACE_CHARS_REGEX.test(keyword)) {
        const key = `${field}`;
        return {
          wildcard: {
            [key]: {
              value: `*${keyword}*`,
              case_insensitive: true,
            },
          },
        };
      }

      return endingChars?.length
        ? endingChars.map((endingChar) => ({ match_phrase: { [field]: `${keyword}${endingChar}` } }))
        : { match_phrase: { [field]: keyword } };
    };

    const excludeConditions = [];
    if (excludeCompanies.length) {
      excludeConditions.push({
        bool: {
          should: excludeCompanies.map((searchCompany) => ({
            wildcard: {
              'company.keyword': {
                value: searchCompany.trim(),
                case_insensitive: true,
              },
            },
          })),
        },
      });
    }
    if (excludeTitles.length) {
      excludeConditions.push({
        bool: {
          should: [...excludeTitles.map((item) => ({ match_phrase: { jobtitle: item.trim() } }))],
        },
      });
    }
    if (excludeKeywords.length) {
      excludeConditions.push({
        bool: {
          should: [
            ...excludeKeywords.map((item) => getKeywordCondition(item.trim(), 'jobtitle')).flat(),
            ...excludeKeywords.map((item) => getKeywordCondition(item.trim(), 'description')).flat(),
            ...excludeKeywords.map((item) => getKeywordCondition(item.trim(), 'company')).flat(),
          ],
        },
      });
    }

    let condition: any = {
      bool: {
        must: [
          {
            bool: {
              should: [
                ...keywords.map((item) => getKeywordCondition(item, 'jobtitle')).flat(),
                ...keywords.map((item) => getKeywordCondition(item, 'company')).flat(),
                ...keywords
                  .map((item) =>
                    getKeywordCondition(item, 'description', ['', '.', ',', ':', ';', ')', '‘', '’', '“', '”'])
                  )
                  .flat(),
              ]
            },
          },
          {
            bool: {
              must_not: [
                {
                  terms: {
                    'company.keyword': {
                      index: 'reported_agency_country',
                      id: 'Global',
                      path: 'reported_agencies.company_name',
                    },
                  },
                },
                ...excludeConditions,
              ],
            },
          },
        ],
      },
    };
    if (additionalKeywordsArray.length) {
      condition.bool.must.push({
        bool: {
          should: [
            ...additionalKeywordsArray.map((item) => getKeywordCondition(item, 'jobtitle')).flat(),
            ...additionalKeywordsArray.map((item) => getKeywordCondition(item, 'description')).flat(),
            ...additionalKeywordsArray.map((item) => getKeywordCondition(item, 'company')).flat(),
          ],
        },
      });
    }

    if (filterJobBoards.length) {
      condition.bool.must.push({
        bool: {
          should: [...filterJobBoards.map((item) => ({ match: { source: item } }))],
        },
      });
    }

    if (jobTitles && jobTitles.filter(Boolean).length) {
      condition.bool.must.push({
        bool: {
          should: [...jobTitles.map((item) => ({ match_phrase: { jobtitle: item } }))],
        },
      });
    }

    if (!jobTitles && jobTitlesEntity && jobTitlesEntity?.filter(Boolean).length) {
      condition.bool.must.push({
        bool: {
          should: [...jobTitlesEntity.map((item) => ({ match_phrase: { jobtitle: item } }))],
        },
      });
    }

    const term = [];
    if (arrCompany.length) {
      term.push({
        bool: {
          should: arrCompany.map((searchCompany) => ({
            wildcard: {
              'company.keyword': {
                value: searchCompany,
                case_insensitive: true,
              },
            },
          })),
        },
      });
    }

    const range = [];
    if (standardMinSalary) {
      range.push({
        bool: {
          should: {
            range: {
              min_salary: {
                gte: standardMinSalary,
              },
            },
          },
        },
      });
    }
    if (standardMaxSalary) {
      range.push({
        bool: {
          should: {
            range: {
              max_salary: {
                lte: standardMaxSalary,
              },
            },
          },
        },
      });
    }
    if (standardStartDate) {
      range.push({
        bool: {
          should: {
            range: {
              posted: {
                gte: standardStartDate,
              },
            },
          },
        },
      });
    }
    if (standardEndDate) {
      range.push({
        bool: {
          should: {
            range: {
              posted: {
                lte: standardEndDate,
              },
            },
          },
        },
      });
    }


    if (queryParams?.fromCreatedAt) {
      range.push({
        bool: {
          should: {
            range: {
              created_at: {
                gte: queryParams.fromCreatedAt.toISOString(),
              },
            },
          },
        },
      });
    }
    if (queryParams?.toCreatedAt) {
      range.push({
        bool: {
          should: {
            range: {
              created_at: {
                lte: queryParams.toCreatedAt.toISOString(),
              },
            },
          },
        },
      });
    }
    if (stopScapingAt) {
      range.push({
        bool: {
          should: {
            range: {
              created_at: {
                lte: stopScapingAt,
              },
            },
          },
        },
      });
    }

    const match = [];
    const defaultCountry = country ?? 'United Kingdom';

    if (adminLevelOneArea?.trim()) {
      match.push({
        bool: {
          should: adminLevelOneArea.split(',').map((area: string) => ({
            match_phrase: { joblocationinput: area.trim() },
          })),
        },
      });
    } else if (country?.trim()) {
      match.push({
        bool: {
          should: [
            { match_phrase: { joblocationinput: country } },
            { match_phrase: { joblocationinput: `remote, ${defaultCountry}` } },
            { match_phrase: { joblocationinput: `Anywhere, ${defaultCountry}` } },
          ],
        },
      });
    }
    // State/ counties only
    if (location) {
      match.push({
        bool: {
          should: location.split(',').map((area: string) => ({
            match_phrase: { joblocationinput: area.trim() },
          })),
        },
      });
    }

    if (term.length) {
      condition.bool.must.push(...term);
    }
    if (range.length) {
      condition.bool.must.push(...range);
    }
    if (match.length) {
      condition.bool.must.push(...match);
    }

    // const myJobLeads = await this.dataSource
    //   .createQueryBuilder(JobLead, 'jl')
    //   .where({ dataKey: user.id })
    //   .select('jl.job_board_id as "jobBoardId"')
    //   .getRawMany<{ jobBoardId: string }>();
    // Add the condition to exclude jobs that have been sent to Bullhorn by the current user

    condition.bool.must_not = [
      {
        match_phrase: {
          send_to_bullhorn_by_user_ids: user.id,
        },
      },
    ];

    if (adminLevelOneArea?.trim()) {
      condition.bool.must_not.push(
        ...[
          {
            match_phrase: {
              joblocationcity: 'Anywhere',
            },
          },
          {
            match_phrase: {
              joblocationcity: 'Remote',
            },
          },
        ]
      );
    }

    if (includeSentJob === IncludeSentJobEnum.NOT_SENT) {
      condition.bool.must.push({
        bool: {
          must_not: [
            {
              exists: {
                field: 'send_to_bullhorn_by_user_ids',
              },
            },
          ],
        },
      });
    }

    if (includeSentJob === IncludeSentJobEnum.SENT) {
      condition.bool.must.push({
        bool: {
          must: [
            {
              exists: {
                field: 'send_to_bullhorn_by_user_ids',
              },
            },
          ],
        },
      });
    }

    //TODO: this is a temporary solution for the bug: UK Search with France, India, China jobs
    if (['united kingdom', 'united states', 'ireland', 'australia'].includes(country.toLowerCase())) {
      condition.bool.must_not.push(
        ...['France', 'India', 'China'].map((item) => ({
          match_phrase: {
            joblocationcity: item,
          },
        }))
      );
    }
    // const sentJobIds = myJobLeads.map((item) => item.jobBoardId).filter(Boolean);
    const excludedJobIds = [];
    if (search?.pinnedJobIds) {
      excludedJobIds.push(...(search.pinnedJobIds?.split(',') ?? []));
    }
    if (search?.deletedJobIds) {
      excludedJobIds.push(...(search.deletedJobIds?.split(',') ?? []));
    }

    if (excludedJobIds.length > 0) {
      // condition.bool.must_not = {
      //   terms: {
      //     job_id: excludedJobIds,
      //   },
      // };
      condition.bool.must_not.push({
        terms: {
          'job_id.keyword': excludedJobIds,
        },
      });
    }

    if (jobTitlesEntity) {
      condition.bool.must.push({
        bool: {
          should: [...uniqueElements?.map((item) => ({ match_phrase: { jobtitle: item } }))],
        },
      });
    }

    if (isRefineBySearch) {
      condition.bool.must.push({
        bool: {
          must: [
            {
              terms: {
                'refinedKeywords.keyword': keywords
              }
            },
            {
              terms: {
                'refinedSearches.keyword': [search.id]
              }
            }
          ],
        },
      });
    }

    if (getRefinedJobs === false) {
      condition.bool.must_not.push({
        bool: {
          must: [
            {
              terms: {
                'refinedKeywords.keyword': keywords
              }
            },
            {
              terms: {
                'refinedSearches.keyword': [search.id]
              }
            }
          ],
        },
      });
    }


    return condition;
  }

  async refineJobsByAI({ keywords, opensearchResponse }: { keywords: string[], opensearchResponse: { body: any } }) {
    const startTime = Date.now();

    try {
      console.log('Start refine', new Date().toISOString());

      // Input validation
      if (!keywords?.length) {
        console.warn('[refineJobsByAI] No keywords provided');
        return [];
      }

      if (!opensearchResponse?.body?.hits?.hits?.length) {
        console.warn('[refineJobsByAI] No jobs to refine');
        return [];
      }

      const refineEndpoint = REFINE_API;
      const REFINE_SCORE = 0.38;
      const TIMEOUT_MS = 60000; // 60 seconds timeout

      const { data: processedRefineJobs } = await this.httpService.axiosRef.post(
        refineEndpoint,
        {
          keyword: keywords,
          jobs: opensearchResponse.body,
          size: 0,
          score: REFINE_SCORE,
        },
        {
          timeout: TIMEOUT_MS,
        },
      );
      // fs.writeFileSync(`result-${new Date().toISOString()}.json`, JSON.stringify(processedRefineJobs))

      // Validate response
      if (!Array.isArray(processedRefineJobs)) {
        console.error('[refineJobsByAI] Invalid response format from AI API');
        return [];
      }

      const refineJobIds = processedRefineJobs.map((item) => item.job_id).filter(Boolean);
      const res = opensearchResponse.body.hits.hits.filter((item) =>
        refineJobIds.includes(item._source.job_id),
      );

      const processingTime = Date.now() - startTime;
      console.log(
        `[refineJobsByAI] Refined ${res.length}/${opensearchResponse.body.hits.hits.length} jobs in ${processingTime}ms`,
        new Date().toISOString(),
      );

      return res;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`[refineJobsByAI] Error after ${processingTime}ms:`, error.message);

      // Return empty array on error to prevent breaking the flow
      return [];
    }
  }

  async getJobsHelper(search: Partial<JobSearchEntity>, queryParams: JobBySearchIdQuery, user: { id: string }) {
    const page = queryParams.page || 1;
    const limit = queryParams.limit || 10000;
    const sort = queryParams?.sort?.toLowerCase() || 'desc';

    let { excludeCompanies = [], excludeTitles = [], excludeKeywords = [] } = queryParams as any;
    if (typeof excludeCompanies === 'string') {
      excludeCompanies = excludeCompanies.split(',').filter(Boolean);
    }
    if (typeof excludeTitles === 'string') {
      excludeTitles = excludeTitles.split(',').filter(Boolean);
    }
    if (typeof excludeKeywords === 'string') {
      excludeKeywords = excludeKeywords.split(',').filter(Boolean);
    }

    // update excluded keywords, job titles, companies to the database
    if (queryParams.isSavedExclude) {
      await Promise.all([
        this.jobSearchRepository.update(search.id, {
          excludeCompanies,
          excludeKeywords,
          excludeTitles,
        }),
        this.cacheService.remove(`JOBSEARCH:${search.id}`),
      ]);
    }

    const condition = await this.getJobsConditionByOpenSearch(search, queryParams, user);
    // console.log('condition of getJobsConditionByOpenSearch', search, JSON.stringify(condition));
    const response = await this.elasticsearchService.getClient().search({
      index: JOBS_INDEX, // Replace with your actual index name
      from: (page - 1) * limit, // Assuming page size is 10
      size: limit, // Adjust the size as needed
      body: {
        sort: [
          {
            posted: {
              order: sort,
            },
          },
        ],
        query: condition,
      },
      _source: [
        'jobtitle',
        // 'extracteddate',
        'joblocationcity',
        'link',
        'description',
        'created_at',
        'source',
        // 'salary',
        'jobtype',
        'posted',
        // 'companyrating',
        'max_salary',
        // 'updated_at',
        'job_id',
        // 'min_salary',
        'company',
        // 'joblocationinput',
        // 'logo_company',
        'send_to_bullhorn_by_user_ids',
        'logoCompany',
        'raw_salary',
        'jobLogs',
        'skills',
        'refinedKeywords',
        'refinedSearches'
      ],
    });

    const responseJobs = response;

    return { responseJobs };
  }

  async formatJobs(search: JobSearchEntity, responseJobs: any, queryParams: JobBySearchIdQuery) {
    const userIds = [];
    const data = responseJobs.body.hits.hits.map((e: { _source: any }) => {
      if (e._source.jobLogs) {
        e._source?.jobLogs?.split(',').forEach((jobLog) => {
          const userId = jobLog?.split(':')[1];
          userIds.push(userId);
        });
      }

      return {
        ...e._source,
        logoCompany: e._source.logoCompany ?? getLogoCompany(e._source?.company, e._source?.source),
        sendToBullHornByUserIds: e._source.send_to_bullhorn_by_user_ids,
        salary: e._source?.raw_salary,
        jobtype: convertCamelToNormalCase(
          standardizeJobType(e._source?.jobtype?.replace('{', '').replace('}', '').replace(/\"/g, '')).join(', ')
        ),
      };
    });

    const users = await this.userRepository.find({
      where: {
        id: In([...new Set(userIds)]),
      },
    });
    const usersMapping = convertArrayToObject(users, 'id');

    data.forEach((item: { jobLogs: string | string[] }) => {
      const jobLogs = item.jobLogs as string;
      if (jobLogs) {
        const convertedJobLogs = jobLogs
          .split(',')
          .map((log: string) => {
            const [type, userId] = log.split(':');
            const user = usersMapping?.[userId];
            const userName = user?.fullName || user?.username || user?.email?.split('@')?.[0];
            if (!userName) {
              return null;
            }

            const typeTextMapping = {
              [JobLogEnum.EDITING_BH_VACANCY_SUBMISSION]: 'Being edited by',
              [JobLogEnum.SENT_TO_BH_VACANCY_SUBMISSION_SUCCESSFULLY]: 'Sent by',
            };
            const typeText = typeTextMapping[type];
            if (!typeText) {
              return null;
            }

            return [typeText, userName].join(' ');
          })
          .filter(Boolean);

        item.jobLogs = uniqueArray(convertedJobLogs);
      }
    });

    if (queryParams?.jobTitles && queryParams.jobTitles.length > 0) {
      search.jobTitles = search?.jobTitles?.concat(queryParams.jobTitles);
    }
    const uniqueJobTitlesSet = new Set(search.jobTitles);

    search.jobTitles = [...uniqueJobTitlesSet];
    const responseData = {
      data,
      search,
      totalCount: data?.length,
      pageSize: queryParams.limit,
      currentPage: queryParams.page,
    };

    return responseData;
  }

  async getJobBoardBySearchIdOpenSearch(searchId: string, queryParams: JobBySearchIdQuery, user: { id: string }): Promise<IResponseCommon> {
    try {
      console.log(queryParams, "queryParams")
      const search: JobSearchEntity = await this.getJobSearchById(searchId, user.id);

      if (!search) {
        return this.formatOutputData(
          { key: 'JOB_SEARCH_FETCHED_OPEN_SEARCH' },
          {
            data: {
              data: [],
              search: {}
            },
          }
        );
      }

      if (search.latestRefinedJobDate === null && search.isRefineBySearch) {
        queryParams.limit = 1000;
        queryParams.page = 1;
        queryParams.getRefinedJobs = false;
        const { responseJobs } = await this.getJobsHelper({ ...search, isRefineBySearch: false }, { ...queryParams, getRefinedJobs: false }, user);
        // fs.writeFileSync(`response-${new Date().toISOString()}.json`, JSON.stringify(responseJobs))
        const refinedJobs = await this.refineJobsByAI({ keywords: search.keywords.split(","), opensearchResponse: responseJobs });
        await this.updateRefineKeywordForJobs(refinedJobs, search);

        const refinedJobIds = refinedJobs.map((item: any) => item._source.job_id);
        const unmatchingJobIds = responseJobs.body.hits.hits.filter((item: any) => !refinedJobIds.includes(item._source.job_id)).map((item: any) => item._source.job_id);
        await this.elasticsearchService.markedJobAsRefined(unmatchingJobIds, search.id);

        await this.jobSearchRepository.update(searchId, {
          latestRefinedJobDate: responseJobs.body.hits.hits[0]?._source?.created_at,
          oldestRefinedJobDate: responseJobs.body.hits.hits[responseJobs.body.hits.hits.length - 1]?._source.created_at
        })

        await this.cacheService.remove(`JOBSEARCH:${searchId}`);
        return this.formatOutputData(
          { key: 'SCAN_FIRST_TIME_REFINED_SEARCH' },
          {
            data: {
              data: [],
              search: {}
            },
          }
        );
      }

      const { responseJobs } = await this.getJobsHelper(search, queryParams, user);

      if (Number(queryParams.page) === 1 && search.isRefineBySearch) {
        const { responseJobs: rawFirstJob } = await this.getJobsHelper({ ...search, isRefineBySearch: false }, { ...queryParams, limit: 1, }, user)
        const firstJob = rawFirstJob.body.hits.hits?.[0];
        //if firstJob's created_at > search.latestRefinedJobDate, then we need to refine jobs in background
        console.log(firstJob, "firstJob")
        if (firstJob && new Date(firstJob._source?.created_at) > new Date(search.latestRefinedJobDate)) {
          console.log(`Start refinedJobsInBackground for Search: ${search.id}, Size: 50, Direction: LATEST`);
          await this.refinedJobsInBackground(search, 50, 'LATEST');
          console.log(`End refinedJobsInBackground for Search: ${search.id}, Size: 50, Direction: LATEST`);
        }
      }

      const result = await this.formatJobs(search, responseJobs, queryParams);

      return this.formatOutputData(
        { key: 'JOB_SEARCH_FETCHED_OPEN_SEARCH' },
        {
          data: result,
        }
      );
    } catch (error) {
      console.error(`Error fetching job-boards in getJobBoardBySearchIdOpenSearch by search id ${searchId}`, error);
      // return await this.throwCommonMessage('JOB_SEARCH_FETCHED', error);
      return this.formatOutputData(
        { key: 'JOB_SEARCH_FETCHED_OPEN_SEARCH_FAILED' },
        {
          data: {
            data: [],
            totalCount: 10, //TODO: remove later
            pageSize: queryParams.limit || 10, //TODO: remove later
            currentPage: 1, //TODO: remove later
          },
        }
      );
    }
  }

  // async triggerRefineJobs(searchId: string, responseJobs: any) {
  // }


  async updateRefineKeywordForJobs(jobs: any, search: JobSearchEntity) {
    console.log(`Start updateRefineKeywordForJobs ${new Date().toISOString()}`)

    const searchKeywords = search.keywords.split(',').filter(Boolean);

    // Filter jobs that need to be updated (not already refined with these keywords for this search)
    const updatedRefinedKeywordJobs = jobs.filter((item: any) => {
      if (!item?._source?.job_id) {
        return false; // Skip invalid jobs
      }

      const existingRefinedKeywords = item._source.refinedKeywords || [];
      const existingRefinedSearches = item._source.refinedSearches || [];

      // Check if this job is already refined for this search
      const isRefinedForThisSearch = existingRefinedSearches.includes(search.id);

      // If already refined for this search, check if all keywords are present
      if (isRefinedForThisSearch) {
        // Check if ALL search keywords are already present
        const hasAllSearchKeywords = searchKeywords.every((keyword) =>
          existingRefinedKeywords.includes(keyword),
        );

        // Only skip if ALL keywords are already present for this search
        return !hasAllSearchKeywords;
      }

      // If not refined for this search yet, include it for processing
      return true;
    });

    const updatedJobIds = updatedRefinedKeywordJobs.map(item => item._source.job_id);
    console.log(
      `[updateRefineKeywordForJobs] Processing ${updatedJobIds.length}/${jobs.length} jobs for search ${search.id}`,
    );

    if (updatedJobIds.length === 0) {
      console.log(`[updateRefineKeywordForJobs] No jobs to update for search ${search.id}`);
      return { success: true, updated: 0, errors: [], total: 0 };
    }

    const res = await this.elasticsearchService.bulkUpdateRefinedJobs(
      updatedJobIds,
      searchKeywords,
      search.id,
    );

    console.log(`End updateRefineKeywordForJobs ${new Date().toISOString()}`)
    return res;
  }

  async refinedJobsInBackground(search: JobSearchEntity, size = 50, direction: 'LATEST' | 'OLDEST') {
    const startTime = Date.now();

    try {
      console.log(
        `[refinedJobsInBackground] Starting for Search: ${search.id}, Size: ${size}, Direction: ${direction}`,
      );

      // Input validation
      if (!search?.keywords) {
        console.warn(`[refinedJobsInBackground] No keywords found for search ${search.id}`);
        return { processed: 0, refined: 0, failed: 0 };
      }

      const queryParams: JobBySearchIdQuery = { limit: size, page: 1 };
      if (direction === 'LATEST') {
        queryParams.fromCreatedAt = new Date(search.latestRefinedJobDate);
        queryParams.sort = SortJobEnum.ASC;
      } else {
        queryParams.toCreatedAt = new Date(search.oldestRefinedJobDate);
      }

      search.isRefineBySearch = false; //for query purpose, just get
      const { responseJobs } = await this.getJobsHelper(
        search,
        { ...queryParams, getRefinedJobs: false },
        { id: search.dataKey },
      );

      // Validate response
      if (!responseJobs?.body?.hits?.hits?.length) {
        console.log(
          `[refinedJobsInBackground] No jobs found for search ${search.id} in direction ${direction}`,
        );
        return { processed: 0, refined: 0, failed: 0 };
      }

      const jobs = responseJobs.body.hits.hits;
      console.log(
        `[refinedJobsInBackground] Processing ${jobs.length} jobs for search ${search.id}`,
      );

      // fs.writeFileSync(`test-${new Date().toISOString()}.json`, JSON.stringify(jobs.map(item => item._source)))
      const refinedJobs = await this.refineJobsByAI({
        keywords: search.keywords.split(','),
        opensearchResponse: responseJobs,
      });

      const refinedJobIds = refinedJobs.map((item: any) => item._source.job_id);
      const unmatchingJobIds = jobs
        .filter((item: any) => !refinedJobIds.includes(item._source.job_id))
        .map((item: any) => item._source.job_id);

      // Process refined and unrefined jobs
      await Promise.all([
        this.elasticsearchService.markedJobAsRefined(unmatchingJobIds, search.id),
        this.updateRefineKeywordForJobs(refinedJobs, search),
      ]);

      // Fix: Calculate correct latest and oldest dates
      const latestRefinedJobDate =
        direction === 'LATEST'
          ? jobs[jobs.length - 1]?._source?.created_at // Last job when processing LATEST
          : search.latestRefinedJobDate; // Keep existing when processing OLDEST

      const oldestRefinedJobDate =
        direction === 'OLDEST'
          ? jobs[jobs.length - 1]?._source?.created_at // Last job when processing OLDEST
          : search.oldestRefinedJobDate; // Keep existing when processing LATEST

      const updatedSearch: Partial<JobSearchEntity> = {};

      // Always update lastProcessedAt to prevent infinite loops
      updatedSearch.updatedAt = new Date();

      // Update refined job dates only if we have jobs and valid dates
      if (direction === 'LATEST' && latestRefinedJobDate) {
        updatedSearch.latestRefinedJobDate = latestRefinedJobDate;
      } else if (direction === 'OLDEST' && oldestRefinedJobDate) {
        updatedSearch.oldestRefinedJobDate = oldestRefinedJobDate;
      }

      // Update scan completion status
      if (jobs.length < size && direction === 'OLDEST') {
        updatedSearch.isScanOldest = true; // Mark as completed scanning oldest jobs
      }

      // Update search entity if there are changes
      if (Object.keys(updatedSearch).length > 0) {
        await this.jobSearchRepository.update(search.id, updatedSearch);
        console.log(`[refinedJobsInBackground] Updated search ${search.id}:`, updatedSearch);
      }

      await this.cacheService.remove(`JOBSEARCH:${search.id}`);

      const processingTime = Date.now() - startTime;
      console.log(
        `[refinedJobsInBackground] Completed for search ${search.id}: ${refinedJobs.length}/${jobs.length} refined in ${processingTime}ms`,
      );

      return {
        processed: jobs.length,
        refined: refinedJobs.length,
        failed: 0,
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(
        `[refinedJobsInBackground] Error for search ${search.id} after ${processingTime}ms:`,
        error,
      );

      return {
        processed: 0,
        refined: 0,
        failed: size,
      };
    }
  }


  async getAllJobByCompanyName(searchId: string, queryParams: JobBySearchIdQuery, user: { id: string }) {
    try {
      const search = await this.getJobSearchById(searchId, user.id);
      if (!search) {
        return [];
      }
      const condition = await this.getJobsConditionByOpenSearch(search, queryParams, user);
      const response = await this.elasticsearchService.getClient().search({
        index: JOBS_INDEX,
        size: queryParams.limit,
        body: {
          query: condition,
        },
      });

      const data = response.body.hits.hits.map((e: { _source: any }) => ({
        ...e._source,
        logoCompany: e._source.logo_company,
        sendToBullHornByUserIds: e._source.send_to_bullhorn_by_user_ids,
      }));

      return data;
    } catch (error) {
      return [];
    }
  }

  async getPaginationOfSearchDetailByOpenSearch(
    searchId: string,
    queryParams: JobBySearchIdQuery,
    user: { id: string }
  ) {
    try {
      const search = await this.getJobSearchById(searchId, user.id);
      if (!search) {
        return this.formatOutputData(
          { key: 'JOB_SEARCH_FETCHED' },
          {
            data: {
              data: [],
              search,
            },
          }
        );
      }
      const condition = await this.getJobsConditionByOpenSearch(search, queryParams, user);

      const response = await this.elasticsearchService.getClient().count({
        index: JOBS_INDEX, // Replace with your actual index name
        body: {
          query: condition,
        },
      });

      const totalCount = response.body.count;
      console.log({ totalCount, counts: search.counts, isUpdateCount: this.isUpdateCount(queryParams, search) });
      if (totalCount !== search.counts && this.isUpdateCount(queryParams, search)) {
        await this.jobSearchRepository.update({ id: search.id }, { counts: totalCount });
        await this.cacheService.remove(`JOBSEARCH:${search.id}`);
      }

      return this.formatOutputData(
        { key: 'JOB_SEARCH_FETCHED_PAGINATION' },
        {
          data: {
            totalCount,
            pageSize: queryParams.limit || 10, //for now, 10 is allowed only,
            currentPage: Number(queryParams.page) || 1,
            search,
          },
        }
      );
    } catch (error) {
      console.log('Error in getPaginationOfSearchDetailByOpenSearch', error);
    }
  }

  async getPaginationOfCompaniesByOpenSearch(searchId: string, queryParams: JobBySearchIdQuery, userId: string) {
    try {
      const search = await this.getJobSearchById(searchId, userId);
      if (!search) {
        return this.formatOutputData(
          { key: 'JOB_SEARCH_FETCHED' },
          {
            data: {
              data: [],
              search,
            },
          }
        );
      }
      const condition = await this.getJobsConditionByOpenSearch(search, queryParams, { id: userId });

      const response = await this.elasticsearchService.getClient().search({
        index: JOBS_INDEX, // Replace with your actual index name
        body: {
          query: condition,
          aggs: {
            company_counts: {
              terms: {
                field: 'company.keyword',
                size: 65535,
              },
            },
          },
        },
      });

      const totalCount = response?.body?.aggregations?.company_counts?.buckets?.length || 0;
      // if (totalCount !== search.counts && this.isUpdateCount(queryParams)) {
      //   await this.jobSearchRepository.update({ id: search.id }, { counts: totalCount });
      // }

      return this.formatOutputData(
        { key: 'COMPANIES_FETCHED_PAGINATION' },
        {
          data: {
            totalCount,
            // pageSize: queryParams.limit || 10, //for now, 10 is allowed only,
            // currentPage: Number(queryParams.page) || 1,
          },
        }
      );
    } catch (error) {
      console.log('Error in getPaginationOfCompaniesByOpenSearch', error);
    }
  }

  async setGroupJobByCompany(searchId: string, groupByCompanyStatus: SetGroupByCompanyRequestDto, user: IJwtPayload) {
    try {
      const isGroupByCompany = groupByCompanyStatus.status === SetGroupByCompanyEnum.ON;
      await this.jobSearchRepository.update({ id: searchId }, { isGroupByCompany });
      await this.cacheService.remove(`JOBSEARCH:${searchId}`);

      return this.formatOutputData({ key: 'SET_GROUP_JOB_BY_COMPANY' }, { data: { isGroupByCompany } });
    } catch (error) {
      console.log('Error in setGroupJobByCompany', error);
      return this.throwCommonMessage('SET_GROUP_JOB_BY_COMPANY', error);
    }
  }

  async setRefineBySearch(searchId: string, refineBySearchStatus: SetGroupByCompanyRequestDto, userId: string) {
    try {
      const search = await this.getJobSearchById(searchId, userId);
      if (!search) {
        throw new NotFoundException('Search not found');
      }

      const isRefineBySearch = refineBySearchStatus.status === SetGroupByCompanyEnum.ON;
      await this.jobSearchRepository.update({ id: searchId }, { isRefineBySearch });
      await this.cacheService.remove(`JOBSEARCH:${searchId}`);

      return this.formatOutputData({ key: 'SET_REFINE_BY_SEARCH' }, { data: { isRefineBySearch } });
    } catch (error) {
      console.log('Error in setRefineBySearch', error);
      return this.throwCommonMessage('SET_REFINE_BY_SEARCH', error);
    }
  }
}

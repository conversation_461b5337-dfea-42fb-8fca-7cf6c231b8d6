import { Injectable } from '@nestjs/common';
import { LeadStatus } from '../entities/lead-statuses.entity';
import { LeadStatusesRepository } from '../repository/lead-statuses.repository';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { BulkUpdateItemRequestDto, BulkUpdateRequestDto } from '../dto/bulk-update.dto';
import { DataSource, FindOptionsWhere, IsNull } from 'typeorm';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { JobLeadsRepository } from '../repository/job-leads.repository';
import { LeadStatusQueryDto, LeadStatusSimpleQuery } from '../dto/lead-status/lead-status.dto';
import { convertArrayToObject } from 'src/common/utils/helpers.util';

@Injectable()
export class LeadStatusesService {
  constructor(
    private readonly leadStatusRepository: LeadStatusesRepository,
    private dataSource: DataSource,
    private readonly jobLeadRepository: JobLeadsRepository,
  ) {}

  async getStatuses(userId?: string, queryDto?: LeadStatusQueryDto): Promise<LeadStatus[]> {
    const { leadStatusType, leadSheetId } = queryDto ?? {};
    if (userId) {
      const user = await this.dataSource.createQueryBuilder(UserEntity, 'u').where({ id: userId }).getOne();

      const leadStatusCondition: FindOptionsWhere<LeadStatus> = {
        organizationId: user.organizationId ?? IsNull(),
        isDeleted: false,
      };
      if (leadStatusType) {
        leadStatusCondition.leadStatusType = leadStatusType;
      }
      if (leadSheetId) {
        leadStatusCondition.leadSheetId = leadSheetId;
      }
      const leadStatuses = await this.leadStatusRepository.findBy(leadStatusCondition);
      // Get list id of lead status
      let leadStatusIds = leadStatuses.map((leadStatus) => leadStatus.id);
      if (leadStatusIds.length === 0) {
        return [];
      }
      const listDataLead = await this.jobLeadRepository
        .createQueryBuilder('jl')
        .select('lead_status_id as lead_id, COUNT(*) as total_company, SUM(jl.salary) as total_salary')
        .where(
          '((jl.lead_status_id IN (:...statusIds) AND jl.creator_id = :userId) OR (jl.lead_status_id IN (:...statusIds) AND jl.assignee_id = :userId))',
          { statusIds: leadStatusIds, userId }
        )
        .andWhere('(jl.is_done = true or jl.creator_id = :userId)')
        .groupBy('lead_status_id')
        .getRawMany<{ lead_id: string; total_company: string; total_salary: string }>();

      const leadsMapping = convertArrayToObject(listDataLead, 'lead_id');

      return leadStatuses.map((leadStatus) => {
        const additionalInfo = leadsMapping[leadStatus.id];

        return {
          ...leadStatus,
          ...additionalInfo,
        };
      });
    }

    return this.leadStatusRepository.find({ where: { isDeleted: false } });
  }

  async getSimpleStatuses(userId: string, queryDto: LeadStatusSimpleQuery): Promise<LeadStatus[]> {
    const user = await this.dataSource.createQueryBuilder(UserEntity, 'u').where({ id: userId }).getOne();
    const organizationId = user.organizationId;
    const queryCondition: FindOptionsWhere<LeadStatus> = { leadStatusType: queryDto.leadStatusType, isDeleted: false };
    if (organizationId) {
      queryCondition.organizationId = organizationId;
    }
    return this.leadStatusRepository.findBy(queryCondition);
  }

  async updateStatuses(
    bulkUpdateRequestDto: BulkUpdateRequestDto,
    user: IJwtPayload
  ): Promise<{ message: string; data: BulkUpdateItemRequestDto[] }> {
    try {
      const { data: recordsToUpdate, updatedFor } = bulkUpdateRequestDto;

      let organizationId = user.organizationId;
      if (updatedFor) {
        const user = await this.dataSource
          .createQueryBuilder(UserEntity, 'u')
          .where({ id: updatedFor })
          .select()
          .getOne();
        organizationId = user.organizationId;
      }

      //TODO: update Promise here
      for (const record of recordsToUpdate) {
        const { orderOfDisplay, name, color, leadStatusType, leadSheetId } = record;
        if (record.isDeleted) {
          await this.leadStatusRepository.delete(record.id);
        } else {
          if (record.isNew && name) {
            const newStatus = await this.leadStatusRepository.save({
              name,
              color,
              orderOfDisplay,
              organizationId,
              updatedBy: user.id,
              leadStatusType,
              leadSheetId,
            });
            record.id = newStatus.id;
          } else {
            const updateData = {
              color,
              orderOfDisplay,
              updatedBy: user.id,
            };
            if (name) {
              updateData['name'] = name;
            }
            await this.leadStatusRepository.update(record.id, updateData);
          }
        }
      }
      return { message: 'Lead statuses updated successfully.', data: recordsToUpdate };
    } catch (error) {
      console.error('Error updating lead statuses:', error);
      throw new Error('An error occurred while updating lead statuses.');
    }
  }
}

import { Inject, Injectable, Logger, } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import * as NodeGeocoder from 'node-geocoder';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CACHE_PREFIX, CACHE_TTL } from '../constants/job.const';

const IGNORE_ADDRESSTYPES = [
    'emergency',
    'historic',
    'military',
    'natural',
    'landuse',
    'place',
    'railway',
    'man_made',
    'aerialway',
    'boundary',
    'amenity',
    'aeroway',
    'club',
    'craft',
    'leisure',
    'office',
    'mountain_pass',
    'shop',
    'tourism',
    'bridge',
    'tunnel',
    'waterway',
    'house_number',
    'house_name',
];

@Injectable()
export class GeocoderService extends BaseAbstractService {
    private readonly logger = new Logger(GeocoderService.name);
    private readonly geocoder = NodeGeocoder({
        provider: 'openstreetmap',
        timeout: 3000,
    });
    private readonly cacheKeyPrefix = CACHE_PREFIX.JOB_LOCATION;
    private readonly cacheTTL = CACHE_TTL.JOB_LOCATION; // 24h; Actually it's not working now

    constructor(
        private readonly i18nService: I18nService,
        @Inject(CACHE_MANAGER) private cacheService: Cache,
    ) {
        super(i18nService);
    }

    formatResult(result: any) {
        return {
            formattedAddress: result.display_name,
            country: result.address.country,
            city: result.address.city || result.address.town || result.address.village || result.address.hamlet,
            state: result.address.state,
            countryCode: result.address.country_code?.toUpperCase(),
            addressType: result.addresstype,
        };
    }

    /**
     * 
     * @param address {string}
     * @returns {Promise<{}>}
     * @example {
            formattedAddress: 'Cambridge, Cambridgeshire, Cambridgeshire and Peterborough, England, United Kingdom',
            country: 'United Kingdom',
            city: 'Cambridge',
            state: 'England',
            countryCode: 'GB',
            addresstype: 'city',
            provider: 'openstreetmap'
        }
     */
    async getLocation(address: string) {
        if (!address) {
            return {};
        }

        try {
            const cachedKey = `${this.cacheKeyPrefix}${address.toLowerCase().replace(/\s+/g, ' ').trim()}`;
            const cachedLocation = await this.cacheService.get(cachedKey);
            if (cachedLocation) {
                this.logger.log(`Getting location data from cache with key '${cachedKey}'!`);

                return cachedLocation;
            }

            const data = await this.geocoder.geocode(address);
            const locations = [];
            if (data.raw instanceof Array) {
                for (const address of data.raw) {
                    if (!IGNORE_ADDRESSTYPES.includes(address.addresstype)) {
                        locations.push(this.formatResult(address));
                        break;
                    }
                }
            } else {
                locations.push(this.formatResult(address));
            }
            const [location = {}] = locations;
            if (location.formattedAddress) {
                await this.cacheService.set(cachedKey, location, this.cacheTTL);
            }

            return location;
        } catch (error) {
            this.logger.error(error.message);
        }

        return {};
    }

    async compareLocation(inputLocation: string, responseLocation: string) {
        try {
            const inputLocationObj = await this.getLocation(inputLocation);
            const responseLocationObj = await this.getLocation(responseLocation);

            return inputLocationObj.countryCode === responseLocationObj.countryCode;
        } catch (error) {
            this.logger.error(error.message);
        }

        return true
    }
}

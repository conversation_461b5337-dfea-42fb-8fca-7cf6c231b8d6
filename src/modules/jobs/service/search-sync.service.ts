import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { JobSyncRepository } from '../repository/job-sync.repository';
import { JobSearchRepository } from '../repository/job-search.repository';
import { IJwtPayload, ISimpleUser } from 'src/modules/auth/payloads/jwt-payload.payload';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { ToggleEnum, ToggleSyncRequestDto } from '../dto/toggle-sync.dto';
import { In } from 'typeorm';
import { getReferenceNameBySearchesName } from '../utils/sync.util';
import { CreateSyncRequestDto } from '../dto/job-request.dto';
import { JobSyncQuery, SyncedSearchesQueryDto } from '../dto/job-sync-query.dto';

@Injectable()
export class SearchSyncService extends BaseAbstractService {
  private readonly logger = new Logger(SearchSyncService.name);
  constructor(
    private readonly jobSyncRepository: JobSyncRepository,
    private readonly jobSearchRepository: JobSearchRepository,
    readonly i18nService: I18nService
  ) {
    super(i18nService);
  }

  async getSyncOfSearches(user: ISimpleUser) {
    const data = await this.jobSyncRepository.find({ where: { dataKey: user.id, isDeleted: false } });

    return this.formatOutputData({ key: 'GET_LIST_SYNC_SEARCH' }, { data });
  }

  async getSyncedSearches(user: { id: string }, queryDto: SyncedSearchesQueryDto) {
    const { page = 1, limit = 10, searchText } = queryDto;

    const query = this.jobSyncRepository.createQueryBuilder('job_sync')
      .leftJoinAndSelect('job_sync.jobSearches', 'job_search')
      .where('job_sync.dataKey = :dataKey', { dataKey: user.id })
      .andWhere('job_sync.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('job_search.isDeleted = :isDeleted', { isDeleted: false });

    if (searchText) {
        query.andWhere(
            '(LOWER(job_search.search_name) LIKE LOWER(:searchText) OR LOWER(job_search.keywords) LIKE LOWER(:searchText))',
            { searchText: `%${searchText.toLowerCase()}%` }
        );
    }

    query.select(['job_sync.id', 'job_search.id'])
         .skip((page - 1) * limit)
         .take(limit);

    const jobSyncs = await query.getMany();
    const jobCount = await query.getCount();

    const searchIds = jobSyncs.flatMap((item) => item.jobSearches.map((jobSearch) => jobSearch.id));

    const data = searchIds.length
      ? await this.jobSearchRepository.find({
          where: { id: In(searchIds) },
          // take: limit,
          // skip: (page - 1) * limit,
        })
      : [];

    return this.formatOutputData(
      { key: 'GET_SYNCED_SEARCHES' },
      { data: { data, pageSize: Number(limit), totalCount: jobCount, currentPage: page } }
    );
}


  async getSyncById(syncId: string, user: ISimpleUser, queryParams: JobSyncQuery): Promise<any> {
    const limit = queryParams.limit || 10;
    const offset = ((queryParams.page ?? 1) - 1) * limit;
    const [data, totalCount] = await this.jobSyncRepository.findAndCount({
      relations: {
        jobSearches: true,
      },
      where: {
        id: syncId,
        dataKey: user.id,
        jobSearches: {
          isDeleted: false,
        },
        isDeleted: false,
      },
      take: limit,
      skip: offset,
    });
    return this.formatOutputData(
      { key: 'GET_SYNC_BY_ID' },
      {
        data: {
          data: data,
          currentPage: queryParams.page ?? 1,
          totalCount: totalCount,
          pageSize: limit,
        },
      }
    );
  }

  async createSyncOfSearches(requestDto: CreateSyncRequestDto, user: IJwtPayload) {
    const { searchIds, updatedFor } = requestDto;
    const userId = updatedFor ?? user.id;
    try {
      await this.checkDuplicateSync(user.id, searchIds);
      const jobSearches = await this.jobSearchRepository.findByIds(searchIds);
      const referenceName = getReferenceNameBySearchesName(jobSearches.map((item) => item.searchName));
      const newJobSync = { jobSearches, referenceName, dataKey: userId };

      await this.jobSyncRepository.save(newJobSync);

      return this.formatOutputData({ key: 'CREATE_SYNC_SEARCH' }, { data: {} });
    } catch (error) {
      this.logger.error(error);
      return this.throwCommonMessage('CREATE_SYNC_SEARCH', error);
    }
  }

  async toggleSync(requestDto: ToggleSyncRequestDto, user: IJwtPayload) {
    try {
      const { jobSearchId, toggle, updatedFor } = requestDto;
      const userId = updatedFor ?? user.id;

      const jobSearch = await this.jobSearchRepository.findOneBy({ id: jobSearchId });
      const referenceName = `Sync of ${jobSearch.searchName}`;
      if (toggle === ToggleEnum.ON) {
        await this.checkDuplicateSync(user.id, [jobSearchId]);
        const newJobSync = { jobSearches: [jobSearch], referenceName, dataKey: userId, updatedBy: user.id };

        await this.jobSyncRepository.save(newJobSync);
      } else {
        //TODO: better if referenceName is indexed
        await this.jobSyncRepository.delete({ referenceName });
      }

      return this.formatOutputData({ key: 'TOGGLE_SYNC_SEACH' }, { data: {} });
    } catch (error) {
      this.logger.error(error);
      return this.throwCommonMessage('TOGGLE_SYNC_SEACH', error);
    }
  }

  async checkDuplicateSync(userId: string, jobSearchIds: string[]) {
    const syncs = await this.jobSyncRepository.find({
      where: { jobSearches: { id: In(jobSearchIds) }, dataKey: userId },
      relations: { jobSearches: true },
      order: { jobSearches: { id: 'DESC' } },
    });

    const descSort = (ids: string[]) =>
      ids.sort((a, b) => {
        return b > a ? 1 : -1;
      });

    const sortedJobSearchIds = descSort(jobSearchIds);
    await Promise.all(
      syncs.map(async (sync) => {
        const { id } = sync;
        const jobSearches = await this.jobSearchRepository.find({
          where: { jobSyncs: { id } },
          order: { jobSyncs: { id: 'DESC' } },
        });
        const dbJobSearchIds = jobSearches.map((item) => item.id);
        const sortedDbJobSearchIds = descSort(dbJobSearchIds);

        if (jobSearches.length === sortedJobSearchIds.length) {
          let count = 0;
          sortedDbJobSearchIds.forEach((id, index) => {
            if (id === sortedJobSearchIds[index]) {
              count++;
            }
          });

          if (count === jobSearches.length) {
            throw new BadRequestException('The sync has been already created');
          }
        }

        return true;
      })
    );
  }

  async getToggleSyncStatus(searchId: string, user: ISimpleUser) {
    //TODO: reuse this referenceName
    const jobSearch = await this.jobSearchRepository.findOneBy({ id: searchId });
    const referenceName = `Sync of ${jobSearch.searchName}`;

    const search = await this.jobSyncRepository.findOneBy({ referenceName, dataKey: user.id });

    return this.formatOutputData(
      { key: 'GET_TOGGLE_SYNC_STATUS' },
      { data: { status: search ? ToggleEnum.ON : ToggleEnum.OFF } }
    );
  }
}

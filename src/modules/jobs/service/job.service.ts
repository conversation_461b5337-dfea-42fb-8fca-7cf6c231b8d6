import { Promise } from 'bluebird';
import { UserEntity } from './../../user/entities/user.entity';
import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { JobBoard } from '../entities/job-boards.entity';
import { JobBoardsRepository } from '../repository/job-boards.repository';
// import { CacheService } from 'src/modules/cache/cache.service';
import { DuplicateJobRepository } from '../repository/duplicate-job.repository';
import { JobBySearchIdQuery } from '../dto/job-by-search-id.dto';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { ILike, In, IsNull, Like, Not } from 'typeorm';
import { JobLogRepository } from '../repository/job-log.repository';
import { JobLogEnum } from '../entities/job-log.entity';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { DeleteJobLogDto, UpdateJobLogDto } from '../dto/job-log/job-log.dto';
import { isWithinXMonths, standardizeJobType, subtractTime } from 'src/common/utils/helpers.util';
import { OpensearchService } from 'src/modules/opensearch/service/opensearch.service';
import { CACHE_PREFIX, CACHE_TTL, getLogoCompany } from '../constants/job.const';
import { GeocoderService } from './geocoder.service';
import { GB_CITY_TO_COUNTY } from '../constants/cities-counties.constant';
import { SSEService } from 'src/modules/sse/sse.service';
import { RECENTLY_ADDED_JOB_EVENT_NAME } from 'src/modules/sse/sse.constant';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EMITTER_EVENTS, JOBS_INDEX } from 'src/configs/configs.constants';
import { CacheService } from 'src/modules/cache/cache.service';
import { JobSearchService } from './job-search.service';
import axios from 'axios';
import { COUNTRY_CODE } from '../constants/country-code.constant';

@Injectable()
export class JobService extends BaseAbstractService {
  private readonly logger = new Logger(JobService.name);
  constructor(
    private readonly jobRepository: JobBoardsRepository,
    private readonly duplicateJobRepository: DuplicateJobRepository, // private readonly cacheService: CacheService
    private readonly i18nService: I18nService,
    private readonly jobLogRepository: JobLogRepository,
    private readonly elasticsearchService: OpensearchService,
    private readonly geocoderService: GeocoderService,
    private readonly sseService: SSEService,
    private readonly eventEmitter: EventEmitter2,
    private readonly cacheService: CacheService,
    private readonly jobSearchService: JobSearchService
  ) {
    super(i18nService);
  }

  async getJobLocationInput(job) {
    if (['remote', 'anywhere'].includes(job.joblocationcity?.toLowerCase())) {
      return `${job.joblocationcity}, ${job.joblocationinput}`;
    }

    const cacheKeyPrefix = CACHE_PREFIX.JOB_LOCATION;
    const cachedKey = `${cacheKeyPrefix}${job.joblocationcity.toLowerCase().replace(/\s+/g, ' ').trim()}`;
    const cachedLocation: any = await this.cacheService.get(cachedKey);

    if (cachedLocation && typeof cachedLocation === 'object') {
      return cachedLocation?.address || cachedLocation?.formattedAddress;
    }

    const [locationMapping] = await this.elasticsearchService.searchLocationMappings(job.joblocationcity);

    if (locationMapping) {
      await this.cacheService.set(cachedKey, locationMapping._source, CACHE_TTL.JOB_LOCATION);

      return locationMapping._source.address;
    }

    const formattedLocation = await this.getFormattedLocationByProvider(job);
    if (formattedLocation?.address) {
      const county =
        formattedLocation.countryCode === 'GB' && formattedLocation.city
          ? GB_CITY_TO_COUNTY[formattedLocation.city]
          : '';
      const extraCounty = county ? `, ${county}` : '';
      let address = `${formattedLocation.address}${extraCounty}`;
      if (
        // Case Australia only
        job.joblocationcity?.toLowerCase() === formattedLocation.country?.toLowerCase()
      ) {
        address = job.joblocationcity;
      }
      formattedLocation.address = address;
      await this.cacheService.set(cachedKey, formattedLocation, CACHE_TTL.JOB_LOCATION);
      this.elasticsearchService.addLocationMapping(job.joblocationcity, formattedLocation);

      return address;
    }

    //add this job as unknown location jobs, then after later
    await this.elasticsearchService.createUnknownLocationJob({ job_id: job.job_id, location: job.joblocationcity });

    // this could lead to less jobs
    return `${job.joblocationcity}`;
  }

  async getFormattedLocationByProvider(job) {
    try {
      //call http to lambda function
      const { data } = await axios.get(
        `https://xxieanq762xwskg7wfzs5n3b6u0fwxwa.lambda-url.eu-west-2.on.aws/?q=${job.joblocationcity}`
      );

      if (!data.formattedAddress) {
        throw new Error('Retry');
      }
      const standardCountryName = COUNTRY_CODE[data.countryCode]?.name;
      const isCorrectCountryName = standardCountryName === data.country;
      if (!isCorrectCountryName) {
        data.formattedAddress = (data.formattedAddress as string).replace(data.country, standardCountryName);
        data.country = standardCountryName;
      }

      return {
        address: data.formattedAddress,
        city: data.city,
        state: data.state,
        country: data.country,
        countryCode: data.countryCode,
      };
    } catch (error) {
      console.error('Error at getFormattedLocationByProvider', { error: error.response || error.message, job });
      const location = job.joblocationcity;

      //remove the unknown part from left to right
      // ABC, DEF => search (DEF)
      const stringLocationPart = location.split(', ');
      stringLocationPart.shift();
      if (stringLocationPart.length === 0) {
        await this.elasticsearchService.createUnknownLocationJob({ job_id: job.job_id, location: job.joblocationcity });
        return null;
      }

      const updatedLocation = stringLocationPart.join(', ');
      return this.getFormattedLocationByProvider({ ...job, joblocationcity: updatedLocation });
    }
  }

  async getJobById(id: string) {
    const job = await this.elasticsearchService.getById(JOBS_INDEX, id);

    return this.formatOutputData({ key: 'GET_JOB_BY_ID' }, { data: job });
  }

  // @Deprecated function - new jobs are created by crawl job event at src/consumers/crawl-job-event.processor.ts
  async createJob(job: Partial<JobBoard>) {
    const logoCompany = job.logoCompany ?? getLogoCompany(job.company, job.source);
    job.logoCompany = logoCompany;
    const newJobPostedAt = new Date();
    newJobPostedAt.setDate(new Date().getDate() - 6); //7 days ago

    job.joblocationinput = await this.getJobLocationInput(job);
    if (!job.joblocationcity && job.country) {
      job.joblocationcity = job.country;
    }
    // As the current job type is saved as a string - join the result with comma
    job.jobtype = standardizeJobType(job.jobtype).join(', ');

    //posted order
    const query = {
      index: JOBS_INDEX, // Replace with your actual index name
      body: {
        sort: [
          {
            posted: {
              order: 'desc',
            },
          },
        ],
        query: {
          bool: {
            must: [
              {
                match: {
                  description: {
                    query: `${job.description}`,
                    minimum_should_match: '90%',
                  },
                },
              },
              {
                match: {
                  jobtitle: {
                    query: `${job.jobtitle}`,
                    minimum_should_match: '90%',
                  },
                },
              },
              {
                term: {
                  'company.keyword': job.company,
                },
              },
              // {
              //   match: {
              //     joblocationcity: job.joblocationcity,
              //   },
              // },
              {
                range: {
                  posted: {
                    gte: newJobPostedAt.toISOString(),
                  },
                },
              },
              {
                bool: {
                  must_not: [
                    {
                      terms: {
                        job_id: [job.job_id],
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
      },
    };
    const response = await this.elasticsearchService.getClient().search(query);
    const duplicatedJobs = response.body.hits.hits.map((e: { _source: any }) => ({
      ...e._source,
      logoCompany: e._source.logo_company,
      sendToBullHornByUserIds: e._source.send_to_bullhorn_by_user_ids,
    }));

    if (duplicatedJobs.length) {
      const overridePropertiesJob = {
        joblocationcity: duplicatedJobs[0].joblocationcity ?? job.joblocationcity,
        salary: duplicatedJobs[0].salary ?? job.salary,
        min_salary: duplicatedJobs[0].min_salary ?? job.min_salary,
        max_salary: duplicatedJobs[0].max_salary ?? job.max_salary,
        description: duplicatedJobs[0].description ?? job.description,
        jobtype: duplicatedJobs[0].jobtype ?? job.jobtype,
        logoCompany: duplicatedJobs[0].logoCompany?.length ? duplicatedJobs[0].logoCompany : logoCompany,
        job_id: duplicatedJobs[0].job_id,
      };

      // update origin job
      const { job_id, ...updatedJob } = overridePropertiesJob;
      const updatedRes = await this.jobRepository.update({ job_id }, updatedJob);
      if (updatedRes.affected) {
        await this.elasticsearchService.insertOrUpdate(JOBS_INDEX, job_id, updatedJob);
      }

      //remove duplicated jobs
      const deletedDuplicatedJobIds = duplicatedJobs.map((item) => item.job_id).slice(1);
      if (deletedDuplicatedJobIds.length) {
        console.log(`[DUPLICATED] ${job?.job_id} is duplicated with Job IDs: `, deletedDuplicatedJobIds, job_id);
        const deleteRes = await this.jobRepository.delete({ job_id: In(deletedDuplicatedJobIds) });
        if (deleteRes.affected) {
          await this.elasticsearchService.deleteDocuments(JOBS_INDEX, deletedDuplicatedJobIds);
        }
      }

      //save duplicated jobs
      return this.duplicateJobRepository.save({
        ...job,
        logoCompany,
        sameWithJobIds: duplicatedJobs[0].job_id,
      });
    }

    job.posted = new Date(job.posted);
    if (isNaN(job.posted.getTime())) {
      job.posted = new Date(subtractTime(1, 'month'));
    }
    if (isWithinXMonths(job.posted, 2)) {
      // within 2 months
      const insertRes = await this.jobRepository.save({ ...job, logoCompany });
      if (insertRes) {
        const existingJobOS = await this.elasticsearchService.getById(JOBS_INDEX, job.job_id);
        const searchKeywords = existingJobOS?.search_keywords?.split(',')?.map((keyword) => keyword.trim()) || [];
        if (!searchKeywords.includes(job.search_keywords)) {
          searchKeywords.push(job.search_keywords);
        }

        await this.elasticsearchService.insertOrUpdate(JOBS_INDEX, job.job_id, {
          ...job,
          search_keywords: searchKeywords.join(', '),
          created_at: new Date(),
          insert_from_app: true,
        });

        this.sseService.emitEvent(RECENTLY_ADDED_JOB_EVENT_NAME, insertRes);
        this.eventEmitter.emit(EMITTER_EVENTS.REPORT_AGENCY_COMPANY_ADDED, {
          company: insertRes.company,
        });
      }
      this.jobSearchService.removeSearchJobCachedByCompany(job.company).catch(() => {});
      return insertRes;
    }
  }

  async getDuplicateJobs(queryDto: JobBySearchIdQuery) {
    const { page = 1, limit = 10, searchText = '' } = queryDto;
    const [jobs, totalCount] = await this.duplicateJobRepository.findAndCount({
      where: [
        { jobtitle: ILike(`%${searchText}%`) },
        { description: ILike(`%${searchText}%`) },
        { company: ILike(`%${searchText}%`) },
      ],
      take: limit,
      skip: (page - 1) * limit,
      order: {
        createdAt: 'DESC',
      },
    });

    return this.formatOutputData(
      { key: 'GET_DUPLICATED_JOB' },
      {
        data: {
          totalCount,
          data: jobs,
          pageSize: limit,
          currentPage: page,
        },
      }
    );
  }

  async getJobLogsByJobId(jobId: string) {
    const logs = await this.jobLogRepository
      .createQueryBuilder('jl')
      .leftJoin(UserEntity, 'u', 'u.id = jl.user_id')
      .where('jl.job_id = :jobId', { jobId })
      .select(
        `jl.id,
        jl.job_id as "jobId",
        jl.user_id as "userId",
        jl.type,
        jl.updated_by as "updatedBy",
        jl.updated_at as "updatedAt",
        u.email as "senderEmail"`
      )
      .getRawMany<{
        id: string;
        jobId: string;
        userId: string;
        type: JobLogEnum;
        updatedBy: string;
        updatedAt: string;
        senderEmail: string;
      }>();

    return this.formatOutputData(
      {
        key: 'GET_JOB_LOG_BY_JOB_ID',
      },
      { data: { data: logs } }
    );
  }

  async insertOrUpdateJobLog(user: IJwtPayload, updatedJobLogDto: UpdateJobLogDto) {
    const { updatedFor, type, jobId } = updatedJobLogDto;
    if (!jobId) {
      return this.formatOutputData({ key: 'INSERT_OR_UPDATE_JOB_LOG' }, { data: {} });
    }
    const userId = updatedFor ?? user.id;
    const log = `${type}:${userId}`;

    await this.elasticsearchService.updateJobLog(JOBS_INDEX, jobId, log, 3);

    return this.formatOutputData({ key: 'INSERT_OR_UPDATE_JOB_LOG' }, { data: {} });
  }

  async deleteJobLog(user: IJwtPayload, bodyDto: DeleteJobLogDto) {
    const { updatedFor, jobId } = bodyDto;
    const userId = updatedFor ?? user.id;

    // We only able to delete editing log
    const log = `${JobLogEnum.EDITING_BH_VACANCY_SUBMISSION}:${userId}`;
    await this.elasticsearchService.removeJobLog(JOBS_INDEX, jobId, log);

    return this.formatOutputData({ key: 'DELETE_JOB_LOG' }, { data: {} });
  }

  async updateLocationGBCounty() {
    const jobs = await this.jobRepository.find({
      where: { joblocationinput: Like('%, United Kingdom,%,%') },
      order: {
        updatedAt: 'DESC',
      },
    });

    await Promise.map(
      jobs,
      async (job: JobBoard) => {
        job.joblocationinput = await this.getJobLocationInput(job);
        const jobResult = await this.jobRepository.save(job);
        if (jobResult) {
          await this.elasticsearchService.insertOrUpdate(JOBS_INDEX, job.job_id, {
            ...jobResult,
            insert_from_app: true,
          });
        }
      },
      { concurrency: 50 }
    );

    return this.formatOutputData(
      { key: 'UPDATE_LOCATION_GB_COUNTY' },
      {
        data: { success: true, items: jobs.length },
      }
    );
  }

  async updateLocationAnywhere() {
    const response = await this.elasticsearchService.getClient().search({
      index: JOBS_INDEX,
      body: {
        size: 10000,
        sort: [
          {
            posted: {
              order: 'desc',
            },
          },
        ],
        query: {
          bool: {
            must: [
              {
                match: {
                  joblocationinput: 'Go Anywhere Slab, Lilongwe, Central Region, Malawi',
                },
              },
            ],
          },
        },
      },
    });

    const jobs = response.body.hits.hits;

    await Promise.map(
      jobs,
      async ({ _source: job }) => {
        await this.elasticsearchService.insertOrUpdate(JOBS_INDEX, job.job_id, {
          ...job,
          joblocationinput: 'Anywhere, United Kingdom',
          insert_from_app: true,
        });
      },
      { concurrency: 50 }
    );

    return this.formatOutputData(
      { key: 'UPDATE_LOCATION_ANYWHERE' },
      {
        data: { success: true, items: jobs.length },
      }
    );
  }

  async updateJobCreatedAt() {
    const response = await this.elasticsearchService.getClient().search({
      index: JOBS_INDEX,
      body: {
        size: 10000,
        sort: [
          {
            posted: {
              order: 'desc',
            },
          },
        ],
        query: {
          bool: {
            must_not: [
              {
                exists: {
                  field: 'created_at',
                },
              },
            ],
          },
        },
      },
    });

    const jobs = response.body.hits.hits;

    await Promise.map(
      jobs,
      async ({ _source: job }) => {
        await this.elasticsearchService.insertOrUpdate(JOBS_INDEX, job.job_id, {
          ...job,
          created_at: job.posted || job.updated_at || new Date().toISOString(),
          insert_from_app: true,
        });
      },
      { concurrency: 50 }
    );

    return this.formatOutputData(
      { key: 'UPDATE_JOB_CREATED_AT' },
      {
        data: { success: true, items: jobs.length },
      }
    );
  }

  async contributeLocation(fromDate: string) {
    let date: string;

    if (Number.isNaN(new Date(fromDate).getTime())) {
      // Set date to 30 days ago if fromDate is empty or invalid
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      [date] = thirtyDaysAgo.toISOString().split('T');
    } else {
      [date] = new Date(fromDate).toISOString().split('T');
    }
    const response = await this.elasticsearchService.getClient().search({
      index: JOBS_INDEX,
      scroll: '1m',
      body: {
        size: 1000,
        query: {
          bool: {
            must_not: [
              {
                script: {
                  script: {
                    source:
                      "if (doc['joblocationcity.keyword'].size() == 0) return false; return doc['joblocationinput.keyword'].value.startsWith(doc['joblocationcity.keyword'].value + ', ');",
                    lang: 'painless',
                  },
                },
              },
            ],
            filter: [
              {
                range: {
                  created_at: {
                    gte: date,
                    format: 'yyyy-MM-dd',
                  },
                },
              },
            ],
          },
        },
        sort: [{ created_at: { order: 'desc' } }],
      },
    });

    let scrollId = response.body._scroll_id;
    let jobs = response.body.hits.hits;
    let total = 0;
    if (jobs.length === 0) {
      console.log('No jobs found in the first scroll.');
    } else {
      do {
        const bulkOperations = [];
        for (const job of jobs) {
          // Assuming each job's _source field contains the job data
          const jobData = job._source;
          if (jobData.joblocationinput.split(',').length <= 3) {
            continue;
          }
          const location = jobData.joblocationcity;
          const action = {
            index: { _index: 'location_mappings', _id: location.toLowerCase().replace(/\s+/g, '-').trim() },
          };
          const data = {
            address: jobData.joblocationinput,
            country: jobData.country,
            location,
          };
          bulkOperations.push(action, data);
        }
        if (bulkOperations.length > 0) {
          await this.elasticsearchService.getClient().bulk({ body: bulkOperations });
        }
        total += jobs.length;

        // Fetch the next batch of results
        const nextResponse = await this.elasticsearchService.getClient().scroll({
          scroll_id: scrollId,
          scroll: '1m',
        });

        jobs = nextResponse.body.hits.hits;
        scrollId = nextResponse.body._scroll_id;
      } while (jobs.length > 0);

      // Close the scroll context when done
      await this.elasticsearchService.getClient().clearScroll({
        scroll_id: scrollId,
      });
    }

    return this.formatOutputData(
      { key: 'CONTRIBUTE_LOCATION' },
      {
        data: { success: true, items: total },
      }
    );
  }

  async resetLocationOfJob() {
    const cacheClient = this.cacheService.getClient();
    const elasticSearchClient = this.elasticsearchService.getClient();
    const locationKeys = await cacheClient.keys('geocoder:location:*');
    await Promise.all(locationKeys.map((item) => cacheClient.del(item)));
  }

  async standardizeExistingJobType() {
    const response = await this.elasticsearchService.getClient().search({
      index: JOBS_INDEX,
      body: {
        size: 10000,
        sort: [{ updated_at: { order: 'asc' } }],
        query: {
          bool: {
            must: [
              {
                bool: {
                  should: [
                    {
                      regexp: {
                        jobtype: '.*[a-z].*',
                      },
                    },
                    {
                      regexp: {
                        jobtype: '.*-.*',
                      },
                    },
                    {
                      bool: {
                        must_not: {
                          exists: {
                            field: 'jobtype',
                          },
                        },
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
      },
    });

    const jobs = response.body.hits.hits;

    await Promise.map(
      jobs,
      async ({ _source: job }) => {
        // console.log('standardizeJobType: ', job.jobtype, standardizeJobType(job.jobtype).join(', '));
        await this.elasticsearchService.insertOrUpdate(JOBS_INDEX, job.job_id, {
          ...job,
          jobtype: standardizeJobType(job.jobtype).join(', '),
          updated_at: new Date().toISOString(),
        });
      },
      { concurrency: 50 }
    );

    return this.formatOutputData(
      { key: 'UPDATE_LOCATION_ANYWHERE' },
      {
        data: { success: true, items: jobs.length },
      }
    );
  }

  async patchSearchKeywords(job: Partial<JobBoard>) {
    const jobOS = await this.elasticsearchService.getById(JOBS_INDEX, job.job_id);
    const searchKeywords = jobOS?.search_keywords?.split(',')?.map((keyword) => keyword.trim()) || [];
    if (!jobOS || !job.search_keywords || searchKeywords.includes(job.search_keywords)) {
      return this.formatOutputData(
        { key: 'PATCH_SEARCH_KEYWORD' },
        {
          data: { success: true },
        }
      );
    }

    searchKeywords.push(job.search_keywords);

    await this.elasticsearchService.getClient().update({
      index: JOBS_INDEX,
      id: job.job_id,
      body: {
        doc: {
          ...jobOS,
          search_keywords: searchKeywords.join(', '),
        },
      },
    });

    return this.formatOutputData(
      { key: 'PATCH_SEARCH_KEYWORD' },
      {
        data: { success: true },
      }
    );
  }
}

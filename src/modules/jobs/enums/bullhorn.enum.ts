export enum BullhornEntityEnum {
  CLIENT_CONTACT = "ClientContact",
  TEARSHEET = "Tearsheet",
  JOB_ORDER = "JobOrder",
  JOB_SUBMISSION = "JobSubmission",
  SENDOUT = "Sendout",
  NOTE = "Note",
  BUSINESS_SECTOR = "BusinessSector",
  SKILL = "Skill",
  CATEGORY = "Category",
  OPPORTUNITY = "Opportunity",
  LEAD = "Lead",
  CORPORATE_USER = "CorporateUser",
  CLIENT_CORPORATION = "ClientCorporation",
  CANDIDATE = 'Candidate',
  APPOINTMENT = 'Appointment',
  PLACEMENT = 'Placement',
  WORKFLOW = 'Workflow',
  VACANCY = 'Vacancy',
  COMPANY = 'Company',
  USERMESSAGE = 'UserMessage'
}
import { Keyv } from 'keyv';
import KeyvRedis from '@keyv/redis';
import { Module, Global, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { JobLeadsController } from './controllers/job-leads.controller';
import { JobSearchController } from './controllers/job-search.controller';
import { LeadStatusesController } from './controllers/lead-statuses.controller';
import { ReportedAgencyController } from './controllers/reported-agency.controller';
import { SearchSyncController } from './controllers/search-sync.controller';
import { JobLeadsService } from './service/job-leads.service';
import { JobSearchService } from './service/job-search.service';
import { LeadStatusesService } from './service/lead-statuses.service';
import { ReportedAgencyService } from './service/reported-agency.service';
import { SearchSyncService } from './service/search-sync.service';
import { JobBoardsRepository } from './repository/job-boards.repository';
import { JobLeadsRepository } from './repository/job-leads.repository';
import { JobSearchRepository } from './repository/job-search.repository';
import { JobSyncRepository } from './repository/job-sync.repository';
import { LeadStatusesRepository } from './repository/lead-statuses.repository';
import { ReportedAgencyRepository } from './repository/reported-agency.repository';
import { BullhornIntegrationController } from './controllers/bullhorn-integration.controller';
import { BullhornIntegrationService } from './service/bullhorn-integration.service';
import { BullHornService } from '../../middlewares/bullhorn/bullhorn.service';
import { BullHornMiddleware } from '../../middlewares/bullhorn/bullhorn.middleware';
import { BullModule } from '@nestjs/bullmq';
import { JobScrawlingService } from './service/job-crawling.service';
import { JobController } from './controllers/job.controller';
import { JobService } from './service/job.service';
import { AwsDBModule } from '../aws/aws-db.module';
import { DuplicateJobRepository } from './repository/duplicate-job.repository';
import { JobLogRepository } from './repository/job-log.repository';
import { OpensearchService } from '../opensearch/service/opensearch.service';
import { NotificationModule } from '../notification/notification.module';
import { UserRepository } from '../user/repositories/user.repository';
import { SentJobRepository } from './repository/sent-job.repository';
import { MailModule } from '../mail/mail.module';
import { GeocoderService } from './service/geocoder.service';
import { CacheModule } from '@nestjs/cache-manager';
import * as redisStore from 'cache-manager-redis-store';
import { SSEService } from '../sse/sse.service';
import { OpenAIService } from '../openai/openai.service';
import { RedisModule } from '../redis/redis.module';
import { VerifiedCompanyRepository } from './repository/verified_company.repository';
import { MyCacheModule } from '../cache/cache.module';
import { LeadSheetController } from './controllers/lead-sheet.controller';
import { LeadSheetService } from './service/lead-sheet.service';
import { LeadSheetRepository } from './repository/lead-sheet.repository';
import { JobLeadKeywordRepository } from './repository/job-lead-keywords.repository';
import { StatsEntityDataRepository } from './repository/stats-entity-data.repository';
import { EmailValidationResultRepository } from '../email-finder/repositories/email-validation-result.repository';
import { CrmBullhornService } from '../crm/services/crm-bullhorn.service';
import { CrmCompanyRepository } from '../crm/repositories/crm-company.repository';
import { CrmContactRepository } from '../crm/repositories/crm-contact.repository';
import { CrmIndustryRepository } from '../crm/repositories/crm-industry.repository';
import { CrmTagRepository } from '../crm/repositories/crm-tag.repository';
import { CrmSkillsRepository } from '../crm/repositories/crm-skill.repository';
import { WebhookModule } from '../webhook/webhook.module';

@Global()
@Module({
  imports: [
    HttpModule,
    BullModule.registerQueue({
      name: 'notification',
      prefix: '{bull_notification}',
    }),
    BullModule.registerQueue({
      name: 'job-search',
      prefix: '{bull_job_search}',
    }),
    MyCacheModule,
    AwsDBModule,
    NotificationModule,
    MailModule,
    CacheModule.registerAsync({
      useFactory: async () => {
        return [
          new Keyv({
            store: new KeyvRedis(process.env.REDIS_CONNECTION),
            ttl: 24 * 60 * 60,
          }),
        ];
      },
    }),
    RedisModule,
    WebhookModule,
  ],
  providers: [
    BullHornService,
    BullhornIntegrationService,
    JobLeadsService,
    JobSearchService,
    LeadStatusesService,
    ReportedAgencyService,
    SearchSyncService,
    JobBoardsRepository,
    JobLeadsRepository,
    JobSearchRepository,
    JobSyncRepository,
    LeadStatusesRepository,
    ReportedAgencyRepository,
    JobScrawlingService,
    JobService,
    DuplicateJobRepository,
    JobLogRepository,
    OpensearchService,
    UserRepository,
    SentJobRepository,
    GeocoderService,
    SSEService,
    OpenAIService,
    VerifiedCompanyRepository,
    // RedisService,
    LeadSheetService,
    LeadSheetRepository,
    JobLeadKeywordRepository,
    StatsEntityDataRepository,
    EmailValidationResultRepository,
    CrmBullhornService,
    CrmCompanyRepository,
    CrmContactRepository,
    CrmSkillsRepository,
    CrmIndustryRepository,
    CrmTagRepository,
  ],
  controllers: [
    JobLeadsController,
    BullhornIntegrationController,
    JobSearchController,
    LeadStatusesController,
    ReportedAgencyController,
    SearchSyncController,
    JobController,
    LeadSheetController,
  ],
  exports: [JobScrawlingService, JobSearchService, JobService, BullhornIntegrationService],
})
export class JobsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // consumer.apply(BullHornMiddleware).forRoutes(BullhornIntegrationController, JobLeadsController); // Apply middleware to the specified controller
  }
}

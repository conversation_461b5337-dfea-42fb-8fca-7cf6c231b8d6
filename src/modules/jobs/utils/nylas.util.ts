/* eslint-disable camelcase */
/* eslint-disable indent */
import axios from "axios";
import * as FormData from 'form-data';
import { safeParseJSON } from "src/common/utils/helpers.util";
import { NYLAS_CONFIG } from 'src/configs/configs.constants';

const extractGrantId = (config: any): string | null => {
    const { url = '' } = config;

    const regex = /\/grants\/([^\/?&]+)/i.exec(url);
    return regex ? regex[1] : null;
};


const getClient = () => {
    const client = axios.create({
        baseURL: NYLAS_CONFIG.apiUri,
        headers: {
            Accept: 'application/json',
            'User-Agent': 'Nylas Node SDK v7.5.2',
            Authorization: `Bearer ${NYLAS_CONFIG.apiKey}`,
        },
    });

    client.interceptors.response.use(
        (response) => {
            const { status, data, config } = response;
            const grantId = extractGrantId(config);
            console.log(`[NYLAS] @GrantID: ${grantId}, @Method: ${config?.method?.toUpperCase()}, @Path: ${config?.url}, @Status: ${status}`);
    
            return response;
        },
        (error) => {
            const { code, response: { status = null, data = null } = {}, config = {} } = error;
            const grantId = extractGrantId(config);
            console.error(`[NYLAS] @GrantID: ${grantId}, @Method: ${config.method?.toUpperCase()}, @Path: ${config.url}, @Status: ${status || code}, @Response:`, JSON.stringify(data));
    
            return Promise.reject(error);
        },
    );

    return client;
};

const redactImgAttributes = (html: string) => html.replace(
    /<img\s+([^>]*?)\s*\/?>/gi,
    (match, attributes) => {
        // Split the attributes into an array
        const attrsArray = attributes.trim().split(/\s+/);
        // Find the src attribute and remove it from the array
        const srcAttrIndex = attrsArray.findIndex((attr: string) => attr.startsWith('src='));
        const srcAttr = attrsArray.splice(srcAttrIndex, 1)[0];
        // Reconstruct the attributes with src first
        return `<img ${srcAttr} ${attrsArray.join(' ')}>`;
    },
);

export const send = async ({
    identifier,
    requestBody,
}: {
    identifier: string;
    requestBody: any;
}) => {
    const client = getClient();
    try {
        const formData = new FormData();
        const { attachments = [], ...message } = requestBody;
        let hasInline = false;
        attachments.forEach((attachment: any) => {
            if (attachment.contentId) {
                hasInline = true;
            }

            formData.append(
                attachment.contentId || 'file',
                typeof attachment.content === 'string' ? Buffer.from(attachment.content, 'base64') : attachment.content,
                {
                    filename: attachment.filename,
                    contentType: attachment.contentType,
                },
            );
        });
        if (hasInline) {
            message.body = redactImgAttributes(message.body);
        }
        formData.append('message', JSON.stringify(message));
        const { data } = await client.post(`/v3/grants/${encodeURIComponent(identifier)}/messages/send`, formData, {
            headers: {
                ...formData.getHeaders(),
                'Content-Type': 'multipart/form-data',
            },
        });

        return data;
    } catch (error) {
        console.error('[NYLAS] Error occurred send:', error.response?.data || error);

        throw error;
    }
};

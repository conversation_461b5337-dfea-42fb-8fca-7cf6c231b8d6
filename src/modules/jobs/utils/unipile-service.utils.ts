/* eslint-disable camelcase */
/* eslint-disable indent */
import axios from "axios";
import { safeParseJSON, sizeOf } from "src/common/utils/helpers.util";
import { APP_CONFIG, unipileConfig } from 'src/configs/configs.constants';
import { UnipileResendCheckpointDto, UnipileSolveCheckpointDto } from "src/modules/user/dto/linked-connection.dto";
import * as FormData from 'form-data';
import { LinkedInManualRawSearchDto } from "src/modules/linkedin-finder/dto/get-data-finder.dto";


const extractAccountId = (config: any): string | null => {
    const { data, url = '' } = config;
    if (data?.account_id) {
        return data.account_id;
    }
    if (safeParseJSON(data)?.account_id) {
        return safeParseJSON(data).account_id;
    }

    const regex = /account_id=([^&]+)/i.exec(url) || /\/accounts\/([^\/?&]+)/i.exec(url);
    return regex ? regex[1] : null;
};

const getClient = () => {
    const client = axios.create({
        baseURL: unipileConfig.UNIPILE_BASE_URL,
        // timeout: 100000,
        headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': unipileConfig.UNIPILE_KEY,
        },
    });

    client.interceptors.response.use((response) => {
        const { status, data, config = {} }: any = response;
        const accountId = extractAccountId(config);
        if (!config.url?.includes('api/v1/users/invite/sent')) {
            console.log(`[UNIPILE] @AccountID: ${accountId}, @Method: ${config.method?.toUpperCase()}, @Path: ${config.url}, @Status: ${status}, @Response:`, sizeOf(data) > 100_000 ? 'Data too large' : JSON.stringify(data));
        }

            return response;
        },
        (error) => {
            const { code, response: { status = null, data = null } = {}, config = {} } = error;
            const accountId = extractAccountId(config);
            console.error(`[UNIPILE] @AccountID: ${accountId}, @Method: ${config.method?.toUpperCase()}, @Path: ${config.url}, @Status: ${status || code}, @Response:`, JSON.stringify(data));

            throw error;
        },
    );

    return client;
};

export const getLinkedInIdentifier = (url: string = '') => {
    // Decode the URL to handle percent-encoded Unicode characters
    const decodedUrl = decodeURIComponent(url);
    // Use Unicode property escapes to match any kind of letter from any language,
    // as well as digits and hyphens
    const LINKEDIN_REGEX = /\/in\/([\p{L}\p{N}-]+)/u;

    return decodedUrl.match(LINKEDIN_REGEX)?.[1];
};


/* example data
{
  "object": "UserProfile",
  "provider": "LINKEDIN",
  "provider_id": "ACoAACQW8ZIBTixJ1vPBkrbOkjzakApf46GGS24",
  "public_identifier": "tam-dd",
  "first_name": "Đỗ",
  "last_name": "Đức Tâm",
  "headline": "Software Engineering Team Lead | Agile Development, NestJS, Unit Testing",
  "primary_locale": {
    "country": "US",
    "language": "en"
  },
  "is_open_profile": false,
  "is_premium": false,
  "is_influencer": false,
  "is_creator": false,
  "is_relationship": false,
  "network_distance": "THIRD_DEGREE",
  "is_self": false,
  "websites": [],
  "follower_count": 531,
  "connections_count": 534,
  "location": "Quận Nam Từ Liêm, Hanoi, Vietnam",
  "profile_picture_url": "https://media.licdn.com/dms/image/v2/D5603AQF41DebvWJSIg/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/*************?e=**********&v=beta&t=pfNFmsaL4Z3dz9i2kUJSConxFA57a8A0uPqfzSZabq4",
  "background_picture_url": "https://media.licdn.com/dms/image/v2/C5616AQEwAp3LlaBHlA/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/*************?e=**********&v=beta&t=iRmxkdSaPantk-9uiIcrkAkR1w3mQp1cGcve2Mu1_8A"
}
*/
export const getProfile = async (account_id: string, identifier: string, linkedinApi?: string, notify?: boolean) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/users/${encodeURIComponent(identifier)}?account_id=${account_id}${linkedinApi ? `&linkedin_api=${linkedinApi}` : ''}${notify ? `&notify=true` : ''}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getProfile:', error.response?.data || error);

        throw error;
    }
};

// Request connection
export const sendInvitation = async ({
    provider_id,
    account_id,
    message,
}: {
    provider_id: string;
    account_id: string;
    message: string;
}) => {
    const client = getClient();
    try {
        const { data } = await client.post('/api/v1/users/invite', {
            provider_id,
            account_id,
            message,
        });

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred sendInvitation:', error.response?.data || error);

        throw error;
    }
};

export const cancelInvitation = async ({
    invitation_id,
    account_id,
}: {
    invitation_id: string;
    account_id: string;
}) => {
    const client = getClient();
    try {
        const { data } = await client.delete(`/api/v1/users/invite/sent/${invitation_id}?account_id=${account_id}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred cancelInvitation:', error.response?.data || error);

        throw error;
    }
};

export const sendInMailMessage = async (
    account_id: string,
    attendees_ids: string[],
    { text, subject }: { text: string, subject?: string },
    api?: string,
) => {
    const client = getClient();

    try {
        console.log('[UNIPILE] attendees_ids, account_id, api: ', attendees_ids, account_id, api);

        const { data } = await client.post('/api/v1/chats', {
            account_id,
            text,
            attendees_ids,
            subject,
            linkedin: {
                api,
                inmail: true,
            },
        });


        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred sendInMailMessage:', error.response?.data || error);

        throw error;
    }
};

export const sendNormalMessage = async (
    account_id: string,
    attendees_ids: string[],
    { text, subject }: { text: string, subject?: string },
) => {
    const client = getClient();

    try {
        console.log('[UNIPILE] attendees_ids, account_id, api: ', attendees_ids, account_id);

        const { data } = await client.post('/api/v1/chats', {
            account_id,
            text,
            attendees_ids,
            subject,
        });


        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred sendNormalMessage:', error.response?.data || error);

        throw error;
    }
};

export const connectAccount = async (payload: any) => {
    const client = getClient();
    try {
        const { data } = await client.post('/api/v1/accounts', payload);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred connectAccount:', error.response?.data || error);

        throw error;
    }
};

export const syncAccountDataMessaging = async (accountId: string) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/accounts/${accountId}/sync`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred syncAccountDataMessaging:', error.response?.data || error);

        throw error;
    }
};

export const resolveCheckpoint = async (payload: UnipileSolveCheckpointDto) => {
    const client = getClient();
    try {
        const { data } = await client.post('/api/v1/accounts/checkpoint', payload);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred resolveCheckpoint:', error.response?.data || error);

        throw error;
    }
};

export const resendCheckpoint = async (payload: UnipileResendCheckpointDto) => {
    const client = getClient();
    try {
        const { data } = await client.post('/api/v1/accounts/checkpoint/resend', payload);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred resendCheckpoint:', error.response?.data || error);

        throw error;
    }
};

export const deleteAccount = async (id) => {
    const client = getClient();
    try {
        const { data } = await client.delete(`/api/v1/accounts/${id}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred resendCheckpoint:', error.response?.data || error);

        throw error;
    }
};

export const retrieveOwnProfile = async (accountId: string) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/users/me/?account_id=${accountId}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred retrieveOwnProfile:', error.response?.data || error);

        throw error;
    }
}

export const retrieveAccount = async (id) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/accounts/${id}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred retrieveAccount:', error.response?.data || error);

        throw error;
    }
};

export const getListAccounts = async () => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/accounts`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getListAccounts:', error.response?.data || error);

        throw error;
    }
};

export const reconnectAccount = async (id, body) => {
    const client = getClient();
    try {
        const { data } = await client.post(`/api/v1/accounts/${id}`, body);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred reconnectAccount:', error.response?.data || error);
        throw error;
    }
};

export const getParameters = async (accountId: string, limit: number, keywords: string, type: string) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/linkedin/search/parameters?type=${type}&limit=${limit}&keywords=${encodeURIComponent(keywords)}&account_id=${accountId}`);
        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getParameters:', error.response?.data || error);
        throw error;
    }
}

export const getLinkedInDataPeople = async (body, accountId, query: any) => {
    const client = getClient();
    try {
        const { data } = await client.post(`/api/v1/linkedin/search?account_id=${accountId}${query.limit ? `&limit=${query.limit}` : ""}${query.cursor ? `&cursor=${query.cursor}` : ""}`, body);
 
        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getLinkedInDataPeople:', error.response?.data || error);
        throw error;
    }
}

export const getSentInvitations = async (accountId: string, cursor?: string, limit: number = 100) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/users/invite/sent?account_id=${accountId}${cursor ? `&cursor=${cursor}` : ''}${limit ? `&limit=${limit}` : ''}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getInvitationSent:', error.response?.data || error);

        throw error;
    }
};

export const getRelations = async (accountId: string, cursor?: string, limit: number = 1000) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/users/relations?account_id=${accountId}${cursor ? `&cursor=${cursor}` : ''}${limit ? `&limit=${limit}` : ''}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getInvitationSent:', error.response?.data || error);

        throw error;
    }
};

export const getListLinkedinChats = async (accountId: string, cursor?: string): Promise<any[]> => {
    const client = getClient();
    let allItems: any[] = [];

    try {
        const { data } = await client.get(
            `/api/v1/messages?account_id=${accountId}${cursor ? `&cursor=${cursor}` : ""}`
        );

        allItems = [...allItems, ...data.items];

        if (data.cursor) {
            const nextItems = await getListLinkedinChats(accountId, data.cursor);
            allItems = [...allItems, ...nextItems];
        }
    } catch (error) {
        console.error('[UNIPILE] Error occurred in getListLinkedinChats:', error.response?.data || error);
        throw error;
    }

    return allItems;
};

export const getLinkedinListChat = async (accountId: string, cursor?: string) => {
    const client = getClient();
    try {
        const response = await client.get(
            `/api/v1/chats?account_id=${accountId}${cursor ? `&cursor=${cursor}` : ""}&limit=12`
        );
        return response.data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred in fetchLinkedinChatData:', error.response?.data || error);
        throw error;
    }
};


export const getLinkedinListChatDetail = async (chatId: string) => {
    const client = getClient();

    try {
        const { data } = await client.get(
            `/api/v1/chats/${chatId}`
        );

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred in getLinkedinListChatDetail:', error.response?.data || error);
        throw error;
    }
};

export const getListAttendeesFromAChat = async (chatId: string) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/chats/${chatId}/attendees`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getListAttendeesFromAChat:', error.response?.data || error);

        throw error;
    }
};

export const getLinkedinMessageInChat = async (accountId: string, chatId: string, cursor?: string) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/chats/${chatId}/messages`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getLinkedinMessageInChat:', error.response?.data || error);

        throw error;
    }
};

export const updateUnreadChat = async (read: boolean, chatId: string) => {
    const client = getClient();
    try {
        const { data } = await client.patch(`/api/v1/chats/${chatId}`, {
            action: "setReadStatus",
            value: read
        });

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred updateUnreadChat:', error.response?.data || error);

        throw error;
    }
};

export const sendMessagesToChat = async (accountId: string, chatId: string, text: string, attachments?: any[]) => {
    const client = getClient();
    try {
        const formData = new FormData();
        formData.append('text', text);

        attachments.forEach((attachment) => {
            formData.append(
                'attachments',
                attachment.buffer,
                {
                    filename: attachment.originalname,
                    contentType: attachment.mimetype,
                },
            );
        });

        const { data } = await client.post(`/api/v1/chats/${chatId}/messages`, formData, {
            headers: {
                ...formData.getHeaders(),
            },
        });

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred sendMessagesToChat:', error.response?.data || error);

        throw error;
    }
}

export const getFileFromAMessage = async (messageId: string, attachmentId: string) => {
    const client = getClient();
    try {
        const url = `api/v1/messages/${messageId}/attachments/${attachmentId}`;
        const { data } = await client.get(url, {responseType: 'arraybuffer'});

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getFileFromAMessage:', error.response?.data || error);

        throw error;
    }
}

export const getMessageDetail = async (messageId: string) => {
    const client = getClient();
    try {
        const url = `api/v1/messages/${messageId}/`;
        const { data } = await client.get(url);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getMessageDetail:', error.response?.data || error);

        throw error;
    }
}

export const retrieveAPost = async (accountId: string, postId: string) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/posts/${postId}?account_id=${accountId}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred retrieveAPost:', error.response?.data || error);

        throw error;
    }
}

export const reactAPost = async (accountId: string, postId: string, reactionType: string) => {
    const client = getClient();
    try {
        const { data } = await client.post(`/api/v1/posts/reaction`, {
            account_id: accountId,
            post_id: postId,
            reaction_type: reactionType,
        });

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred reactAPost:', error.response?.data || error);

        throw error;
    }
}

export const listAllPostOfUser = async (accountId: string, identifier: string, limit = 100) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/users/${identifier}/posts?limit=${limit}&account_id=${accountId}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred listAllPostOfUser:', error.response?.data || error);

        return [];
    }
}

export const followUser = async(ocaid: string, accountId: string) => {
    const client = getClient();
    try {
        const { data } = await client.post(`/api/v1/linkedin`, {
            "body": {"patch":{"$set":{"following":true}}},
            "account_id": accountId,
            "method": "POST",
            "request_url": `https://www.linkedin.com/voyager/api/feed/dash/followingStates/urn:li:fsd_followingState:urn:li:fsd_profile:${ocaid}`,
            "encoding": false
        });

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred followUser:', error.response?.data || error);

        throw error;
    }
}


export const unfollowUser = async(ocaid: string, accountId: string) => {
    const client = getClient();
    try {
        const { data } = await client.post(`/api/v1/linkedin`, {
            "body": {"patch":{"$set":{"following":false}}},
            "account_id": accountId,
            "method": "POST",
            "request_url": `https://www.linkedin.com/voyager/api/feed/dash/followingStates/urn:li:fsd_followingState:urn:li:fsd_profile:${ocaid}`,
            "encoding": false
        });

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred unfollowUser:', error.response?.data || error);

        throw error;
    }
}

export const getParametersData = async (body, accountId: string, limit: string, cursor?: string) => {
    const client = getClient();
    try {
        const { data } = await client.post(`/api/v1/linkedin/search?account_id=${accountId}&limit=${limit}${cursor ? `&cursor=${cursor}` : ""}`, body);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getParametersData:', error.response?.data || error);

        throw error;
    }
}

export const getCompanyDetail = async (accountId: string, companyId: string) => {
    const client = getClient();
    try {
        const { data } = await client.get(`api/v1/linkedin/company/${companyId}?account_id=${accountId}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getCompanyDetail:', error.response?.data || error);

        throw error;
    }
}

export const retrieveProfile = async (account_id: string, identifier: string) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/users/${encodeURIComponent(identifier)}?account_id=${account_id}&linkedin_sections=skills&linkedin_sections=experience&linkedin_sections=education`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred retrieveProfile:', error.response?.data || error);

        throw error;
    }
};

export const getLinkedinRawSearch = async (account_id: string, dto: LinkedInManualRawSearchDto) => {
    const client = getClient();
    try {
        const body = {
            "query_params": {
              "variables": `(query:${dto.keyword})`,
                "queryId":"voyagerSearchDashTypeahead.eeea0c1dfdf533272e9de4e98a59d725"
            },
            "account_id": account_id,
            "method": "GET",
            "request_url": "https://www.linkedin.com/voyager/api/graphql",
            "encoding": false,
            "includeWebMetadata": true
          }
        const { data } = await client.post(`/api/v1/linkedin`, body);
        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getLinkedinRawSearch:', error.response?.data || error);
        throw error;
    }
};

export const getOauthLink = async (userId: string) => {
    const client = getClient();
    try {
        const { data } = await client.post(`/api/v1/hosted/accounts/link`, {
            type: 'create',
            bypass_success_screen: true,
            providers: [
                'GOOGLE',
                'OUTLOOK',
            ],
            name: 'account_id',
            expiresOn: new Date(Date.now() + 15 * 60 * 1000),
            api_url: unipileConfig.UNIPILE_BASE_URL,
            success_redirect_url: `${APP_CONFIG.CLIENT_URL}/unipile-callback`,
            failure_redirect_url: `${APP_CONFIG.CLIENT_URL}/unipile-callback?failed=true`,
            notify_url: `${APP_CONFIG.API_URL}/users/unipile/${userId}/webhook`,
        });

        return data?.url;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getEmailDetail:', error.response?.data || error);

        throw error;
    }
};

export const getEmailDetail = async (accountId: string, emailId: string, includeHeaders: boolean = false) => {
    const client = getClient();
    try {
        const { data } = await client.get(`/api/v1/emails/${emailId}?account_id=${accountId}${includeHeaders ? `&include_headers=true` : ''}`);

        return data;
    } catch (error) {
        console.error('[UNIPILE] Error occurred getEmailDetail:', error.response?.data || error);

        throw error;
    }
};

import { join } from 'path';
import axios from 'axios';
import { stringify } from 'querystring';
import { GetCountiesQueryDto, GetCountriesQueryDto } from '../dto/bull-horn/get-counties.dto';
import { GetCorporateUserQueryDto } from '../dto/get-corporate.dto';
import { BullhornEntityEnum } from '../enums/bullhorn.enum';
import { BullhornSubmission, BullhornSubmissionTotal } from '../dto/bull-horn/bullhorn-job.dto';
import { BHUploadFileDto } from '../dto/bull-horn/update-file.dto';
import * as FormData from 'form-data';
import { convertToLuceneQuery } from 'src/common/utils/helpers.util';

export const specialChars = /[`!@#$%^&*()_\-+=\[\]{};':"\\|,.<>\/?~ ]/;

const removeTextWithSpecialCharacter = (text) => {
  if (!text) return;
  if (!text.trim()) return text;

  const splittedText = text.split(' ');
  const returnTextArr = [];
  for (const word of splittedText) {
    if (!word.includes(`'`)) {
      returnTextArr.push(word);
    }
  }

  return returnTextArr.join(' ');
};

function objectToQueryString(obj: object) {
  const parts = [];
  for (const [key, value] of Object.entries(obj)) {
    if (Array.isArray(value)) {
      value.forEach((item) => parts.push(`${key}=${encodeURIComponent(item)}`));
    } else {
      parts.push(`${key}=${encodeURIComponent(value)}`);
    }
  }

  return parts.join('&');
}

export const createJobOrder = async (jobData: any, bhpToken: string, corporateRestEndpoint: string) => {
  const finalUrl = `${corporateRestEndpoint}entity/JobOrder?BhRestToken=${bhpToken}`;
  try {
    const { data } = await axios.put(finalUrl, jobData);
    return data;
  } catch (error) {
    console.error('Error occurred createJobOrder:', error.response.data);
    throw error;
  }
};

export const updateJobOrder = async (jobData: any, bhpToken: string, corporateRestEndpoint: string) => {
  const finalUrl = `${corporateRestEndpoint}entity/JobOrder/${jobData.bullhornjoborderid}?BhRestToken=${bhpToken}`;
  try {
    const { data } = await axios.post(finalUrl, jobData);
    return data;
  } catch (error) {
    console.error('Error occurred updateJobOrder:', error.response.data);
    throw error;
  }
};

export const updateEntity = async (
  payload: any,
  entityId: string,
  entityName: string,
  bhpToken: string,
  corporateRestEndpoint: string
) => {
  const finalUrl = `${corporateRestEndpoint}entity/${entityName}/${entityId}?BhRestToken=${bhpToken}`;
  try {
    const { entityName, ...necessaryPayload } = payload;
    const { data } = await axios.post(finalUrl, necessaryPayload);
    return data;
  } catch (error) {
    console.error('Error occurred updateEntity:', error.response.data);
    throw error;
  }
};

export const insertEntity = async (
  payload: any,
  entityName: string,
  bhpToken: string,
  corporateRestEndpoint: string
) => {
  const finalUrl = `${corporateRestEndpoint}entity/${entityName}?BhRestToken=${bhpToken}`;
  try {
    const { data } = await axios.put(finalUrl, payload);
    return data;
  } catch (error) {
    console.error('Error occurred insertEntity:', error.response.data);
    throw error;
  }
};

export const insertEntityWorkflow = async (
  payload: any,
  entityName: string,
  bhpToken: string,
  corporateRestEndpoint: string
) => {
  const finalUrl = `${corporateRestEndpoint}entity/${entityName}?BhRestToken=${bhpToken}`;
  try {
    const { data } = await axios.put(finalUrl, payload);
    return data;
  } catch (error) {
    console.error('Error occurred insertEntity:', error.response.data);
    throw error;
  }
};

export const associateEntityWithJobOrder = async (
  jobOrderId: string,
  associateEntityName: string,
  idList: string,
  bhpToken: string,
  corporateRestEndpoint: string
) => {
  const finalUrl = `${corporateRestEndpoint}/entity/JobOrder/${jobOrderId}/${associateEntityName}/${idList}?BhRestToken=${bhpToken}`;
  try {
    const { data } = await axios.put(finalUrl, {});
    return data;
  } catch (error) {
    console.error('Error occurred associateEntityWithJobOrder:', error.response.data);
    throw error;
  }
};

export const createClientCorporation = async (updatedClient: any, bhpToken: string, corporateRestEndpoint: string) => {
  const finalUrl = `${corporateRestEndpoint}entity/ClientCorporation?BhRestToken=${bhpToken}`;
  try {
    const { data } = await axios.put(finalUrl, updatedClient);
    return data;
  } catch (error) {
    console.error('Error occurred createClientCorporation:', error.response.data);
    throw error;
  }
};

export const createClientCorporationContact = async (
  updatedClientContact: any,
  clientCorporationId: string,
  bhpToken: string,
  corporateRestEndpoint: string
) => {
  const finalUrl = `${corporateRestEndpoint}/entity/ClientContact?BhRestToken=${bhpToken}`;
  try {
    const { data } = await axios.put(finalUrl, {
      ...updatedClientContact,
      clientCorporation: {
        id: clientCorporationId,
      },
    });
    return data;
  } catch (error) {
    console.error('Error occurred createClientCorporationContact:', error.response.data);
    throw error;
  }
};

export const getFile = async (
  requestPayload: GetCorporateUserQueryDto,
  entityName: string,
  bhpToken: string,
  corporateRestEndpoint: string
) => {
  const { query: rawQuery, start, count, clientCorporationId, queryId } = requestPayload;

  const query = removeTextWithSpecialCharacter(rawQuery) || rawQuery;
  let finalUrl = `${corporateRestEndpoint}entity/${entityName}/${clientCorporationId}/fileAttachments?BhRestToken=${bhpToken}&start=${start}&count=${count}&`;
  let queryPayload = { where: 'isDeleted=false' };
  let fields = [];
  switch (entityName) {
    case BullhornEntityEnum.JOB_ORDER:
      fields = [
        'id',
        'dateAdded',
        'name',
        'type',
        'isPrivate',
        'fileSize',
        'fileExtension',
        'directory',
        'description',
        'usersSharedWith',
        'isEncrypted',
        'distribution',
      ];
      break;
    case BullhornEntityEnum.CLIENT_CORPORATION:
      fields = [
        'id',
        'dateAdded',
        'name',
        'type',
        'isPrivate',
        'fileSize',
        'fileExtension',
        'directory',
        'description',
        'usersSharedWith',
        'isEncrypted',
      ];
      break;
    default:
      return;
  }

  const queryPayloadURLEncoded = stringify(queryPayload);

  finalUrl = finalUrl + queryPayloadURLEncoded + `&fields=${fields.join(',')}` + '&showTotalMatched=true';
  try {
    const { data } = await axios.get(finalUrl);
    if (data.data && Array.isArray(data.data)) {
      data.data = data.data.map((obj) => ({
        ...obj,
        url: `${corporateRestEndpoint}file/${entityName}/${clientCorporationId}/${obj.id}/raw`,
      }));
    }
    return data;
  } catch (e) {
    console.error('Error occurred getFile:', e);
    throw e;
  }
};

export const uploadBHFile = async (
  requestBody: BHUploadFileDto,
  entityName: string,
  bhpToken: string,
  corporateRestEndpoint: string,
  file: any
) => {
  const { id, name, fileSize, fileType, distribution } = requestBody;

  const formData = new FormData();
  const blob = new Blob([file.buffer], { type: file.mimetype });
  formData.append('file', file.buffer, {
    filename: file.originalname,
    contentType: file.mimetype,
  });
  formData.append('name', name);
  formData.append('fileSize', fileSize.toString());
  formData.append('fileType', fileType);
  formData.append('distribution', distribution);

  const finalUrl = `${corporateRestEndpoint}file/${entityName}/${id}/raw?externalID=-1&BhRestToken=${bhpToken}`;
  try {
    const { data } = await axios.put(finalUrl, formData, {
      headers: {
        ...formData.getHeaders(),
        'Content-Type': 'multipart/form-data',
      },
    });
    return data;
  } catch (e) {
    console.error('Error occurred during upload:', e);
    throw e;
  }
};

export const deleteFileBH = async ({
  entity,
  id,
  parentId,
  bhpToken,
  corporateRestEndpoint,
}: {
  entity: BullhornEntityEnum;
  id: string;
  parentId: string;
  bhpToken: string;
  corporateRestEndpoint: string;
}) => {
  const deleteEntityUrl = `${corporateRestEndpoint}file/${entity}/${parentId}/${id}?BhRestToken=${bhpToken}`;

  try {
    const { data } = await axios.delete(deleteEntityUrl);
    return data;
  } catch (error) {
    console.error('Error occurred deleteFile:', error?.response?.data);
    throw error;
  }
};

function isValidEmail(email) {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
}

export const queryGetListShortList = async (
  bhpToken: string,
  corporateRestEndpoint: string,
  start?: number,
  jobId?: string,
  shortListId?: string
) => {
  let finalUrl = `${corporateRestEndpoint}search/JobSubmission?BhRestToken=${bhpToken}&`;
  finalUrl += `&count=200&`;
  if (start) {
    finalUrl += `&start=${start}&`;
  }
  let queryPayload = { query: '' };
  queryPayload.query +=
    'isDeleted:0  AND NOT status:Archive AND NOT candidate.status:Archive AND NOT jobOrder.status:Archive AND NOT candidate.isDeleted:1 AND NOT jobOrder.isDeleted:1 ';
  if (jobId) {
    queryPayload.query += `AND jobOrder.id:${jobId}`;
  }
  if (shortListId) {
    queryPayload.query += `AND id:[${shortListId} TO ${shortListId}]`;
  }
  const fields = [
    'id',
    'dateAdded',
    'dateLastModified',
    'candidate(id,firstName,lastName,email,source,address,isAnonymized,occupation, phone, companyName)',
    'status',
    'sendingUser',
    'source',
    'jobOrder(id,title,owner,employmentType,startDate,dateEnd,clientContact(id,firstName,lastName,email,address),clientCorporation(id,name))',
    'appointments(id,parentAppointment(id,subject,location))',
    'latestAppointment(id,subject,location)',
    'payRate',
    'billRate',
    'salary',
    'customText1',
    'startDate',
    'endDate',
  ];
  const queryPayloadURLEncoded = stringify(queryPayload);

  finalUrl =
    finalUrl + queryPayloadURLEncoded + `&fields=${fields.join(',')}` + '&showTotalMatched=true&sort=-dateAdded';
  const { data } = await axios.get(finalUrl);
  const response = data;
  return response;
};

export const queryGetListVacancy = async (
  bhpToken: string,
  corporateRestEndpoint: string,
  start?: number,
  companyId?: string
) => {
  let finalUrl = `${corporateRestEndpoint}search/JobOrder?BhRestToken=${bhpToken}&`;
  finalUrl += `&count=200&`;
  if (start) {
    finalUrl += `&start=${start}&`;
  }
  let queryPayload = { query: '' };
  queryPayload.query += 'isDeleted:0  AND NOT status:Archive ';
  if (companyId) {
    queryPayload.query += `AND clientCorporation.id:${companyId}`;
  }
  const fields = ['id', 'submissions', 'title', 'clientCorporation(id)'];
  const queryPayloadURLEncoded = stringify(queryPayload);

  finalUrl =
    finalUrl + queryPayloadURLEncoded + `&fields=${fields.join(',')}` + '&showTotalMatched=true&sort=-dateAdded';

  console.log('finalUrl', finalUrl);

  const { data } = await axios.get(finalUrl);
  const response = data;
  return response;
};

export const queryEntity = async (
  requestPayload: GetCorporateUserQueryDto,
  entityName: string,
  bhpToken: string,
  corporateRestEndpoint: string
) => {
  const {
    query: rawQuery,
    start,
    count,
    clientCorporationId,
    vacancyId,
    queryId,
    email,
    fastSearch,
    tearSheetId,
    fromContactFinder,
    customSearchField,
    customSearchValue,
    optionCustom,
    typeCustom,
  } = requestPayload;
  const query = removeTextWithSpecialCharacter(rawQuery) || rawQuery;
  // const query = !rawQuery ? rawQuery : encodeURIComponent(rawQuery);
  //TODO: update url
  let searchMethod = 'query';
  if (
    entityName === BullhornEntityEnum.NOTE ||
    entityName === BullhornEntityEnum.CANDIDATE ||
    entityName === BullhornEntityEnum.USERMESSAGE ||
    entityName === BullhornEntityEnum.CLIENT_CONTACT
  ) {
    searchMethod = 'search';
  }
  let finalUrl = `${corporateRestEndpoint}${searchMethod}/${entityName}?BhRestToken=${bhpToken}&`;

  if (start && count) {
    finalUrl +=
      entityName === 'BusinessSector' || entityName === 'Skill' || entityName === 'Category'
        ? `&start=${start}&count=100&orderBy=name&`
        : `&start=${start}&count=${count}&`;
  }

  let nameQuery = `name LIKE '%${query}%'`;
  if (entityName === BullhornEntityEnum.SKILL) {
    nameQuery = `name IS NOT NULL`;
  }
  if (entityName === BullhornEntityEnum.CLIENT_CONTACT) {
    if (query?.trim() == '') {
      nameQuery = '';
    } else {
      const nameArr = query?.trim()?.split(' ');
      const nameQueryStr = nameArr.join(' AND ');
      const queryRelation = email && isValidEmail(email) ? 'OR' : 'AND';
      nameQuery = `isDeleted:0 ${queryRelation} name:(${nameQueryStr}) `;
    }
  }

  let fields = ['name', 'id'];
  let queryPayload = query ? { where: nameQuery } : { where: '' };
  if (entityName === BullhornEntityEnum.JOB_ORDER) {
    fields = [
      'title',
      'id',
      'clientCorporation',
      'address',
      'description',
      'clientContact',
      'owner',
      'status',
      'dateAdded',
    ];
    const standardClientCorporationId = clientCorporationId?.toString().trim() ? clientCorporationId : null;
    queryPayload = standardClientCorporationId
      ? { where: `clientCorporation.id=${standardClientCorporationId}` }
      : { where: '' };

    if (query) {
      queryPayload.where += `title LIKE '%${query}%'`;
    }
    queryPayload.where += standardClientCorporationId || query ? `AND isDeleted=false` : `isDeleted=false`;
  }
  if (entityName === BullhornEntityEnum.OPPORTUNITY) {
    fields = ['title', 'id', 'clientCorporation', 'address', 'owner', 'status', 'dateAdded'];
    queryPayload =
      query || clientCorporationId ? { where: `clientCorporation.id=${clientCorporationId ?? query}` } : { where: '' };
  }
  if (entityName === BullhornEntityEnum.LEAD) {
    fields = ['title', 'id', 'clientCorporation', 'clientContact', 'owner', 'status', 'dateAdded'];
    queryPayload =
      query || clientCorporationId ? { where: `clientCorporation.id=${clientCorporationId ?? query}` } : { where: '' };
  }
  switch (entityName) {
    case BullhornEntityEnum.BUSINESS_SECTOR:
    case BullhornEntityEnum.CATEGORY:
      queryPayload.where = `name IS NOT NULL`;
      break;
    case BullhornEntityEnum.SKILL:
      if (!query) {
        queryPayload.where = `name IS NOT NULL AND enabled=true AND categories.total > 0`;
      }
      fields.push('categories');
      fields.push('enabled');
      break;
    case BullhornEntityEnum.CORPORATE_USER:
      if (!query || query == "") {
        nameQuery = `name IS NOT NULL`;
        queryPayload.where += nameQuery
      }
      queryPayload.where += ` AND isDeleted=false AND isHidden=0 AND isLockedOut=false`;
      fields.push('email');
      break;
    case BullhornEntityEnum.CANDIDATE:
      queryPayload.where = `isDeleted:0  AND NOT status:Archive`;
      fields = [
        'id',
        'name',
        'firstName',
        'lastName',
        'owner(id,firstName,lastName)',
        'employmentPreference',
        'occupation',
        'companyName',
        'email',
        'status',
      ];
      if (query) {
        queryPayload.where += `AND name:(+"${query}")`;
      }

      if (tearSheetId) queryPayload.where += ` AND tearsheets.id:${tearSheetId}`;
      break;
    case BullhornEntityEnum.SENDOUT:
      // queryPayload.where += `jobOrder.id=${clientCorporationId}`;
      if (vacancyId) {
        queryPayload.where += `jobOrder.id=${vacancyId}`;
      }

      if (clientCorporationId) {
        queryPayload.where += `clientCorporation.id=${clientCorporationId}`;
      }
      fields = [
        'candidate(firstName,id,lastName,email,isAnonymized)',
        'clientCorporation(id,name)',
        'clientContact(firstName,id,lastName)',
        'dateAdded',
        'id',
        'numTimesRead',
        'jobSubmission',
        'jobOrder(id,employmentType,owner)',
        'user',
      ];
      break;
    case BullhornEntityEnum.APPOINTMENT:
      if (vacancyId) {
        queryPayload.where += `isDeleted=false AND clientContactReference.id>=1 AND candidateReference.id>=1 AND jobOrder.isDeleted=false AND jobOrder.id=${vacancyId} AND parentAppointment.id=0 AND type='Interview'`;
      }

      if (clientCorporationId) {
        queryPayload.where += `isDeleted=false AND clientContactReference.id>=1 AND candidateReference.id>=1 AND jobOrder.isDeleted=false AND jobOrder.clientCorporation.id=${clientCorporationId} AND parentAppointment.id=0 AND type='Interview'`;
      }
      // queryPayload.where += `isDeleted=false AND clientContactReference.id>=1 AND candidateReference.id>=1 AND jobOrder.isDeleted=false AND jobOrder.id=${clientCorporationId} AND parentAppointment.id=0 AND type='Interview'`;
      fields = [
        'id',
        'subject',
        'dateBegin',
        'candidateReference(id,lastName,firstName,address)',
        'clientContactReference(id,firstName,lastName,address)',
        'jobOrder(id,title)',
        'type',
        'location',
        'owner(id,firstName,lastName)',
        'parentAppointment',
      ];
      break;
    case BullhornEntityEnum.PLACEMENT:
      // queryPayload.where += `jobOrder.id=${clientCorporationId}`;

      if (vacancyId) {
        queryPayload.where += `jobOrder.id=${vacancyId}`;
      }

      if (clientCorporationId) {
        queryPayload.where += `jobOrder.clientCorporation.id=${clientCorporationId}`;
      }

      fields = [
        'id',
        'status',
        'dateAdded',
        'jobSubmission',
        'candidate(id,firstName,lastName,source,isAnonymized)',
        'jobOrder(id,title,clientCorporation(id,name),clientContact(id,firstName,lastName,email))',
        'salary',
        'payRate',
        'clientBillRate',
        'dateBegin',
        'dateEnd',
        'employmentType',
        'fee',
      ];
      break;
    case BullhornEntityEnum.USERMESSAGE:
      if (vacancyId) {
        queryPayload.where += `jobOrderID:${vacancyId} AND isDeleted:false`;
      }

      if (clientCorporationId) {
        queryPayload.where += `clientCorporationId:${clientCorporationId} AND isDeleted:false`;
      }

      fields = [
        'id',
        'dateAdded',
        'comments',
        'externalFrom',
        'externalTo',
        'externalCC',
        'ccRecipients(name,email,id)',
        'toRecipients(name,email,id)',
        'isPrivate',
        'sender(name,email,id)',
        'smtpHeaderData',
        'smtpSendDate',
        'smtpReceiveDate',
        'subject',
        'threadID',
        'messageFiles(id,name)',
        'emailRecipients',
      ];
      break;
    case BullhornEntityEnum.NOTE:
      if (vacancyId) {
        queryPayload.where += `jobOrderID:${vacancyId} AND isDeleted:false`;
      }

      if (clientCorporationId) {
        queryPayload.where += `clientContactUserID:\"^(clientCorporationID=${clientCorporationId})\" AND isDeleted:false`;
      }

      fields = [
        'id',
        'action',
        'commentingPerson(firstName,lastName,name)',
        'clientContacts(name,id,firstName,lastName)',
        'candidates',
        'jobOrders',
        'leads(firstName,lastName,name)',
        'opportunities(id,title)',
        'placements(id, candidate, jobOrder)',
        'personReference(name,id,firstName,lastName)',
        'dateAdded',
        'comments',
        'primaryDepartmentName',
      ];
      break;
    case BullhornEntityEnum.JOB_SUBMISSION:
      queryPayload.where += `${query ? ' AND ' : ''}isDeleted=false `;
      if (vacancyId) {
        queryPayload.where += `AND jobOrder.id=${vacancyId} AND jobOrder.isDeleted=false`;
      }

      if (clientCorporationId) {
        queryPayload.where += `AND jobOrder.clientCorporation.id=:${clientCorporationId} AND isDeleted=false`;
      }
      fields = [
        'id',
        'dateAdded',
        'dateLastModified',
        'candidate(id,firstName,lastName,email,source,address,isAnonymized,occupation)',
        'status',
        'sendingUser',
        'source',
        'jobOrder(id,title,owner,employmentType,startDate,dateEnd,clientContact(id,firstName,lastName,email,address),clientCorporation(id,name))',
        'appointments(id,parentAppointment(id,subject,location))',
        'latestAppointment(id,subject,location)',
        'payRate',
        'billRate',
        'salary',
        'customText1',
        'startDate',
        'endDate',
      ];
      break;
    case BullhornEntityEnum.CLIENT_CONTACT:
      if (!query || query.trim() == '') {
        queryPayload.where += 'isDeleted:0 ';
      }
      if (email && isValidEmail(email)) {
        queryPayload.where += ` ${fromContactFinder ? 'AND' : 'OR '} email:(${email})`;
      }
      queryPayload.where += `AND NOT status:Archive `;
      if (clientCorporationId) queryPayload.where += `AND clientCorporation.id:${clientCorporationId}`;
      if (tearSheetId) queryPayload.where += `AND tearsheets.id:${tearSheetId}`;
      if (customSearchField && customSearchValue) {
        const searchQuery =
          optionCustom === 'LIKE'
            ? `AND ${customSearchField} LIKE '%${customSearchValue}%'`
            : typeCustom == 'STRING'
            ? `AND ${customSearchField} = '${customSearchValue}'`
            : `AND ${customSearchField} = ${customSearchValue}`;
        queryPayload.where += searchQuery;
      }
      fields.push(
        'email',
        'fax',
        'phone',
        'status',
        'trackTitle',
        'clientCorporation',
        'occupation',
        'address',
        'businessSectors',
        'massMailOptOut',
        'firstName',
        'lastName',
        'middleName',
        'customText1',
        'dateLastVisit',
        'dateLastModified',
        'dateAdded',
        'owner'
      );
      break;
    case BullhornEntityEnum.CLIENT_CORPORATION:
      queryPayload.where += ` AND status <> 'Archive'`;
      fields.push('fax', 'phone', 'status', 'trackTitle', 'address', 'businessSectorList');
      break;
    case BullhornEntityEnum.TEARSHEET:
      if (tearSheetId) queryPayload.where = `id = ${tearSheetId}`;
      queryPayload.where += ` ${queryPayload.where ? 'AND' : ''} isDeleted=false AND isUserTearsheet=false`;
      fields.push('clientContacts', 'dateAdded', 'description', 'candidateCount', 'candidates', 'clientContactCount');
      break;
    default:
      break;
  }

  let updatedQuery: any = { ...queryPayload };

  if (
    entityName === BullhornEntityEnum.NOTE ||
    entityName === BullhornEntityEnum.CANDIDATE ||
    entityName === BullhornEntityEnum.USERMESSAGE ||
    entityName === BullhornEntityEnum.CLIENT_CONTACT
  ) {
    const { where, ...rest } = updatedQuery;
    updatedQuery = { ...rest, query: where };
  }

  const queryPayloadURLEncoded = stringify(updatedQuery);

  finalUrl =
    finalUrl + queryPayloadURLEncoded + `&fields=${fields.join(',')}` + '&showTotalMatched=true&sort=-dateAdded';
  if (queryId) {
    let fields = [];
    if (entityName === BullhornEntityEnum.CLIENT_CORPORATION) {
      fields = [
        'id',
        'name',
        'address',
        'fax',
        'phone',
        'status',
        'parentClientCorporation',
        'childClientCorporations',
        'owners',
        'industryList',
        'companyDescription',
        'companyURL',
        'billingAddress',
        'billingContact',
        'billingPhone',
        'billingFrequency',
        'invoiceFormat',
        'feeArrangement',
        'notes',
        'businessSectorList',
      ];
    } else if (entityName === BullhornEntityEnum.CLIENT_CONTACT) {
      fields = [
        'id',
        'firstName',
        'middleName',
        'lastName',
        'name',
        'status',
        'type',
        'secondaryOwners',
        'clientCorporation',
        'division',
        'namePrefix',
        'occupation', //job title
        'reportToPerson',
        'email',
        'email2',
        'email3',
        'fax',
        'fax2',
        'fax3',
        'phone',
        'phone2',
        'phone3',
        'dateLastVisit',
        'dateLastModified',
        'dateAdded',
        'owner',
        'customText1', // get Linked Profile URL
      ];
    } else if (entityName === BullhornEntityEnum.JOB_ORDER) {
      fields = fields = [
        'id',
        'address',
        'businessSectors',
        'categories',
        'clientBillRate',
        'clientContact(id,address,email,mobile,phone,phone2,clientCorporation)',
        'clientCorporation(id,companyDescription,address,billingAddress,billingPhone,billingContact)',
        'dateEnd',
        'description',
        'employmentType',
        'feeArrangement',
        'isOpen',
        'markUpPercentage',
        'numOpenings',
        'owner',
        'payRate',
        'publicDescription',
        'salary',
        'salaryUnit',
        'skills',
        'source',
        'startDate',
        'status',
        'title',
        'dateAdded',
      ];
    }
    let checkMeta = false;
    if (entityName === BullhornEntityEnum.NOTE) {
      checkMeta = true;
    }
    finalUrl = `${corporateRestEndpoint}/entity/${entityName}/${queryId}?BhRestToken=${bhpToken}${
      checkMeta ? '&meta=full' : ''
    }&fields=${fields.join(',')}&sort=-dateAdded`;
  }

  try {
    console.log('finalUrl', finalUrl);
    const { data } = await axios.get(finalUrl);
    const response = data.data;
    const finalResponse = Array.isArray(response) ? response : [response];

    let fastSearchData;
    if (entityName === BullhornEntityEnum.CLIENT_CORPORATION) {
      const encodedQuery = encodeURIComponent(rawQuery);
      const response = await searchCompanies(
        { ...requestPayload, query: encodedQuery },
        bhpToken,
        corporateRestEndpoint
      );
      const rawData = Array.isArray(response.data) ? response.data : [response.data];

      fastSearchData = (rawData as any[])
        .filter((item) => item.status !== 'Archive')
        .sort((a, b) => {
          const aMatches = a.name.toLowerCase().includes(query.toLowerCase());
          const bMatches = b.name.toLowerCase().includes(query.toLowerCase());

          if (aMatches && !bMatches) {
            return -1; // a should be placed above b
          } else if (!aMatches && bMatches) {
            return 1; // b should be placed above a
          } else {
            return 0; // no change in order
          }
        });
    }

    if (entityName === 'Skill') {
      return finalResponse.filter((it) => it.categories.total > 0).map(({ id, name }) => ({ id, name }));
    }

    if (entityName === 'ClientContact') {
      return finalResponse.map((item) => ({
        ...item,
        linkedinProfileUrl: item?.customText1,
      }));
    }

    if (!fastSearchData || !fastSearchData.length) {
      return finalResponse;
    }

    if (query.includes(' ')) {
      return finalResponse;
    }

    return [...fastSearchData, ...finalResponse];
  } catch (error) {
    console.error('Error occurred queryEntity:', error);
    throw error;
  }
};

export const searchCompanies = async (
  requestPayload: GetCorporateUserQueryDto,
  bhpToken: string,
  corporateRestEndpoint: string
) => {
  const { query, start = 1, count = 10 } = requestPayload;
  const fields = [
    'id',
    'name',
    'address',
    'fax',
    'phone',
    'status',
    'parentClientCorporation',
    'owners',
    'industryList',
    'companyDescription',
    'companyURL',
    'billingAddress',
    'billingContact',
    'billingPhone',
    'billingFrequency',
    'invoiceFormat',
    'feeArrangement',
    'notes',
    'businessSectorList',
  ];
  let finalUrl = `${corporateRestEndpoint}search/ClientCorporation?BhRestToken=${bhpToken}&query=name:"${query}"&fields=${fields.join(
    ','
  )}`;
  try {
    const { data } = await axios.get(finalUrl);
    return data;
  } catch (error) {
    console.error('Error occurred getCounties:', error.response.data);
    throw error;
  }
};

export const getCounties = async (bhpToken: string, corporateRestEndpoint: string, queryDto: GetCountiesQueryDto) => {
  const { filter, countryId } = queryDto;

  let finalUrl = `${corporateRestEndpoint}options/StateText?BhRestToken=${bhpToken}&count=200&country.id=${
    countryId || '2359'
  }&`;
  if (filter) {
    finalUrl += `filter=${filter}`;
  }

  try {
    const { data } = await axios.get(finalUrl);
    return data;
  } catch (error) {
    // console.log(error, "error")
    console.error('Error occurred getCounties:', error.response.data);
    throw error;
  }
};

export const getTotalDataEntity = async (bhpToken: string, corporateRestEndpoint: string, queryDto: any) => {
  const { customSearchField, customSearchValue, optionCustom, typeCustom, searchName } = queryDto;
  let queryPayload = { where: '' };
  let whereName = '';

  switch (searchName) {
    case BullhornEntityEnum.VACANCY:
      whereName = 'jobOrderID';
      break;
    case BullhornEntityEnum.COMPANY:
      whereName = `clientContactUserID:\"^(clientCorporationID=${queryDto.id})\"`;
      break;
    default:
      whereName = `jobOrderID:${queryDto.id}`;
      break;
  }

  switch (queryDto.entityName) {
    case BullhornEntityEnum.CLIENT_CONTACT:
      queryPayload.where += `clientCorporation.id=${queryDto.id}`;

      if (customSearchField && customSearchValue) {
        const searchQuery =
          optionCustom === 'LIKE'
            ? ` AND ${customSearchField} LIKE '%${customSearchValue}%'`
            : typeCustom == 'STRING'
            ? ` AND ${customSearchField} = '${customSearchValue}'`
            : ` AND ${customSearchField} = ${customSearchValue}`;
        queryPayload.where += searchQuery;
      }
      break;
    case BullhornEntityEnum.NOTE:
      queryPayload.where += `${whereName} AND isDeleted:false`;
      break;
    case BullhornEntityEnum.JOB_SUBMISSION:
      if (searchName === BullhornEntityEnum.VACANCY) {
        queryPayload.where += `jobOrder.id=${queryDto.id} AND jobOrder.isDeleted=false`;
      }

      if (searchName === BullhornEntityEnum.COMPANY) {
        queryPayload.where += `jobOrder.clientCorporation.id=:${queryDto.id} AND isDeleted=false`;
      }
      break;
    case BullhornEntityEnum.SENDOUT:
      // queryPayload.where += `jobOrder.id=${clientCorporationId}`;
      if (searchName === BullhornEntityEnum.VACANCY) {
        queryPayload.where += `jobOrder.id=${queryDto.id}`;
      }

      if (searchName === BullhornEntityEnum.COMPANY) {
        queryPayload.where += `clientCorporation.id=${queryDto.id}`;
      }
      break;
    case BullhornEntityEnum.APPOINTMENT:
      if (searchName === BullhornEntityEnum.VACANCY) {
        queryPayload.where += `isDeleted=false AND clientContactReference.id>=1 AND candidateReference.id>=1 AND jobOrder.isDeleted=false AND jobOrder.id=${queryDto.id} AND parentAppointment.id=0 AND type='Interview'`;
      }

      if (searchName === BullhornEntityEnum.COMPANY) {
        queryPayload.where += `isDeleted=false AND clientContactReference.id>=1 AND candidateReference.id>=1 AND jobOrder.isDeleted=false AND jobOrder.clientCorporation.id=${queryDto.id} AND parentAppointment.id=0 AND type='Interview'`;
      }
      break;
    case BullhornEntityEnum.PLACEMENT:
      // queryPayload.where += `jobOrder.id=${clientCorporationId}`;

      if (searchName === BullhornEntityEnum.VACANCY) {
        queryPayload.where += `jobOrder.id=${queryDto.id}`;
      }

      if (searchName === BullhornEntityEnum.COMPANY) {
        queryPayload.where += `jobOrder.clientCorporation.id=${queryDto.id}`;
      }
      break;
    case BullhornEntityEnum.JOB_ORDER:
      if (searchName === BullhornEntityEnum.VACANCY) {
        queryPayload.where += `jobOrder.id=${queryDto.id} AND  isDeleted=false`;
      }

      if (searchName === BullhornEntityEnum.COMPANY) {
        queryPayload.where += `clientCorporation.id=${queryDto.id}  AND isDeleted=false`;
      }
      break;
    default:
      queryPayload.where += `isDeleted=false`;
  }

  let updatedQuery: any = { ...queryPayload };

  if (queryDto.entityName === BullhornEntityEnum.NOTE) {
    const { where, ...rest } = updatedQuery;
    updatedQuery = { ...rest, query: where };
  }

  const queryPayloadURLEncoded = stringify(updatedQuery);

  let searchMethod = 'query';
  if (queryDto.entityName === BullhornEntityEnum.NOTE) {
    searchMethod = 'search';
  }
  let finalUrl = `${corporateRestEndpoint}${searchMethod}/${queryDto.entityName}?BhRestToken=${bhpToken}&${queryPayloadURLEncoded}&fields=id&showTotalMatched=true`;
  try {
    const { data } = await axios.get(finalUrl);
    return data.total;
  } catch (error) {
    console.error('Error occurred getTotalDataEntity:', error.response.data);
    throw error;
  }
};

export const getCountries = async (bhpToken: string, corporateRestEndpoint: string, queryDto: GetCountriesQueryDto) => {
  const { filter } = queryDto;

  let finalUrl = `${corporateRestEndpoint}options/Country?BhRestToken=${bhpToken}&count=500&translated=true&`;
  if (filter) {
    finalUrl += `filter=${filter}`;
  }

  try {
    const { data } = await axios.get(finalUrl);
    return data;
  } catch (error) {
    console.error('Error occurred getCountries:', error?.response?.data);
    throw error;
  }
};

export const sendNoteToBullhorn = async (bhpToken: string, corporateRestEndpoint: string, requestBody: any) => {
  try {
    return insertEntity(requestBody, 'Note', bhpToken, corporateRestEndpoint);
  } catch (error) {
    console.error('Error occurred sendNoteToBullhorn:', error);
    throw error;
  }
};

export const searchVacancies = async (bhpToken: string, corporateRestEndpoint: string, params: object) => {
  let finalUrl = `${corporateRestEndpoint}search/JobOrder?BhRestToken=${bhpToken}`;
  if (params) {
    const queryString = objectToQueryString(params);
    finalUrl = `${finalUrl}&${queryString}`;
  }

  try {
    const { data } = await axios.get(finalUrl);
    return data;
  } catch (error) {
    console.error('Error occurred searchVacancies:', error?.response?.data);
    throw error;
  }
};

export const getJobVacancy = async (bhpToken: string, corporateRestEndpoint: string, externalJobId: string) => {
  const jobId = externalJobId?.replace(/^(bullhorn-)+/g, '');
  const fields = [
    'id',
    'title',
    'clientContact',
    'clientCorporation',
    'employmentType',
    'address',
    'description',
    'startDate',
    'dateAdded',
    'owner',
    'status',
    'salary',
    'jobOrderRateCardID',
  ];
  const finalUrl = `${corporateRestEndpoint}entity/JobOrder/${jobId}?BhRestToken=${bhpToken}&fields=${fields.join(
    ','
  )}`;

  try {
    const { data } = await axios.get(finalUrl);
    return data.data;
  } catch (error) {
    console.error('Error occurred getJobVacancy:', error?.response?.data);
    throw error;
  }
};

export const deleteEntity = async ({
  entity,
  id,
  bhpToken,
  corporateRestEndpoint,
}: {
  entity: BullhornEntityEnum;
  id: string;
  bhpToken: string;
  corporateRestEndpoint: string;
}) => {
  const deleteEntityUrl = `${corporateRestEndpoint}entity/${entity}/${id}?BhRestToken=${bhpToken}`;

  try {
    const { data } = await axios.delete(deleteEntityUrl);
    return data;
  } catch (error) {
    console.error('Error occurred deleteEntity:', error?.response?.data);
    throw error;
  }
};

export const updateMassAdvance = async ({
  entity,
  body,
  bhpToken,
  corporateRestEndpoint,
}: {
  entity: BullhornEntityEnum;
  body: any;
  bhpToken: string;
  corporateRestEndpoint: string;
}) => {
  const updateStatusEntity = `${corporateRestEndpoint}services/${entity}/massAdvance?BhRestToken=${bhpToken}`;

  try {
    const { data } = await axios.post(updateStatusEntity, body);
    return data;
  } catch (error) {
    console.error('Error occurred updateMassAdvance:', error?.response?.data);
    throw error;
  }
};

export const insertMassAdvance = async ({
  entity,
  body,
  bhpToken,
  corporateRestEndpoint,
}: {
  entity: BullhornEntityEnum;
  body: any;
  bhpToken: string;
  corporateRestEndpoint: string;
}) => {
  const updateStatusEntity = `${corporateRestEndpoint}services/${entity}/create?BhRestToken=${bhpToken}`;
  try {
    const { data } = await axios.post(updateStatusEntity, body);
    return data;
  } catch (error) {
    console.error('Error occurred insertMassAdvance:', error?.response?.data);
    throw error;
  }
};

export const searchLookupData = async ({
  entity,
  body,
  bhpToken,
  corporateRestEndpoint,
}: {
  entity: BullhornEntityEnum;
  body: any;
  bhpToken: string;
  corporateRestEndpoint: string;
}) => {
  const queryData = `${corporateRestEndpoint}lookup/expanded?BhRestToken=${bhpToken}&entity=${entity}&start=${body.start}&filter=${body.filter}`;

  try {
    const { data } = await axios.get(queryData, body);
    return data;
  } catch (error) {
    console.error('Error occurred searchLookupData:', error?.response?.data);
    throw error;
  }
};

export const searchContactDetail = async ({
  bhpToken,
  corporateRestEndpoint,
  id,
}: {
  bhpToken: string;
  corporateRestEndpoint: string;
  id: string;
}) => {
  const fields = [
    'id',
    'firstName',
    'lastName',
    'isDeleted',
    'linkedPerson',
    'clientCorporation',
    'address',
    'status',
    'owner',
    'secondaryOwners',
    'email',
    'occupation',
  ];
  const queryData = `${corporateRestEndpoint}entity/ClientContact/${id}?BhRestToken=${bhpToken}&fields=${fields.join(
    ','
  )}`;

  try {
    const { data } = await axios.get(queryData);
    return data;
  } catch (error) {
    console.error('Error occurred searchContactDetail:', error?.response?.data);
    throw error;
  }
};

export const sendAppointment = async ({
  entity,
  body,
  bhpToken,
  corporateRestEndpoint,
}: {
  entity: BullhornEntityEnum;
  body: any;
  bhpToken: string;
  corporateRestEndpoint: string;
}) => {
  const updateStatusEntity = `${corporateRestEndpoint}insert/${entity}?BhRestToken=${bhpToken}&format=JSON`;
  try {
    const { data } = await axios.put(updateStatusEntity, body);
    return data;
  } catch (error) {
    console.error('Error occurred sendAppointment:', error?.response?.data);
    throw error;
  }
};

export const queryClientContacts = async (
  requestPayload: GetCorporateUserQueryDto,
  entityName: string,
  bhpToken: string,
  corporateRestEndpoint: string
) => {
  let entities = [];
  let hasMore = true;
  const maxItems = Number(requestPayload.count) || Infinity;
  const MAX_ITEMS_PER_REQUEST = 200;

  const { query: rawQuery, start, count, clientCorporationId, email, tearSheetId } = requestPayload;
  const query = removeTextWithSpecialCharacter(rawQuery) || rawQuery;
  const searchMethod = entityName === 'Candidate' ? 'search' : 'query';
  const baseUrl = `${corporateRestEndpoint}${searchMethod}/${entityName || 'ClientContact'}`;

  const fields = [
    'id',
    'name',
    'email',
    'fax',
    'phone',
    'status',
    'occupation',
    'address',
    'massMailOptOut',
    'firstName',
    'lastName',
    'customText1',
  ];
  if (entityName === 'ClientContact') {
    fields.push('clientCorporation');
  }

  const where = [`isDeleted=false AND NOT status='Archive'`];
  if (query) {
    where.push(`name LIKE '%${query}%'`);
  }

  if (clientCorporationId) {
    where.push(`clientCorporation.id=${clientCorporationId}`);
  }
  if (tearSheetId) {
    where.push(`tearsheets.id=${tearSheetId}`);
  }
  if (email) {
    where.push(`email='${email}'`);
  }

  try {
    const queryParams = {
      BhRestToken: bhpToken,
      start: start || 0,
      count: count || MAX_ITEMS_PER_REQUEST,
      fields: fields.join(','),
      sort: '-dateAdded',
      showTotalMatched: true,
      ...(searchMethod === 'search'
        ? { query: convertToLuceneQuery(where.join(' AND ')) }
        : { where: where.join(' AND ') }),
    };

    while (hasMore) {
      queryParams.count = Math.min(MAX_ITEMS_PER_REQUEST, Number(maxItems) - entities.length);
      // eslint-disable-next-line no-await-in-loop
      const { data } = await axios.get(`${baseUrl}?${stringify(queryParams)}`);
      const { total, data: contacts } = data;

      entities = entities.concat(
        contacts.map((item) => ({
          ...item,
          linkedinProfileUrl: item?.customText1,
        }))
      );
      hasMore = data.start + data.count < total && entities.length < maxItems;
      queryParams.start = data.start + data.count;

      // Exit loop if no items are fetched or the requested count is reached
      if (!contacts.length || entities.length >= maxItems) {
        break;
      }
    }

    return entities;
  } catch (error) {
    return entities;
  }
};

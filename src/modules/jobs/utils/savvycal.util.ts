import axios from 'axios';
import { SAVVYCAL_CONFIG } from 'src/configs/configs.constants';

const DEFAULT_TIMEZONES = [
  {
    id: 'Etc/GMT+12',
    offset: -43200,
    period: {
      until: null,
      from: null,
    },
    aliases: [],
    legacy: false,
    abbreviation: '-12',
    offset_utc: -43200,
    offset_std: 0,
    long_name: 'Dateline Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Dateline Standard Time',
    },
    generic_long_name: 'Dateline Standard Time',
    formatted_offset: '-12:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Etc/GMT+11',
    offset: -39600,
    period: {
      until: null,
      from: null,
    },
    aliases: [],
    legacy: false,
    abbreviation: '-11',
    offset_utc: -39600,
    offset_std: 0,
    long_name: 'UTC-11',
    metazone: null,
    windows_zone: {
      name: 'UTC-11',
    },
    generic_long_name: 'UTC-11',
    formatted_offset: '-11:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Pacific/Honolulu',
    offset: -36000,
    period: {
      until: null,
      from: '1947-06-08T02:30:00-10:00',
    },
    aliases: [
      'Pacific/Johnston',
      'US/Hawaii',
    ],
    legacy: false,
    abbreviation: 'HST',
    offset_utc: -36000,
    offset_std: 0,
    long_name: 'Hawaii-Aleutian Standard Time',
    metazone: {
      name: 'Hawaii_Aleutian',
      short: {
        current: 'HST',
        standard: 'HST',
        generic: 'HST',
        daylight: 'HDT',
      },
      long: {
        current: 'Hawaii-Aleutian Standard Time',
        standard: 'Hawaii-Aleutian Standard Time',
        generic: 'Hawaii-Aleutian Time',
        daylight: 'Hawaii-Aleutian Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Hawaiian Standard Time',
    },
    generic_long_name: 'Hawaii-Aleutian Time',
    formatted_offset: '-10:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Adak',
    offset: -36000,
    period: {
      until: '2025-03-09T01:59:59.999999-10:00',
      from: '2024-11-03T01:00:00-09:00',
    },
    aliases: [
      'America/Atka',
      'US/Aleutian',
    ],
    legacy: false,
    abbreviation: 'HST',
    offset_utc: -36000,
    offset_std: 0,
    long_name: 'Hawaii-Aleutian Standard Time',
    metazone: {
      name: 'Hawaii_Aleutian',
      short: {
        current: 'HAST',
        standard: 'HAST',
        generic: 'HAT',
        daylight: 'HADT',
      },
      long: {
        current: 'Hawaii-Aleutian Standard Time',
        standard: 'Hawaii-Aleutian Standard Time',
        generic: 'Hawaii-Aleutian Time',
        daylight: 'Hawaii-Aleutian Daylight Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Aleutian Standard Time',
    },
    generic_long_name: 'Hawaii-Aleutian Time',
    formatted_offset: '-10:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Pacific/Marquesas',
    offset: -34200,
    period: {
      until: null,
      from: '1912-09-30T23:48:00-09:30',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-0930',
    offset_utc: -34200,
    offset_std: 0,
    long_name: 'Marquesas Time',
    metazone: {
      name: 'Marquesas',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Marquesas Time',
        standard: 'Marquesas Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Marquesas Standard Time',
    },
    generic_long_name: 'Marquesas Time',
    formatted_offset: '-09:30',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Etc/GMT+9',
    offset: -32400,
    period: {
      until: null,
      from: null,
    },
    aliases: [],
    legacy: false,
    abbreviation: '-09',
    offset_utc: -32400,
    offset_std: 0,
    long_name: 'UTC-09',
    metazone: null,
    windows_zone: {
      name: 'UTC-09',
    },
    generic_long_name: 'UTC-09',
    formatted_offset: '-09:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Anchorage',
    offset: -32400,
    period: {
      until: '2025-03-09T01:59:59.999999-09:00',
      from: '2024-11-03T01:00:00-08:00',
    },
    aliases: [
      'US/Alaska',
    ],
    legacy: false,
    abbreviation: 'AKST',
    offset_utc: -32400,
    offset_std: 0,
    long_name: 'Alaska Standard Time',
    metazone: {
      name: 'Alaska',
      short: {
        current: 'AKST',
        standard: 'AKST',
        generic: 'AKT',
        daylight: 'AKDT',
      },
      long: {
        current: 'Alaska Standard Time',
        standard: 'Alaska Standard Time',
        generic: 'Alaska Time',
        daylight: 'Alaska Daylight Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Alaskan Standard Time',
    },
    generic_long_name: 'Alaska Time',
    formatted_offset: '-09:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Etc/GMT+8',
    offset: -28800,
    period: {
      until: null,
      from: null,
    },
    aliases: [],
    legacy: false,
    abbreviation: '-08',
    offset_utc: -28800,
    offset_std: 0,
    long_name: 'UTC-08',
    metazone: null,
    windows_zone: {
      name: 'UTC-08',
    },
    generic_long_name: 'UTC-08',
    formatted_offset: '-08:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Tijuana',
    offset: -28800,
    period: {
      until: '2025-03-09T01:59:59.999999-08:00',
      from: '2024-11-03T01:00:00-07:00',
    },
    aliases: [
      'America/Santa_Isabel',
      'Mexico/BajaNorte',
      'America/Ensenada',
    ],
    legacy: false,
    abbreviation: 'PST',
    offset_utc: -28800,
    offset_std: 0,
    long_name: 'Pacific Standard Time',
    metazone: {
      name: 'America_Pacific',
      short: {
        current: 'PST',
        standard: 'PST',
        generic: 'PT',
        daylight: 'PDT',
      },
      long: {
        current: 'Pacific Standard Time',
        standard: 'Pacific Standard Time',
        generic: 'Pacific Time',
        daylight: 'Pacific Daylight Time',
      },
      territories: [
        'MX',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Pacific Standard Time (Mexico)',
    },
    generic_long_name: 'Pacific Time',
    formatted_offset: '-08:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Los_Angeles',
    offset: -28800,
    period: {
      until: '2025-03-09T01:59:59.999999-08:00',
      from: '2024-11-03T01:00:00-07:00',
    },
    aliases: [
      'US/Pacific',
    ],
    legacy: false,
    abbreviation: 'PST',
    offset_utc: -28800,
    offset_std: 0,
    long_name: 'Pacific Standard Time',
    metazone: {
      name: 'America_Pacific',
      short: {
        current: 'PST',
        standard: 'PST',
        generic: 'PT',
        daylight: 'PDT',
      },
      long: {
        current: 'Pacific Standard Time',
        standard: 'Pacific Standard Time',
        generic: 'Pacific Time',
        daylight: 'Pacific Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Pacific Standard Time',
    },
    generic_long_name: 'Pacific Time',
    formatted_offset: '-08:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Whitehorse',
    offset: -25200,
    period: {
      until: null,
      from: '2020-11-01T00:00:00-07:00',
    },
    aliases: [
      'Canada/Yukon',
    ],
    legacy: false,
    abbreviation: 'MST',
    offset_utc: -25200,
    offset_std: 0,
    long_name: 'Yukon Time',
    metazone: {
      name: 'Yukon',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Yukon Time',
        standard: 'Yukon Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Yukon Standard Time',
    },
    generic_long_name: 'Yukon Time',
    formatted_offset: '-07:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Phoenix',
    offset: -25200,
    period: {
      until: null,
      from: '1968-03-21T00:00:00-07:00',
    },
    aliases: [
      'US/Arizona',
      'America/Creston',
    ],
    legacy: false,
    abbreviation: 'MST',
    offset_utc: -25200,
    offset_std: 0,
    long_name: 'Mountain Standard Time',
    metazone: {
      name: 'America_Mountain',
      short: {
        current: 'MST',
        standard: 'MST',
        generic: 'MT',
        daylight: 'MDT',
      },
      long: {
        current: 'Mountain Standard Time',
        standard: 'Mountain Standard Time',
        generic: 'Mountain Time',
        daylight: 'Mountain Daylight Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'US Mountain Standard Time',
    },
    generic_long_name: 'Mountain Time',
    formatted_offset: '-07:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Mazatlan',
    offset: -25200,
    period: {
      until: null,
      from: '2022-10-30T01:00:00-06:00',
    },
    aliases: [
      'Mexico/BajaSur',
    ],
    legacy: false,
    abbreviation: 'MST',
    offset_utc: -25200,
    offset_std: 0,
    long_name: 'Mexican Pacific Standard Time',
    metazone: {
      name: 'Mexico_Pacific',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Mexican Pacific Standard Time',
        standard: 'Mexican Pacific Standard Time',
        generic: 'Mexican Pacific Time',
        daylight: 'Mexican Pacific Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Mountain Standard Time (Mexico)',
    },
    generic_long_name: 'Mexican Pacific Time',
    formatted_offset: '-07:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Denver',
    offset: -25200,
    period: {
      until: '2025-03-09T01:59:59.999999-07:00',
      from: '2024-11-03T01:00:00-06:00',
    },
    aliases: [
      'US/Mountain',
      'America/Shiprock',
    ],
    legacy: false,
    abbreviation: 'MST',
    offset_utc: -25200,
    offset_std: 0,
    long_name: 'Mountain Standard Time',
    metazone: {
      name: 'America_Mountain',
      short: {
        current: 'MST',
        standard: 'MST',
        generic: 'MT',
        daylight: 'MDT',
      },
      long: {
        current: 'Mountain Standard Time',
        standard: 'Mountain Standard Time',
        generic: 'Mountain Time',
        daylight: 'Mountain Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Mountain Standard Time',
    },
    generic_long_name: 'Mountain Time',
    formatted_offset: '-07:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Regina',
    offset: -21600,
    period: {
      until: null,
      from: '1960-04-24T03:00:00-06:00',
    },
    aliases: [
      'Canada/Saskatchewan',
    ],
    legacy: false,
    abbreviation: 'CST',
    offset_utc: -21600,
    offset_std: 0,
    long_name: 'Central Standard Time',
    metazone: {
      name: 'America_Central',
      short: {
        current: 'CST',
        standard: 'CST',
        generic: 'CT',
        daylight: 'CDT',
      },
      long: {
        current: 'Central Standard Time',
        standard: 'Central Standard Time',
        generic: 'Central Time',
        daylight: 'Central Daylight Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Canada Central Standard Time',
    },
    generic_long_name: 'Central Time',
    formatted_offset: '-06:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Mexico_City',
    offset: -21600,
    period: {
      until: null,
      from: '2022-10-30T01:00:00-05:00',
    },
    aliases: [
      'Mexico/General',
    ],
    legacy: false,
    abbreviation: 'CST',
    offset_utc: -21600,
    offset_std: 0,
    long_name: 'Central Standard Time',
    metazone: {
      name: 'America_Central',
      short: {
        current: 'CST',
        standard: 'CST',
        generic: 'CT',
        daylight: 'CDT',
      },
      long: {
        current: 'Central Standard Time',
        standard: 'Central Standard Time',
        generic: 'Central Time',
        daylight: 'Central Daylight Time',
      },
      territories: [
        'MX',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Central Standard Time (Mexico)',
    },
    generic_long_name: 'Central Time',
    formatted_offset: '-06:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Guatemala',
    offset: -21600,
    period: {
      until: null,
      from: '2006-09-30T23:00:00-05:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'CST',
    offset_utc: -21600,
    offset_std: 0,
    long_name: 'Central Standard Time',
    metazone: {
      name: 'America_Central',
      short: {
        current: 'CST',
        standard: 'CST',
        generic: 'CT',
        daylight: 'CDT',
      },
      long: {
        current: 'Central Standard Time',
        standard: 'Central Standard Time',
        generic: 'Central Time',
        daylight: 'Central Daylight Time',
      },
      territories: [
        'GT',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Central America Standard Time',
    },
    generic_long_name: 'Central Time',
    formatted_offset: '-06:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Chicago',
    offset: -21600,
    period: {
      until: '2025-03-09T01:59:59.999999-06:00',
      from: '2024-11-03T01:00:00-05:00',
    },
    aliases: [
      'US/Central',
    ],
    legacy: false,
    abbreviation: 'CST',
    offset_utc: -21600,
    offset_std: 0,
    long_name: 'Central Standard Time',
    metazone: {
      name: 'America_Central',
      short: {
        current: 'CST',
        standard: 'CST',
        generic: 'CT',
        daylight: 'CDT',
      },
      long: {
        current: 'Central Standard Time',
        standard: 'Central Standard Time',
        generic: 'Central Time',
        daylight: 'Central Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Central Standard Time',
    },
    generic_long_name: 'Central Time',
    formatted_offset: '-06:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Pacific/Easter',
    offset: -18000,
    period: {
      until: '2025-04-05T22:00:00-06:00',
      from: '2024-09-07T23:00:00-05:00',
    },
    aliases: [
      'Chile/EasterIsland',
    ],
    legacy: false,
    abbreviation: '-05',
    offset_utc: -21600,
    offset_std: 3600,
    long_name: 'Easter Island Summer Time',
    metazone: {
      name: 'Easter',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Easter Island Summer Time',
        standard: 'Easter Island Standard Time',
        generic: 'Easter Island Time',
        daylight: 'Easter Island Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Easter Island Standard Time',
    },
    generic_long_name: 'Easter Island Time',
    formatted_offset: '-05:00',
    golden: true,
    dst: true,
    canonical: true,
  },
  {
    id: 'America/Port-au-Prince',
    offset: -18000,
    period: {
      until: '2025-03-09T01:59:59.999999-05:00',
      from: '2024-11-03T01:00:00-04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'EST',
    offset_utc: -18000,
    offset_std: 0,
    long_name: 'Eastern Standard Time',
    metazone: {
      name: 'America_Eastern',
      short: {
        current: 'EST',
        standard: 'EST',
        generic: 'ET',
        daylight: 'EDT',
      },
      long: {
        current: 'Eastern Standard Time',
        standard: 'Eastern Standard Time',
        generic: 'Eastern Time',
        daylight: 'Eastern Daylight Time',
      },
      territories: [
        'HT',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Haiti Standard Time',
    },
    generic_long_name: 'Eastern Time',
    formatted_offset: '-05:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/New_York',
    offset: -18000,
    period: {
      until: '2025-03-09T01:59:59.999999-05:00',
      from: '2024-11-03T01:00:00-04:00',
    },
    aliases: [
      'US/Eastern',
    ],
    legacy: false,
    abbreviation: 'EST',
    offset_utc: -18000,
    offset_std: 0,
    long_name: 'Eastern Standard Time',
    metazone: {
      name: 'America_Eastern',
      short: {
        current: 'EST',
        standard: 'EST',
        generic: 'ET',
        daylight: 'EDT',
      },
      long: {
        current: 'Eastern Standard Time',
        standard: 'Eastern Standard Time',
        generic: 'Eastern Time',
        daylight: 'Eastern Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Eastern Standard Time',
    },
    generic_long_name: 'Eastern Time',
    formatted_offset: '-05:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Havana',
    offset: -18000,
    period: {
      until: '2025-03-08T23:59:59.999999-05:00',
      from: '2024-11-03T00:00:00-04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'CST',
    offset_utc: -18000,
    offset_std: 0,
    long_name: 'Cuba Standard Time',
    metazone: {
      name: 'Cuba',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Cuba Standard Time',
        standard: 'Cuba Standard Time',
        generic: 'Cuba Time',
        daylight: 'Cuba Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Cuba Standard Time',
    },
    generic_long_name: 'Cuba Time',
    formatted_offset: '-05:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Grand_Turk',
    offset: -18000,
    period: {
      until: '2025-03-09T01:59:59.999999-05:00',
      from: '2024-11-03T01:00:00-04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'EST',
    offset_utc: -18000,
    offset_std: 0,
    long_name: 'Eastern Standard Time',
    metazone: {
      name: 'America_Eastern',
      short: {
        current: 'EST',
        standard: 'EST',
        generic: 'ET',
        daylight: 'EDT',
      },
      long: {
        current: 'Eastern Standard Time',
        standard: 'Eastern Standard Time',
        generic: 'Eastern Time',
        daylight: 'Eastern Daylight Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Turks And Caicos Standard Time',
    },
    generic_long_name: 'Eastern Time',
    formatted_offset: '-05:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Cancun',
    offset: -18000,
    period: {
      until: null,
      from: '2015-02-01T03:00:00-05:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'EST',
    offset_utc: -18000,
    offset_std: 0,
    long_name: 'Eastern Standard Time',
    metazone: {
      name: 'America_Eastern',
      short: {
        current: 'EST',
        standard: 'EST',
        generic: 'ET',
        daylight: 'EDT',
      },
      long: {
        current: 'Eastern Standard Time',
        standard: 'Eastern Standard Time',
        generic: 'Eastern Time',
        daylight: 'Eastern Daylight Time',
      },
      territories: [],
      exemplar_city: 'Cancún',
    },
    windows_zone: {
      name: 'Eastern Standard Time (Mexico)',
    },
    generic_long_name: 'Eastern Time',
    formatted_offset: '-05:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Bogota',
    offset: -18000,
    period: {
      until: null,
      from: '1993-02-06T23:00:00-04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-05',
    offset_utc: -18000,
    offset_std: 0,
    long_name: 'Colombia Standard Time',
    metazone: {
      name: 'Colombia',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Colombia Standard Time',
        standard: 'Colombia Standard Time',
        generic: 'Colombia Time',
        daylight: 'Colombia Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'SA Pacific Standard Time',
    },
    generic_long_name: 'Colombia Time',
    formatted_offset: '-05:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/La_Paz',
    offset: -14400,
    period: {
      until: null,
      from: '1932-03-20T23:32:36-03:32',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-04',
    offset_utc: -14400,
    offset_std: 0,
    long_name: 'Bolivia Time',
    metazone: {
      name: 'Bolivia',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Bolivia Time',
        standard: 'Bolivia Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'SA Western Standard Time',
    },
    generic_long_name: 'Bolivia Time',
    formatted_offset: '-04:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Halifax',
    offset: -14400,
    period: {
      until: '2025-03-09T01:59:59.999999-04:00',
      from: '2024-11-03T01:00:00-03:00',
    },
    aliases: [
      'Canada/Atlantic',
    ],
    legacy: false,
    abbreviation: 'AST',
    offset_utc: -14400,
    offset_std: 0,
    long_name: 'Atlantic Standard Time',
    metazone: {
      name: 'Atlantic',
      short: {
        current: 'AST',
        standard: 'AST',
        generic: 'AT',
        daylight: 'ADT',
      },
      long: {
        current: 'Atlantic Standard Time',
        standard: 'Atlantic Standard Time',
        generic: 'Atlantic Time',
        daylight: 'Atlantic Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Atlantic Standard Time',
    },
    generic_long_name: 'Atlantic Time',
    formatted_offset: '-04:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Cuiaba',
    offset: -14400,
    period: {
      until: null,
      from: '2019-02-16T23:00:00-03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-04',
    offset_utc: -14400,
    offset_std: 0,
    long_name: 'Amazon Standard Time',
    metazone: {
      name: 'Amazon',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Amazon Standard Time',
        standard: 'Amazon Standard Time',
        generic: 'Amazon Time',
        daylight: 'Amazon Summer Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Central Brazilian Standard Time',
    },
    generic_long_name: 'Amazon Time',
    formatted_offset: '-04:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Caracas',
    offset: -14400,
    period: {
      until: null,
      from: '2016-05-01T03:00:00-04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-04',
    offset_utc: -14400,
    offset_std: 0,
    long_name: 'Venezuela Time',
    metazone: {
      name: 'Venezuela',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Venezuela Time',
        standard: 'Venezuela Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Venezuela Standard Time',
    },
    generic_long_name: 'Venezuela Time',
    formatted_offset: '-04:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/St_Johns',
    offset: -12600,
    period: {
      until: '2025-03-09T01:59:59.999999-03:30',
      from: '2024-11-03T01:00:00-02:30',
    },
    aliases: [
      'Canada/Newfoundland',
    ],
    legacy: false,
    abbreviation: 'NST',
    offset_utc: -12600,
    offset_std: 0,
    long_name: 'Newfoundland Standard Time',
    metazone: {
      name: 'Newfoundland',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Newfoundland Standard Time',
        standard: 'Newfoundland Standard Time',
        generic: 'Newfoundland Time',
        daylight: 'Newfoundland Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: 'St. John’s',
    },
    windows_zone: {
      name: 'Newfoundland Standard Time',
    },
    generic_long_name: 'Newfoundland Time',
    formatted_offset: '-03:30',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Sao_Paulo',
    offset: -10800,
    period: {
      until: null,
      from: '2019-02-16T23:00:00-02:00',
    },
    aliases: [
      'Brazil/East',
    ],
    legacy: false,
    abbreviation: '-03',
    offset_utc: -10800,
    offset_std: 0,
    long_name: 'Brasilia Standard Time',
    metazone: {
      name: 'Brasilia',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Brasilia Standard Time',
        standard: 'Brasilia Standard Time',
        generic: 'Brasilia Time',
        daylight: 'Brasilia Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'E. South America Standard Time',
    },
    generic_long_name: 'Brasilia Time',
    formatted_offset: '-03:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Santiago',
    offset: -10800,
    period: {
      until: '2025-04-06T00:00:00-04:00',
      from: '2024-09-08T01:00:00-03:00',
    },
    aliases: [
      'Chile/Continental',
    ],
    legacy: false,
    abbreviation: '-03',
    offset_utc: -14400,
    offset_std: 3600,
    long_name: 'Chile Summer Time',
    metazone: {
      name: 'Chile',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Chile Summer Time',
        standard: 'Chile Standard Time',
        generic: 'Chile Time',
        daylight: 'Chile Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Pacific SA Standard Time',
    },
    generic_long_name: 'Chile Time',
    formatted_offset: '-03:00',
    golden: true,
    dst: true,
    canonical: true,
  },
  {
    id: 'America/Punta_Arenas',
    offset: -10800,
    period: {
      until: null,
      from: '2016-08-14T01:00:00-03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-03',
    offset_utc: -10800,
    offset_std: 0,
    long_name: 'Magallanes Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Magallanes Standard Time',
    },
    generic_long_name: 'Magallanes Standard Time',
    formatted_offset: '-03:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Montevideo',
    offset: -10800,
    period: {
      until: null,
      from: '2015-03-08T01:00:00-02:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-03',
    offset_utc: -10800,
    offset_std: 0,
    long_name: 'Uruguay Standard Time',
    metazone: {
      name: 'Uruguay',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Uruguay Standard Time',
        standard: 'Uruguay Standard Time',
        generic: 'Uruguay Time',
        daylight: 'Uruguay Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Montevideo Standard Time',
    },
    generic_long_name: 'Uruguay Time',
    formatted_offset: '-03:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Miquelon',
    offset: -10800,
    period: {
      until: '2025-03-09T01:59:59.999999-03:00',
      from: '2024-11-03T01:00:00-02:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-03',
    offset_utc: -10800,
    offset_std: 0,
    long_name: 'St. Pierre & Miquelon Standard Time',
    metazone: {
      name: 'Pierre_Miquelon',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'St. Pierre & Miquelon Standard Time',
        standard: 'St. Pierre & Miquelon Standard Time',
        generic: 'St. Pierre & Miquelon Time',
        daylight: 'St. Pierre & Miquelon Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Saint Pierre Standard Time',
    },
    generic_long_name: 'St. Pierre & Miquelon Time',
    formatted_offset: '-03:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Cayenne',
    offset: -10800,
    period: {
      until: null,
      from: '1967-10-01T01:00:00-03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-03',
    offset_utc: -10800,
    offset_std: 0,
    long_name: 'French Guiana Time',
    metazone: {
      name: 'French_Guiana',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'French Guiana Time',
        standard: 'French Guiana Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'SA Eastern Standard Time',
    },
    generic_long_name: 'French Guiana Time',
    formatted_offset: '-03:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Bahia',
    offset: -10800,
    period: {
      until: null,
      from: '2012-10-21T00:00:00-03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-03',
    offset_utc: -10800,
    offset_std: 0,
    long_name: 'Brasilia Standard Time',
    metazone: {
      name: 'Brasilia',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Brasilia Standard Time',
        standard: 'Brasilia Standard Time',
        generic: 'Brasilia Time',
        daylight: 'Brasilia Summer Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Bahia Standard Time',
    },
    generic_long_name: 'Brasilia Time',
    formatted_offset: '-03:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'America/Asuncion',
    offset: -10800,
    period: {
      until: '2025-03-23T00:00:00-04:00',
      from: '2024-10-06T01:00:00-03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-03',
    offset_utc: -14400,
    offset_std: 3600,
    long_name: 'Paraguay Summer Time',
    metazone: {
      name: 'Paraguay',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Paraguay Summer Time',
        standard: 'Paraguay Standard Time',
        generic: 'Paraguay Time',
        daylight: 'Paraguay Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: 'Asunción',
    },
    windows_zone: {
      name: 'Paraguay Standard Time',
    },
    generic_long_name: 'Paraguay Time',
    formatted_offset: '-03:00',
    golden: true,
    dst: true,
    canonical: true,
  },
  {
    id: 'America/Araguaina',
    offset: -10800,
    period: {
      until: null,
      from: '2013-09-01T00:00:00-03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-03',
    offset_utc: -10800,
    offset_std: 0,
    long_name: 'Brasilia Standard Time',
    metazone: {
      name: 'Brasilia',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Brasilia Standard Time',
        standard: 'Brasilia Standard Time',
        generic: 'Brasilia Time',
        daylight: 'Brasilia Summer Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Tocantins Standard Time',
    },
    generic_long_name: 'Brasilia Time',
    formatted_offset: '-03:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Etc/GMT+2',
    offset: -7200,
    period: {
      until: null,
      from: null,
    },
    aliases: [],
    legacy: false,
    abbreviation: '-02',
    offset_utc: -7200,
    offset_std: 0,
    long_name: 'UTC-02',
    metazone: null,
    windows_zone: {
      name: 'UTC-02',
    },
    generic_long_name: 'UTC-02',
    formatted_offset: '-02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Atlantic/Cape_Verde',
    offset: -3600,
    period: {
      until: null,
      from: '1975-11-25T03:00:00-01:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-01',
    offset_utc: -3600,
    offset_std: 0,
    long_name: 'Cape Verde Standard Time',
    metazone: {
      name: 'Cape_Verde',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Cape Verde Standard Time',
        standard: 'Cape Verde Standard Time',
        generic: 'Cape Verde Time',
        daylight: 'Cape Verde Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Cape Verde Standard Time',
    },
    generic_long_name: 'Cape Verde Time',
    formatted_offset: '-01:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Atlantic/Azores',
    offset: -3600,
    period: {
      until: '2025-03-29T23:59:59.999999-01:00',
      from: '2024-10-27T00:00:00+00:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '-01',
    offset_utc: -3600,
    offset_std: 0,
    long_name: 'Azores Standard Time',
    metazone: {
      name: 'Azores',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Azores Standard Time',
        standard: 'Azores Standard Time',
        generic: 'Azores Time',
        daylight: 'Azores Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Azores Standard Time',
    },
    generic_long_name: 'Azores Time',
    formatted_offset: '-01:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/London',
    offset: 0,
    period: {
      until: '2025-03-30T00:59:59.999999+00:00',
      from: '2024-10-27T01:00:00+01:00',
    },
    aliases: [
      'Europe/Jersey',
      'Europe/Belfast',
      'Europe/Isle_of_Man',
      'Europe/Guernsey',
    ],
    legacy: false,
    abbreviation: 'GMT',
    offset_utc: 0,
    offset_std: 0,
    long_name: 'Greenwich Mean Time',
    metazone: {
      name: 'GMT',
      short: {
        current: 'GMT',
        standard: 'GMT',
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Greenwich Mean Time',
        standard: 'Greenwich Mean Time',
        generic: null,
        daylight: 'British Summer Time',
      },
      territories: [
        'GB',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'GMT Standard Time',
    },
    generic_long_name: 'Greenwich Mean Time',
    formatted_offset: '+00:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Etc/UTC',
    offset: 0,
    period: {
      until: null,
      from: null,
    },
    aliases: [
      'Etc/Zulu',
      'Etc/UCT',
      'Etc/Universal',
    ],
    legacy: false,
    abbreviation: 'UTC',
    offset_utc: 0,
    offset_std: 0,
    long_name: 'Coordinated Universal Time',
    metazone: {
      name: 'GMT',
      short: {
        current: 'UTC',
        standard: 'UTC',
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Coordinated Universal Time',
        standard: 'Coordinated Universal Time',
        generic: null,
        daylight: null,
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'UTC',
    },
    generic_long_name: 'Coordinated Universal Time',
    formatted_offset: '+00:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Sao_Tome',
    offset: 0,
    period: {
      until: null,
      from: '2019-01-01T01:00:00+01:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'GMT',
    offset_utc: 0,
    offset_std: 0,
    long_name: 'Greenwich Mean Time',
    metazone: {
      name: 'GMT',
      short: {
        current: 'GMT',
        standard: 'GMT',
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Greenwich Mean Time',
        standard: 'Greenwich Mean Time',
        generic: null,
        daylight: null,
      },
      territories: [],
      exemplar_city: 'São Tomé',
    },
    windows_zone: {
      name: 'Sao Tome Standard Time',
    },
    generic_long_name: 'Greenwich Mean Time',
    formatted_offset: '+00:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Warsaw',
    offset: 3600,
    period: {
      until: '2025-03-30T01:59:59.999999+01:00',
      from: '2024-10-27T02:00:00+02:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'CET',
    offset_utc: 3600,
    offset_std: 0,
    long_name: 'Central European Standard Time',
    metazone: {
      name: 'Europe_Central',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Central European Standard Time',
        standard: 'Central European Standard Time',
        generic: 'Central European Time',
        daylight: 'Central European Summer Time',
      },
      territories: [
        'PL',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Central European Standard Time',
    },
    generic_long_name: 'Central European Time',
    formatted_offset: '+01:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Paris',
    offset: 3600,
    period: {
      until: '2025-03-30T01:59:59.999999+01:00',
      from: '2024-10-27T02:00:00+02:00',
    },
    aliases: [
      'Europe/Monaco',
    ],
    legacy: false,
    abbreviation: 'CET',
    offset_utc: 3600,
    offset_std: 0,
    long_name: 'Central European Standard Time',
    metazone: {
      name: 'Europe_Central',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Central European Standard Time',
        standard: 'Central European Standard Time',
        generic: 'Central European Time',
        daylight: 'Central European Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Romance Standard Time',
    },
    generic_long_name: 'Central European Time',
    formatted_offset: '+01:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Budapest',
    offset: 3600,
    period: {
      until: '2025-03-30T01:59:59.999999+01:00',
      from: '2024-10-27T02:00:00+02:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'CET',
    offset_utc: 3600,
    offset_std: 0,
    long_name: 'Central European Standard Time',
    metazone: {
      name: 'Europe_Central',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Central European Standard Time',
        standard: 'Central European Standard Time',
        generic: 'Central European Time',
        daylight: 'Central European Summer Time',
      },
      territories: [
        'HU',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Central Europe Standard Time',
    },
    generic_long_name: 'Central European Time',
    formatted_offset: '+01:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Berlin',
    offset: 3600,
    period: {
      until: '2025-03-30T01:59:59.999999+01:00',
      from: '2024-10-27T02:00:00+02:00',
    },
    aliases: [
      'Atlantic/Jan_Mayen',
      'Europe/Stockholm',
      'Arctic/Longyearbyen',
      'Europe/Oslo',
      'Europe/Copenhagen',
    ],
    legacy: false,
    abbreviation: 'CET',
    offset_utc: 3600,
    offset_std: 0,
    long_name: 'Central European Standard Time',
    metazone: {
      name: 'Europe_Central',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Central European Standard Time',
        standard: 'Central European Standard Time',
        generic: 'Central European Time',
        daylight: 'Central European Summer Time',
      },
      territories: [
        'DE',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'W. Europe Standard Time',
    },
    generic_long_name: 'Central European Time',
    formatted_offset: '+01:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Lagos',
    offset: 3600,
    period: {
      until: null,
      from: '1919-09-01T00:30:00+01:00',
    },
    aliases: [
      'Africa/Douala',
      'Africa/Niamey',
      'Africa/Porto-Novo',
      'Africa/Luanda',
      'Africa/Brazzaville',
      'Africa/Libreville',
      'Africa/Malabo',
      'Africa/Kinshasa',
      'Africa/Bangui',
    ],
    legacy: false,
    abbreviation: 'WAT',
    offset_utc: 3600,
    offset_std: 0,
    long_name: 'West Africa Standard Time',
    metazone: {
      name: 'Africa_Western',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'West Africa Standard Time',
        standard: 'West Africa Standard Time',
        generic: 'West Africa Time',
        daylight: 'West Africa Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'W. Central Africa Standard Time',
    },
    generic_long_name: 'West Africa Time',
    formatted_offset: '+01:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Casablanca',
    offset: 3600,
    period: {
      until: '2025-02-23T03:00:00+00:00',
      from: '2024-04-14T03:00:00+01:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+01',
    offset_utc: 3600,
    offset_std: 0,
    long_name: 'Morocco Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Morocco Standard Time',
    },
    generic_long_name: 'Morocco Standard Time',
    formatted_offset: '+01:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Kaliningrad',
    offset: 7200,
    period: {
      until: null,
      from: '2014-10-26T01:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'EET',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Eastern European Standard Time',
    metazone: {
      name: 'Europe_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Eastern European Standard Time',
        standard: 'Eastern European Standard Time',
        generic: 'Eastern European Time',
        daylight: 'Eastern European Summer Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Kaliningrad Standard Time',
    },
    generic_long_name: 'Eastern European Time',
    formatted_offset: '+02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Chisinau',
    offset: 7200,
    period: {
      until: '2025-03-30T01:59:59.999999+02:00',
      from: '2024-10-27T02:00:00+03:00',
    },
    aliases: [
      'Europe/Tiraspol',
    ],
    legacy: false,
    abbreviation: 'EET',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Eastern European Standard Time',
    metazone: {
      name: 'Europe_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Eastern European Standard Time',
        standard: 'Eastern European Standard Time',
        generic: 'Eastern European Time',
        daylight: 'Eastern European Summer Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'E. Europe Standard Time',
    },
    generic_long_name: 'Eastern European Time',
    formatted_offset: '+02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Bucharest',
    offset: 7200,
    period: {
      until: '2025-03-30T02:59:59.999999+02:00',
      from: '2024-10-27T03:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'EET',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Eastern European Standard Time',
    metazone: {
      name: 'Europe_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Eastern European Standard Time',
        standard: 'Eastern European Standard Time',
        generic: 'Eastern European Time',
        daylight: 'Eastern European Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'GTB Standard Time',
    },
    generic_long_name: 'Eastern European Time',
    formatted_offset: '+02:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Jerusalem',
    offset: 7200,
    period: {
      until: '2025-03-28T01:59:59.999999+02:00',
      from: '2024-10-27T01:00:00+03:00',
    },
    aliases: [
      'Asia/Tel_Aviv',
    ],
    legacy: false,
    abbreviation: 'IST',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Israel Standard Time',
    metazone: {
      name: 'Israel',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Israel Standard Time',
        standard: 'Israel Standard Time',
        generic: 'Israel Time',
        daylight: 'Israel Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Israel Standard Time',
    },
    generic_long_name: 'Israel Time',
    formatted_offset: '+02:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Hebron',
    offset: 7200,
    period: {
      until: '2025-04-12T01:59:59.999999+02:00',
      from: '2024-10-26T01:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'EET',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Eastern European Standard Time',
    metazone: {
      name: 'Europe_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Eastern European Standard Time',
        standard: 'Eastern European Standard Time',
        generic: 'Eastern European Time',
        daylight: 'Eastern European Summer Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'West Bank Standard Time',
    },
    generic_long_name: 'Eastern European Time',
    formatted_offset: '+02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Beirut',
    offset: 7200,
    period: {
      until: '2025-03-29T23:59:59.999999+02:00',
      from: '2024-10-26T23:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'EET',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Eastern European Standard Time',
    metazone: {
      name: 'Europe_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Eastern European Standard Time',
        standard: 'Eastern European Standard Time',
        generic: 'Eastern European Time',
        daylight: 'Eastern European Summer Time',
      },
      territories: [
        'LB',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Middle East Standard Time',
    },
    generic_long_name: 'Eastern European Time',
    formatted_offset: '+02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Windhoek',
    offset: 7200,
    period: {
      until: null,
      from: '2017-09-03T03:00:00+02:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'CAT',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Central Africa Time',
    metazone: {
      name: 'Africa_Central',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Central Africa Time',
        standard: 'Central Africa Time',
        generic: null,
        daylight: null,
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Namibia Standard Time',
    },
    generic_long_name: 'Central Africa Time',
    formatted_offset: '+02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Tripoli',
    offset: 7200,
    period: {
      until: null,
      from: '2013-10-25T02:00:00+02:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'EET',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Eastern European Standard Time',
    metazone: {
      name: 'Europe_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Eastern European Standard Time',
        standard: 'Eastern European Standard Time',
        generic: 'Eastern European Time',
        daylight: 'Eastern European Summer Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Libya Standard Time',
    },
    generic_long_name: 'Eastern European Time',
    formatted_offset: '+02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Khartoum',
    offset: 7200,
    period: {
      until: null,
      from: '2017-10-31T23:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'CAT',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Central Africa Time',
    metazone: {
      name: 'Africa_Central',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Central Africa Time',
        standard: 'Central Africa Time',
        generic: null,
        daylight: null,
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Sudan Standard Time',
    },
    generic_long_name: 'Central Africa Time',
    formatted_offset: '+02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Juba',
    offset: 7200,
    period: {
      until: null,
      from: '2021-01-31T23:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'CAT',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Central Africa Time',
    metazone: {
      name: 'Africa_Central',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Central Africa Time',
        standard: 'Central Africa Time',
        generic: null,
        daylight: null,
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'South Sudan Standard Time',
    },
    generic_long_name: 'Central Africa Time',
    formatted_offset: '+02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Johannesburg',
    offset: 7200,
    period: {
      until: null,
      from: '1944-03-19T01:00:00+03:00',
    },
    aliases: [
      'Africa/Maseru',
      'Africa/Mbabane',
    ],
    legacy: false,
    abbreviation: 'SAST',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'South Africa Standard Time',
    metazone: {
      name: 'Africa_Southern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'South Africa Standard Time',
        standard: 'South Africa Standard Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'South Africa Standard Time',
    },
    generic_long_name: 'South Africa Standard Time',
    formatted_offset: '+02:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Cairo',
    offset: 7200,
    period: {
      until: '2025-04-24T23:59:59.999999+02:00',
      from: '2024-10-31T23:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'EET',
    offset_utc: 7200,
    offset_std: 0,
    long_name: 'Eastern European Standard Time',
    metazone: {
      name: 'Europe_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Eastern European Standard Time',
        standard: 'Eastern European Standard Time',
        generic: 'Eastern European Time',
        daylight: 'Eastern European Summer Time',
      },
      territories: [
        'EG',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Egypt Standard Time',
    },
    generic_long_name: 'Eastern European Time',
    formatted_offset: '+02:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Volgograd',
    offset: 10800,
    period: {
      until: null,
      from: '2020-12-27T01:00:00+04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'MSK',
    offset_utc: 10800,
    offset_std: 0,
    long_name: 'Volgograd Standard Time',
    metazone: {
      name: 'Volgograd',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Volgograd Standard Time',
        standard: 'Volgograd Standard Time',
        generic: 'Volgograd Time',
        daylight: 'Volgograd Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Volgograd Standard Time',
    },
    generic_long_name: 'Volgograd Time',
    formatted_offset: '+03:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Moscow',
    offset: 10800,
    period: {
      until: null,
      from: '2014-10-26T01:00:00+04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'MSK',
    offset_utc: 10800,
    offset_std: 0,
    long_name: 'Moscow Standard Time',
    metazone: {
      name: 'Moscow',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Moscow Standard Time',
        standard: 'Moscow Standard Time',
        generic: 'Moscow Time',
        daylight: 'Moscow Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Russian Standard Time',
    },
    generic_long_name: 'Moscow Time',
    formatted_offset: '+03:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Minsk',
    offset: 10800,
    period: {
      until: null,
      from: '2011-03-27T03:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+03',
    offset_utc: 10800,
    offset_std: 0,
    long_name: 'Moscow Standard Time',
    metazone: {
      name: 'Moscow',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Moscow Standard Time',
        standard: 'Moscow Standard Time',
        generic: 'Moscow Time',
        daylight: 'Moscow Summer Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Belarus Standard Time',
    },
    generic_long_name: 'Moscow Time',
    formatted_offset: '+03:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Istanbul',
    offset: 10800,
    period: {
      until: null,
      from: '2016-09-07T00:00:00+03:00',
    },
    aliases: [
      'Asia/Istanbul',
    ],
    legacy: false,
    abbreviation: '+03',
    offset_utc: 10800,
    offset_std: 0,
    long_name: 'Turkey Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Turkey Standard Time',
    },
    generic_long_name: 'Turkey Standard Time',
    formatted_offset: '+03:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Riyadh',
    offset: 10800,
    period: {
      until: null,
      from: '1947-03-13T23:53:08+03:00',
    },
    aliases: [
      'Asia/Aden',
      'Antarctica/Syowa',
      'Asia/Kuwait',
    ],
    legacy: false,
    abbreviation: '+03',
    offset_utc: 10800,
    offset_std: 0,
    long_name: 'Arabian Standard Time',
    metazone: {
      name: 'Arabian',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Arabian Standard Time',
        standard: 'Arabian Standard Time',
        generic: 'Arabian Time',
        daylight: 'Arabian Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Arab Standard Time',
    },
    generic_long_name: 'Arabian Time',
    formatted_offset: '+03:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Damascus',
    offset: 10800,
    period: {
      until: null,
      from: '2022-10-28T00:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+03',
    offset_utc: 10800,
    offset_std: 0,
    long_name: 'Eastern European Standard Time',
    metazone: {
      name: 'Europe_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Eastern European Standard Time',
        standard: 'Eastern European Standard Time',
        generic: 'Eastern European Time',
        daylight: 'Eastern European Summer Time',
      },
      territories: [
        'SY',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Syria Standard Time',
    },
    generic_long_name: 'Eastern European Time',
    formatted_offset: '+03:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Baghdad',
    offset: 10800,
    period: {
      until: null,
      from: '2007-10-01T03:00:00+04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+03',
    offset_utc: 10800,
    offset_std: 0,
    long_name: 'Arabian Standard Time',
    metazone: {
      name: 'Arabian',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Arabian Standard Time',
        standard: 'Arabian Standard Time',
        generic: 'Arabian Time',
        daylight: 'Arabian Daylight Time',
      },
      territories: [
        'IQ',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Arabic Standard Time',
    },
    generic_long_name: 'Arabian Time',
    formatted_offset: '+03:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Amman',
    offset: 10800,
    period: {
      until: null,
      from: '2022-10-28T01:00:00+03:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+03',
    offset_utc: 10800,
    offset_std: 0,
    long_name: 'Eastern European Standard Time',
    metazone: {
      name: 'Europe_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Eastern European Standard Time',
        standard: 'Eastern European Standard Time',
        generic: 'Eastern European Time',
        daylight: 'Eastern European Summer Time',
      },
      territories: [
        'JO',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Jordan Standard Time',
    },
    generic_long_name: 'Eastern European Time',
    formatted_offset: '+03:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Africa/Nairobi',
    offset: 10800,
    period: {
      until: null,
      from: '1942-08-01T00:15:00+03:00',
    },
    aliases: [
      'Africa/Djibouti',
      'Africa/Asmera',
      'Africa/Asmara',
      'Indian/Antananarivo',
      'Indian/Comoro',
      'Africa/Addis_Ababa',
      'Africa/Mogadishu',
      'Africa/Kampala',
      'Africa/Dar_es_Salaam',
      'Indian/Mayotte',
    ],
    legacy: false,
    abbreviation: 'EAT',
    offset_utc: 10800,
    offset_std: 0,
    long_name: 'East Africa Time',
    metazone: {
      name: 'Africa_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'East Africa Time',
        standard: 'East Africa Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'E. Africa Standard Time',
    },
    generic_long_name: 'East Africa Time',
    formatted_offset: '+03:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Tehran',
    offset: 12600,
    period: {
      until: null,
      from: '2022-09-21T23:00:00+04:30',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+0330',
    offset_utc: 12600,
    offset_std: 0,
    long_name: 'Iran Standard Time',
    metazone: {
      name: 'Iran',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Iran Standard Time',
        standard: 'Iran Standard Time',
        generic: 'Iran Time',
        daylight: 'Iran Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Iran Standard Time',
    },
    generic_long_name: 'Iran Time',
    formatted_offset: '+03:30',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Indian/Mauritius',
    offset: 14400,
    period: {
      until: null,
      from: '2009-03-29T01:00:00+05:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+04',
    offset_utc: 14400,
    offset_std: 0,
    long_name: 'Mauritius Standard Time',
    metazone: {
      name: 'Mauritius',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Mauritius Standard Time',
        standard: 'Mauritius Standard Time',
        generic: 'Mauritius Time',
        daylight: 'Mauritius Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Mauritius Standard Time',
    },
    generic_long_name: 'Mauritius Time',
    formatted_offset: '+04:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Saratov',
    offset: 14400,
    period: {
      until: null,
      from: '2016-12-04T03:00:00+04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+04',
    offset_utc: 14400,
    offset_std: 0,
    long_name: 'Saratov Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Saratov Standard Time',
    },
    generic_long_name: 'Saratov Standard Time',
    formatted_offset: '+04:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Samara',
    offset: 14400,
    period: {
      until: null,
      from: '2011-03-27T03:00:00+04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+04',
    offset_utc: 14400,
    offset_std: 0,
    long_name: 'Samara Standard Time',
    metazone: {
      name: 'Samara',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Samara Standard Time',
        standard: 'Samara Standard Time',
        generic: 'Samara Time',
        daylight: 'Samara Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Russia Time Zone 3',
    },
    generic_long_name: 'Samara Time',
    formatted_offset: '+04:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Europe/Astrakhan',
    offset: 14400,
    period: {
      until: null,
      from: '2016-03-27T03:00:00+04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+04',
    offset_utc: 14400,
    offset_std: 0,
    long_name: 'Astrakhan Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Astrakhan Standard Time',
    },
    generic_long_name: 'Astrakhan Standard Time',
    formatted_offset: '+04:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Yerevan',
    offset: 14400,
    period: {
      until: null,
      from: '2011-10-30T02:00:00+05:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+04',
    offset_utc: 14400,
    offset_std: 0,
    long_name: 'Armenia Standard Time',
    metazone: {
      name: 'Armenia',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Armenia Standard Time',
        standard: 'Armenia Standard Time',
        generic: 'Armenia Time',
        daylight: 'Armenia Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Caucasus Standard Time',
    },
    generic_long_name: 'Armenia Time',
    formatted_offset: '+04:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Tbilisi',
    offset: 14400,
    period: {
      until: null,
      from: '2005-03-27T03:00:00+04:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+04',
    offset_utc: 14400,
    offset_std: 0,
    long_name: 'Georgia Standard Time',
    metazone: {
      name: 'Georgia',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Georgia Standard Time',
        standard: 'Georgia Standard Time',
        generic: 'Georgia Time',
        daylight: 'Georgia Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Georgian Standard Time',
    },
    generic_long_name: 'Georgia Time',
    formatted_offset: '+04:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Dubai',
    offset: 14400,
    period: {
      until: null,
      from: '1920-01-01T00:18:48+04:00',
    },
    aliases: [
      'Asia/Muscat',
      'Indian/Reunion',
      'Indian/Mahe',
    ],
    legacy: false,
    abbreviation: '+04',
    offset_utc: 14400,
    offset_std: 0,
    long_name: 'Gulf Standard Time',
    metazone: {
      name: 'Gulf',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Gulf Standard Time',
        standard: 'Gulf Standard Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Arabian Standard Time',
    },
    generic_long_name: 'Gulf Standard Time',
    formatted_offset: '+04:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Baku',
    offset: 14400,
    period: {
      until: null,
      from: '2015-10-25T04:00:00+05:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+04',
    offset_utc: 14400,
    offset_std: 0,
    long_name: 'Azerbaijan Standard Time',
    metazone: {
      name: 'Azerbaijan',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Azerbaijan Standard Time',
        standard: 'Azerbaijan Standard Time',
        generic: 'Azerbaijan Time',
        daylight: 'Azerbaijan Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Azerbaijan Standard Time',
    },
    generic_long_name: 'Azerbaijan Time',
    formatted_offset: '+04:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Kabul',
    offset: 16200,
    period: {
      until: null,
      from: '1945-01-01T00:30:00+04:30',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+0430',
    offset_utc: 16200,
    offset_std: 0,
    long_name: 'Afghanistan Time',
    metazone: {
      name: 'Afghanistan',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Afghanistan Time',
        standard: 'Afghanistan Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Afghanistan Standard Time',
    },
    generic_long_name: 'Afghanistan Time',
    formatted_offset: '+04:30',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Yekaterinburg',
    offset: 18000,
    period: {
      until: null,
      from: '2014-10-26T01:00:00+06:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+05',
    offset_utc: 18000,
    offset_std: 0,
    long_name: 'Yekaterinburg Standard Time',
    metazone: {
      name: 'Yekaterinburg',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Yekaterinburg Standard Time',
        standard: 'Yekaterinburg Standard Time',
        generic: 'Yekaterinburg Time',
        daylight: 'Yekaterinburg Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Ekaterinburg Standard Time',
    },
    generic_long_name: 'Yekaterinburg Time',
    formatted_offset: '+05:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Tashkent',
    offset: 18000,
    period: {
      until: null,
      from: '1992-01-01T00:00:00+05:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+05',
    offset_utc: 18000,
    offset_std: 0,
    long_name: 'Uzbekistan Standard Time',
    metazone: {
      name: 'Uzbekistan',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Uzbekistan Standard Time',
        standard: 'Uzbekistan Standard Time',
        generic: 'Uzbekistan Time',
        daylight: 'Uzbekistan Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'West Asia Standard Time',
    },
    generic_long_name: 'Uzbekistan Time',
    formatted_offset: '+05:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Qyzylorda',
    offset: 18000,
    period: {
      until: null,
      from: '2018-12-20T23:00:00+06:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+05',
    offset_utc: 18000,
    offset_std: 0,
    long_name: 'West Kazakhstan Time',
    metazone: {
      name: 'Kazakhstan_Western',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'West Kazakhstan Time',
        standard: 'West Kazakhstan Time',
        generic: null,
        daylight: null,
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Qyzylorda Standard Time',
    },
    generic_long_name: 'West Kazakhstan Time',
    formatted_offset: '+05:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Karachi',
    offset: 18000,
    period: {
      until: null,
      from: '2009-10-31T23:00:00+06:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'PKT',
    offset_utc: 18000,
    offset_std: 0,
    long_name: 'Pakistan Standard Time',
    metazone: {
      name: 'Pakistan',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Pakistan Standard Time',
        standard: 'Pakistan Standard Time',
        generic: 'Pakistan Time',
        daylight: 'Pakistan Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Pakistan Standard Time',
    },
    generic_long_name: 'Pakistan Time',
    formatted_offset: '+05:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Almaty',
    offset: 18000,
    period: {
      until: null,
      from: '2024-02-29T23:00:00+06:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+05',
    offset_utc: 18000,
    offset_std: 0,
    long_name: 'East Kazakhstan Time',
    metazone: {
      name: 'Kazakhstan_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'East Kazakhstan Time',
        standard: 'East Kazakhstan Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Central Asia Standard Time',
    },
    generic_long_name: 'East Kazakhstan Time',
    formatted_offset: '+05:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Colombo',
    offset: 19800,
    period: {
      until: null,
      from: '2006-04-15T00:00:00+06:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+0530',
    offset_utc: 19800,
    offset_std: 0,
    long_name: 'India Standard Time',
    metazone: {
      name: 'India',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'India Standard Time',
        standard: 'India Standard Time',
        generic: null,
        daylight: null,
      },
      territories: [
        'LK',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Sri Lanka Standard Time',
    },
    generic_long_name: 'India Standard Time',
    formatted_offset: '+05:30',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Omsk',
    offset: 21600,
    period: {
      until: null,
      from: '2014-10-26T01:00:00+07:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+06',
    offset_utc: 21600,
    offset_std: 0,
    long_name: 'Omsk Standard Time',
    metazone: {
      name: 'Omsk',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Omsk Standard Time',
        standard: 'Omsk Standard Time',
        generic: 'Omsk Time',
        daylight: 'Omsk Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Omsk Standard Time',
    },
    generic_long_name: 'Omsk Time',
    formatted_offset: '+06:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Dhaka',
    offset: 21600,
    period: {
      until: null,
      from: '2009-12-31T23:00:00+07:00',
    },
    aliases: [
      'Asia/Dacca',
    ],
    legacy: false,
    abbreviation: '+06',
    offset_utc: 21600,
    offset_std: 0,
    long_name: 'Bangladesh Standard Time',
    metazone: {
      name: 'Bangladesh',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Bangladesh Standard Time',
        standard: 'Bangladesh Standard Time',
        generic: 'Bangladesh Time',
        daylight: 'Bangladesh Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Bangladesh Standard Time',
    },
    generic_long_name: 'Bangladesh Time',
    formatted_offset: '+06:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Tomsk',
    offset: 25200,
    period: {
      until: null,
      from: '2016-05-29T03:00:00+07:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+07',
    offset_utc: 25200,
    offset_std: 0,
    long_name: 'Tomsk Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Tomsk Standard Time',
    },
    generic_long_name: 'Tomsk Standard Time',
    formatted_offset: '+07:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Novosibirsk',
    offset: 25200,
    period: {
      until: null,
      from: '2016-07-24T03:00:00+07:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+07',
    offset_utc: 25200,
    offset_std: 0,
    long_name: 'Novosibirsk Standard Time',
    metazone: {
      name: 'Novosibirsk',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Novosibirsk Standard Time',
        standard: 'Novosibirsk Standard Time',
        generic: 'Novosibirsk Time',
        daylight: 'Novosibirsk Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'N. Central Asia Standard Time',
    },
    generic_long_name: 'Novosibirsk Time',
    formatted_offset: '+07:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Krasnoyarsk',
    offset: 25200,
    period: {
      until: null,
      from: '2014-10-26T01:00:00+08:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+07',
    offset_utc: 25200,
    offset_std: 0,
    long_name: 'Krasnoyarsk Standard Time',
    metazone: {
      name: 'Krasnoyarsk',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Krasnoyarsk Standard Time',
        standard: 'Krasnoyarsk Standard Time',
        generic: 'Krasnoyarsk Time',
        daylight: 'Krasnoyarsk Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'North Asia Standard Time',
    },
    generic_long_name: 'Krasnoyarsk Time',
    formatted_offset: '+07:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Hovd',
    offset: 25200,
    period: {
      until: null,
      from: '2016-09-23T23:00:00+08:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+07',
    offset_utc: 25200,
    offset_std: 0,
    long_name: 'Hovd Standard Time',
    metazone: {
      name: 'Hovd',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Hovd Standard Time',
        standard: 'Hovd Standard Time',
        generic: 'Hovd Time',
        daylight: 'Hovd Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'W. Mongolia Standard Time',
    },
    generic_long_name: 'Hovd Time',
    formatted_offset: '+07:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Barnaul',
    offset: 25200,
    period: {
      until: null,
      from: '2016-03-27T03:00:00+07:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+07',
    offset_utc: 25200,
    offset_std: 0,
    long_name: 'Altai Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Altai Standard Time',
    },
    generic_long_name: 'Altai Standard Time',
    formatted_offset: '+07:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Bangkok',
    offset: 25200,
    period: {
      until: null,
      from: '1920-04-01T00:17:56+07:00',
    },
    aliases: [
      'Asia/Phnom_Penh',
      'Asia/Vientiane',
      'Indian/Christmas',
    ],
    legacy: false,
    abbreviation: '+07',
    offset_utc: 25200,
    offset_std: 0,
    long_name: 'Indochina Time',
    metazone: {
      name: 'Indochina',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Indochina Time',
        standard: 'Indochina Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'SE Asia Standard Time',
    },
    generic_long_name: 'Indochina Time',
    formatted_offset: '+07:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Australia/Perth',
    offset: 28800,
    period: {
      until: null,
      from: '2009-03-29T02:00:00+09:00',
    },
    aliases: [
      'Australia/West',
    ],
    legacy: false,
    abbreviation: 'AWST',
    offset_utc: 28800,
    offset_std: 0,
    long_name: 'Australian Western Standard Time',
    metazone: {
      name: 'Australia_Western',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Australian Western Standard Time',
        standard: 'Australian Western Standard Time',
        generic: 'Western Australia Time',
        daylight: 'Australian Western Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'W. Australia Standard Time',
    },
    generic_long_name: 'Western Australia Time',
    formatted_offset: '+08:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Ulaanbaatar',
    offset: 28800,
    period: {
      until: null,
      from: '2016-09-23T23:00:00+09:00',
    },
    aliases: [
      'Asia/Choibalsan',
      'Asia/Ulan_Bator',
    ],
    legacy: false,
    abbreviation: '+08',
    offset_utc: 28800,
    offset_std: 0,
    long_name: 'Ulaanbaatar Standard Time',
    metazone: {
      name: 'Mongolia',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Ulaanbaatar Standard Time',
        standard: 'Ulaanbaatar Standard Time',
        generic: 'Ulaanbaatar Time',
        daylight: 'Ulaanbaatar Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Ulaanbaatar Standard Time',
    },
    generic_long_name: 'Ulaanbaatar Time',
    formatted_offset: '+08:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Taipei',
    offset: 28800,
    period: {
      until: null,
      from: '1979-09-30T23:00:00+09:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'CST',
    offset_utc: 28800,
    offset_std: 0,
    long_name: 'Taipei Standard Time',
    metazone: {
      name: 'Taipei',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Taipei Standard Time',
        standard: 'Taipei Standard Time',
        generic: 'Taipei Time',
        daylight: 'Taipei Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Taipei Standard Time',
    },
    generic_long_name: 'Taipei Time',
    formatted_offset: '+08:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Singapore',
    offset: 28800,
    period: {
      until: null,
      from: '1982-01-01T00:00:00+08:00',
    },
    aliases: [
      'Asia/Kuala_Lumpur',
    ],
    legacy: false,
    abbreviation: '+08',
    offset_utc: 28800,
    offset_std: 0,
    long_name: 'Singapore Standard Time',
    metazone: {
      name: 'Singapore',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Singapore Standard Time',
        standard: 'Singapore Standard Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Singapore Standard Time',
    },
    generic_long_name: 'Singapore Standard Time',
    formatted_offset: '+08:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Shanghai',
    offset: 28800,
    period: {
      until: null,
      from: '1991-09-15T01:00:00+09:00',
    },
    aliases: [
      'Asia/Harbin',
      'Asia/Chungking',
      'Asia/Chongqing',
    ],
    legacy: false,
    abbreviation: 'CST',
    offset_utc: 28800,
    offset_std: 0,
    long_name: 'China Standard Time',
    metazone: {
      name: 'China',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'China Standard Time',
        standard: 'China Standard Time',
        generic: 'China Time',
        daylight: 'China Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'China Standard Time',
    },
    generic_long_name: 'China Time',
    formatted_offset: '+08:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Irkutsk',
    offset: 28800,
    period: {
      until: null,
      from: '2014-10-26T01:00:00+09:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+08',
    offset_utc: 28800,
    offset_std: 0,
    long_name: 'Irkutsk Standard Time',
    metazone: {
      name: 'Irkutsk',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Irkutsk Standard Time',
        standard: 'Irkutsk Standard Time',
        generic: 'Irkutsk Time',
        daylight: 'Irkutsk Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'North Asia East Standard Time',
    },
    generic_long_name: 'Irkutsk Time',
    formatted_offset: '+08:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Australia/Eucla',
    offset: 31500,
    period: {
      until: null,
      from: '2009-03-29T02:00:00+09:45',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+0845',
    offset_utc: 31500,
    offset_std: 0,
    long_name: 'Aus Central W. Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Aus Central W. Standard Time',
    },
    generic_long_name: 'Aus Central W. Standard Time',
    formatted_offset: '+08:45',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Yakutsk',
    offset: 32400,
    period: {
      until: null,
      from: '2014-10-26T01:00:00+10:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+09',
    offset_utc: 32400,
    offset_std: 0,
    long_name: 'Yakutsk Standard Time',
    metazone: {
      name: 'Yakutsk',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Yakutsk Standard Time',
        standard: 'Yakutsk Standard Time',
        generic: 'Yakutsk Time',
        daylight: 'Yakutsk Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Yakutsk Standard Time',
    },
    generic_long_name: 'Yakutsk Time',
    formatted_offset: '+09:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Tokyo',
    offset: 32400,
    period: {
      until: null,
      from: '1951-09-09T00:00:00+10:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'JST',
    offset_utc: 32400,
    offset_std: 0,
    long_name: 'Japan Standard Time',
    metazone: {
      name: 'Japan',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Japan Standard Time',
        standard: 'Japan Standard Time',
        generic: 'Japan Time',
        daylight: 'Japan Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Tokyo Standard Time',
    },
    generic_long_name: 'Japan Time',
    formatted_offset: '+09:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Seoul',
    offset: 32400,
    period: {
      until: null,
      from: '1988-10-09T02:00:00+10:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'KST',
    offset_utc: 32400,
    offset_std: 0,
    long_name: 'Korean Standard Time',
    metazone: {
      name: 'Korea',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Korean Standard Time',
        standard: 'Korean Standard Time',
        generic: 'Korean Time',
        daylight: 'Korean Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Korea Standard Time',
    },
    generic_long_name: 'Korean Time',
    formatted_offset: '+09:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Pyongyang',
    offset: 32400,
    period: {
      until: null,
      from: '2018-05-05T00:00:00+09:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: 'KST',
    offset_utc: 32400,
    offset_std: 0,
    long_name: 'Korean Standard Time',
    metazone: {
      name: 'Korea',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Korean Standard Time',
        standard: 'Korean Standard Time',
        generic: 'Korean Time',
        daylight: 'Korean Daylight Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'North Korea Standard Time',
    },
    generic_long_name: 'Korean Time',
    formatted_offset: '+09:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Chita',
    offset: 32400,
    period: {
      until: null,
      from: '2016-03-27T03:00:00+09:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+09',
    offset_utc: 32400,
    offset_std: 0,
    long_name: 'Yakutsk Standard Time',
    metazone: {
      name: 'Yakutsk',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Yakutsk Standard Time',
        standard: 'Yakutsk Standard Time',
        generic: 'Yakutsk Time',
        daylight: 'Yakutsk Summer Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Transbaikal Standard Time',
    },
    generic_long_name: 'Yakutsk Time',
    formatted_offset: '+09:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Australia/Darwin',
    offset: 34200,
    period: {
      until: null,
      from: '1944-03-26T02:00:00+10:30',
    },
    aliases: [
      'Australia/North',
    ],
    legacy: false,
    abbreviation: 'ACST',
    offset_utc: 34200,
    offset_std: 0,
    long_name: 'Australian Central Standard Time',
    metazone: {
      name: 'Australia_Central',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Australian Central Standard Time',
        standard: 'Australian Central Standard Time',
        generic: 'Central Australia Time',
        daylight: 'Australian Central Daylight Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'AUS Central Standard Time',
    },
    generic_long_name: 'Central Australia Time',
    formatted_offset: '+09:30',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Pacific/Port_Moresby',
    offset: 36000,
    period: {
      until: null,
      from: '1895-01-01T00:11:28+10:00',
    },
    aliases: [
      'Pacific/Chuuk',
      'Pacific/Truk',
      'Pacific/Yap',
      'Antarctica/DumontDUrville',
    ],
    legacy: false,
    abbreviation: '+10',
    offset_utc: 36000,
    offset_std: 0,
    long_name: 'Papua New Guinea Time',
    metazone: {
      name: 'Papua_New_Guinea',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Papua New Guinea Time',
        standard: 'Papua New Guinea Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'West Pacific Standard Time',
    },
    generic_long_name: 'Papua New Guinea Time',
    formatted_offset: '+10:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Australia/Brisbane',
    offset: 36000,
    period: {
      until: null,
      from: '1992-03-01T02:00:00+11:00',
    },
    aliases: [
      'Australia/Queensland',
    ],
    legacy: false,
    abbreviation: 'AEST',
    offset_utc: 36000,
    offset_std: 0,
    long_name: 'Australian Eastern Standard Time',
    metazone: {
      name: 'Australia_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Australian Eastern Standard Time',
        standard: 'Australian Eastern Standard Time',
        generic: 'Eastern Australia Time',
        daylight: 'Australian Eastern Daylight Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'E. Australia Standard Time',
    },
    generic_long_name: 'Eastern Australia Time',
    formatted_offset: '+10:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Vladivostok',
    offset: 36000,
    period: {
      until: null,
      from: '2014-10-26T01:00:00+11:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+10',
    offset_utc: 36000,
    offset_std: 0,
    long_name: 'Vladivostok Standard Time',
    metazone: {
      name: 'Vladivostok',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Vladivostok Standard Time',
        standard: 'Vladivostok Standard Time',
        generic: 'Vladivostok Time',
        daylight: 'Vladivostok Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Vladivostok Standard Time',
    },
    generic_long_name: 'Vladivostok Time',
    formatted_offset: '+10:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Australia/Adelaide',
    offset: 37800,
    period: {
      until: '2025-04-06T03:00:00+09:30',
      from: '2024-10-06T03:00:00+10:30',
    },
    aliases: [
      'Australia/South',
    ],
    legacy: false,
    abbreviation: 'ACDT',
    offset_utc: 34200,
    offset_std: 3600,
    long_name: 'Australian Central Daylight Time',
    metazone: {
      name: 'Australia_Central',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Australian Central Daylight Time',
        standard: 'Australian Central Standard Time',
        generic: 'Central Australia Time',
        daylight: 'Australian Central Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Cen. Australia Standard Time',
    },
    generic_long_name: 'Central Australia Time',
    formatted_offset: '+10:30',
    golden: true,
    dst: true,
    canonical: true,
  },
  {
    id: 'Pacific/Guadalcanal',
    offset: 39600,
    period: {
      until: null,
      from: '1912-10-01T00:20:12+11:00',
    },
    aliases: [
      'Pacific/Ponape',
      'Pacific/Pohnpei',
    ],
    legacy: false,
    abbreviation: '+11',
    offset_utc: 39600,
    offset_std: 0,
    long_name: 'Solomon Islands Time',
    metazone: {
      name: 'Solomon',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Solomon Islands Time',
        standard: 'Solomon Islands Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Central Pacific Standard Time',
    },
    generic_long_name: 'Solomon Islands Time',
    formatted_offset: '+11:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Pacific/Bougainville',
    offset: 39600,
    period: {
      until: null,
      from: '2014-12-28T03:00:00+11:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+11',
    offset_utc: 39600,
    offset_std: 0,
    long_name: 'Bougainville Standard Time',
    metazone: null,
    windows_zone: {
      name: 'Bougainville Standard Time',
    },
    generic_long_name: 'Bougainville Standard Time',
    formatted_offset: '+11:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Australia/Sydney',
    offset: 39600,
    period: {
      until: '2025-04-06T03:00:00+10:00',
      from: '2024-10-06T03:00:00+11:00',
    },
    aliases: [
      'Australia/ACT',
      'Australia/Canberra',
      'Australia/NSW',
    ],
    legacy: false,
    abbreviation: 'AEDT',
    offset_utc: 36000,
    offset_std: 3600,
    long_name: 'Australian Eastern Daylight Time',
    metazone: {
      name: 'Australia_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Australian Eastern Daylight Time',
        standard: 'Australian Eastern Standard Time',
        generic: 'Eastern Australia Time',
        daylight: 'Australian Eastern Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'AUS Eastern Standard Time',
    },
    generic_long_name: 'Eastern Australia Time',
    formatted_offset: '+11:00',
    golden: true,
    dst: true,
    canonical: true,
  },
  {
    id: 'Australia/Lord_Howe',
    offset: 39600,
    period: {
      until: '2025-04-06T02:00:00+10:30',
      from: '2024-10-06T02:30:00+11:00',
    },
    aliases: [
      'Australia/LHI',
    ],
    legacy: false,
    abbreviation: '+11',
    offset_utc: 37800,
    offset_std: 1800,
    long_name: 'Lord Howe Daylight Time',
    metazone: {
      name: 'Lord_Howe',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Lord Howe Daylight Time',
        standard: 'Lord Howe Standard Time',
        generic: 'Lord Howe Time',
        daylight: 'Lord Howe Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Lord Howe Standard Time',
    },
    generic_long_name: 'Lord Howe Time',
    formatted_offset: '+11:00',
    golden: true,
    dst: true,
    canonical: true,
  },
  {
    id: 'Australia/Hobart',
    offset: 39600,
    period: {
      until: '2025-04-06T03:00:00+10:00',
      from: '2024-10-06T03:00:00+11:00',
    },
    aliases: [
      'Australia/Tasmania',
      'Australia/Currie',
    ],
    legacy: false,
    abbreviation: 'AEDT',
    offset_utc: 36000,
    offset_std: 3600,
    long_name: 'Australian Eastern Daylight Time',
    metazone: {
      name: 'Australia_Eastern',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Australian Eastern Daylight Time',
        standard: 'Australian Eastern Standard Time',
        generic: 'Eastern Australia Time',
        daylight: 'Australian Eastern Daylight Time',
      },
      territories: [],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Tasmania Standard Time',
    },
    generic_long_name: 'Eastern Australia Time',
    formatted_offset: '+11:00',
    golden: false,
    dst: true,
    canonical: true,
  },
  {
    id: 'Asia/Srednekolymsk',
    offset: 39600,
    period: {
      until: null,
      from: '2014-10-26T01:00:00+12:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+11',
    offset_utc: 39600,
    offset_std: 0,
    long_name: 'Russia Time Zone 10',
    metazone: null,
    windows_zone: {
      name: 'Russia Time Zone 10',
    },
    generic_long_name: 'Russia Time Zone 10',
    formatted_offset: '+11:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Sakhalin',
    offset: 39600,
    period: {
      until: null,
      from: '2016-03-27T03:00:00+11:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+11',
    offset_utc: 39600,
    offset_std: 0,
    long_name: 'Sakhalin Standard Time',
    metazone: {
      name: 'Sakhalin',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Sakhalin Standard Time',
        standard: 'Sakhalin Standard Time',
        generic: 'Sakhalin Time',
        daylight: 'Sakhalin Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Sakhalin Standard Time',
    },
    generic_long_name: 'Sakhalin Time',
    formatted_offset: '+11:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Magadan',
    offset: 39600,
    period: {
      until: null,
      from: '2016-04-24T03:00:00+11:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+11',
    offset_utc: 39600,
    offset_std: 0,
    long_name: 'Magadan Standard Time',
    metazone: {
      name: 'Magadan',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Magadan Standard Time',
        standard: 'Magadan Standard Time',
        generic: 'Magadan Time',
        daylight: 'Magadan Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Magadan Standard Time',
    },
    generic_long_name: 'Magadan Time',
    formatted_offset: '+11:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Pacific/Norfolk',
    offset: 43200,
    period: {
      until: '2025-04-06T03:00:00+11:00',
      from: '2024-10-06T03:00:00+12:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+12',
    offset_utc: 39600,
    offset_std: 3600,
    long_name: 'Norfolk Island Daylight Time',
    metazone: {
      name: 'Norfolk',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Norfolk Island Daylight Time',
        standard: 'Norfolk Island Standard Time',
        generic: 'Norfolk Island Time',
        daylight: 'Norfolk Island Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Norfolk Standard Time',
    },
    generic_long_name: 'Norfolk Island Time',
    formatted_offset: '+12:00',
    golden: true,
    dst: true,
    canonical: true,
  },
  {
    id: 'Pacific/Fiji',
    offset: 43200,
    period: {
      until: null,
      from: '2021-01-17T02:00:00+13:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+12',
    offset_utc: 43200,
    offset_std: 0,
    long_name: 'Fiji Standard Time',
    metazone: {
      name: 'Fiji',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Fiji Standard Time',
        standard: 'Fiji Standard Time',
        generic: 'Fiji Time',
        daylight: 'Fiji Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Fiji Standard Time',
    },
    generic_long_name: 'Fiji Time',
    formatted_offset: '+12:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Etc/GMT-12',
    offset: 43200,
    period: {
      until: null,
      from: null,
    },
    aliases: [],
    legacy: false,
    abbreviation: '+12',
    offset_utc: 43200,
    offset_std: 0,
    long_name: 'UTC+12',
    metazone: null,
    windows_zone: {
      name: 'UTC+12',
    },
    generic_long_name: 'UTC+12',
    formatted_offset: '+12:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Asia/Kamchatka',
    offset: 43200,
    period: {
      until: null,
      from: '2011-03-27T03:00:00+12:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+12',
    offset_utc: 43200,
    offset_std: 0,
    long_name: 'Petropavlovsk-Kamchatski Standard Time',
    metazone: {
      name: 'Kamchatka',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Petropavlovsk-Kamchatski Standard Time',
        standard: 'Petropavlovsk-Kamchatski Standard Time',
        generic: 'Petropavlovsk-Kamchatski Time',
        daylight: 'Petropavlovsk-Kamchatski Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Russia Time Zone 11',
    },
    generic_long_name: 'Petropavlovsk-Kamchatski Time',
    formatted_offset: '+12:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Pacific/Tongatapu',
    offset: 46800,
    period: {
      until: null,
      from: '2017-01-15T02:00:00+14:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+13',
    offset_utc: 46800,
    offset_std: 0,
    long_name: 'Tonga Standard Time',
    metazone: {
      name: 'Tonga',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Tonga Standard Time',
        standard: 'Tonga Standard Time',
        generic: 'Tonga Time',
        daylight: 'Tonga Summer Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Tonga Standard Time',
    },
    generic_long_name: 'Tonga Time',
    formatted_offset: '+13:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Pacific/Auckland',
    offset: 46800,
    period: {
      until: '2025-04-06T03:00:00+12:00',
      from: '2024-09-29T03:00:00+13:00',
    },
    aliases: [
      'Antarctica/South_Pole',
      'Antarctica/McMurdo',
    ],
    legacy: false,
    abbreviation: 'NZDT',
    offset_utc: 43200,
    offset_std: 3600,
    long_name: 'New Zealand Daylight Time',
    metazone: {
      name: 'New_Zealand',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'New Zealand Daylight Time',
        standard: 'New Zealand Standard Time',
        generic: 'New Zealand Time',
        daylight: 'New Zealand Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'New Zealand Standard Time',
    },
    generic_long_name: 'New Zealand Time',
    formatted_offset: '+13:00',
    golden: true,
    dst: true,
    canonical: true,
  },
  {
    id: 'Pacific/Apia',
    offset: 46800,
    period: {
      until: null,
      from: '2021-04-04T03:00:00+14:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+13',
    offset_utc: 46800,
    offset_std: 0,
    long_name: 'Apia Standard Time',
    metazone: {
      name: 'Apia',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Apia Standard Time',
        standard: 'Apia Standard Time',
        generic: 'Apia Time',
        daylight: 'Apia Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Samoa Standard Time',
    },
    generic_long_name: 'Apia Time',
    formatted_offset: '+13:00',
    golden: true,
    dst: false,
    canonical: true,
  },
  {
    id: 'Etc/GMT-13',
    offset: 46800,
    period: {
      until: null,
      from: null,
    },
    aliases: [],
    legacy: false,
    abbreviation: '+13',
    offset_utc: 46800,
    offset_std: 0,
    long_name: 'UTC+13',
    metazone: null,
    windows_zone: {
      name: 'UTC+13',
    },
    generic_long_name: 'UTC+13',
    formatted_offset: '+13:00',
    golden: false,
    dst: false,
    canonical: true,
  },
  {
    id: 'Pacific/Chatham',
    offset: 49500,
    period: {
      until: '2025-04-06T03:45:00+12:45',
      from: '2024-09-29T03:45:00+13:45',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+1345',
    offset_utc: 45900,
    offset_std: 3600,
    long_name: 'Chatham Daylight Time',
    metazone: {
      name: 'Chatham',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Chatham Daylight Time',
        standard: 'Chatham Standard Time',
        generic: 'Chatham Time',
        daylight: 'Chatham Daylight Time',
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Chatham Islands Standard Time',
    },
    generic_long_name: 'Chatham Time',
    formatted_offset: '+13:45',
    golden: true,
    dst: true,
    canonical: true,
  },
  {
    id: 'Pacific/Kiritimati',
    offset: 50400,
    period: {
      until: null,
      from: '1995-01-01T00:00:00+14:00',
    },
    aliases: [],
    legacy: false,
    abbreviation: '+14',
    offset_utc: 50400,
    offset_std: 0,
    long_name: 'Line Islands Time',
    metazone: {
      name: 'Line_Islands',
      short: {
        current: null,
        standard: null,
        generic: null,
        daylight: null,
      },
      long: {
        current: 'Line Islands Time',
        standard: 'Line Islands Time',
        generic: null,
        daylight: null,
      },
      territories: [
        '001',
      ],
      exemplar_city: null,
    },
    windows_zone: {
      name: 'Line Islands Standard Time',
    },
    generic_long_name: 'Line Islands Time',
    formatted_offset: '+14:00',
    golden: true,
    dst: false,
    canonical: true,
  }
];

export const MAPPING_TIMEZONE_NAMES = {
  'Dateline Standard Time': 'International Date Line West',
  'UTC-11': 'Coordinated Universal Time-11',
  'Hawaiian Standard Time': 'Hawaii',
  'Aleutian Standard Time': 'Aleutian Islands',
  'Marquesas Standard Time': 'Marquesas Islands',
  'UTC-09': 'Coordinated Universal Time-09',
  'Alaskan Standard Time': 'Alaska',
  'UTC-08': 'Coordinated Universal Time-08',
  'Pacific Standard Time (Mexico)': 'Baja California',
  'Pacific Standard Time': 'Pacific Time (US & Canada)',
  'US Mountain Standard Time': 'Arizona',
  'Mountain Standard Time (Mexico)': 'Chihuahua, La Paz, Mazatlan',
  'Mountain Standard Time': 'Mountain Time (US & Canada)',
  'Canada Central Standard Time': 'Saskatchewan',
  'Central Standard Time (Mexico)': 'Guadalajara, Mexico City, Monterrey',
  'Central America Standard Time': 'Central America',
  'Central Standard Time': 'Central Time (US & Canada)',
  'Easter Island Standard Time': 'Easter Island',
  'Haiti Standard Time': 'Haiti',
  'Eastern Standard Time': 'Eastern Time (US & Canada)',
  'Cuba Standard Time': 'Havana',
  'Turks And Caicos Standard Time': 'Turks and Caicos',
  'Eastern Standard Time (Mexico)': 'Chetumal',
  'SA Pacific Standard Time': 'Bogota, Lima, Quito, Rio Branco',
  'SA Western Standard Time': 'Georgetown, La Paz, Manaus, San Juan',
  'Atlantic Standard Time': 'Atlantic Time (Canada)',
  'Central Brazilian Standard Time': 'Cuiaba',
  'Venezuela Standard Time': 'Caracas',
  'Newfoundland Standard Time': 'Newfoundland',
  'E. South America Standard Time': 'Brasilia',
  'Pacific SA Standard Time': 'Santiago',
  'Magallanes Standard Time': 'Punta Arenas',
  'Montevideo Standard Time': 'Montevideo',
  'Saint Pierre Standard Time': 'Saint Pierre and Miquelon',
  'SA Eastern Standard Time': 'Cayenne, Fortaleza',
  'Bahia Standard Time': 'Salvador',
  'Paraguay Standard Time': 'Asuncion',
  'Tocantins Standard Time': 'Araguaina',
  'UTC-02': 'Coordinated Universal Time-02',
  'Cape Verde Standard Time': 'Cabo Verde Is.',
  'Azores Standard Time': 'Azores',
  'GMT Standard Time': 'Dublin, Edinburgh, Lisbon, London',
  'UTC': '(UTC) Coordinated Universal Time',
  'Sao Tome Standard Time': 'Sao Tome',
  'Central European Standard Time': 'Sarajevo, Skopje, Warsaw, Zagreb',
  'Romance Standard Time': 'Brussels, Copenhagen, Madrid, Paris',
  'Central Europe Standard Time': 'Belgrade, Bratislava, Budapest, Ljubljana, Prague',
  'W. Europe Standard Time': 'Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna',
  'W. Central Africa Standard Time': 'West Central Africa',
  'Morocco Standard Time': 'Casablanca',
  'Kaliningrad Standard Time': 'Kaliningrad',
  'E. Europe Standard Time': 'Chisinau',
  'GTB Standard Time': 'Athens, Bucharest',
  'Israel Standard Time': 'Jerusalem',
  'West Bank Standard Time': 'Gaza, Hebron',
  'Middle East Standard Time': 'Beirut',
  'Namibia Standard Time': 'Windhoek',
  'Libya Standard Time': 'Tripoli',
  'Sudan Standard Time': 'Khartoum',
  'South Africa Standard Time': 'Harare, Pretoria',
  'Egypt Standard Time': 'Cairo',
  'Volgograd Standard Time': 'Volgograd',
  'Russian Standard Time': 'Moscow, St. Petersburg',
  'Belarus Standard Time': 'Minsk',
  'Turkey Standard Time': 'Istanbul',
  'Arab Standard Time': 'Kuwait, Riyadh',
  'Syria Standard Time': 'Damascus',
  'Arabic Standard Time': 'Baghdad',
  'Jordan Standard Time': 'Amman',
  'E. Africa Standard Time': 'Nairobi',
  'Iran Standard Time': 'Tehran',
  'Mauritius Standard Time': 'Port Louis',
  'Saratov Standard Time': 'Saratov',
  'Russia Time Zone 3': 'Izhevsk, Samara',
  'Astrakhan Standard Time': 'Astrakhan, Ulyanovsk',
  'Caucasus Standard Time': 'Yerevan',
  'Georgian Standard Time': 'Tbilisi',
  'Arabian Standard Time': 'Abu Dhabi, Muscat',
  'Azerbaijan Standard Time': 'Baku',
  'Afghanistan Standard Time': 'Kabul',
  'Ekaterinburg Standard Time': 'Ekaterinburg',
  'West Asia Standard Time': 'Ashgabat, Tashkent',
  'Qyzylorda Standard Time': 'Qyzylorda',
  'Pakistan Standard Time': 'Islamabad, Karachi',
  'Central Asia Standard Time': 'Astana',
  'Sri Lanka Standard Time': 'Sri Jayawardenepura',
  'Omsk Standard Time': 'Omsk',
  'Bangladesh Standard Time': 'Dhaka',
  'Tomsk Standard Time': 'Tomsk',
  'N. Central Asia Standard Time': 'Novosibirsk',
  'North Asia Standard Time': 'Krasnoyarsk',
  'W. Mongolia Standard Time': 'Hovd',
  'Altai Standard Time': 'Barnaul, Gorno-Altaysk',
  'SE Asia Standard Time': 'Bangkok, Hanoi, Jakarta',
  'W. Australia Standard Time': 'Perth',
  'Ulaanbaatar Standard Time': 'Ulaanbaatar',
  'Taipei Standard Time': 'Taipei',
  'Singapore Standard Time': 'Kuala Lumpur, Singapore',
  'China Standard Time': 'Beijing, Chongqing, Hong Kong, Urumqi',
  'North Asia East Standard Time': 'Irkutsk',
  'Aus Central W. Standard Time': 'Eucla',
  'Yakutsk Standard Time': 'Yakutsk',
  'Tokyo Standard Time': 'Osaka, Sapporo, Tokyo',
  'Korea Standard Time': 'Seoul',
  'North Korea Standard Time': 'Pyongyang',
  'Transbaikal Standard Time': 'Chita',
  'AUS Central Standard Time': 'Darwin',
  'West Pacific Standard Time': 'Guam, Port Moresby',
  'E. Australia Standard Time': 'Brisbane',
  'Vladivostok Standard Time': 'Vladivostok',
  'Cen. Australia Standard Time': 'Adelaide',
  'Central Pacific Standard Time': 'Solomon Is., New Caledonia',
  'Bougainville Standard Time': 'Bougainville Island',
  'AUS Eastern Standard Time': 'Canberra, Melbourne, Sydney',
  'Lord Howe Standard Time': 'Lord Howe Island',
  'Tasmania Standard Time': 'Hobart',
  'Russia Time Zone 10': 'Chokurdakh',
  'Sakhalin Standard Time': 'Sakhalin',
  'Magadan Standard Time': 'Magadan',
  'Norfolk Standard Time': 'Norfolk Island',
  'Fiji Standard Time': 'Fiji',
  'UTC+12': 'Coordinated Universal Time+12',
  'Russia Time Zone 11': 'Anadyr, Petropavlovsk-Kamchatsky',
  'Tonga Standard Time': "Nuku'alofa",
  'New Zealand Standard Time': 'Auckland, Wellington',
  'Samoa Standard Time': 'Samoa',
  'UTC+13': 'Coordinated Universal Time+13',
  'Chatham Islands Standard Time': 'Chatham Islands',
  'Line Islands Standard Time': 'Kiritimati Island'
};

const getClient = () => {
  const client = axios.create({
    baseURL: SAVVYCAL_CONFIG.BASE_URL,
    headers: {
      Accept: 'application/json',
      'User-Agent': 'Savvycal Node SDK',
      Authorization: `Bearer ${SAVVYCAL_CONFIG.PRIVATE_KEY}`,
    },
  });

  return client;
};

/**
 * Get timezones
 * Docs: https://savvycal.com/docs/api/endpoints/time-zones
 * @returns {Promise<any>}
 */
export const getListTimezones = async () => {
  const client = getClient();
  try {
    const { data } = await client.get('v1/time_zones', { timeout: 3000 });

    return data;
  } catch (error) {
    console.error('[SAVVYCAL] Error occurred getListTimezones:', error.response?.data || error);

    return DEFAULT_TIMEZONES;
  }
};

import { pipeline, env, PipelineType } from '@huggingface/transformers';

export class HuggingfacePipeline {
  static task: PipelineType = 'feature-extraction';
  static model = 'Xenova/all-MiniLM-L6-v2';
  static instance = null;
  static loaded = false;

  static async getInstance(progress_callback = null) {
    if (this.instance === null) {
      // NOTE: Uncomment this to change the cache directory
      env.cacheDir = './.cache';

      this.instance = await pipeline(this.task, this.model, { progress_callback });
      this.loaded = true;
    }

    return this.instance;
  }
}
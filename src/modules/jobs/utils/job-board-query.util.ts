//TODO: update params
export const jobBoardsQueryBuild = (
  jobBoardQueryBuilder,
  jobSearchFilterObject,
  minDate?: Date,
  isFromSearchTool = false
) => {
  if (!jobSearchFilterObject) {
    return jobBoardQueryBuilder;
  }

  let {
    searchText,
    keywords,
    location,
    jobBoards,
    maxSalary,
    minSalary,
    postedStartDate,
    postedEndDate,
    stopScapingAt,
    country,
    adminLevelOneArea,
    company,
    jobTitles,
    datePosted
  } = jobSearchFilterObject;

  if (company) {
    jobBoardQueryBuilder.andWhere('job_boards.company = :company', { company });
  }

  if (jobTitles) {
    jobBoardQueryBuilder.andWhere('job_boards.jobtitle in (:...jobTitles)', { jobTitles });
  }

  //min salary
  if (minSalary) {
    jobBoardQueryBuilder = jobBoardQueryBuilder.andWhere('job_boards.min_salary >= :minSalary', { minSalary });
  }
  //max salary
  if (maxSalary) {
    jobBoardQueryBuilder = jobBoardQueryBuilder.andWhere('job_boards.max_salary <= :maxSalary', { maxSalary });
  }

  if (minDate) {
    jobBoardQueryBuilder.andWhere('job_boards.updated_at >= :minDate', {
      minDate,
    });
  }

  if (stopScapingAt) {
    jobBoardQueryBuilder.andWhere('job_boards.updated_at <= :stopScapingAt', {
      stopScapingAt,
    });
  }

  if (postedEndDate) {
    jobBoardQueryBuilder = jobBoardQueryBuilder.andWhere('job_boards.posted <= :postedEndDate', { postedEndDate });
  }
  if (postedStartDate) {
    jobBoardQueryBuilder = jobBoardQueryBuilder.andWhere('job_boards.posted >= :postedStartDate', { postedStartDate });
  }
  

  const jobBoardsList = (jobBoards || '').split(',').filter((board: any) => board);
  if (jobBoardsList.length > 0) {
    jobBoardQueryBuilder = jobBoardQueryBuilder.andWhere('job_boards.source IN (:...jobBoardsList)', { jobBoardsList });
  }
  const keywordsArray = [];
  if (keywords) {
    // keywordsArray.push(...(keywords as string).split(',').flatMap((keyword: string) => keyword.trim().split(' ')));
    keywordsArray.push(...(keywords as string).split(',').map((item) => refineKeyword(item)));
  }
  if (searchText && searchText.trim() !== '') {
    // keywordsArray.push(
    //   ...searchText
    //     .trim()
    //     .split(',')
    //     .flatMap((keyword: string) => keyword.trim().split(' '))
    // );
    keywordsArray.push(refineKeyword(searchText));
  }
  if (keywordsArray?.length) {
    const isContainWord = (word: string, array: string[]) => {
      return array.some((item) => word.includes(item) && item !== word);
    };
    const refine1KeyWordsCondition = [
      ...new Set(keywordsArray.filter((item) => !Number(item) && !['and', 'or', '-'].includes(item))),
    ];
    const standardKeyWordsCondition = refine1KeyWordsCondition.filter(
      (item) => !isContainWord(item, refine1KeyWordsCondition)
    );

    const tsQueries = [];
    standardKeyWordsCondition.forEach((item) => {
      const q = `(to_tsvector('simple', ("job_boards"."jobtitle" || ' ' || "job_boards"."description"))
      @@ ( plainto_tsquery('simple', '${item}'))) `;
      tsQueries.push(q);
    });

    jobBoardQueryBuilder.andWhere(`( ${tsQueries.join(' OR ')} ) `);
  }
  //location
  if (country || adminLevelOneArea) {
    if (adminLevelOneArea) {
      //if filter by adminLevelOneArea, use joblocationcity
      const locationWildcard = `%${adminLevelOneArea}%`;
      jobBoardQueryBuilder = jobBoardQueryBuilder.andWhere('job_boards.joblocationcity ILIKE :adminLevelOneArea', {
        adminLevelOneArea: locationWildcard,
      });
    } else {
      const locationWildcard = `%${country}%`;
      jobBoardQueryBuilder = jobBoardQueryBuilder.andWhere('job_boards.joblocationinput ILIKE :country', {
        country: locationWildcard,
      });
    }
  } else {
    let filteredLocation = null;
    if (isFromSearchTool) {
      filteredLocation = location;
    } else {
      filteredLocation = 'United Kingdom';
    }

    if (filteredLocation) {
      jobBoardQueryBuilder = jobBoardQueryBuilder.andWhere('job_boards.joblocationinput = :location', {
        location: filteredLocation,
      });
    }
  }

  return jobBoardQueryBuilder;
};

const refineKeyword = (keyword: string) => {
  return keyword;
  // const result = keyword.replace(/–/g, '').replace(/&/g, '').replace(/-/g, '').replace(/,/g, '').split(' ').join(' ');
  // // .replace(/ /g, ' <-> ');

  // if (!result.includes(' ')) {
  //   return result;
  // }

  // // return `''${result}''`;

  // return `''${result}''`;
};

import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';
import { LeadStatus } from './lead-statuses.entity';
import { CommentEntity } from 'src/modules/comments/entities/comment.entity';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { CrmLeadEntity } from 'src/modules/crm/entities/crm-lead.entity';
import { JobLeadPriorityEnum } from '../enums/job-lead.enum';

@Entity('job_leads')
// @Index(['dataKey'])
export class JobLead {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: false, nullable: true })
  job_lead_external_id: string;

  @Column({ nullable: true })
  job_board_id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  consultant_id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  consultant_name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  company_contact_id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  company_contact_name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  company_id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  company_name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'clean_description', type: 'text', nullable: true })
  cleanDescription: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  title: string;

  @Column({ type: 'numeric', precision: 10, scale: 2, nullable: true })
  salary: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  address_city: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  address_line_1: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  address_line_2: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  address_country_id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  address_country: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  status: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  employment_type: string;

  @Column({ type: 'varchar', length: 100, nullable: true, name: 'job_type', })
  jobType: string;

  @Column({ type: 'boolean', nullable: true })
  is_open: boolean;

  @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  date_added: Date;

  @Column({ type: 'timestamptz', nullable: true  })
  assignee_at: Date;

  @Column({ type: 'numeric', name: 'min_salary', precision: 10, scale: 3, nullable: true })
  minSalary: number;

  @Column({ type: 'numeric', name: 'max_salary', precision: 10, scale: 3, nullable: true })
  maxSalary: number;

  @Column({ nullable: true })
  email: string;

  @ManyToOne(() => LeadStatus, (leadStatus) => leadStatus.jobLeads, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'lead_status_id' })
  lead_status: LeadStatus;

  @Column({ type: 'uuid' })
  lead_status_id: string;

  @Column({ nullable: true, type: 'varchar' })
  dataKey: string;

  @Column({ name: 'updated_by', length: 36, nullable: true })
  updatedBy: string;

  @Column({ name: 'is_done', default: false, nullable: true })
  isDone: boolean;

  @Column({ name: 'assignee_id', nullable: true })
  assigneeId: string;

  @Column({ name: 'creator_id', nullable: true })
  creatorId: string;
  
  @Column({ type: 'text', nullable: true, name: 'logo_company' })
  logoCompany: string;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt: Date;

  // @ManyToOne(() => JobBoard, jb => jb.jobLeads)
  // @JoinColumn({ name: 'job_board_id', referencedColumnName: 'job_id' })
  // jobBoard: JobBoard;

  @OneToMany(() => CommentEntity, (comment) => comment.creator, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'lead_id' })
  comments: CommentEntity[];

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ name: 'assigner_id', nullable: true })
  assignerId: string;

  @ManyToOne(() => UserEntity, (user) => user.myAssigneeJobLeads)
  @JoinColumn({ name: 'assigner_id' })
  assigner: UserEntity;

  @Column({ name: 'is_stared', nullable: true, default: false })
  isStared: boolean;

  @Column({ name: 'crm_lead_id', type: 'uuid', nullable: true })
  crmLeadId: string;

  @ManyToOne(() => CrmLeadEntity, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'crm_lead_id' })
  crmLead: CrmLeadEntity;

  @Column({ name: 'priority_level', nullable: true, default: JobLeadPriorityEnum.MEDIUM })
  priorityLevel: JobLeadPriorityEnum;

  @Column({ name: 'skills', nullable: true, type: 'text', array: true })
  skills: string[];
}

import { UserEntity } from '../../user/entities/user.entity';
import { Entity, PrimaryGeneratedColumn, Column, Index, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ReportedAgencyCountryEntity } from './reported-agency-country.entity';

@Entity('verified_companies')
@Index(['userId'])
export class VerifiedCompanyEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: true, name: 'company_name' })
  companyName: string;

  @Column({ nullable: true, name: 'user_id' })
  userId: string;

  @Column({ nullable: true, name: 'country', default: 'United Kingdom' })
  @ManyToOne(() => ReportedAgencyCountryEntity)
  @JoinColumn({ name: 'country' })
  country: string;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => UserEntity, (user) => user.verifiedCompany)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
} from 'typeorm';

@Entity('job_lead_keywords')
export class JobLeadKeyword {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  keyword: string;

  @Column({ type: 'varchar', length: 100, nullable: true, name: 'user_id' })
  userId: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  country: string;

  @Column({ name: 'count', default: 0 })
  count: number;

  @Column({ type: 'date' })
  date: string;
}

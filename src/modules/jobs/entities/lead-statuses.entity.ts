import { <PERSON>tity, PrimaryGeneratedColumn, Column, OneToMany, ManyToOne, JoinC<PERSON>umn } from 'typeorm';
import { JobLead } from './job-leads.entity';
import { LeadSheetEntity } from './lead-sheet.entity';

export enum LeadStatusType {
  VACANCY = 'VACANCY',
  OPPORTUNITY = 'OPPORTUNITY',
  LEAD = 'LEAD',
}

@Entity('lead_statuses')
export class LeadStatus {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  name: string;

  @Column({ type: 'boolean', name: 'is_deleted', default: false })
  isDeleted: boolean;

  @Column({ type: 'integer', nullable: true, name: 'order_of_display' })
  orderOfDisplay: number;

  @Column({ name: 'organization_id', nullable: true })
  organizationId: string;

  @Column({ nullable: true })
  color: string;

  @Column({ name: 'updated_by', length: 36, nullable: true })
  updatedBy: string;

  @OneToMany(() => JobLead, (jobLead) => jobLead.lead_status, { onDelete: 'CASCADE' })
  jobLeads: JobLead[];

  @Column({ type: 'varchar', array: true, nullable: true, name: 'ordered_companies' })
  orderedCompanies: string[];

  @Column({ name: 'lead_status_type', default: LeadStatusType.VACANCY, nullable: true })
  leadStatusType: LeadStatusType;

  @Column({ name: 'lead_sheet_id', nullable: true })
  leadSheetId: string;

  @ManyToOne(() => LeadSheetEntity, (leadSheet) => leadSheet.leadStatuses)
  @JoinColumn({ name: 'lead_sheet_id' })
  leadSheet: LeadSheetEntity;
}

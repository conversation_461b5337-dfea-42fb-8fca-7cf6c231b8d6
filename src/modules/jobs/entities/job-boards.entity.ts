import { IsNotEmpty } from 'class-validator';
import { Entity, PrimaryColumn, Column, UpdateDateColumn, CreateDateColumn } from 'typeorm';

@Entity('job_boards')
export class JobBoard {
  @PrimaryColumn({ type: 'text' })
  job_id: string;

  @Column({ type: 'text', nullable: true })
  source: string;

  @Column({ type: 'text', nullable: true })
  @IsNotEmpty()
  jobtitle: string;

  @Column({ type: 'text', nullable: true })
  jobtype: string;

  @Column({ type: 'text', nullable: true })
  joblocationcity: string;

  @Column({ type: 'text', nullable: true })
  @IsNotEmpty()
  company: string;

  @Column({ type: 'text', nullable: true })
  companyrating: string;

  @Column({ type: 'text', nullable: true })
  salary: string;

  @Column({ type: 'text', nullable: true })
  link: string;

  @Column({ type: 'timestamptz', nullable: true })
  posted: Date;

  @Column({ type: 'date', nullable: true })
  extracteddate: Date;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'numeric', precision: 20, scale: 3, nullable: true })
  min_salary: number;

  @Column({ type: 'numeric', precision: 20, scale: 3, nullable: true })
  max_salary: number;

  @Column({ type: 'text', nullable: true })
  email: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ type: 'text', nullable: true })
  joblocationinput: string;

  @Column({ type: 'text', nullable: true, name: 'logo_company' })
  logoCompany: string;

  @Column({ type: 'text', nullable: true, name: 'send_to_bullhorn_by_user_ids' })
  sendToBullHornByUserIds: string;

  // @OneToMany(() => JobLead, jl => jl.jobBoard)
  // jobLeads: JobLead[];

  // @Column({ name: 'search_keywords', type: 'text', nullable: true })
  search_keywords: string;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  admin_level_one_area: string;

  country: string;
}

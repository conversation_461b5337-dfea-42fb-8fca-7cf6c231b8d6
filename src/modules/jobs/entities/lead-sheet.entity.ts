import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { LeadStatus, LeadStatusType } from './lead-statuses.entity';

@Entity({ name: 'lead_sheets' })
export class LeadSheetEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'name', type: 'varchar' })
  name: string;

  @Column({ name: 'lead_status_type' })
  leadStatusType: LeadStatusType;

  @Column({ name: 'user_Id' })
  userId: string;

  @OneToMany(() => LeadStatus, (leadStatus) => leadStatus.leadSheet, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ referencedColumnName: 'lead_sheet_id' })
  leadStatuses: LeadStatus[];
}

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  UpdateDateColumn,
  DeleteDateColumn,
  CreateDateColumn,
} from 'typeorm';

export enum StatusSentJobEnum {
  SENT = "SENT",
  NOT_SENT = "NOT_SENT",
}

@Entity('sent_jobs')
export class SentJobEntity {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column({ name: 'job_board_id', nullable: true })
  jobBoardId: string;

  @Column({ name: 'company_name',  type: 'varchar', length: 100, nullable: true })
  companyName: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  title: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  location: string;

  @Column({ type: 'varchar', length: 100, nullable: true, name: "job_type" })
  jobType?: string;

  @Column({ type: 'varchar', length: 255, nullable: true, name: "salary" })
  salary?: string;

  @Column({ type: 'varchar', enum: StatusSentJobEnum, nullable: true })
  status?: string;

  @Column({name: 'date_added', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  dateAdded: Date;

  @Column({ name: 'receiver_id', nullable: true })
  receiverId: string;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt?: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt?: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt?: Date;
}

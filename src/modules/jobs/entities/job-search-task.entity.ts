import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { JobSearchEntity } from './job-search.entity';

@Entity('job_search_tasks')
export class JobSearchTaskEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'keyword', nullable: true })
  keyword: string;

  @Column({ name: 'job_board', nullable: true })
  jobBoard: string;

  @Column({ name: 'admin_level_one_area', nullable: true })
  adminLevelOneArea: string;

  @Column({ name: 'country', nullable: true })
  country: string;

  @Column({ name: 'finished_scraping_all', nullable: true, default: false })
  finishedScrapingAll: boolean;

  @Index()
  @Column({ name: 'task_key', nullable: true })
  taskKey: string;

  @Column({ name: 'latest_rabbitmq_task_id', nullable: true })
  latestRabbitmqTaskId: string;

  @Column({ name: 'finish_time_of_lastest_task', type: 'timestamptz', nullable: true })
  finishTimeOfLastestTask: Date;

  @Column({ name: 'job_search_id', type: 'varchar', length: 100, nullable: true })
  jobSearchId: string;

  @Column({ name: 'is_active', type: 'boolean', default: false })
  isActive: string;

  // @ManyToOne(() => JobSearchEntity, (jobSearch) => jobSearch.jobSearchTasks)
  // @JoinColumn({ name: 'job_search_id' })
  // jobSearch: JobSearchEntity;
}

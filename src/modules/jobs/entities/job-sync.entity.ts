import { <PERSON>ti<PERSON>, PrimaryGeneratedC<PERSON>umn, Column, OneToMany, JoinTable, JoinC<PERSON>umn, ManyToMany, Index } from 'typeorm';
import { JobSearchEntity } from "./job-search.entity";

@Entity('job_sync')
@Index(['dataKey'])
export class JobSync {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'reference_name', nullable: true })
  referenceName: string;

  @Column({ nullable: true })
  dataKey: string;

  @Column({ default: false })
  isDeleted: boolean;

  @Column({name: 'updated_by', length: 36, nullable: true})
  updatedBy: string;

  @ManyToMany(() => JobSearchEntity, jobSearch => jobSearch.jobSyncs, {
    cascade: true
  })
  @JoinTable({
    name: 'job_sync_job_search',
    joinColumn: {
      name: 'job_sync_id',
      referencedColumnName: 'id'
    },
    inverseJoinColumn: {
      name: 'job_search_id',
      referencedColumnName: 'id'
    }
  })
  jobSearches: JobSearchEntity[];
}

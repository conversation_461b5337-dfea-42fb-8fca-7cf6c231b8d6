import { BaseEntity, Column, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum JobLogEnum {
  EDITING_BH_VACANCY_SUBMISSION = 'EDITING_BH_VACANCY_SUBMISSION',
  SENT_TO_BH_VACANCY_SUBMISSION_SUCCESSFULLY = 'SENT_TO_BH_VACANCY_SUBMISSION_SUCCESSFULLY',
}

@Entity('job_logs')
@Index(['jobId'])
export class JobLogEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'job_id', type: 'varchar' })
  jobId: string;

  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({ name: 'type', type: 'varchar' })
  type: JobLogEnum;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date;
}

import { UserEntity } from '../../../modules/user/entities/user.entity';
import { Entity, PrimaryGeneratedColumn, Column, Index, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { ReportedAgencyCountryEntity } from './reported-agency-country.entity';

@Entity('reported_agency')
@Index(['dataKey'])
export class ReportedAgency {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'text', nullable: true, name: 'company_name' })
  companyName: string;

  @Column({ type: 'text', array: true, name: 'alias_company_names', nullable: true })
  aliasCompanyNames: string[];

  @Column({ nullable: true, name: 'data_key' })
  dataKey: string;

  @Column({ nullable: true, name: 'country', default: 'United Kingdom' })
  @ManyToOne(() => ReportedAgencyCountryEntity)
  @JoinColumn({ name: 'country' })
  country: string;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => UserEntity, (user) => user.reportedAgencies)
  @JoinColumn({ name: 'data_key' })
  user: UserEntity;

  @Column({ name: 'status', nullable: true, default: 'APPROVED' })
  status: string;
}

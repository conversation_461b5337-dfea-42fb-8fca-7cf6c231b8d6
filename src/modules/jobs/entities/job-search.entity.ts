import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
  BaseEntity,
  Index,
  OneToMany,
  JoinColumn,
  UpdateDateColumn,
} from 'typeorm';
import { JobSync } from './job-sync.entity';
import { UserTrackingEntity } from '../../../modules/user-tracking/entities/user-tracking.entity';
import { JobSearchTaskEntity } from './job-search-task.entity';

@Entity('job_search')
@Index(['dataKey'])
export class JobSearchEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: false, name: 'search_name' })
  searchName: string;

  @Column({ type: 'varchar', length: 1024, nullable: true, name: 'keywords' })
  keywords: string;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'location' })
  location: string;

  @Column({ type: 'date', nullable: true, name: 'posted_start_date' })
  postedStartDate: Date;

  @Column({ type: 'date', nullable: true, name: 'posted_end_date' })
  postedEndDate: Date;

  @Column({ type: 'numeric', nullable: true, name: 'min_salary' })
  minSalary: number;

  @Column({ type: 'numeric', nullable: true, name: 'max_salary' })
  maxSalary: number;

  @Column({ type: 'varchar', length: 1024, nullable: true, name: 'job_boards' })
  jobBoards: string;

  @Column({ type: 'int', nullable: true, name: 'counts', default: 0 })
  counts: number;

  @Column({ type: 'boolean', nullable: false, default: false, name: 'is_deleted' })
  isDeleted: boolean;

  //when stable => active_subprocess_id ===> active_subprocess_ids
  @Column({ type: 'varchar', array: true, nullable: true, name: 'active_subprocess_id' })
  activeSubprocessIds: string[];

  @Column({ nullable: true })
  dataKey: string;

  @ManyToMany(() => JobSync, (jobSync) => jobSync.jobSearches)
  jobSyncs: JobSync[];

  @Column({ nullable: true })
  pinnedJobIds: string;

  @Column({ nullable: true })
  deletedJobIds: string;

  @Column({ name: 'stop_scaping_at', nullable: true, default: null })
  stopScapingAt: Date;

  @Column({ name: 'admin_level_one_area', nullable: true })
  adminLevelOneArea: string;

  @Column({ name: 'country', nullable: false, default: 'United Kingdom' })
  country: string;

  @Column({ name: 'job_titles', nullable: true, type: 'text', array: true })
  jobTitles: string[];

  @Column({ name: 'date_posted', nullable: true })
  datePosted: string;

  @Column({ name: 'updated_by', length: 36, nullable: true })
  updatedBy: string;

  @Column({ name: 'stop_by', nullable: true })
  stopBy: string;

  @Column({ name: 'is_group_by_company', default: false })
  isGroupByCompany: boolean;

  @Column({ name: 'excluded_companies', nullable: true, type: 'text', array: true })
  excludeCompanies: string[];

  @Column({ name: 'excluded_keywords', nullable: true, type: 'text', array: true })
  excludeKeywords: string[];

  @Column({ name: 'excluded_titles', nullable: true, type: 'text', array: true })
  excludeTitles: string[];

  @OneToMany(() => UserTrackingEntity, (userTracking) => userTracking.jobSearch, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'user_id' })
  userTrackings: UserTrackingEntity[];

  // @OneToMany(() => JobSearchTaskEntity, (jobSearchTask) => jobSearchTask.jobSearch, {
  //   cascade: true,
  // })
  // @JoinColumn({ referencedColumnName: 'job_search_id' })
  // jobSearchTasks: JobSearchTaskEntity[];

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'is_refine_by_search', default: false })
  isRefineBySearch: boolean;

  @Column({ name: 'latest_refined_job_date', nullable: true })
  latestRefinedJobDate: Date;

  @Column({ name: 'oldest_refined_job_date', nullable: true })
  oldestRefinedJobDate: Date;

  @Column({ name: 'is_scan_oldest', default: false })
  isScanOldest: boolean;
}

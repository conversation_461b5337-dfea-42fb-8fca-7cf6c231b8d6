import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, MinLength } from 'class-validator';

export class UpdateReportedAgencyDto {
  @ApiPropertyOptional()
  @IsOptional()
  aliasCompanyNames?: string[];
}

class BulkUpdateItem extends UpdateReportedAgencyDto {
  @ApiProperty()
  @IsNotEmpty()
  id: string;
}

export class BulkUpdateReportedAgencyDto {
  values: BulkUpdateItem[];
}
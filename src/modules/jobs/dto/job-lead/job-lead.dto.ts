import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { JobLeadPriorityEnum } from '../../enums/job-lead.enum';

export class UpdateLeadDto {
  @ApiPropertyOptional()
  @IsOptional()
  assigneeId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  creatorId: string;

  @ApiProperty()
  @IsNotEmpty()
  updatedBy: string;
}


export class CoreJobLead {
  @ApiPropertyOptional()
  address_city?: string;

  @ApiPropertyOptional()
  address_country?: string;

  @ApiPropertyOptional()
  address_line_1?: string;

  @ApiPropertyOptional()
  address_line_2?: string;

  @ApiPropertyOptional()
  company_name?: string;

  @ApiPropertyOptional()
  employment_type?: string;

  @ApiPropertyOptional()
  is_open?: boolean;

  @ApiPropertyOptional()
  salary?: number;

  @ApiPropertyOptional()
  title?: string;

  owner?: {
    firstName: string;
    fullName: string;
    email: string;
  };
}

export class StaredJobLeadDto {
  @ApiProperty()
  @IsNotEmpty()
  star: boolean;
}

export class UpdatePriorityDto {
  @ApiPropertyOptional()
  leadIds?: string[];

  @ApiPropertyOptional()
  companyNames?: string[];

  @ApiProperty()
  @IsNotEmpty()
  priorityLevel: JobLeadPriorityEnum;
}
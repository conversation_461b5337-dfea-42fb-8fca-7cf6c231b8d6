import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class JobLeadStatsDto {
  @ApiPropertyOptional()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  country?: string;

  @ApiPropertyOptional()
  @IsOptional()
  fromDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  toDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  action?: string;
};

export class LinkedinMetricDto {
  @ApiPropertyOptional()
  @IsOptional()
  action?: string;

  @ApiPropertyOptional()
  @IsOptional()
  fromDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  toDate?: Date;
};


export class JobLeadKeywordDto extends JobLeadStatsDto {
  @ApiPropertyOptional()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional()
  @IsOptional()
  offset?: number;

  @ApiPropertyOptional()
  @IsOptional()
  page?: number;
};

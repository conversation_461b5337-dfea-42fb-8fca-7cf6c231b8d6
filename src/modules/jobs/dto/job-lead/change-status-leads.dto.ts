import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsOptional } from "class-validator";

export class ChangeStatusLeadsDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsArray()
    ids: string[]

    @ApiProperty()
    @IsNotEmpty()
    leadStatusId: string
}



export class ChangeStatusLeadsByNameDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    ids?: string[]

    @ApiProperty()
    @IsNotEmpty()
    leadStatusName: string

    @ApiPropertyOptional()
    @IsOptional()
    leadSheetName?: string

    @ApiPropertyOptional()
    @IsOptional()
    @IsArray()
    jobIds?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    sequenceId?: string
}
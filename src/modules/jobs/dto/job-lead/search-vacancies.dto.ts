import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsOptional,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
  buildMessage,
} from 'class-validator';

const IsValidDateRange = (validationOptions?: ValidationOptions) => {
  return (object: Object, propertyName: string) => {
    registerDecorator({
      name: 'isValidDateRange',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [start, end] = value;
          return start <= end;
        },
        defaultMessage: buildMessage(
          (eachPrefix, args) =>
            `The start date ${args.value[0]} must be less than or equal to the end date ${args.value[1]}`,
          validationOptions,
        ),
      },
    });
  };
};
export class SearchVacanciesDto {
  @ApiPropertyOptional()
  @IsOptional()
  companyIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional()
  @IsOptional()
  required?: string;

  @ApiPropertyOptional()
  @IsOptional()
  excluded?: string;

  @ApiPropertyOptional()
  @IsOptional()
  start?: number;

  @ApiPropertyOptional()
  @IsOptional()
  count?: number;

  @ApiPropertyOptional()
  @IsOptional()
  datePostedFrom?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  datePostedTo?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  datePosted?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(2)
  @ArrayMaxSize(2)
  @IsValidDateRange()
  inRange?: Date[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ArrayMinSize(2)
  @ArrayMaxSize(2)
  @IsValidDateRange()
  outRange?: Date[];

  @ApiPropertyOptional()
  @IsOptional()
  salary?: number;

  @ApiPropertyOptional()
  @IsOptional()
  minSalary?: number;

  @ApiPropertyOptional()
  @IsOptional()
  maxSalary?: number;

  @ApiPropertyOptional()
  @IsOptional()
  statusId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  source?: string;

  @ApiPropertyOptional()
  @IsOptional()
  jobType?: string;
}

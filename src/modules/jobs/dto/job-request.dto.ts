import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { BaseUpdateDto } from 'src/common/dto/update.dto';
import { convertToArray } from 'src/common/utils/helpers.util';

export class CreateJobSearchDto extends BaseUpdateDto {
  @IsOptional()
  searchName: string;

  @IsOptional()
  keywords: string[];

  @IsOptional()
  location: string;

  @IsOptional()
  @IsArray()
  postedWithin: string[];

  @IsOptional()
  minSalary: string;

  @IsOptional()
  maxSalary: string;

  @IsOptional()
  jobBoards: string[];

  @ApiPropertyOptional()
  @IsOptional()
  adminLevelOneArea?: string;

  @ApiProperty()
  @IsNotEmpty()
  country: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => (!value ? undefined : convertToArray(value)))
  jobTitles?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  datePosted?: string;
}

export class CreateSyncRequestDto extends BaseUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  searchIds: string[];
}

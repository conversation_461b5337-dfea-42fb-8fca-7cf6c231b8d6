import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsOptional, IsString } from "class-validator";

export enum SortOrderEnum {
    ASC = 'ASC',
    DESC = 'DESC',
}

export enum StatusEnum {
    APPROVED = 'APPROVED',
    PENDING = 'PENDING',
    REJECTED = 'REJECTED',
}

export class GetReportedAgencyQuery {
    @ApiPropertyOptional()
    @IsOptional()
    page?: number;

    @ApiPropertyOptional()
    @IsOptional()
    limit?: number;

    @IsOptional()
    @IsString()
    searchName?: string;

    @IsOptional()
    @IsEnum(SortOrderEnum)
    sortOrder?: SortOrderEnum;
    
    @IsOptional()
    @IsEnum(StatusEnum)
    status?: StatusEnum;
}
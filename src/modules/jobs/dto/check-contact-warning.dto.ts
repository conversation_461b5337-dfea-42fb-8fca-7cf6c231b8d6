import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional } from "class-validator";
import { BullhornEntityEnum } from "../enums/bullhorn.enum";

export class ContactWarningDto {
    @ApiPropertyOptional()
    @IsOptional()
    emails?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    tearsheetIds?: string[];
}

export class DeleteBHEntityDto {
    @ApiProperty()
    @IsEnum(BullhornEntityEnum)
    entity: BullhornEntityEnum;

    @ApiProperty()
    @IsNotEmpty()
    id: string;

    @ApiPropertyOptional()
    @IsOptional()
    parentId: string;
}

export class updateMassAdvanceDto {
    @ApiProperty()
    @IsEnum(BullhornEntityEnum)
    entity: BullhornEntityEnum;

    @ApiProperty()
    @IsNotEmpty()
    ids: string;

    @ApiProperty()
    @IsNotEmpty()
    comments: string;

    @ApiProperty()
    @IsNotEmpty()
    status: string;
}

export class searchLookupDataDto {
    @ApiProperty()
    @IsEnum(BullhornEntityEnum)
    entity: BullhornEntityEnum;

    @ApiProperty()
    @IsNotEmpty()
    start: number;

    @ApiProperty()
    @IsNotEmpty()
    filter: string;
}


export class InsertMassAdvanceDto {
    @ApiProperty()
    @IsEnum(BullhornEntityEnum)
    entity: BullhornEntityEnum;

    @ApiProperty()
    @IsNotEmpty()
    data: any;
}

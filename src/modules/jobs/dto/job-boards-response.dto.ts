import { ApiProperty } from '@nestjs/swagger';

export class JobBoardResponseDto {
  @ApiProperty()
  job_id: string;

  @ApiProperty()
  source: string;

  @ApiProperty()
  jobtitle: string;

  @ApiProperty()
  jobtype: string;

  @ApiProperty()
  joblocationcity: string;

  @ApiProperty()
  company: string;

  @ApiProperty()
  companyrating: string;

  @ApiProperty()
  salary: string;

  @ApiProperty()
  link: string;

  @ApiProperty()
  posted: Date;

  @ApiProperty()
  extracteddate: Date;

  @ApiProperty()
  description: string;

  @ApiProperty()
  min_salary: number;

  @ApiProperty()
  max_salary: number;

  @ApiProperty()
  email: string;

  @ApiProperty()
  address: string;

  @ApiProperty({default: false})
  isPinned: boolean;

  @ApiProperty()
  logoCompany: string;

  @ApiProperty()
  jobLogs: string;
}

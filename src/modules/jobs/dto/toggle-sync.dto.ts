import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty } from "class-validator";
import { BaseUpdateDto } from "src/common/dto/update.dto";

export enum ToggleEnum {
    ON = 'ON',
    OFF = 'OFF'
}

export class ToggleSyncRequestDto extends BaseUpdateDto {
    @ApiProperty()
    @IsNotEmpty()
    jobSearchId: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsEnum(ToggleEnum)
    toggle: ToggleEnum;
}
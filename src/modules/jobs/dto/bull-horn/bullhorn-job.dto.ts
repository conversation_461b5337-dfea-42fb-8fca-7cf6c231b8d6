import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { IApolloContactDto } from 'src/modules/employee-finder/dto/employee-finder.dto';

export class BullhornSubmission {
  @ApiProperty()
  @IsNotEmpty()
  entityName: string;

  @ApiPropertyOptional()
  @IsOptional()
  rawInformation?: IApolloContactDto;
}

export class BullhornSubmissionTotal {
  @ApiProperty()
  @IsNotEmpty()
  entityName: string;

  @ApiProperty()
  searchName: string;

  @ApiPropertyOptional()
  @IsNotEmpty()
  id: string;

  @IsOptional()
  customSearchField?: string;

  @IsOptional()
  customSearchValue?: string;

  @IsOptional()
  optionCustom?: string;

  @IsOptional()
  typeCustom?: string;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional, MaxLength, ValidateNested } from 'class-validator';
import { SendEmailDto } from 'src/modules/mail/dto/send-email.dto';
import { JobBoardResponseDto } from '../job-boards-response.dto';

export class BHUploadFileDto {
  @IsNotEmpty()
  @ApiProperty()
  entityName: string;

  @IsNotEmpty()
  @ApiProperty()
  id: string;

  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @ApiProperty()
  fileType: string;

  @IsNotEmpty()
  @ApiProperty()
  fileSize: string;

  @IsNotEmpty()
  @ApiProperty()
  distribution: string;
}

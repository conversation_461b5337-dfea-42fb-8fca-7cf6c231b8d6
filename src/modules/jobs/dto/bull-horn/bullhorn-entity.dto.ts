import { ApiProperty } from '@nestjs/swagger';
import { BullhornEntityEnum } from '../../enums/bullhorn.enum';
import { IsEnum, IsNotEmpty } from 'class-validator';

export class MassUpdateDto {
  @ApiProperty()
  @IsEnum(BullhornEntityEnum)
  entityName: BullhornEntityEnum;

  @ApiProperty({ description: 'Ref here: https://bullhorn.github.io/rest-api-docs/#post-massupdate-entitytype' })
  @IsNotEmpty()
  updatedPayload: object;
}

export class BullhornGuessDto{
  @ApiProperty()
  @IsNotEmpty()
  bhClientId: string;

  @ApiProperty()
  @IsNotEmpty()
  bhUsername: string;

  @ApiProperty()
  @IsNotEmpty()
  bhPassword: string;

  @ApiProperty()
  @IsNotEmpty()
  bhClientSecret: string;
}
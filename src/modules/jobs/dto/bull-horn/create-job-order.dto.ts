//TODO: add job order fields later; this now stores clientCorporation and clientContact
//TODO: not used yet, just use all data from client first

export class CreateClientCorporationDto {
  name: string;
  address: {
    address1: string;
    address2: string;
    city: string;
    countryCode: string;
    countryID: number;
    countryName: string;
    state: string;
    timezone: string;
    zip: string;
  };
  fax: string;
  phone: string;
  status: string;
  parentClientCorporation: string;
  owners: {
    total: number;
    data: {
      id: number;
      firstName: string;
      lastName: string;
    }[];
  };
  industryList: any;
  companyDescription: string;
  companyURL: string;
  billingAddress: {
    address1: string;
    address2: string;
    city: string;
    state: string;
    zip: null;
    countryID: number;
    countryName: string;
    countryCode: string;
  };
  billingContact: string;
  billingPhone: string;
  billingFrequency: any;
  invoiceFormat: string;
  feeArrangement: number;
}

// export class CreateJobOrderDto {
//   clientCorporation:
// }

import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class GetJobSearchListQuery {
    @ApiPropertyOptional()
    @IsOptional()
    sortField?: string;

    @ApiPropertyOptional()
    @IsOptional()
    sortOrder?: string;

    @ApiPropertyOptional()
    @IsOptional()
    page?: number;

    @ApiPropertyOptional()
    @IsOptional()
    limit?: number;

    @ApiPropertyOptional()
    @IsOptional()
    searchText?: string;

    @ApiPropertyOptional()
    @IsOptional()
    isGettingActiveSearch?: boolean;
}
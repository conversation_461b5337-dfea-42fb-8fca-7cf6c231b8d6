import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { JobLogEnum } from '../../entities/job-log.entity';

export class JobLogRequestDto {
  //TODO: update later, jobId should be required
  //change to optional because hot-fix to manual lead creation
  @ApiPropertyOptional()
  @IsOptional()
  jobId?: string;
}

export class UpdateJobLogDto extends JobLogRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  updatedFor?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(JobLogEnum)
  type?: JobLogEnum;
}

export class DeleteJobLogDto extends JobLogRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  updatedFor?: string;
}

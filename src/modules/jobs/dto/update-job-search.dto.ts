import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsArray, IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { BaseUpdateDto } from 'src/common/dto/update.dto';
import { convertToArray } from 'src/common/utils/helpers.util';

export class UpdateJobSearchDto extends BaseUpdateDto {
  @IsOptional()
  searchName: string;

  @IsOptional()
  keywords: string[];

  @IsOptional()
  location: string;

  @IsOptional()
  @IsArray()
  postedWithin: string[];

  @IsOptional()
  minSalary: number;

  @IsOptional()
  maxSalary: number;

  @IsOptional()
  jobBoards: string[];

  @ApiPropertyOptional()
  @IsOptional()
  adminLevelOneArea?: string;

  @ApiPropertyOptional()
  @IsOptional()
  country?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => (!value ? undefined : convertToArray(value)))
  jobTitles?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  datePosted?: string;
}

export enum SetGroupByCompanyEnum {
  ON = 'ON',
  OFF = 'OFF',
}
export class SetGroupByCompanyRequestDto {
  @IsNotEmpty()
  @IsEnum(SetGroupByCompanyEnum)
  status: SetGroupByCompanyEnum;
}

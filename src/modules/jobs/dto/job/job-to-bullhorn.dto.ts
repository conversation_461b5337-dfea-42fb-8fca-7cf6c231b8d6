import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional, MaxLength, ValidateNested } from 'class-validator';
import { SendEmailDto } from 'src/modules/mail/dto/send-email.dto';
import { JobBoardResponseDto } from '../job-boards-response.dto';
import { Type } from 'class-transformer';

export class TagItemDto {
  @ApiProperty()
  @IsNotEmpty()
  label: string;

  @ApiProperty()
  @IsNotEmpty()
  key: string;

  @ApiProperty()
  @IsNotEmpty()
  value: string;
}

class JobDto {
  @ApiProperty()
  job_id: string;

  @ApiPropertyOptional()
  @IsOptional()
  source?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  jobtitle?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  jobtype?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  joblocationcity?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  company?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  companyrating?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  salary?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  link?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  posted?: Date; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  extracteddate?: Date; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  description?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  min_salary?: number; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  max_salary?: number; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  email?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  address?: string; // consider removing this field in the future

  @ApiPropertyOptional({ default: false })
  @IsOptional()
  isPinned?: boolean; // consider removing this field in the future

  @ApiProperty()
  logoCompany: string;

  @ApiPropertyOptional()
  @IsOptional()
  jobLogs?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  skills?: string[]; 
}

export class BulkImportJobDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SendJobToBullhornDto)
  data: SendJobToBullhornDto[];
}

export class SendJobToBullhornDto {
  @ApiPropertyOptional()
  @IsOptional()
  source: string;

  @ApiPropertyOptional()
  @IsOptional()
  sentJobId: string;

  @ApiPropertyOptional()
  @IsOptional()
  bullHornJobId: string;

  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(100)
  jobtitle: string;

  @ApiPropertyOptional()
  @IsOptional()
  jobtype: string;

  @ApiPropertyOptional()
  @IsOptional()
  joblocationcity?: string; // consider removing this field in the future

  @ApiPropertyOptional()
  @IsOptional()
  stateId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  clientContact: object;

  @ApiProperty()
  @IsOptional()
  companyDetail?: any;

  @ApiProperty()
  @IsNotEmpty()
  companyId: string;

  @ApiProperty()
  @IsNotEmpty()
  company: string;

  @ApiPropertyOptional()
  @IsOptional()
  sendEmail?: SendEmailDto;

  @ApiPropertyOptional()
  @IsOptional()
  job?: JobDto;

  @ApiPropertyOptional()
  @IsOptional()
  searchId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  isNotSendSequence: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  cleanDescription?: string;

  @ApiPropertyOptional()
  @IsOptional()
  publishedDescription?: string;

  @ApiPropertyOptional()
  @IsOptional()
  numOpenings?: number;

  @ApiPropertyOptional()
  @IsOptional()
  permFee?: number;

  @ApiPropertyOptional()
  @IsOptional()
  salary?: string;

  @ApiPropertyOptional()
  @IsOptional()
  max_salary?: string;

  @ApiPropertyOptional()
  @IsOptional()
  min_salary?: string;

  @ApiPropertyOptional()
  @IsOptional()
  payRate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  currencyUnit?: string;

  @ApiPropertyOptional()
  @IsOptional()
  address1?: string;

  @ApiPropertyOptional()
  @IsOptional()
  address2?: string;

  @ApiPropertyOptional()
  @IsOptional()
  city?: string;

  @ApiPropertyOptional()
  @IsOptional()
  county?: string;

  @ApiPropertyOptional()
  @IsOptional()
  zip?: string;

  @ApiPropertyOptional()
  @IsOptional()
  clientCorporation?: string;

  @ApiPropertyOptional()
  @IsOptional()
  markUpPercentage?: string;

  @ApiPropertyOptional()
  @IsOptional()
  clientBillRate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  scheduledEndDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  startDate?: string;

  @ApiProperty()
  @IsNotEmpty()
  updatedFor: string;

  @ApiPropertyOptional()
  @IsOptional()
  leadStatusId?: string;

  @ApiProperty()
  @IsNotEmpty()
  leadSheetId: string;

  @IsOptional()
  crmLeadId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  salaryUnit?: string;
}

export class ConnectedUserJobDto extends SendJobToBullhornDto {
  @ApiProperty()
  @IsNotEmpty()
  status: string;

  @ApiProperty()
  @IsNotEmpty()
  contactId: string;

  @ApiProperty()
  @IsNotEmpty()
  consultantId: string;

  @ApiProperty()
  @IsNotEmpty()
  contact: string;

  @ApiProperty()
  @IsNotEmpty()
  countryAddress: string;

  @ApiPropertyOptional()
  @IsOptional()
  employmentType: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TagItemDto)
  skills: TagItemDto[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TagItemDto)
  categories: TagItemDto[];

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TagItemDto)
  businessSectors?: TagItemDto[];
}

export class StandardUserJobDto extends SendJobToBullhornDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  skills?: [];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  industries?: [];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  tags?: [];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  jobskills?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  website: string;

  @ApiPropertyOptional()
  @IsOptional()
  address: any;
}

class BHList {
  @IsNotEmpty()
  replaceAll: string[];
}

class BHAddress {
  @IsOptional()
  address1: string;

  @IsOptional()
  address2: string;

  @IsOptional()
  city: string;

  @IsOptional()
  state: string;

  @IsOptional()
  timezone: string;

  @IsOptional()
  zip: string;

  @IsOptional()
  countryID: string | number;

  @IsOptional()
  crmLeadId?: string;
}

export class BHJobDataDto {
  @IsOptional()
  title: string;

  @IsOptional()
  clientContact: object;

  @IsOptional()
  email: string;

  @IsOptional()
  clientCorporation: object;

  @IsOptional()
  skills: BHList;

  @IsOptional()
  businessSectors: BHList;

  @IsOptional()
  categories: BHList;

  @IsOptional()
  owner: object;

  @IsOptional()
  address: BHAddress;

  @IsOptional()
  clientBillRate: string;

  @IsOptional()
  markUpPercentage: string;

  @IsOptional()
  payRate: string;

  @IsOptional()
  description: string;

  @IsOptional()
  salary: number;

  @IsOptional()
  source: string;

  @IsOptional()
  employmentType: string;

  @IsOptional()
  status: string;

  @IsOptional()
  startDate: number;

  @IsOptional()
  dateEnd: number;

  @IsOptional()
  currencyUnit: string;

  @IsOptional()
  feeArrangement: number;
}

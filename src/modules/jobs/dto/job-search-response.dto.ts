import { ApiProperty } from '@nestjs/swagger';
import {Column, PrimaryGeneratedColumn} from "typeorm";

export class JobSearchResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  searchName: string;

  @ApiProperty()
  keywords: string;

  @ApiProperty()
  location: string;

  @ApiProperty()
  postedStartDate: Date;

  @ApiProperty()
  postedEndDate: Date;

  @ApiProperty()
  minSalary: number;

  @ApiProperty()
  maxSalary: number;

  @ApiProperty()
  jobBoards: string;

  @ApiProperty()
  counts: number;

  @ApiProperty()
  activeSubprocessId: string;

  @ApiProperty()
  adminLevelOneArea: string;

  @ApiProperty()
  country: string;
}

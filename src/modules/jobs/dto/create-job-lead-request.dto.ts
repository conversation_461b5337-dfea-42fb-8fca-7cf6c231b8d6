// create-job-lead.dto.ts

import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, Validate, ValidateNested } from 'class-validator';
import { BaseUpdateDto } from 'src/common/dto/update.dto';
import { JobLeadPriorityEnum } from '../enums/job-lead.enum';

export class CreateJobLeadDto extends BaseUpdateDto {
  @IsOptional()
  job_lead_external_id: string;

  @IsOptional()
  job_board_id?: string;

  @IsOptional()
  consultant_id: string;

  @IsOptional()
  consultant_name: string;

  @IsOptional()
  company_id: string;

  @IsOptional()
  company_name: string;

  @IsOptional()
  company_contact_id: string;

  @IsOptional()
  company_contact_name: string;

  @IsNotEmpty()
  description: string;

  @IsOptional()
  cleanDescription: string;

  @IsNotEmpty()
  title: string;

  @IsOptional()
  salary: number;

  @IsOptional()
  status: string;

  @IsNotEmpty()
  employment_type: string;

  @IsNotEmpty()
  address_city: string;

  @IsOptional()
  dataKey: string;

  @IsOptional()
  lead_status_id: string;

  @IsOptional()
  minSalary?: number;

  @IsOptional()
  maxSalary?: number;

  @IsOptional()
  email?: string;

  @IsOptional()
  creatorId: string;

  logoCompany?: string;

  @IsOptional()
  jobType: string;

  @IsOptional()
  leadSheetId?: string;

  @IsOptional()
  address_country: string;

  @IsOptional()
  crmLeadId?: string;

  @IsOptional()
  priorityLevel?: JobLeadPriorityEnum;

  @IsOptional()
  skills?: string[];
}

export class BulkCreateJobLeadRequestDto {
  @ApiProperty({
    type: [CreateJobLeadDto],
    description: 'Array of job leads to be created',
  })
  @IsNotEmpty()
  @Type(() => CreateJobLeadDto)
  @ValidateNested({ each: true })
  data: CreateJobLeadDto[];
}
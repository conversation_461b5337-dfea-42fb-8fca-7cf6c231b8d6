import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';

export class CreateReportedAgencyDto {
  @ApiProperty()
  @IsNotEmpty()
  companyName: string;

  @ApiPropertyOptional()
  @IsOptional()
  aliasCompanyNames?: string[];
}

export class BulkCreateReportedAgencyDto {
  @IsNotEmpty()
  @Type(() => CreateReportedAgencyDto)
  @ValidateNested({ each: true })
  agencies: CreateReportedAgencyDto[];
}

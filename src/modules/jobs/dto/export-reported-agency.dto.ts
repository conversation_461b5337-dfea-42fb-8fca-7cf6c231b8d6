import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export enum AgencyType {
  GPT = 'GPT',
  USER = 'USER',
}

export enum CSVDelimiter {
  COMMA = 'COMMA',
  SEMICOLON = 'SEMICOLON',
}

export class FilterExportReportedAgencyDto {
  @ApiPropertyOptional()
  @IsOptional()
  agencyType?: AgencyType;

  @ApiPropertyOptional()
  @IsOptional()
  fromDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  toDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  delimiter: CSVDelimiter;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { BullhornEntityEnum } from '../enums/bullhorn.enum';

export class GetCorporateUserQueryDto {
  @IsOptional()
  query?: string;

  @IsOptional()
  start?: string | number;

  @IsOptional()
  count?: string | number;

  @IsOptional()
  entityName?: string;

  @IsOptional()
  clientCorporationId?: number;

  @IsOptional()
  queryId?: string;

  @IsOptional()
  email?: string;

  @IsOptional()
  fastSearch?: boolean;

  @IsOptional()
  tearSheetId?: string;

  @IsOptional()
  fromContactFinder?: boolean;

  @IsOptional()
  customSearchField?: string;

  @IsOptional()
  customSearchValue?: string;

  @IsOptional()
  optionCustom?: string;

  @IsOptional()
  typeCustom?: string;

  @IsOptional()
  vacancyId?: string;

  @IsOptional()
  companyId?: string;

  @IsOptional()
  fields?: string;
}

export class SearchCommonBullhornDto {
  @ApiProperty()
  @IsEnum(BullhornEntityEnum)
  entityName: BullhornEntityEnum;

  @ApiProperty()
  @IsNotEmpty()
  where: string;

  @ApiProperty({ example: 'id,name' })
  @IsNotEmpty()
  fields: string;

  @ApiPropertyOptional({ example: '-dateAdded' })
  @IsOptional()
  sort?: string;

  @ApiPropertyOptional()
  @IsOptional()
  count?: string;

  @ApiPropertyOptional()
  @IsOptional()
  start?: string;

  @ApiPropertyOptional()
  @IsOptional()
  orderBy?: string;

  @ApiPropertyOptional()
  @IsOptional()
  meta?: string;

  @ApiPropertyOptional()
  @IsOptional()
  showTotalMatched?: boolean;
}

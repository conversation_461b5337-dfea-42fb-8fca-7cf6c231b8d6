import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { LeadStatusType } from '../../entities/lead-statuses.entity';

export class UpdateLeadSheetDto {
  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  userId: string;
}

export class CreateLeadSheetDto extends UpdateLeadSheetDto {
  @ApiProperty()
  @IsEnum(LeadStatusType)
  leadStatusType: LeadStatusType;

  @ApiProperty()
  @IsNotEmpty()
  updateFor: string;
}

export class GetLeadSheetQueryDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(LeadStatusType)
  leadStatusType?: LeadStatusType;

  @ApiProperty()
  @IsNotEmpty()
  userId: string;
}

import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsEnum, IsOptional, ValidateIf } from 'class-validator';
import { convertToArray } from 'src/common/utils/helpers.util';

export enum IncludeSentJobEnum {
  SENT = 'SENT',
  NOT_SENT = 'NOT_SENT',
}

export enum SortJobEnum {
  ASC = 'ASC',
  DESC = 'DESC',
}
export class JobBySearchIdQuery {
  @ApiPropertyOptional()
  @IsOptional()
  exclusiveStartKey?: string;

  @ApiPropertyOptional()
  @IsOptional()
  sort?: SortJobEnum;

  @ApiPropertyOptional()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @IsOptional()
  limit: number;

  @ApiPropertyOptional()
  @IsOptional()
  searchText?: string;

  @ApiPropertyOptional()
  keywords?: string;

  @ApiPropertyOptional()
  @IsOptional()
  location?: string;

  @ApiPropertyOptional()
  @IsOptional()
  jobBoards?: string;

  @ApiPropertyOptional()
  @IsOptional()
  maxSalary?: number | '';

  @ApiPropertyOptional()
  @IsOptional()
  minSalary?: number | '';

  @ApiPropertyOptional()
  @IsOptional()
  postedStartDate?: Date | '';

  @ApiPropertyOptional()
  @IsOptional()
  postedEndDate?: Date | '';

  @ApiPropertyOptional()
  @IsOptional()
  newJobOnly?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  notificationId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => (!value ? undefined : convertToArray(value)))
  companies?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => (!value ? undefined : convertToArray(value)))
  jobTitles?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => value && value === 'true')
  getAll?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((obj) => !!obj.includeSentJob)
  @IsEnum(IncludeSentJobEnum)
  includeSentJob?: IncludeSentJobEnum;

  @ApiPropertyOptional()
  @IsOptional()
  excludeCompanies?: string[]

  @ApiPropertyOptional()
  @IsOptional()
  excludeKeywords?: string[]

  @ApiPropertyOptional()
  @IsOptional()
  excludeTitles?: string[]

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isSavedExclude?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(['keyword', 'knn'])
  searchMode?: 'keyword' | 'knn';

  getRefinedJobs?: boolean;

  fromCreatedAt?: Date;

  toCreatedAt?: Date;
}

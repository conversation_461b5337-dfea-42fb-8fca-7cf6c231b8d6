import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { BaseUpdateDto } from 'src/common/dto/update.dto';

export class ChangeJobLeadStatusDto extends BaseUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  newStatusId: string;

  @ApiProperty()
  @IsNotEmpty()
  jobLeadId: string;

  @ApiProperty()
  @IsOptional()
  isUpdateMailBox?: string;
}

export class ChangeLeadStatusByCompanyDto {
  @ApiProperty()
  @IsNotEmpty()
  newStatusId: string;

  @ApiPropertyOptional()
  @IsOptional()
  oldStatusId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  oldStatusCompanyIdsOrder?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  newStatusCompanyIdsOrder?: string[];

  @ApiProperty()
  @IsNotEmpty()
  companyId: string;

  @ApiPropertyOptional()
  @IsOptional()
  updatedFor?: string;
}

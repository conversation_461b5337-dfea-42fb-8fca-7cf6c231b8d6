import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMinSize, ArrayNotEmpty, IsArray, IsEnum, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { BaseUpdateDto } from 'src/common/dto/update.dto';
import { LeadStatusType } from '../entities/lead-statuses.entity';

export class BulkUpdateItemRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  isDeleted?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  isNew?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  id?: string;

  @ApiPropertyOptional()
  @IsOptional()
  orderOfDisplay?: number;

  @ApiPropertyOptional()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  color?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(LeadStatusType)
  leadStatusType?: LeadStatusType;

  @ApiPropertyOptional()
  @IsOptional()
  leadSheetId?: string;
}

export class BulkUpdateRequestDto extends BaseUpdateDto {
  @ApiProperty()
  @IsNotEmpty()
  @Type(() => BulkUpdateItemRequestDto)
  @ValidateNested({ each: true })
  data: BulkUpdateItemRequestDto[];
}

export class BulkDeleteSearchDto {
  @ApiProperty()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  searchIds: string[];
}

import {
  Controller,
  Get,
  Delete,
  Param,
  Body,
  Put,
  UseGuards,
  Req,
  Query,
  Patch,
  Post,
} from '@nestjs/common';
import { JobLeadsService } from '../service/job-leads.service';
import {
  ChangeJobLeadStatusDto,
  ChangeLeadStatusByCompanyDto,
} from '../dto/change-job-lead-status-request.dto';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { ApiTags } from '@nestjs/swagger';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { PermissionGuard } from 'src/guards/permission.guard';
import { LeadQueryDto } from '../dto/job-lead/lead-query.dto';
import { StaredJobLeadDto, UpdateLeadDto, UpdatePriorityDto } from '../dto/job-lead/job-lead.dto';
import { CreateJobSentDto } from '../dto/create-job-sent.dto';
import {
  ChangeStatusLeadsByNameDto,
  ChangeStatusLeadsDto,
} from '../dto/job-lead/change-status-leads.dto';
import { DeleteJobLeadsDto } from '../dto/job-lead/delete-leads.dto';
import { SearchVacanciesDto } from '../dto/job-lead/search-vacancies.dto';
import { SimilarJobDto } from '../dto/similar-job.dto';
import { JobLeadKeywordDto, JobLeadStatsDto } from '../dto/job-lead/job-lead-stats.dto';
import { BulkCreateJobLeadRequestDto, CreateJobLeadDto } from '../dto/create-job-lead-request.dto';

@Controller('job-lead')
@ApiTags('JobLeads')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class JobLeadsController {
  constructor(private readonly jobLeadsService: JobLeadsService) { }

  @Post()
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async createJobLead(@Body() jobLeadDto: CreateJobLeadDto, @Req() req) {
    return this.jobLeadsService.createJobLead(<IJwtPayload>req.user, jobLeadDto);
  }

  @Get('search-vacancies')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async searchVacancies(@Req() req, @Query() jobLeadsDto: SearchVacanciesDto) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.searchVacancies(userId, jobLeadsDto);
  }

  @Post('bulk')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async bulkCreateJobLead(
    @Body() bulkCreateJobLeadRequestDto: BulkCreateJobLeadRequestDto,
    @Req() req,
  ) {
    return this.jobLeadsService.bulkCreateJobLead(
      <IJwtPayload>req.user,
      bulkCreateJobLeadRequestDto,
    );
  }

  // TODO: Remove this after client migration is complete
  @Get('search-vacancies/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async searchVacanciesLegacy(@Param('userId') userId, @Query() jobLeadsDto: SearchVacanciesDto) {
    return this.jobLeadsService.searchVacancies(userId, jobLeadsDto);
  }

  @Delete('bulk-delete')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async bulkDeleteLeads(@Req() req, @Body() jobLeadsDto: DeleteJobLeadsDto) {
    return this.jobLeadsService.bulkDeleteJobLeads(jobLeadsDto);
  }

  @Put('bulk-change-status')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async bulkChangeStatusLeads(@Req() req, @Body() jobLeadsDto: ChangeStatusLeadsDto) {
    return this.jobLeadsService.bulkChangeStatusJobLeads(req, jobLeadsDto);
  }

  @Put('bulk-change-status-by-name')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async bulkChangeStatusLeadsByName(@Req() req, @Body() jobLeadsDto: ChangeStatusLeadsByNameDto) {
    return this.jobLeadsService.bulkChangeStatusLeadsByName(req.user.id, jobLeadsDto);
  }

  @Get('count-leads')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async countLeads(@Req() req, @Query() query: JobLeadStatsDto) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.countLeads(userId, query);
  }

  //TODO: remove after integrating with FE
  @Get('view-as/:userId/count-leads')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async countLeadsLegacy(@Param('userId') userId: string, @Query() query: JobLeadStatsDto) {
    return this.jobLeadsService.countLeads(query?.userId || userId, query);
  }

  // TODO: Check if restriction is needed for userId then use viewAsUser
  @Get('count-for-each-lead-status')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async countForEachLeadStatus(@Req() req, @Query() query: JobLeadStatsDto) {
    return this.jobLeadsService.countForEachLeadStatus(query?.userId || req.user.id, query);
  }

  @Get('count-leads/:id')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async countLeadsById(@Param('id') id: string, @Query() query: JobLeadStatsDto) {
    return this.jobLeadsService.countLeads(query?.userId || id, query);
  }

  @Get('count-for-each-lead-status/:id')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async countForEachLeadStatusById(@Param('id') id: string, @Query() query: JobLeadStatsDto) {
    return this.jobLeadsService.countForEachLeadStatus(query?.userId || id, query);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getAllJobsLeads(@Req() req: any) {
    return this.jobLeadsService.getAllJobsLeads({ userFromPayload: <IJwtPayload>req.viewAsUser });
  }

  // TODO: Remove this after client migration is complete
  @Get('view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getUserJobLeadsLegacy(@Param('userId') userId: string) {
    return this.jobLeadsService.getAllJobsLeads({ userId });
  }

  // TODO: Consider using viewAsUser
  @Get('leads/:statusId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsByStatusId(@Param('statusId') statusId: string, @Query() queryDto: LeadQueryDto) {
    return this.jobLeadsService.getLeadsByStatusId(statusId, queryDto);
  }

  // TODO: Remove this after client migration is complete
  @Get('leads/:statusId/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsByStatusIdInViewAs(
    @Param('statusId') statusId: string,
    @Param('userId') userId: string,
    @Query() queryDto: LeadQueryDto,
  ) {
    return this.jobLeadsService.getLeadsByStatusIdInViewAs(statusId, queryDto, userId);
  }

  @Get('leads/:statusId/companies')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsCompanyByStatusId(
    @Req() req: any,
    @Param('statusId') statusId: string,
    @Query() queryDto: LeadQueryDto,
  ) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.getLeadsCompanyByStatusIdInViewAs(statusId, queryDto, userId);
  }

  // TODO: Remove this after client migration is complete
  @Get('leads/:statusId/view-as/:userId/companies')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsCompanyByStatusIdInViewAs(
    @Param('statusId') statusId: string,
    @Param('userId') userId: string,
    @Query() queryDto: LeadQueryDto,
  ) {
    return this.jobLeadsService.getLeadsCompanyByStatusIdInViewAs(statusId, queryDto, userId);
  }

  @Get('lead-companies')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getMyLeadCompany(@Req() req: any, @Query() queryDto: LeadQueryDto) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.getMyLeadCompanies(queryDto, userId);
  }

  // TODO: Remove this after client migration is complete
  @Get('lead-companies/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getMyLeadCompanyLegacy(@Param('userId') userId: string, @Query() queryDto: LeadQueryDto) {
    return this.jobLeadsService.getMyLeadCompanies(queryDto, userId);
  }

  @Get('leads/:statusId/company/:companyId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsCompanyJobByStatusId(
    @Req() req: any,
    @Param('statusId') statusId: string,
    @Param('companyId') companyId: string,
  ) {
    const userId = req.viewAsUser.id;

    return this.jobLeadsService.getLeadsJobCompanyByStatusIdInViewAs(statusId, userId, companyId);
  }

  // TODO: Remove this after client migration is complete
  @Get('leads/:statusId/view-as/:userId/company/:companyId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsCompanyJobByStatusIdInViewAs(
    @Param('statusId') statusId: string,
    @Param('userId') userId: string,
    @Param('companyId') companyId: string,
  ) {
    return this.jobLeadsService.getLeadsJobCompanyByStatusIdInViewAs(statusId, userId, companyId);
  }

  @Get('leads-by-company/:companyId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsByCompany(@Req() req: any, @Param('companyId') companyId: string) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.getLeadsByCompany(userId, companyId);
  }

  // TODO: Remove this after client migration is complete
  @Get('leads-by-company/view-as/:userId/company/:companyId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsByCompanyLegacy(
    @Param('userId') userId: string,
    @Param('companyId') companyId: string,
  ) {
    return this.jobLeadsService.getLeadsByCompany(userId, companyId);
  }

  @Put('change-status')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async changeJobLeadStatus(@Body() jobLeadDto: ChangeJobLeadStatusDto, @Req() req) {
    return this.jobLeadsService.changeJobLeadStatus(jobLeadDto, req.user.id);
  }

  @Put('change-status-by-company')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async changeJobLeadStatusByCompany(@Body() bodyDto: ChangeLeadStatusByCompanyDto, @Req() req) {
    return this.jobLeadsService.changeJobLeadStatusByCompany(bodyDto, req.user.id);
  }

  @Delete(':jobLeadId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async deleteJobLead(@Param('jobLeadId') jobLeadId: string) {
    return this.jobLeadsService.deleteJobLead(jobLeadId);
  }

  @Patch(':jobLeadId/update-lead')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async updateLead(
    @Param('jobLeadId') jobLeadId: string,
    @Body() updateLeadDto: UpdateLeadDto,
    @Req() req,
  ) {
    return this.jobLeadsService.updateLead(jobLeadId, updateLeadDto, req);
  }

  @Get('leads/mail-box')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsInMailBox(@Req() req: any, @Query() queryDto: LeadQueryDto) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.getLeadsInMailBox(queryDto, userId);
  }

  // TODO: Remove this after client migration is complete
  @Get('leads/view-as/:userId/mail-box')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsInMailBoxLegacy(@Param('userId') userId: string, @Query() queryDto: LeadQueryDto) {
    return this.jobLeadsService.getLeadsInMailBox(queryDto, userId);
  }

  @Get('count-leads/mail-box')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async countLeadsInMailBox(@Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.countLeadsInMailBox(userId);
  }

  // TODO: Remove this after client migration is complete
  @Get('count-leads/view-as/:userId/mail-box')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async countLeadsInMailBoxLegacy(@Param('userId') userId: string) {
    return this.jobLeadsService.countLeadsInMailBox(userId);
  }

  @Patch('leads/update-status-mail-box/:leadId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async updateLeadInMailBox(@Param('leadId') leadId: string, @Req() req) {
    return this.jobLeadsService.updateLeadInMailBox(req.user.id, leadId, req);
  }

  // TODO: Remove this after client migration is complete
  @Patch('leads/view-as/:userId/update-status-mail-box/:leadId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async updateLeadInMailBoxLegacy(
    @Param('userId') userId: string,
    @Param('leadId') leadId: string,
    @Req() req,
  ) {
    return this.jobLeadsService.updateLeadInMailBox(userId, leadId, req);
  }

  @Post('leads/sent-job')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async createSentJob(@Body() jobLeadDto: CreateJobSentDto) {
    return this.jobLeadsService.createSentJob(jobLeadDto);
  }

  @Delete('leads/sent-job/:id')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async deleteSentJob(@Param('id') id: string) {
    return this.jobLeadsService.deleteSentJob(id);
  }

  @Get('leads/sent-job')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getSentJob(@Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.getSentJob(userId);
  }

  // TODO: Remove this after client migration is complete
  @Get('lead/view-as/:userId/sent-job')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getSentJobLegacy(@Param('userId') userId: string) {
    return this.jobLeadsService.getSentJob(userId);
  }

  @Get('job/:jobId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getJobDetail(@Param('jobId') jobId: string) {
    return this.jobLeadsService.getJobDetail(jobId);
  }

  @Post('similar-jobs')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getSimilarJobs(@Req() req, @Body() similarJobDto: SimilarJobDto) {
    return this.jobLeadsService.getSimilarJobs(req, similarJobDto);
  }

  @Post('job/update-job-sent-data')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async updateJobSentData() {
    return this.jobLeadsService.updateSentJobData();
  }

  @Get('stats/job-keywords')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async statsJobKeywords(@Req() req: any, @Query() query: JobLeadKeywordDto) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.statsJobKeywords(userId, query);
  }

  // TODO: Remove this after client migration is complete
  @Get('stats/view-as/:userId/job-keywords')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async statsJobKeywordsLegacy(@Param('userId') userId: string, @Query() query: JobLeadKeywordDto) {
    return this.jobLeadsService.statsJobKeywords(query?.userId || userId, query);
  }

  @Get('stats/new-leads')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async statsNewLeads(@Req() req: any, @Query() query: JobLeadStatsDto) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.statsNewLeads(userId, query);
  }

  // TODO: Remove this after client migration is complete
  @Get('stats/view-as/:userId/new-leads')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async statsNewLeadsLegacy(@Param('userId') userId: string, @Query() query: JobLeadStatsDto) {
    return this.jobLeadsService.statsNewLeads(query?.userId || userId, query);
  }

  @Get('stats/contact-added')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async statsContactAdded(@Req() req: any, @Query() query: JobLeadStatsDto) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.statsContactAdded(query?.userId || userId, query);
  }

  @Get('stats/company-added')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async statsCompanyAdded(@Req() req: any, @Query() query: JobLeadStatsDto) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.countCompanyAdded(query?.userId || userId, query);
  }

  // TODO: Remove this after client migration is complete
  @Get('stats/view-as/:userId/contact-added')
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async statsContactAddedLegacy(@Param('userId') userId: string, @Query() query: JobLeadStatsDto) {
    return this.jobLeadsService.statsContactAdded(query?.userId || userId, query);
  }

  @Put('star/:id')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Write)
  async starJobLead(@Param('id') id: string, @Body() staredJobLeadDto: StaredJobLeadDto) {
    return this.jobLeadsService.starJobLead(id, staredJobLeadDto);
  }

  @Get('check-leads-by-external-ids')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async checkExistingJobLeadByExternalIds(@Req() req: any, @Query() { ids }: { ids: string[] }) {
    const userId = req.viewAsUser.id;
    return this.jobLeadsService.checkExistingJobLeadByExternalIds({ userId, ids });
  }

  // TODO: Remove this after client migration is complete
  @Get('view-as/:userId/check-leads-by-external-ids')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async checkExistingJobLeadByExternalIdsLegacy(
    @Param('userId') userId: string,
    @Query() { ids }: { ids: string[] },
  ) {
    return this.jobLeadsService.checkExistingJobLeadByExternalIds({ userId, ids });
  }

  @Get(':id')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getDetailLead(@Param('id') id: string) {
    return this.jobLeadsService.getLeadById(id);
  }

  @Get('leads-by-status/:statusId')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getLeadsByStatus(@Param('statusId') statusId: string, @Query() queryDto: LeadQueryDto) {
    return this.jobLeadsService.getLeadsByStatus(statusId, queryDto);
  }

  @Post('update-priority')
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async updatePriority(
    @Body() body: UpdatePriorityDto,
  ) {
    return this.jobLeadsService.updatePriority(body);
  }
}

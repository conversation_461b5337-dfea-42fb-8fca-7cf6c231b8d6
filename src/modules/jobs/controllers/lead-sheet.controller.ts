import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { LeadSheetService } from '../service/lead-sheet.service';
import { CreateLeadSheetDto, GetLeadSheetQueryDto, UpdateLeadSheetDto } from '../dto/lead-sheet/lead-sheet.dto';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@ApiTags('LeadSheets')
@Controller('lead-sheet')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class LeadSheetController {
  constructor(private readonly leadSheetService: LeadSheetService) {}

  @Post()
  @Permission(PermissionResource[ResourceEnum.MANUAL_LEADS].Write)
  create(@Body() bodyDto: CreateLeadSheetDto, @Req() req: any) {
    // TODO: updateFor - update by using req.viewAsUser.id directly in service instead of passing it through payload
    if (!bodyDto.updateFor && req.viewAsUser) {
      bodyDto.updateFor = req.viewAsUser.id;
    }
    return this.leadSheetService.create(bodyDto);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.MANUAL_LEADS].Read)
  getMany(@Query() queryDto: GetLeadSheetQueryDto, @Req() req: any) {
    // TODO: userId - update by using req.viewAsUser.id directly in service instead of passing it through query
    if (!queryDto.userId && req.viewAsUser) {
      queryDto.userId = req.viewAsUser.id;
    }
    return this.leadSheetService.getMany(queryDto);
  }

  @Patch(':id')
  @Permission(PermissionResource[ResourceEnum.MANUAL_LEADS].Write)
  update(@Param('id') id: string, @Body() bodyDto: UpdateLeadSheetDto) {
    return this.leadSheetService.update(id, bodyDto);
  }

  @Delete(':id')
  @Permission(PermissionResource[ResourceEnum.MANUAL_LEADS].Write)
  delete(@Param('id') id: string) {
    return this.leadSheetService.delete(id);
  }
}

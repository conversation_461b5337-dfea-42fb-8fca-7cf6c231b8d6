import { Body, Controller, Delete, Get, Headers, Param, Patch, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { JobService } from '../service/job.service';
import { CreatedJobDto, PatchSearchKeywordsDto } from '../dto/job/created-job.dto';
import { SkipThrottle } from '@nestjs/throttler';
import { ApiKeyGuard } from 'src/guards/apiKey.guard';
import { JobBySearchIdQuery } from '../dto/job-by-search-id.dto';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { DeleteJobLogDto, JobLogRequestDto, UpdateJobLogDto } from '../dto/job-log/job-log.dto';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@ApiTags('Jobs')
@Controller('job')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class JobController {
  constructor(private readonly jobService: JobService) {}

  @Post()
  @UseGuards(ApiKeyGuard)
  // API Key routes don't need Permission decorator as they're authenticated differently
  createJob(@Body() jobDto: CreatedJobDto, @Headers('x_api_key') key: string) {
    return this.jobService.createJob(jobDto);
  }

  @Patch('search-keywords')
  @UseGuards(ApiKeyGuard)
  // API Key routes don't need Permission decorator as they're authenticated differently
  patchSearchKeywords(@Body() jobDto: PatchSearchKeywordsDto, @Headers('x_api_key') key: string) {
    return this.jobService.patchSearchKeywords(jobDto);
  }

  @Get('duplicate-jobs')
  @Permission(PermissionResource[ResourceEnum.DUPLICATED_JOBS].Read)
  getDuplicateJobs(@Query() queryDto: JobBySearchIdQuery) {
    return this.jobService.getDuplicateJobs(queryDto);
  }

  @Post('logs')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  getJobLogsByJobId(@Body() bodyDto: JobLogRequestDto) {
    return this.jobService.getJobLogsByJobId(bodyDto.jobId);
  }

  @Put('logs')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  insertOrUpdateJobLog(@Body() updatedJobLogDto: UpdateJobLogDto, @Req() req: any) {
    // TODO: updatedFor - update by using req.viewAsUser.id directly in service instead of passing it through payload
    if (!updatedJobLogDto.updatedFor && req.viewAsUser) {
      updatedJobLogDto.updatedFor = req.viewAsUser.id;
    }
    return this.jobService.insertOrUpdateJobLog(<IJwtPayload>req.user, updatedJobLogDto);
  }

  @Delete('logs')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  deleteJobLog(@Req() req: any, @Body() bodyDto: DeleteJobLogDto) {
    // TODO: updatedFor - update by using req.viewAsUser.id directly in service instead of passing it through payload
    if (!bodyDto.updatedFor && req.viewAsUser) {
      bodyDto.updatedFor = req.viewAsUser.id;
    }
    return this.jobService.deleteJobLog(<IJwtPayload>req.user, bodyDto);
  }

  // must below same pattern routes
  @Get(':id')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  getJobById(@Param('id') id: string) {
    return this.jobService.getJobById(id);
  }

  @Post('script/location-gb-county')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  updateLocationGBCounty() {
    return this.jobService.updateLocationGBCounty();
  }

  @Post('script/location-anywhere')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  updateLocationAnywhere() {
    return this.jobService.updateLocationAnywhere();
  }

  @Post('script/update-created-at')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  updateJobCreatedAt() {
    return this.jobService.updateJobCreatedAt();
  }

  @Post('script/contribute-location')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  contributeLocation(@Query('fromDate') fromDate: string) {
    return this.jobService.contributeLocation(fromDate);
  }

  @Post('script/standardize-job-type')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  standardizeExistingJobType() {
    return this.jobService.standardizeExistingJobType();
  }
}

import { sendAppointment } from './../utils/bullhorn-service.util';
import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UseGuards,
  Put,
  Param,
  Delete,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { BullhornIntegrationService } from '../service/bullhorn-integration.service';
import { GetCorporateUserQueryDto, SearchCommonBullhornDto } from '../dto/get-corporate.dto';
import { IJwtPayload } from '../../auth/payloads/jwt-payload.payload';
import { GetCountiesQueryDto, GetCountriesQueryDto } from '../dto/bull-horn/get-counties.dto';
import { BullhornSubmission, BullhornSubmissionTotal } from '../dto/bull-horn/bullhorn-job.dto';
import {
  ContactWarningDto,
  DeleteBHEntityDto,
  InsertMassAdvanceDto,
  searchLookupDataDto,
  updateMassAdvanceDto,
} from '../dto/check-contact-warning.dto';
import { Request } from 'express';
import { BulkImportJobDto, ConnectedUserJobDto, SendJobToBullhornDto, StandardUserJobDto } from '../dto/job/job-to-bullhorn.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { MAX_IMAGE_SIZE_IN_BYTES } from 'src/modules/user/constants/user-signature.constants';
import { BHUploadFileDto } from '../dto/bull-horn/update-file.dto';
import { MassUpdateDto, BullhornGuessDto } from '../dto/bull-horn/bullhorn-entity.dto';

@Controller('bullhorn-integration')
@ApiTags('BullhornIntegration')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class BullhornIntegrationController {
  constructor(private readonly bullhornIntegrationService: BullhornIntegrationService) {}

  @Get('search')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  searchBullhornData(@Req() request, @Query() query: GetCorporateUserQueryDto) {
    return this.bullhornIntegrationService.search(request, query);
  }

  @Post('guess/search-corporate-user')
  searchCorporateUser(@Body() requestBody: BullhornGuessDto) {
    return this.bullhornIntegrationService.searchCorporateUser(requestBody);
  }

  @Get('search-short-list/list-vacancy')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  searchBullhornListVacancy(@Req() request, @Query() query: GetCorporateUserQueryDto) {
    return this.bullhornIntegrationService.searchListVacancy(request, query);
  }

  @Get('search-short-list/list-short-list/:vacancyId')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  searchBullhornListShortList(@Req() request, @Param('vacancyId') vacancyId: string) {
    return this.bullhornIntegrationService.searchListShortList(request, vacancyId);
  }

  @Get('common/search')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  searchCommon(@Req() request, @Query() queryDto: SearchCommonBullhornDto) {
    return this.bullhornIntegrationService.searchCommon(request, queryDto);
  }

  @Get('common/query-search')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  querySearchCommon(@Req() request, @Query() queryDto: SearchCommonBullhornDto) {
    return this.bullhornIntegrationService.querySearchCommon(request, queryDto);
  }

  @Get('get-file')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  getFileOfEntity(@Req() request, @Query() query: GetCorporateUserQueryDto) {
    return this.bullhornIntegrationService.searchFile(request, query);
  }

  @Post('upload-file')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
      required: ['file'],
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
    }),
  )
  uploadFile(
    @Req() request,
    @Body() requestBody: BHUploadFileDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.bullhornIntegrationService.uploadFile(request, requestBody, file);
  }

  @Post('send-job')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  sendJobToBullhorn(@Req() request, @Body() requestBody: StandardUserJobDto | ConnectedUserJobDto) {
    return this.bullhornIntegrationService.sendJobToBullhorn(
      request,
      requestBody,
      <IJwtPayload>request.viewAsUser,
    );
  }

  // TODO: Remove this after client migration is complete
  @Post('send-job/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  sendJobToBullhornInViewAsModeLegacy(
    @Req() request,
    @Body() requestBody: StandardUserJobDto | ConnectedUserJobDto,
    @Param('userId') userId: string,
  ) {
    return this.bullhornIntegrationService.sendJobToBullhorn(request, requestBody, { id: userId });
  }

  @Post('bulk-import-job')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  bulkImportJobLead(@Req() request, @Body() requestBody: BulkImportJobDto) {
    const userId = request.viewAsUser.id;
    return this.bullhornIntegrationService.bulkImportJob(requestBody, userId);
  }

  @Get('get-counties')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getCounties(@Req() request, @Query() queryDto: GetCountiesQueryDto) {
    return this.bullhornIntegrationService.getCounties(request, queryDto);
  }

  @Get('get-total-data')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getTotalData(@Req() request, @Query() queryDto: BullhornSubmissionTotal) {
    return this.bullhornIntegrationService.getTotalData(request, queryDto);
  }

  @Get('get-countries')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async getCountries(@Req() request, @Query() queryDto: GetCountriesQueryDto) {
    return this.bullhornIntegrationService.getCountries(request, queryDto);
  }

  @Put('insert')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async insertBullhornData(@Req() request, @Body() requestBody: BullhornSubmission) {
    console.log(requestBody);
    return this.bullhornIntegrationService.insertBullhorn(request, requestBody);
  }

  @Put('insert-workflow')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async insertBullhornWorkFlowData(@Req() request, @Body() requestBody: BullhornSubmission) {
    console.log(requestBody);
    return this.bullhornIntegrationService.insertBullhorn(request, requestBody);
  }

  @Post('update/:id')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async updateBullhornData(
    @Req() request,
    @Body() requestBody: BullhornSubmission,
    @Param('id') id: string,
  ) {
    return this.bullhornIntegrationService.updateBullhorn(request, requestBody, id);
  }

  @Post('check-contact-warning')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async checkContactWarnings(@Req() request: Request, @Body() requestBody: ContactWarningDto) {
    return this.bullhornIntegrationService.checkContactWarnings(request, requestBody);
  }

  @Post('check-hotlist-email/:hotlistId')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async checkHotListContactsInvalid(
    @Req() request: Request,
    @Param('hotlistId') hotlistId: string,
  ) {
    return this.bullhornIntegrationService.checkHotListContactsInvalid(request, hotlistId);
  }

  @Delete('delete')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteEntity(@Req() request: Request, @Body() requestBody: DeleteBHEntityDto) {
    return this.bullhornIntegrationService.deleteBullhornEntity(request, requestBody);
  }

  @Delete('delete-file')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async deleteFileEntity(@Req() request: Request, @Body() requestBody: DeleteBHEntityDto) {
    return this.bullhornIntegrationService.deleteBullhornFile(request, requestBody);
  }

  @Post('update-mass-advance')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async updateMassAdvance(@Req() request: Request, @Body() requestBody: updateMassAdvanceDto) {
    return this.bullhornIntegrationService.updateMassAdvance(request, requestBody);
  }

  @Post('insert-mass-advance')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async insertMassAdvance(@Req() request: Request, @Body() requestBody: InsertMassAdvanceDto) {
    return this.bullhornIntegrationService.insertMassAdvance(request, requestBody);
  }

  @Post('send-appointment')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async sendAppointment(@Req() request: Request, @Body() requestBody: InsertMassAdvanceDto) {
    return this.bullhornIntegrationService.sendAppointment(request, requestBody);
  }

  @Get('search-lookup')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async searchLookUp(@Req() request: Request, @Query() requestBody: searchLookupDataDto) {
    return this.bullhornIntegrationService.searchLookup(request, requestBody);
  }

  @Get('client-contact/:id')
  @Permission(PermissionResource[ResourceEnum.CRM].Read)
  async searchDataContact(@Req() request: Request, @Param('id') id: string) {
    return this.bullhornIntegrationService.searchDataContact(request, id);
  }

  @Post('mass-update')
  @Permission(PermissionResource[ResourceEnum.CRM].Write)
  async massUpdate(@Req() request: Request, @Body() massUpdateDto: MassUpdateDto) {
    return this.bullhornIntegrationService.massUpdate(request, massUpdateDto);
  }
}

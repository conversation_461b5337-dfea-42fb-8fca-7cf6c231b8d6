import { Controller, Get, Body, Post, UseGuards, Req, Param, Query } from '@nestjs/common';
import { LeadStatusesService } from '../service/lead-statuses.service';
import { SkipThrottle } from '@nestjs/throttler';
import { ApiTags } from '@nestjs/swagger';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { BulkUpdateRequestDto } from '../dto/bulk-update.dto';
import { LeadStatusQueryDto, LeadStatusSimpleQuery } from '../dto/lead-status/lead-status.dto';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@ApiTags('LeadStatuses')
@Controller('lead-statuses')
@UseGuards(AuthenticationGuard, PermissionGuard)
@SkipThrottle()
export class LeadStatusesController {
  constructor(private readonly leadStatusesService: LeadStatusesService) {}

  @Get()
  @Permission(PermissionResource[ResourceEnum.TASK].Read)
  async getStatuses(@Req() req) {
    return this.leadStatusesService.getStatuses(req.viewAsUser.id);
  }

  // TODO: Remove this after client migration is complete
  @Get('view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.TASK].Read)
  async getStatusesForUserLegacy(@Param('userId') userId: string, @Query() queryDto: LeadStatusQueryDto) {
    return this.leadStatusesService.getStatuses(userId, queryDto);
  }

  @Get('simple-list')
  @Permission(PermissionResource[ResourceEnum.TASK].Read)
  async getSimpleStatuses(@Req() req, @Query() queryDto: LeadStatusSimpleQuery) {
    const userId = req.viewAsUser.id;
    return this.leadStatusesService.getSimpleStatuses(userId, queryDto);
  }

  // TODO: Remove this after client migration is complete
  @Get('simple-list/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.TASK].Read)
  async getSimpleStatusesLegacy(@Param('userId') userId: string, @Query() queryDto: LeadStatusSimpleQuery) {
    return this.leadStatusesService.getSimpleStatuses(userId, queryDto);
  }

  @Post('bulk-update')
  @Permission(PermissionResource[ResourceEnum.TASK].Write)
  async updateStatuses(@Body() recordsToUpdate: BulkUpdateRequestDto, @Req() req: any): Promise<{ message: string }> {
    // TODO: updatedFor - update by using req.viewAsUser.id directly in service instead of passing it through payload
    if (!recordsToUpdate.updatedFor && req.viewAsUser) {
      recordsToUpdate.updatedFor = req.viewAsUser.id;
    }
    return this.leadStatusesService.updateStatuses(recordsToUpdate, <IJwtPayload>req.viewAsUser);
  }
}

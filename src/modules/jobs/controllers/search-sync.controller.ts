import { Controller, Get, Post, Param, Body, UseGuards, Req, Query } from '@nestjs/common';
import { SearchSyncService } from '../service/search-sync.service';
import { SkipThrottle } from '@nestjs/throttler';
import { ApiTags } from '@nestjs/swagger';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { PermissionGuard } from 'src/guards/permission.guard';
import { ToggleSyncRequestDto } from '../dto/toggle-sync.dto';
import { CreateSyncRequestDto } from '../dto/job-request.dto';
import { JobSyncQuery, SyncedSearchesQueryDto } from '../dto/job-sync-query.dto';

@ApiTags('SearchSync')
@Controller('search-sync')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class SearchSyncController {
  constructor(private readonly searchSyncService: SearchSyncService) {}

  @Get()
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Read)
  async getSyncOfSearches(@Req() req) {
    return this.searchSyncService.getSyncOfSearches(<IJwtPayload>req.viewAsUser);
  }

  // TODO: Remove this after client migration is complete
  @Get('view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Read)
  async getSyncOfSearchesForUser(@Param('userId') userId: string) {
    return this.searchSyncService.getSyncOfSearches({ id: userId });
  }

  @Get('searches')
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Read)
  async getSyncedSearches(@Req() req, @Query() queryDto: SyncedSearchesQueryDto) {
    return this.searchSyncService.getSyncedSearches(<IJwtPayload>req.viewAsUser, queryDto);
  }

  // TODO: Remove this after client migration is complete
  @Get('searches/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Read)
  async getSyncedSearchesLegacy(@Param('userId') userId: string, @Query() queryDto: SyncedSearchesQueryDto) {
    return this.searchSyncService.getSyncedSearches({ id: userId }, queryDto);
  }

  @Get(':syncId')
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Read)
  async getSyncById(@Param('syncId') syncId: string, @Req() req, @Query() queryParams: JobSyncQuery): Promise<any> {
    return this.searchSyncService.getSyncById(syncId, <IJwtPayload>req.viewAsUser, queryParams);
  }

  // TODO: Remove this after client migration is complete
  @Get(':syncId/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Read)
  async getSyncByIdForUser(
    @Param('syncId') syncId: string,
    @Param('userId') userId: string,
    @Query() queryParams: JobSyncQuery
  ): Promise<any> {
    return this.searchSyncService.getSyncById(syncId, { id: userId }, queryParams);
  }

  @Post()
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Write)
  async createSyncOfSearches(@Body() requestDto: CreateSyncRequestDto, @Req() req) {
    return this.searchSyncService.createSyncOfSearches(requestDto, <IJwtPayload>req.user);
  }

  @Post('toggle-sync')
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Write)
  async toggleSync(@Body() requestDto: ToggleSyncRequestDto, @Req() req) {
    return this.searchSyncService.toggleSync(requestDto, <IJwtPayload>req.user);
  }

  @Get('toggle-sync-status/:searchId')
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Read)
  async getToggleSyncStatus(@Param('searchId') searchId, @Req() req) {
    return this.searchSyncService.getToggleSyncStatus(searchId, <IJwtPayload>req.viewAsUser);
  }

  // TODO: Remove this after client migration is complete
  @Get('toggle-sync-status/:searchId/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.JOB_SYNC].Read)
  async getToggleSyncStatusForUser(@Param('searchId') searchId, @Param('userId') userId: string) {
    return this.searchSyncService.getToggleSyncStatus(searchId, { id: userId });
  }
}

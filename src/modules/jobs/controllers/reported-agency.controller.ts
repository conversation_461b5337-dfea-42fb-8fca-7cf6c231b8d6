import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, Res, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { ReportedAgencyService } from '../service/reported-agency.service';
import { BulkCreateReportedAgencyDto, CreateReportedAgencyDto } from '../dto/create-reported-agency.dto';
import { GetReportedAgencyQuery } from '../dto/get-reported-agency.dto';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { PermissionGuard } from 'src/guards/permission.guard';
import { UpdateReportedAgencyDto, BulkUpdateReportedAgencyDto } from '../dto/update-reported-agency.dto';
import { FilterExportReportedAgencyDto } from '../dto/export-reported-agency.dto';

@Controller('reported-agency')
@ApiTags('ReportedAgency')
@UseGuards(AuthenticationGuard, PermissionGuard)
@SkipThrottle()
export class ReportedAgencyController {
  constructor(private readonly reportedAgencyService: ReportedAgencyService) {}

  @Get('get-fuzzy-search/:companyName')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Read)
  async getFuzzySearch(@Param('companyName') companyName: string) {
    return this.reportedAgencyService.getFuzzySearch(companyName);
  }

  @Get('export')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Read)
  async exportAgencies(@Res() res: Response, @Query() filter: FilterExportReportedAgencyDto) {
    await this.reportedAgencyService.exportAgencies(res, filter);
  }

  //TODO: <remove todo when view as is stable>
  //reportedAgency is global -> no need to specify userId
  @Get(':reportedAgencyId')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Read)
  async getJobBoardByReportedAgencyId(
    @Param('reportedAgencyId') reportedAgencyId: string,
    @Query() queryParams: any, // Query parameters object
    @Req() req
  ) {
    return this.reportedAgencyService.getJobBoardByReportedAgencyId(
      reportedAgencyId,
      queryParams.page,
      <IJwtPayload>req.user
    );
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Read)
  async getAllReportedAgency(
    @Query() queryParams: GetReportedAgencyQuery, // Query parameters object
    @Req() req
  ) {
    return this.reportedAgencyService.getAllReportedAgencies(queryParams, <IJwtPayload>req.user);
  }

  @Post()
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Write)
  async createReportedAgency(@Body() createReportedAgencyDto: CreateReportedAgencyDto, @Req() req) {
    return this.reportedAgencyService.createReportedAgency(createReportedAgencyDto, <IJwtPayload>req.user);
  }

  @Put('bulk')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Write)
  async bulkUpdateReportAgency(@Body() payload: BulkUpdateReportedAgencyDto, @Req() req) {
    return this.reportedAgencyService.bulkUpdateReportedAgency(payload, <IJwtPayload>req.user);
  }

  @Put(':id')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Write)
  async updateReportAgency(@Body() payload: UpdateReportedAgencyDto, @Req() req, @Param('id') id: string) {
    return this.reportedAgencyService.bulkUpdateReportedAgency(
      {
        values: [{ ...payload, id }],
      },
      <IJwtPayload>req.user
    );
  }

  @Delete(':id')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Write)
  async deleteReportedAgency(@Req() req, @Param('id') id: string) {
    return this.reportedAgencyService.deleteReportedAgency(id, <IJwtPayload>req.user);
  }

  @Post(':id/review')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Write)
  async reviewReportedAgency(@Req() req, @Param('id') id: string, @Body('status') status: 'APPROVED'|'REJECTED') {
    return this.reportedAgencyService.reviewReportedAgency(id, status, <IJwtPayload>req.user);
  }

  @Post('agency/check')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Read)
  async checkAgency(@Body() body: string[]) {
    return this.reportedAgencyService.checkAgency(body);
  }

  @Post('script/auto-detect-agency')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Write)
  async detectCurrentAgencies() {
    return this.reportedAgencyService.detectCurrentAgencies();
  }

  @Post('bulk-insert')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Write)
  async bulkInsert(@Body() bulkInsertDto: BulkCreateReportedAgencyDto, @Req() req) {
    return this.reportedAgencyService.bulkInsert(bulkInsertDto, <IJwtPayload>req.user);
  }

  @Post('script/sync-rds-report-agency')
  @Permission(PermissionResource[ResourceEnum.REPORTED_AGENCIES].Write)
  async syncRdsAgencies() {
    return this.reportedAgencyService.syncRdsAgencies();
  }
}

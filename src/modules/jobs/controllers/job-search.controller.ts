import { JobScrawlingService } from './../service/job-crawling.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { JobSearchService } from '../service/job-search.service';
import { CreateJobSearchDto } from '../dto/job-request.dto';
import { SetGroupByCompanyRequestDto, UpdateJobSearchDto } from '../dto/update-job-search.dto';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { JobBySearchIdQuery } from '../dto/job-by-search-id.dto';
import { CheckoutNewJobQueryDto } from '../dto/job-search/checkout-new-job-query.dto';
import { NotificationQueryDto } from '../dto/job-search/notification.dto';
import { UpdateKeywordJobSearch } from '../dto/update-keyword-job-search.dto';
import { GetJobSearchListQuery } from '../dto/get-job-search-list.dto';
import { BasiqJobDto, DeleteBasicJobCompanyDto } from '../dto/job/job.dto';
import { BulkDeleteSearchDto } from '../dto/bulk-update.dto';
import { PermissionGuard } from 'src/guards/permission.guard';

@Controller('job-search')
@ApiTags('JobSearch')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class JobSearchController {
  constructor(private readonly jobSearchService: JobSearchService) {}

  //TODO: move to notification later
  @Get('checkout-new-jobs')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async checkoutNewJobs(@Query() queryDto: CheckoutNewJobQueryDto) {
    return this.jobSearchService.checkoutNewJobs(queryDto);
  }

  //TODO: move to notification later
  @Get('count-for-new-job-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async countForNewJobSearch(@Req() req, @Query() queryDto: NotificationQueryDto) {
    return this.jobSearchService.countForNewJobSearch(<IJwtPayload>req.viewAsUser, queryDto);
  }

  @Get('keyword-job-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getKeywordJobSearch() {
    return this.jobSearchService.getKeywordJobSearch();
  }

  @Put('keyword-job-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  async updateKeywordJobSearch(@Req() request, @Body() queryDto: UpdateKeywordJobSearch) {
    return this.jobSearchService.updateKeywordJobSearch(queryDto);
  }

  // TODO: Remove this after client migration is complete
  @Get('count-for-new-job-search/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async countForNewJobSearchForUser(
    @Param('userId') userId: string,
    @Query() queryDto: NotificationQueryDto,
  ) {
    return this.jobSearchService.countForNewJobSearch({ id: userId }, queryDto);
  }

  @Get('count-new-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async countNewSearch(@Req() req) {
    return this.jobSearchService.countNewSearch(<IJwtPayload>req.user);
  }

  @Post('')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  async createJobSearch(@Body() createJobSearchDto: CreateJobSearchDto, @Req() req: any) {
    // TODO: updatedFor - update by using req.viewAsUser.id directly in service instead of passing it through payload
    if (!createJobSearchDto.updatedFor && req.viewAsUser) {
      createJobSearchDto.updatedFor = req.viewAsUser.id;
    }
    return this.jobSearchService.createSearch(createJobSearchDto, <IJwtPayload>req.user);
  }

  @Put('/:searchId')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  async updateJobSearch(
    @Param('searchId') searchId: string,
    @Body() updateJobSearchDto: UpdateJobSearchDto,
    @Req() req: any,
  ) {
    // TODO: updatedFor - update by using req.viewAsUser.id directly in service instead of passing it through payload
    if (!updateJobSearchDto.updatedFor && req.viewAsUser) {
      updateJobSearchDto.updatedFor = req.viewAsUser.id;
    }
    return this.jobSearchService.updateJobSearchById(searchId, updateJobSearchDto, req.user);
  }

  @Delete('/:searchId')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  async deleteJobSearch(@Param('searchId') searchId: string, @Req() req) {
    return this.jobSearchService.deleteJobSearchById(searchId, req.user.id);
  }

  @Patch('/delete-bulk-search')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  async deleteBulkJobSearches(@Body() bulkDeleteSearchDto: BulkDeleteSearchDto, @Req() req) {
    return this.jobSearchService.deleteBulkJobSearches(bulkDeleteSearchDto.searchIds, req.user.id);
  }

  @Get()
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobSearches(
    @Query() queryParams: GetJobSearchListQuery, // Query parameters object
    @Req() req,
  ) {
    return this.jobSearchService.getJobSearches(queryParams, <IJwtPayload>req.viewAsUser);
  }

  // TODO: Remove this after client migration is complete
  @Get('get-all-user-searches/view-as/:userId')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobSearchesForUser(
    @Query() queryParams: GetJobSearchListQuery, // Query parameters object
    @Param('userId') userId: string,
  ) {
    return this.jobSearchService.getJobSearches(queryParams, {
      id: userId,
    });
  }

  @Put('/:searchId/stop')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  async killProcessJob(@Param('searchId') searchId: string, @Req() req: any) {
    // TODO: updatedFor - update by using req.viewAsUser.id directly in service instead of passing it through payload
    const userId = req.viewAsUser?.id || req.user.id;
    return this.jobSearchService.killProcessJob(searchId, userId);
  }

  @Put('/:searchId/start')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  async startProcessJob(@Param('searchId') searchId: string, @Req() req: any) {
    // TODO: updatedFor - update by using req.viewAsUser.id directly in service instead of passing it through payload
    const userId = req.viewAsUser?.id || req.user.id;
    return this.jobSearchService.startProcessJob(searchId, userId);
  }

  @Get(':searchId')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobBoardBySearchId(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery, // Query parameters object
    @Req() req,
  ) {
    return this.jobSearchService.getJobBoardBySearchId(
      searchId,
      queryParams,
      <IJwtPayload>req.viewAsUser,
    );
  }

  // TODO: Remove this after client migration is complete
  @Get(':searchId/view-as/:userId')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobBoardBySearchIdForUser(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery, // Query parameters object
    @Param('userId') userId: string,
  ) {
    return this.jobSearchService.getJobBoardBySearchId(searchId, queryParams, {
      id: userId,
    });
  }

  @Get(':searchId/open-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobBoardBySearchIdForUserOpenSearch(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery,
    @Req() req,
  ) {
    const userId = req.viewAsUser.id;

    return this.jobSearchService.getJobBoardBySearchIdOpenSearch(searchId, queryParams, {
      id: userId,
    });
  }

  // TODO: Remove this after client migration is complete
  @Get(':searchId/view-as/:userId/open-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobBoardBySearchIdForUserOpenSearchLegacy(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery, // Query parameters object
    @Param('userId') userId: string,
  ) {
    return this.jobSearchService.getJobBoardBySearchIdOpenSearch(searchId, queryParams, {
      id: userId,
    });
  }

  @Get(':searchId/company')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getCompaniesOfSearch(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery,
    @Req() req,
  ) {
    const userId = req.viewAsUser.id;
    return this.jobSearchService.getCompaniesOfSearch(searchId, queryParams, { id: userId });
  }

  // TODO: Remove this after client migration is complete
  @Get(':searchId/company/view-as/:userId')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getCompaniesOfSearchLegacy(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery, // Query parameters object
    @Param('userId') userId: string,
  ) {
    return this.jobSearchService.getCompaniesOfSearch(searchId, queryParams, {
      id: userId,
    });
  }

  @Get(':searchId/company/open-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getCompaniesOfSearchByOpenSearch(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery,
    @Req() req,
  ) {
    const userId = req.viewAsUser.id;
    return this.jobSearchService.getCompaniesOfSearchByOpenSearch(searchId, queryParams, {
      id: userId,
    });
  }

  // TODO: Remove this after client migration is complete
  @Get(':searchId/company/view-as/:userId/open-search')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getCompaniesOfSearchByOpenSearchLegacy(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery, // Query parameters object
    @Param('userId') userId: string,
  ) {
    return this.jobSearchService.getCompaniesOfSearchByOpenSearch(searchId, queryParams, {
      id: userId,
    });
  }

  @Get(':searchId/pagination-info')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobBoardPaginationBySearchId(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery, // Query parameters object
    @Req() req,
  ) {
    return this.jobSearchService.getPaginationOfSearchDetail(
      searchId,
      queryParams,
      <IJwtPayload>req.user,
    );
  }

  @Get(':searchId/companies/pagination-info/open-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getCompaniesPaginationInfoByOpenSearch(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery,
    @Req() req,
  ) {
    const userId = req.viewAsUser.id;
    return this.jobSearchService.getPaginationOfCompaniesByOpenSearch(
      searchId,
      queryParams,
      userId,
    );
  }

  // TODO: Remove this after client migration is complete
  @Get(':searchId/companies/pagination-info/view-as/:userId/open-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getCompaniesPaginationBySearchId(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery, // Query parameters object
    @Param('userId') userId: string,
  ) {
    return this.jobSearchService.getPaginationOfCompaniesByOpenSearch(
      searchId,
      queryParams,
      userId,
    );
  }

  @Get(':searchId/pagination-info')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobBoardPaginationBySearchIdForUser(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery,
    @Req() req,
  ) {
    const userId = req.viewAsUser.id;
    return this.jobSearchService.getPaginationOfSearchDetail(searchId, queryParams, { id: userId });
  }

  // TODO: Remove this after client migration is complete
  @Get(':searchId/pagination-info/view-as/:userId')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobBoardPaginationBySearchIdForUserLegacy(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery, // Query parameters object
    @Param('userId') userId: string,
  ) {
    return this.jobSearchService.getPaginationOfSearchDetail(searchId, queryParams, { id: userId });
  }

  @Get(':searchId/pagination-info/open-search')
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobBoardPaginationByOpenSearch(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery,
    @Req() req,
  ) {
    const userId = req.viewAsUser.id;
    return this.jobSearchService.getPaginationOfSearchDetailByOpenSearch(searchId, queryParams, {
      id: userId,
    });
  }

  // TODO: Remove this after client migration is complete
  @Get(':searchId/pagination-info/view-as/:userId/open-search')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getJobsPaginationByOpenSearch(
    @Param('searchId') searchId: string,
    @Query() queryParams: JobBySearchIdQuery, // Query parameters object
    @Param('userId') userId: string,
  ) {
    return this.jobSearchService.getPaginationOfSearchDetailByOpenSearch(searchId, queryParams, {
      id: userId,
    });
  }

  @Put(':searchId/:jobId/pin')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async pinJobToJobSearch(
    @Param('searchId') searchId: string,
    @Param('jobId') jobId: string,
    @Req() req,
  ) {
    return this.jobSearchService.pinJobToJobSearch(
      searchId,
      decodeURIComponent(jobId),
      <IJwtPayload>req.user,
    );
  }

  @Put(':searchId/:jobId/unpin')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async unpinJobToJobSearch(
    @Param('searchId') searchId: string,
    @Param('jobId') jobId: string,
    @Req() req,
  ) {
    return this.jobSearchService.unpinJobToJobSearch(
      searchId,
      decodeURIComponent(jobId),
      <IJwtPayload>req.user,
    );
  }

  @Get(':searchId/pinned')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getPinnedJobsBySearchId(@Param('searchId') searchId: string, @Req() req) {
    return this.jobSearchService.getPinnedJobsBySearchId(searchId, <IJwtPayload>req.viewAsUser);
  }

  // TODO: Remove this after client migration is complete
  @Get(':searchId/pinned/view-as/:userId')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async getPinnedJobsBySearchIdForUser(
    @Param('searchId') searchId: string,
    @Param('userId') userId: string,
  ) {
    return this.jobSearchService.getPinnedJobsBySearchId(searchId, {
      id: userId,
    });
  }

  @Delete(':searchId/remove-job/:jobId')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async removeJobFromSearch(
    @Param('searchId') searchId: string,
    @Param('jobId') jobId: string,
    @Req() req,
  ) {
    return this.jobSearchService.removeJobFromSearch(searchId, jobId, req.user.id);
  }

  @Delete(':searchId/remove-job-v2')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async removeJobFromSearchV2(
    @Param('searchId') searchId: string,
    @Body() jobDto: BasiqJobDto,
    @Req() req,
  ) {
    return this.jobSearchService.removeJobFromSearch(searchId, jobDto.jobId, req.user.id);
  }

  @Delete(':searchId/remove-pin-job-group-by-company')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async removePinnedJobGroupByCompany(
    @Param('searchId') searchId: string,
    @Body() jobIds: string[],
    @Req() req,
  ) {
    return this.jobSearchService.removePinnedJobGroupByCompany(searchId, jobIds, req.user.id);
  }

  @Delete(':searchId/remove-job-by-company')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Read)
  async removeJobByCompanyName(
    @Param('searchId') searchId: string,
    @Body() jobDto: DeleteBasicJobCompanyDto,
    @Req() req,
  ) {
    return this.jobSearchService.removeJobByCompanyName(searchId, jobDto, req.user.id);
  }

  @Patch(':searchId/update-status-group-job-by-company')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  async setGroupJobByCompany(
    @Param('searchId') searchId: string,
    @Req() req,
    @Body() bodyDto: SetGroupByCompanyRequestDto,
  ) {
    return this.jobSearchService.setGroupJobByCompany(searchId, bodyDto, <IJwtPayload>req.user);
  }

  @Patch(':searchId/update-refine-by-search-status')
  @UseGuards(AuthenticationGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_SEARCH].Write)
  async setRefineBySearch(
    @Param('searchId') searchId: string,
    @Req() req,
    @Body() bodyDto: SetGroupByCompanyRequestDto,
  ) {
    const userId = req.viewAsUser?.id || req.user.id;
    return this.jobSearchService.setRefineBySearch(searchId, bodyDto, userId);
  }
}

/* istanbul ignore file */
import { Module } from '@nestjs/common';
import { createClient } from 'redis';
import { SSEService } from './sse.service';
import { redisConfig } from '../../configs/configs.constants';
import { SSEController } from './sse.controller';

@Module({
  providers: [
    SSEService,
  ],
  exports: [
    SSEService,
  ],
  controllers: [SSEController],
})
export class SSEModule {}

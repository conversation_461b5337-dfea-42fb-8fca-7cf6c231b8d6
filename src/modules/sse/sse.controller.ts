import { Controller, Query, Sse } from '@nestjs/common';
import { SkipThrottle } from '@nestjs/throttler';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Observable, fromEvent, map } from 'rxjs';
import { JobBySearchIdQuery } from '../jobs/dto/job-by-search-id.dto';
import { ENRICH_CONTACT_DATA, ENRICH_CONTACT_FLOQER, ENRICH_FLOQER_DATA_LINKEDIN_URL, FLOQUER_ACCESS_EMAIL, FLOQUER_COMPANY_DETAIL, RECENTLY_ADDED_JOB_EVENT_NAME } from './sse.constant';

@Controller('sse')
@SkipThrottle()
export class SSEController {
  constructor(private eventEmitter: EventEmitter2) { }

  filterJobs(job: any, filters: any) {
    const isDateInRange = (date: string, start: string, end: string) => {
      const filterDate = new Date(date).getTime();
      const startDate = new Date(start).getTime();
      const endDate = new Date(end).getTime();

      return Number.isNaN(filterDate)
        || (Number.isNaN(startDate) && Number.isNaN(endDate))
        || (Number.isNaN(startDate) && filterDate <= endDate)
        || (Number.isNaN(endDate) && filterDate >= startDate)
        || (filterDate >= startDate && filterDate <= endDate);
    }

    // Source must exactly match
    if (filters.jobBoards && !filters.jobBoards.split(',').includes(job.source)) {
      return false;
    }

    // Posted date must be within the range (if provided)
    if (!isDateInRange(job.posted, filters.postedStartDate, filters.postedEndDate)) {
      return false;
    }

    // Salary must be within the range (if provided)
    if (job.max_salary && (filters.maxSalary && job.max_salary > filters.maxSalary)) {
      return false;
    }

    if (job.min_salary && (filters.minSalary && job.min_salary < filters.minSalary)) {
      return false;
    }

    if (filters.location) {
      const locationRegex = new RegExp(`(${filters.location.replace(/([.*+?^${}()|[\]\\])/g, '\\$1')})`, 'gis');
      if (!locationRegex.test(job.joblocationinput)) {
        return false;
      }
    }

    // Title, description, or company must include a keyword
    if (filters.keywords) {
      const regex = new RegExp(`(${filters.keywords.replace(/([.*+?^${}()|[\]\\])/g, '\\$1').split(',').join('|')})`, 'gis');
      const searchText = `${job.jobtitle} ${job.description} ${job.company}`;
      if (!regex.test(searchText)) {
        return false;
      }
    }

    return true; // Item matches all filters
  }

  @Sse('recent-jobs')
  connect(@Query() queryParams: JobBySearchIdQuery): Observable<MessageEvent> {
    return fromEvent(this.eventEmitter, RECENTLY_ADDED_JOB_EVENT_NAME).pipe(
      map((data: any) => {
        if (this.filterJobs(data, queryParams)) {
          return new MessageEvent(RECENTLY_ADDED_JOB_EVENT_NAME, {
            data: {
              job_id: data.job_id,
              jobtitle: data.jobtitle,
              posted: data.posted,
            }
          });
        }
      }),
    );
  }

  @Sse('enrich-data')
  sendEvents() {
     return fromEvent(this.eventEmitter, ENRICH_CONTACT_DATA).pipe(
      map((data: any) => {
        return new MessageEvent(ENRICH_CONTACT_DATA, {
          data: data
        });
      }),
    );
  }

  @Sse('enrich-data-floqer')
  sendEventsFloqua() {
     return fromEvent(this.eventEmitter, ENRICH_CONTACT_FLOQER).pipe(
      map((data: any) => {
        return new MessageEvent(ENRICH_CONTACT_FLOQER, {
          data: data
        });
      }),
    );
  }

  @Sse('enrich-data-floqer-linkedin-url')
  sendEventsFloquerLinkedinUrl() {
     return fromEvent(this.eventEmitter, ENRICH_FLOQER_DATA_LINKEDIN_URL).pipe(
      map((data: any) => {
        return new MessageEvent(ENRICH_FLOQER_DATA_LINKEDIN_URL, {
          data: data
        });
      }),
    );
  }

  @Sse('floquer-company-details')
  sendEventsFloquerCompanyDetail() {
     return fromEvent(this.eventEmitter, FLOQUER_COMPANY_DETAIL).pipe(
      map((data: any) => {
        return new MessageEvent(FLOQUER_COMPANY_DETAIL, {
          data: data
        });
      }),
    );
  }

  @Sse('floquer-access-email')
  sendEventsFloquerAccessEmail() {
     return fromEvent(this.eventEmitter, FLOQUER_ACCESS_EMAIL).pipe(
      map((data: any) => {
        return new MessageEvent(FLOQUER_ACCESS_EMAIL, {
          data: data
        });
      }),
    );
  }
}

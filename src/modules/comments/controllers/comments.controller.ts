import { Body, Controller, Delete, Get, Param, Patch, Post, Req, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { CommentService } from '../services/comment.service';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { CreateCommentRequestDto, UpdateCommentRequestDto } from '../dtos/comment.dto';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';

@ApiTags('Comment')
@Controller('comment')
@SkipThrottle()
export class CommentController {
  constructor(private readonly commentService: CommentService) {}

  @Post()
  @UseGuards(AuthenticationGuard)
  createComment(@Body() createCommentDto: CreateCommentRequestDto, @Req() req) {
    return this.commentService.createComment(createCommentDto, <IJwtPayload>req.user);
  }

  @Patch(':id')
  @UseGuards(AuthenticationGuard)
  updateCommentDetail(@Param('id') id: string, @Body() updateCommentDto: UpdateCommentRequestDto, @Req() req) {
    return this.commentService.updateCommentDetail(id, updateCommentDto, <IJwtPayload>req.user);
  }

  @Delete(':id')
  @UseGuards(AuthenticationGuard)
  deleteComment(@Param('id') id: string, @Req() req) {
    return this.commentService.deleteComment(id, <IJwtPayload>req.user);
  }

  @Get(':leadId')
  @UseGuards(AuthenticationGuard)
  getLeadComments(@Param('leadId') leadId: string) {
    return this.commentService.getCommentsByLeadId(leadId);
  }
}

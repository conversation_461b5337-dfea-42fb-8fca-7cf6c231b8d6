import { CommentRepository } from './../repositories/comment.repository';
import { ForbiddenException, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { CreateCommentRequestDto, UpdateCommentRequestDto } from '../dtos/comment.dto';
import { IJwtPayload } from 'src/modules/auth/payloads/jwt-payload.payload';

@Injectable()
export class CommentService extends BaseAbstractService {
  constructor(readonly i18nService: I18nService, readonly commentRepository: CommentRepository) {
    super(i18nService);
  }

  async createComment(createCommentDto: CreateCommentRequestDto, user: IJwtPayload) {
    try {
      const comment = { ...createCommentDto, creatorId: user.id };

      await this.commentRepository.insert(comment);

      return this.formatOutputData({ key: 'CREATE_COMMENT' }, { data: {} });
    } catch (error) {
      console.log('Error in createComment', error);
      return this.throwCommonMessage('CREATE_COMMENT', error);
    }
  }

  async getCommentsByLeadId(leadId: string) {
    const comments = await this.commentRepository.find({
      where: { leadId },
      relations: ['creator'],
      select: {
        creator: {
          isDeleted: false,
          roleId: false,
          organizationId: false,
          expiredRefreshTokenDate: false,
          potentialLeadValue: false,
          updatedBy: false,
          lastActivity: false,
          id: true,
          username: true,
          email: true,
        },
      },
    });

    return this.formatOutputData({ key: 'GET_COMMENTS_BY_LEAD_ID' }, { data: comments });
  }

  async updateCommentDetail(id: string, updateCommentDto: UpdateCommentRequestDto, user: IJwtPayload) {
    const { content } = updateCommentDto;
    const comment = await this.commentRepository.findOneBy({ id, creatorId: user.id });
    if (!comment) {
      return this.throwCommonMessage(
        'UPDATE_COMMENT_BY_ID',
        new ForbiddenException('This comment could be only edited by its owner')
      );
    }

    await this.commentRepository.update({ id }, { content });
    return this.formatOutputData({ key: 'UPDATE_COMMENT_BY_ID' }, { data: {} });
  }

  async deleteComment(id: string, user: IJwtPayload) {
    const comment = await this.commentRepository.findOneBy({ id, creatorId: user.id });
    if (!comment) {
      return this.throwCommonMessage(
        'DELETE_COMMENT',
        new ForbiddenException('This comment could be only deleted by its owner')
      );
    }

    await this.commentRepository.delete({ id });
    return this.formatOutputData({ key: 'DELETE_COMMENT' }, { data: {} });
  }
}

import { JobLead } from 'src/modules/jobs/entities/job-leads.entity';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'comments' })
export class CommentEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'lead_id' })
  leadId: string;

  @Column({ name: 'content' })
  content: string;

  @Column({ name: 'creator_id' })
  creatorId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date;

  @ManyToOne(() => JobLead, (lead) => lead.comments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'lead_id' })
  lead: JobLead;

  @ManyToOne(() => UserEntity, (user) => user.comments)
  @JoinColumn({ name: 'creator_id' })
  creator: UserEntity;
}

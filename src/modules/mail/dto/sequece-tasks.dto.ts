import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsArray, IsEnum, IsObject, IsOptional, ValidateNested } from 'class-validator';
import { RecipientInfo } from './send-email.dto';
import { SequenceTaskCreatingType, SequenceTaskStatusEnum, SequenceTaskTypeEnum } from '../entities/sequence-tasks.entity';

export class CreateSequenceTaskDto {
  @ApiProperty({ type: RecipientInfo })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => RecipientInfo)
  participants: RecipientInfo[];

  @ApiPropertyOptional()
  @IsOptional()
  content?: string;

  @ApiPropertyOptional()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  type?: SequenceTaskTypeEnum;

  @ApiPropertyOptional()
  @IsOptional()
  creatingType?: SequenceTaskCreatingType;

  @ApiPropertyOptional()
  @IsOptional()
  sequenceStepId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  status?: SequenceTaskStatusEnum;

  @ApiPropertyOptional()
  @IsOptional()
  dueDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  groupId?: string;
}

export class GetDataSequenceTaskDto {
  @ApiPropertyOptional()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @IsOptional()
  keyword?: string;

  @ApiPropertyOptional()
  @IsOptional()
  sequenceName?: string;


  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(SequenceTaskStatusEnum)
  status?: SequenceTaskStatusEnum;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(SequenceTaskTypeEnum)
  type?: SequenceTaskTypeEnum;

  @ApiPropertyOptional()
  @IsOptional()
  dueDate?: Date;
}


export class GetSequenceTasksDto extends PickType(GetDataSequenceTaskDto, [
  'limit',
  'page',
  'keyword',
  'status',
  'type',
  'dueDate',
]) {
  @ApiPropertyOptional()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(SequenceTaskCreatingType)
  creatingType?: SequenceTaskCreatingType;
}

export class UpdateSequenceTaskDto extends PickType(CreateSequenceTaskDto, [
  'creatingType',
  'content',
  'name',
  'dueDate',
]) { }

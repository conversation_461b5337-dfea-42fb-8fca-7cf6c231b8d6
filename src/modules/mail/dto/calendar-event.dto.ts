import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsObject, IsOptional, ValidateNested } from 'class-validator';
import { Participant } from 'nylas/lib/types/models/events';

export class CalendarEventDto {
    @ApiPropertyOptional()
    @IsOptional()
    title?: string;

    @ApiPropertyOptional()
    @IsOptional()
    description?: string;

    @ApiPropertyOptional()
    @IsOptional()
    location?: string;

    @ApiPropertyOptional()
    @IsOptional()
    busy?: boolean;

    @ApiProperty()
    @IsNotEmpty()
    startTime: Date;

    @ApiProperty()
    @IsNotEmpty()
    endTime: Date;

    @ApiPropertyOptional()
    @IsOptional()
    @ValidateNested({ each: true })
    participants?: Participant[];
}

export class CalendarEventsQueryDto {
    @ApiPropertyOptional()
    @IsOptional()
    start?: string;

    @ApiPropertyOptional()
    @IsOptional()
    end?: string;

    @ApiPropertyOptional()
    @IsOptional()
    limit?: number;
}

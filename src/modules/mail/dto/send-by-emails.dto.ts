import { Type } from "class-transformer";
import { IsNotEmpty, IsOptional, ValidateNested } from "class-validator";

export class SendByEmailItemDto {
    @IsNotEmpty()
    email: string;

    @IsNotEmpty()
    roleId: string;

    @IsOptional()
    organizationId?: string;

    @IsOptional()
    organizationName?: string;
}

export class SendByEmailsDto {
    @Type(() => SendByEmailItemDto)
    @ValidateNested({ each: true })
    @IsNotEmpty()
    invitedUsers: SendByEmailItemDto[];
}
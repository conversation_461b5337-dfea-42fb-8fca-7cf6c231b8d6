import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  ArrayNotEmpty,
  IsArray,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsOptional,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { SequenceStepType } from '../entities/sequence-step.entity';
import { SequenceStatus } from '../enum/sequence.enum';
import { CoreJobLead } from 'src/modules/jobs/dto/job-lead/job-lead.dto';

export enum SequenceEmailCreatedFrom {
  VACANCY = 'VACANCY',
  HOT_LIST = 'HOT_LIST',
  CONTACT_LIST = 'CONTACT_LIST',
  OPPORTUNITIES = 'OPPORTUNITY',
  LEAD = 'LEAD',
}

export class RecipientInfo {
  @IsOptional()
  id: string;

  @IsNotEmpty()
  email: string;

  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  firstName: string;

  @IsOptional()
  fax?: string;

  @IsOptional()
  phone?: string;

  @IsOptional()
  clientCorporation?: { name: string };

  @IsOptional()
  occupation?: string;

  @IsOptional()
  linkedinProfileUrl?: string;

  @IsOptional()
  massMailOptOut?: boolean;

  @IsOptional()
  companyDetail?: object;

  @IsOptional()
  address?: {
    address1?: string;
    address2?: string;
    city?: string;
    countryCod?: string;
    countryID?: number;
    countryName?: string;
    state?: string;
    timezone?: string;
    zip?: string;
  };
}

enum SequenceStepDelayUnit {
  DAY = 'DAY',
  HOUR = 'HOUR',
}

export class SequenceStepDelay {
  @IsOptional()
  delay?: number;

  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @ValidateIf((obj) => !!obj.delay)
  @IsEnum(SequenceStepDelayUnit)
  unit?: SequenceStepDelayUnit;
}

export class MailAttachmentsInfor {
  fileId: string;
  name: string;
  uid?: string;
  thumbUrl?: string;
  lastModifiedDate?: string | Date;
  size?: string | number;
  type?: string;
  url?: string;
}

export class MailInfo {
  @ApiPropertyOptional()
  @IsOptional()
  subject?: string;

  @ApiPropertyOptional()
  @IsOptional()
  content?: string;

  @ApiPropertyOptional()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  delays: SequenceStepDelay[];

  @ApiPropertyOptional()
  @ApiProperty()
  @IsOptional()
  signatureId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  type?: SequenceStepType;

  @ApiProperty()
  @IsOptional()
  threadId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @Type(() => RecipientInfo)
  @ValidateNested({ each: true })
  recipients?: RecipientInfo[];

  @ApiPropertyOptional()
  @IsOptional()
  hotlistIds?: string[] | number[];

  @ApiPropertyOptional()
  @IsOptional()
  contactListIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  fileList?: MailAttachmentsInfor[];

  @ApiPropertyOptional()
  @IsOptional()
  attachments?: MailAttachmentsInfor[];
}

export enum JobType {
  EXTERNAL = 'external',
  INTERNAL = 'internal',
}

export class GetAllQuery {
  @ApiPropertyOptional()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @IsOptional()
  keyword?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(SequenceStatus)
  status?: SequenceStatus;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(SequenceEmailCreatedFrom)
  createdFrom?: SequenceEmailCreatedFrom;

  @ApiPropertyOptional()
  @IsOptional()
  emailSearch?: string;

  @ApiPropertyOptional()
  @IsOptional()
  fromDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  toDate?: Date;
}

export class UpdateSequenceDto {
  @IsOptional()
  // @IsEnum(SequenceStatus)
  status: SequenceStatus;

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Boolean)
  isStared: boolean;
}

export class AddContactDto {
  @ApiProperty()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @Type(() => String)
  emailSeqIds: string[];

  @ApiProperty()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @Type(() => RecipientInfo)
  @ValidateNested({ each: true })
  recipients: RecipientInfo[];
}

export class JobTypeDto {
  @IsOptional()
  type: JobType;
}

export class SendEmailDto {
  @ApiProperty()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @Type(() => RecipientInfo)
  @ValidateNested({ each: true })
  recipients: RecipientInfo[];

  @ApiProperty()
  @IsOptional()
  jobBoardId: string;

  @ApiProperty()
  @Type(() => MailInfo)
  @ValidateNested({ each: true })
  primaryMail: MailInfo;

  @ApiProperty()
  @IsNotEmpty()
  isNotSendSequence: boolean;

  @ApiProperty()
  @IsOptional()
  externalJobId: string;

  @ApiProperty()
  @IsOptional()
  emailSeqId: string;

  @ApiProperty()
  @IsOptional()
  rawSequence?: object;

  @ApiProperty()
  @IsOptional()
  tearsheetId: string;

  @ApiProperty()
  @IsOptional()
  contactListId: string;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  placeHolders: string[];

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsIn(Object.values(SequenceEmailCreatedFrom), { each: true })
  createdFrom: string[];
}

export class LiveFeedDto {
  @ApiPropertyOptional()
  @IsOptional()
  fromDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  toDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  consultant?: string; // user_id

  @ApiPropertyOptional()
  @IsOptional()
  country?: string;

  @ApiPropertyOptional()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional()
  @IsOptional()
  offset?: number;

  @ApiPropertyOptional()
  @IsOptional()
  type?: string;
}

export class sendTestEmailDto {
  @ApiProperty()
  @Type(() => MailInfo)
  @ValidateNested({ each: true })
  mail: MailInfo;

  @ApiPropertyOptional()
  @IsOptional()
  job: CoreJobLead;

  @ApiPropertyOptional()
  @IsOptional()
  companyDetail: any;
}

export class ValidateContactsInActiveSequenceDto {
  @IsNotEmpty()
  contactEmails: string[]
}

import { SequenceActivityType } from './../entities/sequence-activity-log.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RecipientInfo } from './send-email.dto';
import {
  ArrayMinSize,
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsOptional,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SequenceStepType } from '../entities/sequence-step.entity';

export class ParticipantsDto {
  @ApiPropertyOptional({ type: [RecipientInfo] })
  @IsOptional()
  // @ValidateIf((obj) => !obj.hotlistIds?.length && !obj.contactListIds?.length && !obj.shortListIds?.length)
  @IsArray()
  // @ArrayNotEmpty()
  @IsNotEmpty({
    each: true,
    message:
      'recipients array must have at least one element if both contactListIds and hotlistIds are not present or empty',
  })
  @Type(() => RecipientInfo)
  @ValidateNested({ each: true })
  recipients?: RecipientInfo[];

  @ApiPropertyOptional()
  @IsOptional()
  contactListIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  hotlistIds?: string[];


  @ApiPropertyOptional()
  @IsOptional()
  shortListIds?: string[];
}

export class SequencePartialDto {
  @ApiProperty()
  @IsNotEmpty()
  name: string;
}

export class SequenceActivityQueryDto { 
  @ApiPropertyOptional()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @IsOptional()
  limit?: number; 

  @ApiPropertyOptional()
  @IsOptional()
  activityType?: SequenceActivityType;

  @ApiPropertyOptional()
  @IsOptional()
  stepId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  stepType?: SequenceStepType;

  @ApiPropertyOptional()
  @IsOptional()
  searchText?: string;
}

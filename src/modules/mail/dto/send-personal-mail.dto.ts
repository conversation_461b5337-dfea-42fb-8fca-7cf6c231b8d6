import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMinSize, ArrayNotEmpty, IsArray, IsEnum, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { SequenceStatus } from '../entities/sequence.entity';

export class RecipientInfo {
  @IsNotEmpty()
  email: string;

  @IsOptional()
  name?: string;
}

export class SendPersonalMailDto {
  @ApiProperty()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @Type(() => RecipientInfo)
  @ValidateNested({ each: true })
  recipients: RecipientInfo[];

  @IsNotEmpty()
  subject: string;

  @IsNotEmpty()
  content: string;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1)
  @Type(() => RecipientInfo)
  @ValidateNested({ each: true })
  replyTo?: RecipientInfo[];

  @IsOptional()
  replyToMessageId: string;
}

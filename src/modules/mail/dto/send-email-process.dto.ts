import { JobLead } from 'src/modules/jobs/entities/job-leads.entity';
import { SequenceStepEntity } from '../entities/sequence-step.entity';
import { UserEntity } from 'src/modules/user/entities/user.entity';

export interface ISendMail {
  currentUser: UserEntity;
  jobId: string;
  externalId: string;
  existedMails: SequenceStepEntity[];
  jobLead: JobLead;
}

export const MERGE_TAGS_PLACE_HOLDERS = [
  '{{COMPANY_NAME}}',
  '{{RECIPIENT_NAME}}',
  '{{RECIPIENT_EMAIL}}',
  '{{RECIPIENT_FAX}}',
  '{{RECIPIENT_PHONE_NUMBER}}',
  '{{COMPANY_NAME}}',
  '{{RECIPIENT_OCCUPATION}}',
  '{{RECIPIENT_ADDRESS_1}}',
  '{{RECIPIENT_ADDRESS_2}}',
  '{{RECIPIENT_ADDRESS_CITY}}',
  '{{RECIPIENT_ADDRESS_COUNTRY}}',
  '{{RECIPIENT_ADDRESS_STATE}}',
  '{{RECIPIENT_ADDRESS_TIMEZONE}}',
  '{{VACANCY_ADDRESS_LINK}}',
  '{{VACANCY_ADDRESS}}',
  // '{{VACANCY_ADDRESS_2}}',
  '{{VACANCY_ATS_ID}}',
  '{{VACANCY_CATEGORY}}',
  '{{VACANCY_CITY}}',
  '{{VACANCY_COMPANY_ATS_ID}}',
  '{{VACANCY_COMPANY_NAME}}',
  '{{VACANCY_COUNTRY}}',
  '{{VACANCY_EMPLOYMENT_TYPE}}',
  '{{VACANCY_INDUSTRIES}}',
  '{{VACANCY_IS_PUBLIC}}',
  '{{VACANCY_NUMBER_OF_OPENINGS}}',
  '{{VACANCY_NUMBER_OF_PLACEMENTS}}',
  '{{VACANCY_NUMBER_OF_SUBMISSIONS}}',
  '{{VACANCY_OWNER_CALENDAR_LINK}}',
  '{{VACANCY_OWNER_EMAIL}}',
  '{{VACANCY_OWNER_FIRST_NAME}}',
  '{{VACANCY_OWNER_LAST_NAME}}',
  '{{VACANCY_OWNER_PHONE_NUMBER}}',
  '{{VACANCY_OWNER_POSITION_TITLE}}',
  '{{VACANCY_OWNER_FULL_NAME}}',
  '{{VACANCY_COUNTY}}',
  '{{VACANCY_POST_CODE}}',
  '{{VACANCY_PAY_UNIT}}',
  '{{VACANCY_SALARY}}',
  '{{VACANCY_PAY_RATE}}',
  '{{VACANCY_PERM_FEE}}',
  '{{VACANCY_MARK_UP}}',
  '{{VACANCY_TITLE}}',
  '{{VACANCY_SKILLS}}',
  '{{SENDER_NAME}}',
  '{{SENDER_EMAIL}}',
  '{{SENT_DATE_TIME}}',
  '{{sender_first_name}}',
  '{{CURRENT_SENT_DATE_TIME}}',
  '{{COMPANY_MAIN_ADDRESS}}',
  '{{COMPANY_ADDRESS_2}}',
  '{{COMPANY_ADDRESS_CITY}}',
  '{{COMPANY_ADDRESS_STATE}}',
  '{{COMPANY_ADDRESS_POST_CODE}}',
  '{{COMPANY_ADDRESS_COUNTRY}}',
  // Keep the old merge tags for live sequences  
  '{{RECIPIENT_CLIENT_CORPORATION}}',
  '{{JOB_ADDRESS_LINK}}',
  '{{JOB_ADDRESS_1}}',
  '{{JOB_ADDRESS_2}}',
  '{{JOB_ATS_ID}}',
  '{{JOB_CATEGORY}}',
  '{{JOB_CITY}}',
  '{{JOB_COMPANY_ATS_ID}}',
  '{{JOB_COMPANY_NAME}}',
  '{{JOB_COUNTRY}}',
  '{{JOB_EMPLOYMENT_TYPE}}',
  '{{JOB_INDUSTRIES}}',
  '{{JOB_IS_PUBLIC}}',
  '{{JOB_NUMBER_OF_OPENINGS}}',
  '{{JOB_NUMBER_OF_PLACEMENTS}}',
  '{{JOB_NUMBER_OF_SUBMISSIONS}}',
  '{{JOB_OWNER_CALENDAR_LINK}}',
  '{{JOB_OWNER_EMAIL}}',
  '{{JOB_OWNER_FIRST_NAME}}',
  '{{JOB_OWNER_LAST_NAME}}',
  '{{JOB_OWNER_PHONE_NUMBER}}',
  '{{JOB_OWNER_POSITION_TITLE}}',
  '{{JOB_OWNER_FULL_NAME}}',
  '{{JOB_SKILLS}}',
  '{{JOB_COUNTY}}',
  '{{JOB_POST_CODE}}',
  '{{JOB_PAY_UNIT}}',
  '{{JOB_SALARY}}',
  '{{JOB_PAY_RATE}}',
  '{{JOB_PERM_FEE}}',
  '{{JOB_MARK_UP}}',
  '{{JOB_TITLE}}',
];

export enum MergeTagMappingEnum {
  // COMPANY_NAME = 'sequence.companyName',
  RECIPIENT_NAME = 'recipient.firstName',
  RECIPIENT_EMAIL = 'recipient.email',
  RECIPIENT_FAX = 'recipient.fax',
  RECIPIENT_PHONE_NUMBER = 'recipient.phone',
  COMPANY_NAME = 'recipient.clientCorporation.name',
  RECIPIENT_OCCUPATION = 'recipient.occupation',
  RECIPIENT_ADDRESS_1 = 'recipient.address.address1',
  RECIPIENT_ADDRESS_2 = 'recipient.address.address2',
  RECIPIENT_ADDRESS_CITY = 'recipient.address.city',
  RECIPIENT_ADDRESS_COUNTRY = 'recipient.address.countryName',
  RECIPIENT_ADDRESS_STATE = 'recipient.address.state',
  RECIPIENT_ADDRESS_TIMEZONE = 'recipient.address.timezone',
  VACANCY_ADDRESS = 'job.address_line_1',
  // VACANCY_ADDRESS_2 = 'job.address_line_2',
  VACANCY_CITY = 'job.address_city',
  VACANCY_COMPANY_NAME = 'job.company_name',
  VACANCY_COUNTRY = 'job.address_country',
  VACANCY_EMPLOYMENT_TYPE = 'job.employment_type',
  VACANCY_OWNER_EMAIL = 'job.owner.email', // current User
  VACANCY_OWNER_FIRST_NAME = 'job.owner.firstName', // currentUser
  VACANCY_OWNER_FULL_NAME = 'job.owner.fullName', // currentUser
  VACANCY_SALARY = 'job.salary',
  VACANCY_TITLE = 'job.title',
  VACANCY_SKILLS = 'job.skills',

  SENDER_NAME = 'sender.name',
  SENDER_EMAIL = 'sender.email',
  SENT_DATE_TIME = 'sentDateTime',
  sender_first_name = 'sender.name',

  CURRENT_SENT_DATE_TIME = 'currentSentDateTime',

  COMPANY_MAIN_ADDRESS = "recipient.companyDetail.address.address1",
  COMPANY_ADDRESS_2 = "recipient.companyDetail.address.address2",
  COMPANY_ADDRESS_CITY = "recipient.companyDetail.address.city",
  COMPANY_ADDRESS_COUNTRY = "recipient.companyDetail.address.countryName",
  COMPANY_ADDRESS_POST_CODE = "recipient.companyDetail.address.zip",
  COMPANY_ADDRESS_STATE = "recipient.companyDetail.address.state",

  // Keep the old merge tags for live sequences    
  RECIPIENT_CLIENT_CORPORATION = 'recipient.clientCorporation.name',
  JOB_ADDRESS_1 = 'job.address_line_1',
  JOB_ADDRESS_2 = 'job.address_line_2',
  JOB_CITY = 'job.address_city',
  JOB_COMPANY_NAME = 'job.company_name',
  JOB_COUNTRY = 'job.address_country',
  JOB_EMPLOYMENT_TYPE = 'job.employment_type',
  JOB_OWNER_EMAIL = 'job.owner.email', // current User
  JOB_OWNER_FIRST_NAME = 'job.owner.firstName', // currentUser
  JOB_OWNER_FULL_NAME = 'job.owner.fullName', // currentUser
  JOB_SALARY = 'job.salary',
  JOB_TITLE = 'job.title',
}

import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  ArrayNotEmpty,
  IsArray,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { RecipientInfo } from './send-email.dto';

export class EmailGenerationDto {
  @ApiProperty()
  @IsNotEmpty()
  jobBoardId: string;

  @ApiProperty()
  @IsArray()
  @ArrayMinSize(0)
  @Type(() => RecipientInfo)
  @ValidateNested({ each: true })
  @IsOptional()
  recipients: RecipientInfo[];
}

export class EmailGenerationFromUnfinished {
  @ApiProperty()
  @IsNotEmpty()
  jobBoardId: string;

  @ApiProperty()
  @IsOptional()
  unfinishedContent: string;
}

export class EmailGenerationFromChild {
  @ApiProperty()
  @IsNotEmpty()
  title?: string;

  @ApiProperty()
  @IsOptional()
  content?: string;

  @ApiProperty()
  @IsOptional()
  company?: string;

  @ApiProperty()
  @IsOptional()
  address?: string;
}

export class FreeEmailGenerationDTO {
  @ApiProperty()
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsOptional()
  type: string;

  @ApiProperty()
  @IsOptional()
  location: string;

  @ApiProperty()
  @IsOptional()
  position: string;

  @ApiProperty()
  @IsOptional()
  unfinishedContent: string;
}

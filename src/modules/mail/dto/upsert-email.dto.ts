import { ApiProperty, ApiPropertyOptional, PartialType, PickType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNotEmptyObject,
  IsObject,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { MailInfo, SequenceEmailCreatedFrom } from './send-email.dto';
import { ParticipantsDto } from './sequence.dto';

export enum STOP_BY_TYPE {
  INDIVIDUALLY = 'INDIVIDUALLY',
  COMPANY = 'COMPANY',
}

export class StopRulesDto {
  @IsOptional()
  domain: string;

  @IsOptional()
  email: string;

  @IsOptional()
  stopAll?: boolean;

  @IsOptional()
  @IsEnum(STOP_BY_TYPE)
  stopBy?: STOP_BY_TYPE;
}

export class ScheduleInformationDto {
  @ApiPropertyOptional()
  @IsOptional()
  timezone?: string;

  @ApiPropertyOptional()
  @IsOptional()
  offset?: string;

  @ApiPropertyOptional()
  @IsOptional()
  utc?: string;

  @ApiPropertyOptional()
  @IsOptional()
  ISO3166?: string;

  @ApiPropertyOptional()
  @IsOptional()
  triggerAt?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  isTriggeredNow?: boolean;
}

export class UpsertChildSequenceDto {
  @ApiProperty()
  @IsArray()
  @ArrayMinSize(0)
  @Type(() => MailInfo)
  @ValidateNested({ each: true })
  mails: MailInfo[];

  @ApiPropertyOptional()
  @IsOptional()
  skippedEmails?: string[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  placeHolders: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @Type(() => StopRulesDto)
  @ValidateNested({ each: true })
  stopRules?: StopRulesDto[];

  @ApiPropertyOptional({ type: ScheduleInformationDto })
  @Type(() => ScheduleInformationDto)
  @ValidateNested()
  scheduleInformation: ScheduleInformationDto;

  @ApiPropertyOptional()
  @IsOptional()
  timeZone?: string;

  @ApiPropertyOptional({ type: ParticipantsDto })
  @Type(() => ParticipantsDto)
  @ValidateNested()
  participants: ParticipantsDto;
}

export class UpsertEmailDto {
  @ApiProperty()
  @IsOptional()
  jobBoardId: string;

  @ApiPropertyOptional()
  @IsOptional()
  jobBoardName?: string;

  @ApiProperty()
  @IsArray()
  @ArrayMinSize(0)
  @Type(() => MailInfo)
  @ValidateNested({ each: true })
  mails: MailInfo[];

  @ApiProperty()
  @IsOptional()
  externalJobId: string;

  @ApiProperty()
  @IsOptional()
  crmLeadId?: string;

  @ApiProperty()
  @IsOptional()
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  emailSeqId?: string;

  @ApiProperty()
  @IsOptional()
  rawSequence?: object;

  @ApiPropertyOptional()
  @IsOptional()
  skippedEmails?: string[];

  @ApiProperty()
  @IsOptional()
  companyId: string;

  @ApiProperty()
  @IsOptional()
  companyName: string;

  @ApiPropertyOptional()
  @IsOptional()
  country: string;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  placeHolders: string[];

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsIn(Object.values(SequenceEmailCreatedFrom), { each: true })
  createdFrom?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @Type(() => StopRulesDto)
  @ValidateNested({ each: true })
  stopRules?: StopRulesDto[];

  @ApiPropertyOptional({ type: ScheduleInformationDto })
  @Type(() => ScheduleInformationDto)
  @ValidateNested()
  scheduleInformation: ScheduleInformationDto;

  @ApiPropertyOptional()
  @IsOptional()
  sequenceTemplateId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  timeZone?: string;

  @ApiProperty({ type: ParticipantsDto })
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => ParticipantsDto)
  participants: ParticipantsDto;
}

export class UpsertEmailChildDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  emailSeqId: string;

  @ApiProperty()
  @IsObject()
  @IsNotEmptyObject()
  @Type(() => UpsertChildSequenceDto)
  @ValidateNested()
  childSequence: UpsertChildSequenceDto;
}

export class DeleteSequencesDto {
  @ApiProperty()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  sequenceIds: string[];
}

export class BulkStopSequenceDto extends DeleteSequencesDto {}

export class UpsertEmailDraftDto extends PartialType(UpsertEmailDto) {
  @ApiPropertyOptional({ type: ParticipantsDto })
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => ParticipantsDto)
  participants?: ParticipantsDto;
}
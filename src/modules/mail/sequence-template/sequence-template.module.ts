import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SequenceTemplateEntity } from '../entities/sequence-template.entity';
import { SequenceTemplateController } from './sequence-template.controller';
import { SequenceTemplateService } from './sequence-template.service';

@Module({
  imports: [TypeOrmModule.forFeature([SequenceTemplateEntity])],
  controllers: [SequenceTemplateController],
  providers: [SequenceTemplateService]
})
export class SequenceTemplatesModule {}
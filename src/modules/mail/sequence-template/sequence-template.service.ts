import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { SequenceTemplateEntity } from '../entities/sequence-template.entity';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { QUERY_TEMPLATE_TYPE, TemplateQueryDto } from './dto/template-query.dto';

@Injectable()
export class SequenceTemplateService extends BaseAbstractService {
  constructor(
    @InjectRepository(SequenceTemplateEntity)
    private sequenceTemplateRepository: Repository<SequenceTemplateEntity>,
    private readonly i18nService: I18nService,
    private dataSource: DataSource
  ) {
    super(i18nService);
  }

  async getUserById(id: string) {
    return this.dataSource.createQueryBuilder(UserEntity, 'u').where({ id }).getOne();
  }

  async create(sequenceTemplate: Partial<SequenceTemplateEntity>): Promise<SequenceTemplateEntity | any> {
    try {
      const newSequenceTemplate = this.sequenceTemplateRepository.create({
        ...sequenceTemplate,
        organizationId: sequenceTemplate?.organizationId || null,
        // createdAt: new Date(),
        // updatedAt: new Date(),
      });
      const data = await this.sequenceTemplateRepository.save(newSequenceTemplate);
      return this.formatOutputData({ key: 'CREATE_SEQUENCE_TEMPLATE' }, { data });
    } catch (error) {
      console.log('[Error in sequence-template.service.ts line 30]', error);
      return this.throwCommonMessage('CREATE_SEQUENCE_TEMPLATE', error);
    }
  }

  async findAll(req, query: TemplateQueryDto): Promise<{ data: SequenceTemplateEntity[]; count: number } | any> {
    const { viewAs, page, limit, search, type } = query;
    const offset = (page - 1) * limit || 0;
    const limitPage = limit || 10;

    try {
      let organizationIdTemp = '';
      const { organizationId, id } = req?.user;
      console.log('organizationId', viewAs);
      // const { viewAs } = req?.query;
      if (id === viewAs) {
        organizationIdTemp = organizationId;
      } else {
        const viewAsUser = await this.getUserById(viewAs);
        organizationIdTemp = viewAsUser.organizationId;
      }
      // const sequenceTemplates = await this.sequenceTemplateRepository.find();
      const queryBuilder = this.sequenceTemplateRepository
        .createQueryBuilder('sequence_template')
        .offset(offset)
        .limit(limitPage)
        .select(
          `sequence_template.id, 
            sequence_template.name, 
            sequence_template.organization_id as "organizationId", 
            sequence_template.modified_by as "modifiedBy", 
            sequence_template.created_by as "createdBy", 
            sequence_template.created_at as "createdAt", 
            sequence_template.updated_at as "updatedAt", 
            user1.fullname as "createdFullName", 
            user2.fullname as "modifiedFullName", 
            user1.avatar_id as "createdAvatarId", 
            user2.avatar_id as "modifiedAvatarId", 
            COALESCE(SUM(sequences.opened), 0) AS "totalOpened", 
            COALESCE(SUM(sequences.replied_count), 0) AS "totalReply", 
            COALESCE(SUM(sequences.sent_count), 0) AS "totalSent"`
        )
        .innerJoin('users', 'user1', `sequence_template.created_by = "user1"."id"::text`)
        .innerJoin('users', 'user2', `"user2"."id"::text = sequence_template.modified_by`)
        .leftJoin('sequences', 'sequences', `sequences.sequence_template_id = sequence_template.id`)
        .groupBy('sequence_template.id, user1.fullname, user2.fullname, user1.avatar_id, user2.avatar_id');

      if (organizationIdTemp && type === QUERY_TEMPLATE_TYPE.COMPANY_TEMPLATE) {
        queryBuilder.where(
          '( sequence_template.organization_id = :organizationId AND sequence_template.created_by != :userId )',
          {
            organizationId: organizationIdTemp,
            userId: viewAs,
          }
        );
      }

      if ((viewAs || id) && type === QUERY_TEMPLATE_TYPE.MY_TEMPLATE) {
        queryBuilder.where('( sequence_template.created_by = :id)', {
          id: viewAs || id,
        });
      }

      if (search) {
        queryBuilder.andWhere('sequence_template.name ILIKE :search', { search: `%${search}%` });
      }
      // Get data template and count
      const [sequenceTemplates, count] = await Promise.all([
        queryBuilder.orderBy('sequence_template.updated_at', 'DESC').getRawMany(),
        queryBuilder.getCount(),
      ]);
      const data = {
        items: sequenceTemplates,
        count,
      };
      return this.formatOutputData({ key: 'GET_ALL_SEQUENCE_TEMPLATES' }, { data: data });
    } catch (error) {
      console.log('[Error in sequence-template.service.ts line 57]', error);
      return this.throwCommonMessage('GET_ALL_SEQUENCE_TEMPLATES', error);
    }
  }

  async findOne(id: any): Promise<SequenceTemplateEntity | null | any> {
    try {
      const sequenceTemplate = await this.sequenceTemplateRepository.findOneBy({ id });
      if (!sequenceTemplate) {
        throw new NotFoundException('Sequence Template does not exist!');
      } else {
        return this.formatOutputData({ key: 'GET_SEQUENCE_TEMPLATE' }, { data: sequenceTemplate });
      }
    } catch (error) {
      console.log('[Error in sequence-template.service.ts line 71]', error);
      return this.throwCommonMessage('GET_SEQUENCE_TEMPLATE', error);
    }
  }

  async update(id: number, sequenceTemplate: Partial<SequenceTemplateEntity>): Promise<any> {
    try {
      await this.sequenceTemplateRepository.update(id, sequenceTemplate);
      const sequenceTemplateItem = await this.sequenceTemplateRepository.findOne({ where: { id } });
      return this.formatOutputData({ key: 'UPDATE_SEQUENCE_TEMPLATE' }, { data: sequenceTemplateItem });
    } catch (error) {
      console.log('[Error in sequence-template.service.ts line 82]', error);
      return this.throwCommonMessage('UPDATE_SEQUENCE_TEMPLATE', error);
    }
  }

  async remove(id: any): Promise<any> {
    try {
      const sequenceTemplate = await this.findOne(id);
      if (!sequenceTemplate) {
        throw new NotFoundException('Sequence Template does not exist!');
      }

      await this.sequenceTemplateRepository.delete(id);

      return this.formatOutputData({ key: 'REMOVE_SEQUENCE_TEMPLATE' }, { data: {} });
    } catch (error) {
      console.log('[Error in sequence-template.service.ts line 98]', error);
      return this.throwCommonMessage('REMOVE_SEQUENCE_TEMPLATE', error);
    }
  }
}

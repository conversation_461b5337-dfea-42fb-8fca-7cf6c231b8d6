import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';

export enum QUERY_TEMPLATE_TYPE {
  MY_TEMPLATE = 'MY_TEMPLATE',
  COMPANY_TEMPLATE = 'COMPANY_TEMPLATE',
}

export class TemplateQueryDto {
  @ApiProperty()
  @IsNotEmpty()
  viewAs: string;

  @ApiPropertyOptional()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({ default: QUERY_TEMPLATE_TYPE.COMPANY_TEMPLATE })
  @IsEnum(QUERY_TEMPLATE_TYPE)
  @IsOptional()
  type?: QUERY_TEMPLATE_TYPE;
}

import { Body, Controller, Delete, Get, Param, Post, Put, Req, UseGuards, Request, Query } from '@nestjs/common';
import { ApiBadRequestResponse, ApiNotFoundResponse, ApiTags, ApiUnauthorizedResponse } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { ResponseMessage } from 'src/common/constants/common.constant';
import { BaseErrorResponseDto } from 'src/common/dto/common.dto';
import { SequenceTemplateEntity } from '../entities/sequence-template.entity';
import { SequenceTemplateService } from './sequence-template.service';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import {TemplateQueryDto} from './dto/template-query.dto'

@ApiTags('SequenceTemplate')
@Controller('sequence-template')
@SkipThrottle()
@ApiBadRequestResponse({
  description: ResponseMessage.Common.BAD_REQUEST,
  type: BaseErrorResponseDto,
})
@ApiNotFoundResponse({
  description: ResponseMessage.Common.NOT_FOUND,
  type: BaseErrorResponseDto,
})
@ApiUnauthorizedResponse({
  description: ResponseMessage.Common.UNAUTHORIZED,
  type: BaseErrorResponseDto,
})
@UseGuards(AuthenticationGuard)

export class SequenceTemplateController {
  constructor(private readonly sequenceTemplateService: SequenceTemplateService) {}

  // @Post('send-invitation-email')
  // sendInvitationEmail(@Body() sendByEmailsDto: SendByEmailsDto) {
  //     const { emails } = sendByEmailsDto
  //     return this.mailService.sendInvitationEmailByEmails(emails);
  // }

  @Get('healthcheck')
  async generateEmail(@Req() req) {
    // const result = await this.mailService.generateEmail(<IJwtPayload>req.user, generateEmailDto);
    return req.user;
  }

  @Post()
  async create(@Body() sequenceTemplate: SequenceTemplateEntity): Promise<SequenceTemplateEntity> {
    return this.sequenceTemplateService.create(sequenceTemplate);
  }

  @Get()
  async findAll(
    @Request() req,
    @Query() query: TemplateQueryDto,
  ): Promise<{ data: SequenceTemplateEntity[], count: number }> {
    return this.sequenceTemplateService.findAll(req, query);
  }

  @Get(':id')
  async findOne(@Param('id') id: any): Promise<SequenceTemplateEntity> {
    return await this.sequenceTemplateService.findOne(id);
  }

  @Put(':id')
  async update(@Param('id') id: any, @Body() user: SequenceTemplateEntity): Promise<any> {
    return this.sequenceTemplateService.update(id, user);
  }

  @Delete(':id')
  async delete(@Param('id') id: any): Promise<any> {
    return this.sequenceTemplateService.remove(id);
  }
}

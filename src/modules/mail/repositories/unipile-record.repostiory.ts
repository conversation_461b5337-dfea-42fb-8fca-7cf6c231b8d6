import { Injectable } from '@nestjs/common';
import { DataSource, DeepPartial, Repository } from 'typeorm';
import { SequenceEntity } from '../entities/sequence.entity';
import { UnipileRecordEntity } from '../entities/unipile-record.entity';

@Injectable()
export class UnipileRecordRepository extends Repository<UnipileRecordEntity> {
  constructor(private dataSource: DataSource) {
    super(UnipileRecordEntity, dataSource.createEntityManager());
  }
}

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { SequenceActivityLogEntity } from '../entities/sequence-activity-log.entity';

@Injectable()
export class SequenceActivityLogRepository extends Repository<SequenceActivityLogEntity> {
  constructor(private dataSource: DataSource) {
    super(SequenceActivityLogEntity, dataSource.createEntityManager());
  }
}

import { MiddlewareConsumer, Module, forwardRef } from '@nestjs/common';
import { MailController } from './mail.controller';
import { MailService } from './mail.service';
import { OpenAIService } from '../openai/openai.service';
import { HttpModule } from '@nestjs/axios';
import { SequenceStepRepository } from './repositories/sequence-step.repostiory';
import { SequenceRepository } from './repositories/sequence.repostiory';
import { JobBoardsRepository } from '../jobs/repository/job-boards.repository';
import { UserRepository } from '../user/repositories/user.repository';
import { MulterMiddleware } from '../../middlewares/multer.middleware';
import { OpensearchModule } from '../opensearch/opensearch.module';
import { JobsModule } from '../jobs/jobs.module';
import { JobLeadsRepository } from '../jobs/repository/job-leads.repository';
// Sequence template
import { SequenceTemplateRepository } from './repositories/sequence-template.repository';
import { SequenceTemplatesModule } from './sequence-template/sequence-template.module';
import { SequenceActivityLogRepository } from './repositories/sequence-activity-log.repository';
import { UserSignatureRepository } from '../user/repositories/user-signature.repository';
import { BullHornMiddleware } from '../../middlewares/bullhorn/bullhorn.middleware';
import { BullHornService } from '../../middlewares/bullhorn/bullhorn.service';
import { ContactRepository } from '../user/repositories/contact.repository';
import { SequenceInstanceRepository } from './repositories/sequence-instance.repository';
import { SequenceStepTaskRepository } from './repositories/sequence-step-task.repository';
import { MyCacheModule } from '../cache/cache.module';
import { BullModule } from '@nestjs/bullmq';
import { BULL_QUEUES } from 'src/configs/configs.constants';
import { UserSignatureServices } from '../user/user-signature.service';
import { EmailValidationResultRepository } from '../email-finder/repositories/email-validation-result.repository';
import { SequenceTaskRepository } from './repositories/sequence-tasks.repository';
import { UnipileRecordRepository } from './repositories/unipile-record.repostiory';
import { LinkedInFinderService } from '../linkedin-finder/linkedin-finder.service';
import { ApolloService } from '../employee-finder/services/apollo.service';
import { CrmBullhornService } from '../crm/services/crm-bullhorn.service';
import { CrmContactRepository } from '../crm/repositories/crm-contact.repository';
import { CrmContactSequenceStepRepository } from '../crm/repositories/crm-contact-sequence-step.repository';
import { CrmContactSequenceRepository } from '../crm/repositories/crm-contact-sequence.repository';
import { CrmCompanyRepository } from '../crm/repositories/crm-company.repository';
import { CrmLeadRepository } from '../crm/repositories/crm-lead.repository';
import { CrmNoteRepository } from '../crm/repositories/crm-note.repository';
import { CrmTagRepository } from '../crm/repositories/crm-tag.repository';
import { CrmIndustryRepository } from '../crm/repositories/crm-industry.repository';
import { CrmSkillsRepository } from '../crm/repositories/crm-skill.repository';
import { ActivityLogService } from './services/activity-log.service';

@Module({
  imports: [
    MyCacheModule,
    HttpModule,
    OpensearchModule,
    forwardRef(() => JobsModule),
    SequenceTemplatesModule,
    BullModule.registerQueue(
      {
        name: BULL_QUEUES.SEQUENCE_STEP_TASK,
        prefix: '{bull_sequence_step_task}',
      },
      {
        name: BULL_QUEUES.WEBHOOK_SENDGRID_EVENT,
        prefix: '{bull_webhook_sendgrid_event}',
      }
    ),
  ],
  providers: [
    MailService,
    OpenAIService,
    SequenceRepository,
    SequenceStepRepository,
    SequenceInstanceRepository,
    SequenceStepTaskRepository,
    SequenceActivityLogRepository,
    JobBoardsRepository,
    UserRepository,
    JobLeadsRepository,
    SequenceTemplateRepository,
    UserSignatureRepository,
    BullHornService,
    ContactRepository,
    UserSignatureServices,
    EmailValidationResultRepository,
    SequenceTaskRepository,
    UnipileRecordRepository,
    LinkedInFinderService,
    ApolloService,
    CrmBullhornService,
    CrmContactRepository,
    CrmCompanyRepository,
    CrmLeadRepository,
    CrmNoteRepository,
    CrmContactSequenceStepRepository,
    CrmContactSequenceRepository,
    CrmSkillsRepository,
    CrmIndustryRepository,
    CrmTagRepository,
    ActivityLogService,
  ],
  controllers: [MailController],
  exports: [MailService],
})
export class MailModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MulterMiddleware).forRoutes('emails/incoming-mails');
    // consumer.apply(BullHornMiddleware).forRoutes(MailController);
  }
}

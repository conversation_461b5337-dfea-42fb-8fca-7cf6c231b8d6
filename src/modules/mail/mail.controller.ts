import { ActivityLogService } from './services/activity-log.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { MailService } from './mail.service';
import { ApiBadRequestResponse, ApiBody, ApiConsumes, ApiNotFoundResponse, ApiTags, ApiUnauthorizedResponse } from '@nestjs/swagger';
import {
  EmailGenerationDto,
  EmailGenerationFromChild,
  EmailGenerationFromUnfinished,
  FreeEmailGenerationDTO,
} from './dto/email-generation.dto';
import { SkipThrottle } from '@nestjs/throttler';
import { ResponseMessage } from 'src/common/constants/common.constant';
import { BaseErrorResponseDto } from 'src/common/dto/common.dto';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { IJwtPayload } from '../auth/payloads/jwt-payload.payload';
import { AddContactDto, GetAllQuery, JobTypeDto, LiveFeedDto, SendEmailDto, sendTestEmailDto, UpdateSequenceDto, ValidateContactsInActiveSequenceDto } from './dto/send-email.dto';
import { BulkStopSequenceDto, DeleteSequencesDto, UpsertEmailChildDto, UpsertEmailDraftDto, UpsertEmailDto } from './dto/upsert-email.dto';
import { MulterMiddleware } from '../../middlewares/multer.middleware';
import { DeleteThreadsDto, ThreadMailDto, UpdateThreadMailDto } from './dto/thread-mail.dto';
import { SendPersonalMailDto } from './dto/send-personal-mail.dto';
import { ValidEmailDto, ValidListEmailDto } from './dto/valid-email.dto';
import { CalendarEventDto, CalendarEventsQueryDto } from './dto/calendar-event.dto';
import { MAX_IMAGE_SIZE_IN_BYTES } from '../user/constants/user-signature.constants';
import { FilesInterceptor } from '@nestjs/platform-express';
import { SendMessageInChat } from './dto/linkedin-send-message.dto';
import { Response } from 'express';
import { SequenceActivityQueryDto, SequencePartialDto } from './dto/sequence.dto';
import { LinkedInReactIconEnum } from './dto/linkedin-react.dto';
import { CreateSequenceTaskDto, GetDataSequenceTaskDto, GetSequenceTasksDto, UpdateSequenceTaskDto } from './dto/sequece-tasks.dto';

const linkedinImagesFilter = (
  req: Request,
  file: Express.Multer.File,
  callback: (error: Error, acceptFile: boolean) => void
) => {
  if (!file.mimetype.match(/^image\/(jpg|jpeg|png|gif)$/i) && !file.mimetype.match(/^application\/pdf$/i)) {
    callback(
      new HttpException(
        'Upload not allowed. Upload only files of type: image/jpg, image/jpeg, image/png, image/gif, application/pdf',
        HttpStatus.BAD_REQUEST
      ),
      false
    );
  } else {
    callback(null, true);
  }
};


@ApiTags('Emails')
@Controller('emails')
@SkipThrottle()
@ApiBadRequestResponse({
  description: ResponseMessage.Common.BAD_REQUEST,
  type: BaseErrorResponseDto,
})
@ApiNotFoundResponse({
  description: ResponseMessage.Common.NOT_FOUND,
  type: BaseErrorResponseDto,
})
@ApiUnauthorizedResponse({
  description: ResponseMessage.Common.UNAUTHORIZED,
  type: BaseErrorResponseDto,
})

export class MailController {
  constructor(private readonly mailService: MailService, private readonly activityLogService: ActivityLogService) {}

  // @Post('send-invitation-email')
  // sendInvitationEmail(@Body() sendByEmailsDto: SendByEmailsDto) {
  //     const { emails } = sendByEmailsDto
  //     return this.mailService.sendInvitationEmailByEmails(emails);
  // }
  @Get('test')
  async test(
    @Query('seqId') seqId: string,
    @Query('execute') execute: string,
    @Query('runAt') runAt: string,
  ) {
    return this.mailService.test(seqId, execute, runAt);
  }

  @Patch('sequences/:id/update-partial-sequence')
  async updateSequenceName(@Param('id') id: string, @Body() bodyDto: SequencePartialDto) {
    return this.mailService.updateSequenceName(id, bodyDto);
  }

  @Post('webhook/linkedin-incoming-message')
  async linkedInIncomingMessageWebhook(@Req() req) {
    return this.mailService.linkedInIncomingMessageWebhook(req.body)
  }

  @Post('total-linkedin-message')
  async getTotalLinkedInMessage(@Req() req) {
    const userId = req.viewAsUser.id;
    return this.mailService.getTotalLinkedInMessage(userId);
  }

  // TODO: Remove this after client migration is complete
  @Get('total-linkedin-message/view-as/:userId')
  async getTotalLinkedInMessageLegacy(@Param('userId') userId: string) {
    return this.mailService.getTotalLinkedInMessage(userId)
  }

  @Get('unsubscribe-email')
  async unsubscribeEmail(@Query('email') email: string, @Query('organizationId') organizationId: string, @Query("source") source?: string) {
    return this.mailService.unsubscribeEmail(email, organizationId, source);
  }

  @Get('timezones')
  async getListTimezones() {
    return this.mailService.getListTimezones();
  }

  @UseGuards(AuthenticationGuard)
  @Post('generate')
  async generateEmail(@Req() req, @Body() generateEmailDto: EmailGenerationDto) {
    const result = await this.mailService.generateEmail(<IJwtPayload>req.user, generateEmailDto);
    return result;
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('send')
  async sendEmail(@Req() req, @Body() sendEmailDto: SendEmailDto) {
    const result = await this.mailService.send(<IJwtPayload>req.user, sendEmailDto);
    return result;
  }


  @Post('sequence-config')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  async upsertSequenceEmail(@Req() req, @Body() upsertEmailDto: UpsertEmailDto|UpsertEmailChildDto) {
    const result = await this.mailService.upsertSequenceEmail(<IJwtPayload>req.viewAsUser, upsertEmailDto);
    return result;
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('sequence-config/view-as/:userId')
  async upsertSequenceEmailLegacy(@Param('userId') userId: string, @Body() upsertEmailDto: UpsertEmailDto|UpsertEmailChildDto) {
    const result = await this.mailService.upsertSequenceEmail({ id: userId }, upsertEmailDto);
    return result;
  }

  @UseGuards(AuthenticationGuard)
  @Post('free-sequence-config')
  async upsertFreeSequenceEmail(@Req() req, @Body() upsertEmailDto: UpsertEmailDto) {
    const result = await this.mailService.upsertFreeSequenceEmail(<IJwtPayload>req.user, upsertEmailDto);
    return result;
  }

  @Post('sequence-config/draft')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  async saveDraftSequence(@Req() req, @Body() upsertEmailDto: UpsertEmailDraftDto) {
    const userId = req.viewAsUser.id;
    const result = await this.mailService.upsertSequenceEmail({ id: userId }, upsertEmailDto, { isDraft: true });
    return result;
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('sequence-config/view-as/:userId/draft')
  async saveDraftSequenceLegacy(@Param('userId') userId: string, @Body() upsertEmailDto: UpsertEmailDraftDto) {
    const result = await this.mailService.upsertSequenceEmail({ id: userId }, upsertEmailDto, { isDraft: true });
    return result;
  }

  @Get('/:jobId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  async getMailsByJob(@Req() req, @Param('jobId') jobId: string, @Query() query: JobTypeDto) {
    const userId = req.viewAsUser.id;
    const { type } = query;
    return this.mailService.getMailConfig(jobId, userId, type);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/:jobId/view-as/:userId')
  async getMailsByJobLegacy(@Param('jobId') jobId: string, @Param('userId') userId: string, @Query() query: JobTypeDto) {
    const { type } = query;
    return this.mailService.getMailConfig(jobId, userId, type);
  }

  @Get()
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  async getAllMailConfig(@Req() req, @Query() query: GetAllQuery) {
    const userId = req.viewAsUser.id;
    return this.mailService.getAllMailConfig(userId, query);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/view-as/:userId')
  async getAllMailConfigLegacy(@Param('userId') userId: string, @Query() query: GetAllQuery) {
    return this.mailService.getAllMailConfig(userId, query);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/sequences/:sequenceId')
  async getDetailEmailConfigFromSequence(@Param('sequenceId') sequenceId: string) {
    return this.mailService.getDetailEmailConfigFromSequence(sequenceId);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/sequences/:sequenceId/report')
  async getDataReportSequence(@Param('sequenceId') sequenceId: string) {
    return this.mailService.getDataReportSequence(sequenceId);
  }

  @UseGuards(AuthenticationGuard)
  @Get('/seq-info/:id')
  async getSequence(@Param('id') id: string) {
    return this.mailService.getSequence(id);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Delete('/sequences/:sequenceId')
  async deleteEmailConfigFromSequence(@Param('sequenceId') sequenceId: string) {
    return this.mailService.deleteEmailConfigFromSequence(sequenceId);
  }

  @UseGuards(AuthenticationGuard)
  @Post('/sequences/delete-many')
  async deleteManySequences(@Body() upsertEmailDto: DeleteSequencesDto) {
    return this.mailService.deleteManySequences(upsertEmailDto.sequenceIds);
  }

  @UseGuards(AuthenticationGuard)
  @Patch('/sequences/update-mark-complete')
  async updateMarkCompleted(@Body() upsertEmailDto: DeleteSequencesDto) {
    return this.mailService.updateMarkSequence(upsertEmailDto.sequenceIds);
  }

  @UseGuards(AuthenticationGuard)
  @Patch('/bulk/pause-sequences')
  async updateMarkStop(@Body() upsertEmailDto: BulkStopSequenceDto) {
    return this.mailService.bulkStopSequence(upsertEmailDto.sequenceIds);
  }


  @UseGuards(AuthenticationGuard)
  @Put('/sequences/:sequenceId')
  async updateDetailEmailConfigFromSequence(
    @Param('sequenceId') sequenceId: string,
    @Body() updateMailDto: UpdateSequenceDto
  ) {
    return this.mailService.updateDetailEmailConfigFromSequence(sequenceId, updateMailDto);
  }

  @UseGuards(AuthenticationGuard)
  @Put('/add-contact')
  async addContact(@Body() addContactDto: AddContactDto) {
    return this.mailService.addContact(addContactDto);
  }

  @UseGuards(AuthenticationGuard)
  @Get('subject/:id')
  async generateSubject(@Param('id') jobBoardId: string) {
    return this.mailService.generateSubject(jobBoardId);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('content')
  async generateContent(@Req() req, @Body() generateEmailDto: EmailGenerationFromUnfinished) {
    const result = await this.mailService.generateContent(<IJwtPayload>req.user, generateEmailDto);
    return result;
  }

  @UseGuards(AuthenticationGuard)
  @Post('free-subject')
  async generateFreeSubject(@Body() generateEmailDto: FreeEmailGenerationDTO) {
    return this.mailService.generateFreeSubject(generateEmailDto);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('free-content')
  async generateFreeContent(@Req() req, @Body() generateEmailDto: FreeEmailGenerationDTO) {
    return this.mailService.generateFreeContent(<IJwtPayload>req.user, generateEmailDto);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('gen-email/by-child-content')
  async generateContentByChildContent(@Req() req, @Body() generateEmailDto: EmailGenerationFromChild) {
    const result = await this.mailService.generateContentByChildContent(<IJwtPayload>req.user, generateEmailDto);
    return result;
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('gen-email/by-child-subject')
  async generateContentByChildSubject(@Req() req, @Body() generateEmailDto: EmailGenerationFromChild) {
    const result = await this.mailService.generateContentByChildSubject(<IJwtPayload>req.user, generateEmailDto);
    return result;
  }

  @UseInterceptors(MulterMiddleware)
  @Post('incoming-mails')
  async checkIncomingEmail(@Req() req) {
    return this.mailService.checkIncomingEmail(req);
  }

  // @Get('incoming-mails-nylas')
  // async nylasValidation(@Req() req) {
  //   return req.query.challenge;
  // }

  // @Post('incoming-mails-nylas')
  // @HttpCode(HttpStatus.OK)
  // async nylasCheckIncomingMail(@Req() req) {
  //   return this.mailService.checkNylasNotification(req);
  // }

  @Get('oauth/exchange')
  async saveGrantId(@Query() query) {
    const { code, state } = query;
    return this.mailService.saveGrantId(code, state);
  }

  @UseGuards(AuthenticationGuard)
  @Get('auth')
  async authEmail(@Req() req, @Res() res) {
    const { id } = req.user;
    const { id: viewAsUserId } = req.query;
    const url = await this.mailService.authUrl(viewAsUserId || id);

    return res.redirect(url);
  }

  @Get('oauth/exchange-mailbox')
  async saveGrantMailBoxId(@Query() query) {
    const { code, state } = query;
    const isMailbox = true;
    return this.mailService.saveGrantId(code, state, isMailbox);
  }


  @Get('auth-mailbox')
  @UseGuards(AuthenticationGuard)
  async authMailboxEmail(@Req() req, @Res() res) {
    const userId = req.viewAsUser.id;
    const isMailbox = true;
    const url = await this.mailService.authUrl(userId, isMailbox);

    return res.redirect(url);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('auth-mailbox/user/:userId')
  async authMailboxEmailLegacy(@Req() req, @Res() res, @Param('userId') userId: string) {
    const isMailbox = true;
    const url = await this.mailService.authUrl(userId, isMailbox);

    return res.redirect(url);
  }

  @Get('auth-via-email')
  async authViaEmail(@Query('id') id, @Res() res) {
    const url = await this.mailService.authUrl(id);

    return res.redirect(url);
  }

  @Get('grant/send-grant-link')
  @UseGuards(AuthenticationGuard)
  async sendGrantLink(@Req() req) {
    const userId = req.viewAsUser.id;
    return this.mailService.sendGrantLink(userId);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('grant/send-grant-link/view-as/:userId')
  async sendGrantLinkLegacy(@Param('userId') userId: string) {
    return this.mailService.sendGrantLink(userId);
  }

  @Post('email-event')
  async checkEmailEvent(@Req() req) {
    return this.mailService.checkEmailEvent(req.body);
  }

  @UseGuards(AuthenticationGuard)
  @Get('email-activity-log/:sequenceId')
  async getEmailActivityLog(@Param('sequenceId') sequenceId: string, @Query() queryParams: SequenceActivityQueryDto) {
    return this.mailService.getEmailActivityLog(sequenceId, queryParams);
  }

  @Get('mailbox')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.MAILBOX].Read)
  async getMailBox(@Req() req, @Query() query, @Param('userId') userId: string) {
    const pageToken: string = query.pageToken;
    const limit: string = query.limit;
    return this.mailService.getMailBox(userId, pageToken, limit);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.MAILBOX].Read)
  @Get('mailbox/get-view-as/:userId')
  async getMailBoxLegacy(@Req() req, @Query() query, @Param('userId') userId: string) {
    const pageToken: string = query.pageToken;
    const limit: string = query.limit;
    return this.mailService.getMailBox(userId, pageToken, limit);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.MAILBOX].Read)
  @Post('mailbox-thread')
  async getMailByThread(@Req() req, @Body() body: ThreadMailDto) {
    const { messageIds } = body;
    return this.mailService.getMailByThread(<IJwtPayload>req.user, messageIds);
  }

  @Get('linkedin-message')
  @UseGuards(AuthenticationGuard)
  async getLinkedinMessage(@Req() req, @Query("cursor") cursor) {
    const userId = req.viewAsUser.id;
    return this.mailService.getLinkedinMessage(userId, cursor);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('linkedin-message/get-view-as/:userId')
  async getLinkedinMessageLegacy(@Req() req, @Param('userId') userId: string, @Query("cursor") cursor) {
    return this.mailService.getLinkedinMessage(userId, cursor);
  }

  @Get('linkedin-message/chatId/:chatId')
  @UseGuards(AuthenticationGuard)
  async getLinkedinMessageInChat(@Req() req, @Query("cursor") cursor, @Param('chatId') chatId: string) {
    const userId = req.viewAsUser.id;
    return this.mailService.getLinkedinMessageInChat(userId,chatId, cursor);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('linkedin-message/chatId/:chatId')
  async getLinkedinMessageInChatLegacy(@Req() req, @Param('userId') userId: string, @Query("cursor") cursor, @Param('chatId') chatId: string) {
    return this.mailService.getLinkedinMessageInChat(userId,chatId, cursor);
  }

  @Post('create-message-in-chat')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        content: {
          type: 'string',
          description: 'The content associated with the files uploads.',
        },
        chatId: {
          type: 'string',
          description: 'ChatId',
        },
      },
      required: ['content', 'chatId'],
    },
  })
  @UseInterceptors(
    FilesInterceptor('files', 100, {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: linkedinImagesFilter,
    })
  )
  async createFiles(
    @Param('userId') userId: string,
    @Body() sendMessageInChat: SendMessageInChat,
    @UploadedFiles() files: Array<Express.Multer.File>
  ) {
    return this.mailService.sendMessagesToChat(userId, sendMessageInChat.chatId, sendMessageInChat.content, files);
  }

  // TODO: Remove this after client migration is complete
  @Post('view-as/:userId/create-message-in-chat')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        content: {
          type: 'string',
          description: 'The content associated with the files uploads.',
        },
        chatId: {
          type: 'string',
          description: 'ChatId',
        },
      },
      required: ['content', 'chatId'],
    },
  })
  @UseInterceptors(
    FilesInterceptor('files', 100, {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: linkedinImagesFilter,
    })
  )
  async createFilesLegacy(
    @Param('userId') userId: string,
    @Body() sendMessageInChat: SendMessageInChat,
    @UploadedFiles() files: Array<Express.Multer.File>
  ) {
    return this.mailService.sendMessagesToChat(userId, sendMessageInChat.chatId, sendMessageInChat.content, files);
  }

  @UseGuards(AuthenticationGuard)
  @Get('linkedin-message/sync-messages-data')
  async syncAccountDataMessaging(@Req() req) {
    const userId = req.viewAsUser.id;
    return this.mailService.syncAccountDataMessaging(userId);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('linkedin-message/get-view-as/:userId/sync-messages-data')
  async syncAccountDataMessagingLegacy(@Req() req, @Param('userId') userId: string) {
    return this.mailService.syncAccountDataMessaging(userId);
  }

  @Get('linkedin-message/retrieve-own-profile')
  @UseGuards(AuthenticationGuard)
  async retrieveOwnProfile(@Req() req) {
    const userId = req.viewAsUser.id;
    return this.mailService.retrieveOwnProfile(userId);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('linkedin-message/get-view-as/:userId/retrieve-own-profile')
  async retrieveOwnProfileLegacy(@Req() req, @Param('userId') userId: string) {
    return this.mailService.retrieveOwnProfile(userId);
  }

  @UseGuards(AuthenticationGuard)
  @Get('linkedin-message-file/message/:messageId/attachmentId/:attachmentId')
  async getFileFromMessage(@Req() req, @Res() res: Response, @Param('messageId') messageId: string, @Param('attachmentId') attachmentId: string, @Query("type") type: string) {
    try {
      const userId = req.viewAsUser.id;
      return this.mailService.getFileFromMessage(userId, messageId, attachmentId, res, type);
    } catch (error) {
      res.status(500).send('Error downloading file');
    }
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('linkedin-message-file/get-view-as/:userId/message/:messageId/attachmentId/:attachmentId')
  async getFileFromMessageLegacy(@Res() res: Response, @Param('userId') userId: string, @Param('messageId') messageId: string, @Param('attachmentId') attachmentId: string, @Query("type") type: string) {
    try {
      return this.mailService.getFileFromMessage(userId, messageId, attachmentId, res, type);
    } catch (error) {
      res.status(500).send('Error downloading file');
    }
  }

  @Get('linkedin-message/download-attendee/:attendeeId')
  @UseGuards(AuthenticationGuard)
  async getAttendeeProfileImage(@Res() res: Response, @Param('userId') userId: string, @Param('attendeeId') attendeeId: string) {
    const data = await this.mailService.getAttendeeProfileImage(userId, attendeeId);
    res.set('Content-Type', 'image/jpeg');
    res.send(data);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('linkedin-message/get-view-as/:userId/download-attendee/:attendeeId')
  async getAttendeeProfileImageLegacy(@Res() res: Response, @Param('userId') userId: string, @Param('attendeeId') attendeeId: string) {
    const data = await this.mailService.getAttendeeProfileImage(userId, attendeeId);
    res.set('Content-Type', 'image/jpeg');
    res.send(data);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.MAILBOX].Write)
  @Post('send-personal-mail')
  async sendPersonalMail(@Req() req, @Body() body: SendPersonalMailDto) {
    return this.mailService.sendPersonalMail(<IJwtPayload>req.user, body);
  }

  @UseGuards(AuthenticationGuard)
  @Delete('unlink')
  async destroyGrantId(@Req() req, @Query('type') grantType: string) {
    const userId = req.viewAsUser.id;
    return this.mailService.destroyGrantId(userId, grantType);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Delete('unlink/get-view-as/:userId')
  async destroyGrantIdLegacy(@Param('userId') userId: string, @Query('type') grantType: string) {
    return this.mailService.destroyGrantId(userId, grantType);
  }

  @UseGuards(AuthenticationGuard)
  @Put('update-thread/:threadId')
  async updateThread(@Req() req, @Param('threadId') threadId: string, @Body() updatedData: UpdateThreadMailDto) {
    return this.mailService.updateThread(<IJwtPayload>req.user, threadId, updatedData);
  }

  @UseGuards(AuthenticationGuard)
  @Delete('delete-thread')
  async deleteThreads(@Req() req, @Body() body: DeleteThreadsDto) {
    return this.mailService.deleteThreads(<IJwtPayload>req.user, body.threadIds);
  }

  @UseGuards(AuthenticationGuard)
  @Post('valid-email')
  async validEmail(@Req() req, @Body() body: ValidEmailDto) {
    return this.mailService.validEmail(body.email);
  }

  // @UseGuards(AuthenticationGuard)
  @Post('valid-list-email')
  async validListEmail(@Req() req, @Body() body: ValidListEmailDto) {
    return await this.mailService.validListEmail(body);
  }

  @UseGuards(AuthenticationGuard)
  @Get('calendar/events')
  async getCalendarEvents(@Req() req, @Query() query: CalendarEventsQueryDto) {
    return await this.mailService.getCalendarEvents(req, query);
  }

  @UseGuards(AuthenticationGuard)
  @Post('calendar/event')
  async createCalendarEvent(@Req() req, @Body() event: CalendarEventDto) {
    return await this.mailService.createCalendarEvent(req, event);
  }

  @UseGuards(AuthenticationGuard)
  @Get('calendar/event/:eventId')
  async getCalendarEvent(@Req() req, @Param('eventId') eventId: string) {
    return await this.mailService.getCalendarEvent(req, eventId);
  }

  @Get('schedule/get-my-events')
  getMyEvents(@Req() req) {
    const userId = req.viewAsUser.id;
    return this.mailService.getMyEvents(userId);
  }
  //  Configurations
  //  Get all configurations
  // TODO: Remove this after client migration is complete
  @Get('schedule/get-my-events/view-as/:userId')
  getMyEventsLegacy(@Param('userId') userId: string) {
    return this.mailService.getMyEvents(userId);
  }

  @Post('schedule/my-events')
  @UseGuards(AuthenticationGuard)
  async createConfiguration(@Req() req, @Body() configuration: any) {
    const userId = req.viewAsUser.id;
    return await this.mailService.createConfiguration(userId, configuration);
  }
  //  create configuration
  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Post('schedule/my-events/view-as/:userId')
  async createConfigurationLegacy(@Param('userId') userId: string, @Body() configuration: any) {
    return await this.mailService.createConfiguration(userId, configuration);
  }

  @Get('schedule/calendars')
  @UseGuards(AuthenticationGuard)
  async getCalendars(@Req() req) {
    const userId = req.viewAsUser.id;
    return await this.mailService.getCalendars(userId);
  }
  //  get calendars
  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('schedule/calendars/view-as/:userId')
  async getCalendarsLegacy(@Param('userId') userId: string) {
    return await this.mailService.getCalendars(userId);
  }

  @Get('schedule/my-events/configuration/:configurationId')
  @UseGuards(AuthenticationGuard)
  async getConfigurationDetail(@Req() req, @Param('configurationId') configurationId: string) {
    const userId = req.viewAsUser.id;
    return await this.mailService.getConfigurationDetail(userId, configurationId);
  }

  // get detail configuration
  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Get('schedule/my-events/view-as/:userId/configuration/:configurationId')
  async getConfigurationDetailLegacy(@Param('userId') userId: string, @Param('configurationId') configurationId: string) {
    return await this.mailService.getConfigurationDetail(userId, configurationId);
  }

  @UseGuards(AuthenticationGuard)
  @Delete('schedule/my-events/configuration/:configurationId')
  async deleteConfiguration(@Req() req, @Param('configurationId') configurationId: string) {
    const userId = req.viewAsUser.id;
    return await this.mailService.deleteConfiguration(userId, configurationId);
  }

  // get delete configuration
  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Delete('schedule/my-events/view-as/:userId/configuration/:configurationId')
  async deleteConfigurationLegacy(@Param('userId') userId: string, @Param('configurationId') configurationId: string) {
    return await this.mailService.deleteConfiguration(userId, configurationId);
  }

  @UseGuards(AuthenticationGuard)
  @Put('schedule/my-events/configuration/:configurationId')
  async updateConfiguration(
    @Req() req,
    @Param('configurationId') configurationId: string,
    @Body() configuration: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.mailService.updateConfiguration(userId, configurationId, configuration);
  }

  // update configuration
  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Put('schedule/my-events/view-as/:userId/configuration/:configurationId')
  async updateConfigurationLegacy(
    @Param('userId') userId: string,
    @Param('configurationId') configurationId: string,
    @Body() configuration: any
  ) {
    return await this.mailService.updateConfiguration(userId, configurationId, configuration);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('trigger/sequence/:sequenceId')
  async triggerSequence(
    @Req() req,
    @Param('sequenceId') sequenceId: string,
  ) {
    const userId = req.viewAsUser.id;
    return this.mailService.triggerSequence(userId, sequenceId);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('trigger/view-as/:userId/sequence/:sequenceId')
  async triggerSequenceLegacy(
    @Param('userId') userId: string,
    @Param('sequenceId') sequenceId: string,
  ) {
    return this.mailService.triggerSequence(userId, sequenceId);
  }


  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/live-feed')
  async getLiveFeed(@Req() req, @Query() query: LiveFeedDto) {
    const userId = req.viewAsUser.id;
    return this.mailService.getLiveFeed(userId, query);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/view-as/:userId/live-feed')
  async getLiveFeedLegacy(@Param('userId') userId: string, @Query() query: LiveFeedDto) {
    return this.mailService.getLiveFeed(userId, query);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('stats/email-sent')
  async countEmailSent(@Req() req, @Query() query: LiveFeedDto) {
    const userId = req.viewAsUser.id;
    return this.mailService.countEmailSent(query?.consultant || userId, query);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('stats/view-as/:userId/email-sent')
  async countEmailSentLegacy(@Param('userId') userId: string, @Query() query: LiveFeedDto) {
    return this.mailService.countEmailSent(query?.consultant || userId, query);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('stats/view-as/:userId/active-sequence')
  async countActiveSequence(@Param('userId') userId: string, @Query() query: LiveFeedDto) {
    return this.mailService.countActiveSequence(query?.consultant || userId, query);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('/send-test-email')
  async sendTestEmail(@Req() req, @Body() sendEmailDto: sendTestEmailDto) {
    const userId = req.viewAsUser.id;
    return this.mailService.sendTestEmail(userId, sendEmailDto);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('/view-as/:userId/send-test-email')
  async sendTestEmailLegacy(@Param('userId') userId: string, @Body() sendEmailDto: sendTestEmailDto) {
    return this.mailService.sendTestEmail(userId, sendEmailDto);
  }

  @UseGuards(AuthenticationGuard)
  @Post('/validate-contacts-in-active-sequences')
  async validateContactsInActiveSequences(@Req() req, @Body() bodyDto: ValidateContactsInActiveSequenceDto) {
    const userId = req.viewAsUser.id;
    return this.mailService.validateContactsInActiveSequences(userId, bodyDto);
  }

  // TODO: Remove this after client migration is complete
  @UseGuards(AuthenticationGuard)
  @Post('/view-as/:userId/validate-contacts-in-active-sequences')
  async validateContactsInActiveSequencesLegacy(@Param('userId') userId: string, @Body() bodyDto: ValidateContactsInActiveSequenceDto) {
    return this.mailService.validateContactsInActiveSequences(userId, bodyDto);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('/view-as/:userId/task')
  async createTestDataLegacy(@Param('userId') userId: string, @Body() data: CreateSequenceTaskDto) {
    // TODO: Remove this after client migration is complete
    return this.mailService.createSequenceTask(userId, data);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Post('/task')
  async createTestData(@Req() req: any, @Body() data: CreateSequenceTaskDto) {
    const userId = req.viewAsUser.id;
    return this.mailService.createSequenceTask(userId, data);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/view-as/:userId/get-list-sequence-tasks')
  async getTaskByUserIdLegacy(@Param('userId') userId: string,  @Query() query: GetDataSequenceTaskDto){
    // TODO: Remove this after client migration is complete
    return this.mailService.getListSequenceTasks(userId, query);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/get-list-sequence-tasks')
  async getTaskByUserId(@Req() req: any,  @Query() query: GetDataSequenceTaskDto){
    const userId = req.viewAsUser.id;
    return this.mailService.getListSequenceTasks(userId, query);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/view-as/:userId/get-list-tasks')
  async getTasksByUserIdLegacy(@Param('userId') userId: string,  @Query() query: GetSequenceTasksDto){
    // TODO: Remove this after client migration is complete
    return this.mailService.getSequenceTasks(userId, query);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/get-list-tasks')
  async getTasksByUserId(@Req() req: any,  @Query() query: GetSequenceTasksDto){
    const userId = req.viewAsUser.id;
    return this.mailService.getSequenceTasks(userId, query);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/view-as/:userId/get-task/:taskId')
  async getTaskDetailLegacy(@Param('userId') userId: string, @Param('taskId') taskId: string){
    // TODO: Remove this after client migration is complete
    return this.mailService.getTaskDetail(taskId);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/get-task/:taskId')
  async getTaskDetail(@Req() req: any, @Param('taskId') taskId: string){
    return this.mailService.getTaskDetail(taskId);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Patch('/view-as/:userId/task/:taskId')
  async updateSequenceTaskLegacy(@Param('userId') userId: string, @Param('taskId') taskId: string,  @Body() body: UpdateSequenceTaskDto){
    // TODO: Remove this after client migration is complete
    return this.mailService.updateSequenceTask(userId, taskId, body);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Patch('/task/:taskId')
  async updateSequenceTask(@Req() req: any, @Param('taskId') taskId: string,  @Body() body: UpdateSequenceTaskDto){
    const userId = req.viewAsUser.id;
    return this.mailService.updateSequenceTask(userId, taskId, body);
  }


  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Patch('/view-as/:userId/tasks/groups/:groupId')
  async updateTasksByGroupIdLegacy(@Param('userId') userId: string, @Param('groupId') groupId: string,  @Body() body: UpdateSequenceTaskDto){
    // TODO: Remove this after client migration is complete
    return this.mailService.updateTasksByGroupId(userId, groupId, body);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Patch('/tasks/groups/:groupId')
  async updateTasksByGroupId(@Req() req: any, @Param('groupId') groupId: string,  @Body() body: UpdateSequenceTaskDto){
    const userId = req.viewAsUser.id;
    return this.mailService.updateTasksByGroupId(userId, groupId, body);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Delete('/view-as/:userId/task/:taskId')
  async deleteSequenceTaskLegacy(@Param('userId') userId: string,  @Param('taskId') taskId: string){
    // TODO: Remove this after client migration is complete
    return this.mailService.deleteSequenceTask(userId, taskId);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Write)
  @Delete('/task/:taskId')
  async deleteSequenceTask(@Req() req: any,  @Param('taskId') taskId: string){
    const userId = req.viewAsUser.id;
    return this.mailService.deleteSequenceTask(userId, taskId);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/view-as/:userId/get-total-task-by-type')
  async getTasksTotalByTypeLegacy(@Param('userId') userId: string, @Query("status") status: string){
    // TODO: Remove this after client migration is complete
    return this.mailService.getTasksTotalByType(userId, status);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/get-total-task-by-type')
  async getTasksTotalByType(@Req() req: any, @Query("status") status: string){
    const userId = req.viewAsUser.id;
    return this.mailService.getTasksTotalByType(userId, status);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/view-as/:userId/get-linkedin-profile-data')
  async getLinkedinDataLegacy(@Param('userId') userId: string, @Query('linkedinUrl') linkedinUrl: string){
    // TODO: Remove this after client migration is complete
    return this.mailService.getLinkedinData(userId, linkedinUrl);
  }

  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  @Get('/get-linkedin-profile-data')
  async getLinkedinData(@Req() req: any, @Query('linkedinUrl') linkedinUrl: string){
    const userId = req.viewAsUser.id;
    return this.mailService.getLinkedinData(userId, linkedinUrl);
  }

  @UseGuards(AuthenticationGuard)
  @Get('activity-logs/:sequenceId/stats')
  async getActivityLogsStats(@Param('sequenceId') sequenceId: string) {
    return this.activityLogService.getActivityLogsStats(sequenceId);
  }
}

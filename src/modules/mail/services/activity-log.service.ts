import { DataSource } from 'typeorm';
import { SequenceActivityLogEntity, SequenceActivityType } from './../entities/sequence-activity-log.entity';
import { Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';

@Injectable()
export class ActivityLogService extends BaseAbstractService {
  constructor(
    readonly i18nService: I18nService,
    private readonly dataSource: DataSource,
  ) {
    super(i18nService);
  }

  async getActivityLogsStats(sequenceId: string) {
    const countLogTypes = await this.dataSource
      .createQueryBuilder(SequenceActivityLogEntity, 'sa')
      .select('sa.type', 'type')
      .where('sa.sequence_id = :sequenceId', { sequenceId })
      .addSelect('COUNT(1)', 'count')
      .groupBy('sa.type')
      .getRawMany();

    // Initialize all activity types with 0
    const data = Object.values(SequenceActivityType).reduce(
      (acc, type) => {
        acc[type] = 0;
        return acc;
      },
      {} as Record<SequenceActivityType, number>,
    );

    // Update with actual counts
    countLogTypes.forEach((row) => {
      data[row.type] = parseInt(row.count, 10);
    });

    return this.formatOutputData({ key: 'GET_ACTIVITY_LOGS_STATS' }, { data });
  }
}

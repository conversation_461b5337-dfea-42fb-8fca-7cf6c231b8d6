/* eslint-disable camelcase */
import { Promise as BBPromise } from 'bluebird';
import { ValidListEmailDto } from './dto/valid-email.dto';
import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  PreconditionFailedException,
} from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import * as sgMail from '@sendgrid/mail';
import { simpleParser } from 'mailparser';
import * as sgMailClient from '@sendgrid/client';
import * as sgHelpers from '@sendgrid/helpers';
import { lastValueFrom } from 'rxjs';
import { validate as uuidValidate, v4 as uuid } from 'uuid';
import * as os from 'node:os';

import { BaseAbstractService } from 'src/base/base.abstract.service';
import {
  SendGridConfig,
  NYLAS_CONFIG,
  APP_CONFIG,
  SesConfig,
  EMAIL_DOMAIN_WHITELIST,
  RECIPIENT_PER_TASK,
  BULL_JOB_NAMES,
  BULL_JOB_ATTEMPTS,
  UNIPILE_REQUEST_LIMIT_TIMEOUT,
  unipileConfig,
  JOBS_INDEX,
} from 'src/configs/configs.constants';
import {
  EmailGenerationDto,
  EmailGenerationFromChild,
  EmailGenerationFromUnfinished,
  FreeEmailGenerationDTO,
} from './dto/email-generation.dto';
import { OpenAIService } from '../openai/openai.service';
import { SequenceStepRepository } from './repositories/sequence-step.repostiory';
import { SequenceRepository } from './repositories/sequence.repostiory';
import {
  SequenceStepDelayUnit,
  SequenceStepEntity,
  SequenceStepStatus,
  SequenceStepType,
} from './entities/sequence-step.entity';
import { SequenceEntity, SequenceEvent, SequenceStatus } from './entities/sequence.entity';
import { JobBoardsRepository } from '../jobs/repository/job-boards.repository';
import { IJwtPayload, ISimpleUser } from '../auth/payloads/jwt-payload.payload';
import { UserRepository } from '../user/repositories/user.repository';
import {
  AddContactDto,
  GetAllQuery,
  JobType,
  LiveFeedDto,
  MailInfo,
  RecipientInfo,
  SendEmailDto,
  sendTestEmailDto,
  SequenceEmailCreatedFrom,
  UpdateSequenceDto,
  ValidateContactsInActiveSequenceDto,
} from './dto/send-email.dto';
import {
  Brackets,
  DataSource,
  FindManyOptions,
  FindOptionsWhere,
  ILike,
  In,
  IsNull,
  LessThan,
  Like,
  MoreThan,
  MoreThanOrEqual,
  Not,
  Raw,
} from 'typeorm';
import {
  ScheduleInformationDto,
  STOP_BY_TYPE,
  UpsertChildSequenceDto,
  UpsertEmailChildDto,
  UpsertEmailDraftDto,
  UpsertEmailDto,
} from './dto/upsert-email.dto';
import { OpensearchService } from '../opensearch/service/opensearch.service';
import { cloneDeep, get, set } from 'lodash';
import { JobLeadsRepository } from '../jobs/repository/job-leads.repository';
import Nylas, { ListEventQueryParams, MessageFields } from 'nylas';
import { SequenceActivityLogEntity, SequenceActivityType } from './entities/sequence-activity-log.entity';
import { SequenceActivityLogRepository } from './repositories/sequence-activity-log.repository';
import { SendPersonalMailDto } from './dto/send-personal-mail.dto';
import { UpdateThreadMailDto } from './dto/thread-mail.dto';
import {
  S3,
  chunkArray,
  convertCamelToNormalCase,
  convertTimezone,
  filterOutByKey,
  getMergeTagsContent,
  getSequenceTriggerAt,
  getValueByPath,
  processingContent,
  removeDuplicatesByKey,
  safeParseJSON,
  standardizeJobType,
  validateEmail,
} from '../../common/utils/helpers.util';
import { UserSignatureRepository } from '../user/repositories/user-signature.repository';
import {
  getJobVacancy,
  queryClientContacts,
  queryEntity,
  queryGetListShortList,
  sendNoteToBullhorn,
} from '../jobs/utils/bullhorn-service.util';
import { GetCorporateUserQueryDto } from '../jobs/dto/get-corporate.dto';
import { LicenseType as UserLicenseType, UserEntity } from '../user/entities/user.entity';
import { ISendMail, MERGE_TAGS_PLACE_HOLDERS, MergeTagMappingEnum } from './dto/send-email-process.dto';
import { JobLead } from '../jobs/entities/job-leads.entity';
import { UserSignature } from '../user/entities/user-signature.entity';
import { CoreJobLead } from '../jobs/dto/job-lead/job-lead.dto';
import { CalendarEventDto, CalendarEventsQueryDto } from './dto/calendar-event.dto';
import { HttpService } from '@nestjs/axios';
import { ContactRepository } from '../user/repositories/contact.repository';
import { SequenceInstanceRepository } from './repositories/sequence-instance.repository';
import * as unipileClient from '../jobs/utils/unipile-service.utils';
import { SequenceStepTaskRepository } from './repositories/sequence-step-task.repository';
import { InjectSequenceStepTaskQueue, InjectWebhookSendgridEventQueue } from 'src/modules/queue/queue.decorator';
import { Queue } from 'bullmq';
import { BullhornIntegrationService } from '../jobs/service/bullhorn-integration.service';
import { OrganizationEntity } from '../user/entities/organization.entity';
import { BullHornService } from 'src/middlewares/bullhorn/bullhorn.service';
import { BullHornConfig } from 'src/configs/configs.constants';
import { UserSignatureServices } from '../user/user-signature.service';
import { JOB_BOARDS } from '../jobs/constants/job.const';
import { ParticipantsDto, SequencePartialDto, SequenceActivityQueryDto } from './dto/sequence.dto';
import { FileUploadService } from '../files-upload/file-upload.service';
import * as nylasClient from '../jobs/utils/nylas.util';
import axios from 'axios';
import { SequenceStatus as SequenceStatusEnum } from './enum/sequence.enum';
import { getListTimezones, MAPPING_TIMEZONE_NAMES } from '../jobs/utils/savvycal.util';
import { LinkedInReactIconEnum } from './dto/linkedin-react.dto';
import { EmailValidationResultRepository } from '../email-finder/repositories/email-validation-result.repository';
import { SequenceTaskRepository } from './repositories/sequence-tasks.repository';
import {
  CreateSequenceTaskDto,
  GetDataSequenceTaskDto,
  GetSequenceTasksDto,
  UpdateSequenceTaskDto,
} from './dto/sequece-tasks.dto';
import { SequenceTaskCreatingType, SequenceTaskEntity, SequenceTaskStatusEnum } from './entities/sequence-tasks.entity';
import { UnipileRecordRepository } from './repositories/unipile-record.repostiory';
import { RecordTypeEnum } from './entities/unipile-record.entity';
import { SequenceInstanceEntity } from './entities/sequence-instance.entity';
import { LinkedInFinderService } from '../linkedin-finder/linkedin-finder.service';
import { SequenceStepTaskEntity } from './entities/sequence-step-task.entity';
import { CrmNoteEntity, CrmNoteResourceName } from '../crm/entities/crm-note.entity';
import { CrmBullhornService } from '../crm/services/crm-bullhorn.service';
import { CrmContactSequenceStepRepository } from '../crm/repositories/crm-contact-sequence-step.repository';
import { CrmContactSequenceRepository } from '../crm/repositories/crm-contact-sequence.repository';
import { CrmContactRepository } from '../crm/repositories/crm-contact.repository';


@Injectable()
export class MailService extends BaseAbstractService {
  private appEnv: string;
  private readonly logger = new Logger(MailService.name);
  private readonly nylas = new Nylas({
    apiKey: NYLAS_CONFIG.apiKey,
    apiUri: NYLAS_CONFIG.apiUri, // "https://api.us.nylas.com" or "https://api.eu.nylas.com"
  });
  readonly DELAY_UNIT_MULTIPLE = {
    DAY: 24 * 60 * 60 * 1000,
    HOUR: 60 * 60 * 1000,
  };

  constructor(
    @InjectSequenceStepTaskQueue() private sequenceStepTaskQueue: Queue,
    @InjectWebhookSendgridEventQueue() private webhookSendgridEventQueue: Queue,
    readonly i18nService: I18nService,
    private readonly openAIService: OpenAIService,
    // private readonly userSignatureServices: UserSignatureServices,
    private readonly sequenceRepository: SequenceRepository,
    private readonly sequenceStepRepository: SequenceStepRepository,
    private readonly sequenceInstanceRepository: SequenceInstanceRepository,
    private readonly sequenceStepTaskRepository: SequenceStepTaskRepository,
    private readonly sequenceActivityLogRepository: SequenceActivityLogRepository,
    private readonly jobBoardRepository: JobBoardsRepository,
    private readonly jobLeadRepository: JobLeadsRepository,
    private readonly userRepository: UserRepository,
    private readonly userSignatureRepository: UserSignatureRepository,
    private readonly userSignatureServices: UserSignatureServices,
    private readonly elasticsearchService: OpensearchService,
    private readonly contactRepository: ContactRepository,
    private readonly httpService: HttpService,
    private readonly dataSource: DataSource,
    private readonly bullhornService: BullHornService,
    private readonly fileUploadService: FileUploadService,
    private readonly emailValidationResultRepository: EmailValidationResultRepository,
    private readonly sequenceTaskRepository: SequenceTaskRepository,
    private readonly unipileRecordRepository: UnipileRecordRepository,
    private readonly linkedinFinderService: LinkedInFinderService,
    private readonly crmBullhornService: CrmBullhornService,
    private readonly crmContactSequenceStepRepository: CrmContactSequenceStepRepository,
    private readonly crmContactSequenceRepository: CrmContactSequenceRepository,
    private readonly crmContactRepository: CrmContactRepository
  ) {
    super(i18nService);
    sgMail.setApiKey(SendGridConfig.apiKey);
    sgMailClient.setApiKey(SendGridConfig.apiKeyEmailValid);
    this.appEnv = process.env.APP_ENV;
  }

  public async getBhToken(organizationId) {
    // get access token which use to call to bullhorn
    const org = await this.dataSource
      .createQueryBuilder(OrganizationEntity, 'o')
      .where({ id: organizationId })
      .getOne();

    let { access_token, expires_at, refresh_token, bhRestToken, corporateRestUrl } = org?.bhToken ?? {};

    if (!expires_at || expires_at < Date.now() - 60 * 1000) {
      const { bhClientId, bhUsername, bhPassword, bhClientSecret } = org ?? {
        bhClientId: BullHornConfig.clientId,
        bhUsername: BullHornConfig.username,
        bhPassword: BullHornConfig.password,
        bhClientSecret: BullHornConfig.clientSecret,
      };

      if (!bhClientId || !bhUsername || !bhPassword || !bhClientSecret) {
        return null;
      }

      const { oauthUrl, restUrl } = await this.bullhornService.getDataCenter(bhUsername);
      const {
        access_token: accessToken,
        expires_in,
        refresh_token: refreshToken,
      } = await this.bullhornService.getAccessTokenFromScratch(refresh_token, {
        bhClientId,
        bhUsername,
        bhPassword,
        bhClientSecret,
        rootOauthUrl: oauthUrl,
      });

      access_token = accessToken;
      expires_at = Date.now() + expires_in * 1000 - 60 * 1000;
      refresh_token = refreshToken;

      const { BhRestToken: newBhRestToken, restUrl: newCorporateRestUrl } =
        await this.bullhornService.getBhRestTokenAndCorporateRestEndpoint(accessToken, restUrl);

      bhRestToken = newBhRestToken;
      corporateRestUrl = newCorporateRestUrl;

      const bhToken = {
        access_token,
        expires_at,
        refresh_token,
        bhRestToken,
        corporateRestUrl,
      };

      if (org) {
        await this.dataSource.getRepository(OrganizationEntity).update(org.id, {
          bhToken,
        });
      }

      return { ...bhToken, organizationId: organizationId ?? 0 };
    }

    return { ...org.bhToken, organizationId: organizationId ?? 0 };
  }

  async addBHNoteForEmailSent(recipient: any, user: any) {
    try {
      if (!recipient.id || uuidValidate(recipient.id)) {
        return null;
      }

      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(user?.organizationId)) || {};
      const payload = {
        comments: 'Zileo email sent',
        personReference: {
          id: recipient.id,
          searchEntity: 'ClientContact',
        },
        action: 'Email',
        clientContacts: [
          {
            id: recipient.id,
          },
        ],
        commentingPerson: { id: user?.consultantId || 1 },
      };

      const result = await sendNoteToBullhorn(bhRestToken, corporateRestUrl, payload);

      return result;
    } catch (error) {
      this.logger.error(`Error addBHNoteForEmailSent: ${error.message}`);

      return;
    }
  }

  // email-related function
  async sendInvitationEmailByEmails(emails: string[]) {
    const { fromEmail, invitationSubject } = SendGridConfig;
    try {
      for (let email of emails) {
        const msg = {
          to: email,
          from: {
            name: 'Zileo',
            email: fromEmail,
          }, // Set your verified sender email
          subject: invitationSubject,
          html: `Please go to zileo to complete your registration: <a clicktracking='off' href='${APP_CONFIG.CLIENT_URL}/register?invitedEmail=${email}'>Registration</a>`,
        };
        await sgMail.send(msg);
      }
      return this.formatOutputData({ key: 'SEND_INVITATION_EMAIL_BY_EMAILS' }, { data: {} });
    } catch (error) {
      this.logger.error(error.message);
      console.error(error.response.body);
      if (error.code === HttpStatus.BAD_REQUEST) {
        throw new BadRequestException('Invalid email address');
      }
      // return await this.throwCommonMessage('SEND_INVITATION_EMAIL_BY_EMAILS', error)
      throw error;
    }
  }

  async sendEmailSequenceCreated(user: any, sequence: any) {
    const name = user.fullName || user.username;
    const content = 'Your sequence is now live. To view your live sequence, please click the link.';

    const msg = {
      to: user.email,
      from: {
        name: 'Zileo',
        email: SendGridConfig.fromEmail,
      },
      subject: `Your new sequence ${sequence.name} has been successfully created!`,
      html: `<!doctypehtml><meta charset=utf-8><meta content="ie=edge"http-equiv=x-ua-compatible><title>Your sequence has been created</title><meta content="width=device-width,initial-scale=1"name=viewport><style>@media screen{@font-face{font-family:'Source Sans Pro';font-style:normal;font-weight:400;src:local('Source Sans Pro Regular'),local('SourceSansPro-Regular'),url(https://fonts.gstatic.com/s/sourcesanspro/v10/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff')}@font-face{font-family:'Source Sans Pro';font-style:normal;font-weight:700;src:local('Source Sans Pro Bold'),local('SourceSansPro-Bold'),url(https://fonts.gstatic.com/s/sourcesanspro/v10/toadOcfmlt9b38dHJxOBGFkQc6VGVFSmCnC_l7QZG60.woff) format('woff')}}a,body,table,td{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}table,td{mso-table-rspace:0;mso-table-lspace:0}img{-ms-interpolation-mode:bicubic}a[x-apple-data-detectors]{font-family:inherit!important;font-size:inherit!important;font-weight:inherit!important;line-height:inherit!important;color:inherit!important;text-decoration:none!important}div[style*="margin: 16px 0;"]{margin:0!important}body{width:100%!important;height:100%!important;padding:0!important;margin:0!important}table{border-collapse:collapse!important}a{color:#1a82e2}img{height:auto;line-height:100%;text-decoration:none;border:0;outline:0}</style><body style=background-color:#e9ecef><div class=preheader style=display:none;max-width:0;max-height:0;overflow:hidden;font-size:1px;line-height:1px;color:#fff;opacity:0>Your sequence ${sequence.name} has been created.</div><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td align=center bgcolor=#e9ecef><!--[if (gte mso 9)|(IE)]><table border=0 cellpadding=0 cellspacing=0 width=600 align=center><tr><td align=center valign=top width=600><![endif]--><table border=0 cellpadding=0 cellspacing=0 width=100% style=max-width:600px><tr><td align=left bgcolor=#ffffff style="padding:36px 24px 0;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;border-top:3px solid #d4dadf"><h1 style=margin:0;font-size:32px;font-weight:700;letter-spacing:-1px;line-height:48px>Your new sequence ${sequence.name} has been created</h1></table><!--[if (gte mso 9)|(IE)]><![endif]--><tr><td align=center bgcolor=#e9ecef><!--[if (gte mso 9)|(IE)]><table border=0 cellpadding=0 cellspacing=0 width=600 align=center><tr><td align=center valign=top width=600><![endif]--><table border=0 cellpadding=0 cellspacing=0 width=100% style=max-width:600px><tr><td align=left bgcolor=#ffffff style="padding:24px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;line-height:24px"><p style=margin:0>Hi ${name},</p><br><p style=margin:0>${content}</p><tr><td align=left bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td align=center bgcolor=#ffffff style=padding:12px><table border=0 cellpadding=0 cellspacing=0><tr><td align=center bgcolor=#1a82e2 style=border-radius:6px><a clicktracking="off" href="${APP_CONFIG.CLIENT_URL}/sequence?seqId=${sequence.id}" target=_blank style="display:inline-block;padding:16px 36px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;color:#fff;text-decoration:none;border-radius:6px">View Sequence</a></table></table><tr><td align=left bgcolor=#ffffff style="padding:24px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;line-height:24px;border-bottom:3px solid #d4dadf"><p style=margin:0>Best regards,</p><p style=margin:0>Zileo Team</p></table><!--[if (gte mso 9)|(IE)]><![endif]--></table>`,
    };

    await sgMail.send(msg).catch((warn) => {
      console.warn('Error in sending email', warn?.response?.body?.errors);
    });
  }

  async sendEmailSequenceStop(user: any, sequence: any, isCompleted: boolean = false) {
    const name = user.fullName || user.username;
    const status = isCompleted ? 'completed' : 'stopped';
    const content = isCompleted
      ? 'Your sequence has now completed. Please click the link to view the Completed Sequence.'
      : 'Your sequence has now stopped. Please click the link to view the Stopped Sequence.';

    const msg = {
      to: user.email,
      from: {
        name: 'Zileo',
        email: SendGridConfig.fromEmail,
      },
      subject: `Sequence Updated: Your sequence ${sequence.name} is ${status}`,
      html: `<!doctypehtml><meta charset=utf-8><meta content="ie=edge"http-equiv=x-ua-compatible><title>Your sequence is ${status}</title><meta content="width=device-width,initial-scale=1"name=viewport><style>@media screen{@font-face{font-family:'Source Sans Pro';font-style:normal;font-weight:400;src:local('Source Sans Pro Regular'),local('SourceSansPro-Regular'),url(https://fonts.gstatic.com/s/sourcesanspro/v10/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff')}@font-face{font-family:'Source Sans Pro';font-style:normal;font-weight:700;src:local('Source Sans Pro Bold'),local('SourceSansPro-Bold'),url(https://fonts.gstatic.com/s/sourcesanspro/v10/toadOcfmlt9b38dHJxOBGFkQc6VGVFSmCnC_l7QZG60.woff) format('woff')}}a,body,table,td{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}table,td{mso-table-rspace:0;mso-table-lspace:0}img{-ms-interpolation-mode:bicubic}a[x-apple-data-detectors]{font-family:inherit!important;font-size:inherit!important;font-weight:inherit!important;line-height:inherit!important;color:inherit!important;text-decoration:none!important}div[style*="margin: 16px 0;"]{margin:0!important}body{width:100%!important;height:100%!important;padding:0!important;margin:0!important}table{border-collapse:collapse!important}a{color:#1a82e2}img{height:auto;line-height:100%;text-decoration:none;border:0;outline:0}</style><body style=background-color:#e9ecef><div class=preheader style=display:none;max-width:0;max-height:0;overflow:hidden;font-size:1px;line-height:1px;color:#fff;opacity:0>Your sequence ${sequence.name} is ${status}.</div><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td align=center bgcolor=#e9ecef><!--[if (gte mso 9)|(IE)]><table border=0 cellpadding=0 cellspacing=0 width=600 align=center><tr><td align=center valign=top width=600><![endif]--><table border=0 cellpadding=0 cellspacing=0 width=100% style=max-width:600px><tr><td align=left bgcolor=#ffffff style="padding:36px 24px 0;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;border-top:3px solid #d4dadf"><h1 style=margin:0;font-size:32px;font-weight:700;letter-spacing:-1px;line-height:48px>Your sequence is ${status}</h1></table><!--[if (gte mso 9)|(IE)]><![endif]--><tr><td align=center bgcolor=#e9ecef><!--[if (gte mso 9)|(IE)]><table border=0 cellpadding=0 cellspacing=0 width=600 align=center><tr><td align=center valign=top width=600><![endif]--><table border=0 cellpadding=0 cellspacing=0 width=100% style=max-width:600px><tr><td align=left bgcolor=#ffffff style="padding:24px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;line-height:24px"><p style=margin:0>Hi ${name},</p><br><p style=margin:0>${content}</p><tr><td align=left bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td align=center bgcolor=#ffffff style=padding:12px><table border=0 cellpadding=0 cellspacing=0><tr><td align=center bgcolor=#1a82e2 style=border-radius:6px><a clicktracking="off" href="${APP_CONFIG.CLIENT_URL}/sequence?seqId=${sequence.id}" target=_blank style="display:inline-block;padding:16px 36px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;color:#fff;text-decoration:none;border-radius:6px">View Sequence</a></table></table><tr><td align=left bgcolor=#ffffff style="padding:24px;font-family:'Source Sans Pro',Helvetica,Arial,sans-serif;font-size:16px;line-height:24px;border-bottom:3px solid #d4dadf"><p style=margin:0>Best regards,</p><p style=margin:0>Zileo Team</p></table><!--[if (gte mso 9)|(IE)]><![endif]--></table>`,
    };

    await sgMail.send(msg).catch((warn) => {
      console.warn('Error in sending email sequence stop', warn?.response?.body?.errors);
    });
  }

  async getSequenceAncestorsIds(id: string, onlyRoot = false) {
    const results = await this.dataSource.query(
      `
      WITH RECURSIVE ancestors AS (
          -- Start with the given child ID
          SELECT
              id AS child_id,
              parent_id AS ancestor_id
          FROM sequences
          WHERE id = $1

          UNION ALL

          -- Recursively find ancestors
          SELECT
              a.child_id,
              s.parent_id AS ancestor_id
          FROM ancestors a
          JOIN sequences s ON s.id::text = a.ancestor_id::text
      )
      SELECT ancestor_id
      FROM ancestors
      WHERE ancestor_id IS NOT NULL;
    `,
      [id]
    );

    const ids = results?.map((item) => item.ancestor_id) || [];

    return onlyRoot ? ids.at(-1) : ids;
  }

  async getAllMailConfig(userId: string, query: GetAllQuery) {
    const { page, limit, status, keyword, createdFrom, emailSearch, fromDate, toDate } = query;

    const queryBuilder = this.sequenceRepository
      .createQueryBuilder('sequence')
      .where('sequence.user_id = :userId', { userId });
    queryBuilder.andWhere({ parentId: IsNull() });

    if (keyword) {
      queryBuilder.andWhere('sequence.name ILIKE :keyword', { keyword: `%${keyword}%` });
    }

    if (createdFrom) {
      queryBuilder.andWhere('sequence.created_from ILIKE :createdFrom', { createdFrom: `%${createdFrom}%` });
    }

    if (status) {
      if (status === 'COMPLETED') {
        queryBuilder
          .andWhere('sequence.is_marked_as_completed = :isMarkedAsCompleted', { isMarkedAsCompleted: true })
          .andWhere('sequence.status = :status', { status: 'STOP' });
      } else if (status === 'STOP') {
        queryBuilder
          .andWhere('sequence.is_marked_as_completed = :isMarkedAsCompleted', { isMarkedAsCompleted: false })
          .andWhere('sequence.status = :status', { status });
      } else {
        queryBuilder.andWhere('sequence.status = :status', { status });
      }
    }

    if (fromDate && toDate) {
      if (fromDate === toDate) {
        queryBuilder.andWhere(`sequence.recreated_at::date = :fromDate::date`, { fromDate });
      } else {
        queryBuilder.andWhere('sequence.recreated_at BETWEEN :fromDate AND :toDate', { fromDate, toDate });
      }
    }

    if (emailSearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('sequence.recipients ILIKE :emailSearch', { emailSearch: `%${emailSearch}%` })
            .orWhere('sequence.company_name ILIKE :emailSearch', { emailSearch: `%${emailSearch}%` })
            .orWhere('sequence.contact_names ILIKE :emailSearch', { emailSearch: `%${emailSearch}%` })
            .orWhere('sequence.name ILIKE :emailSearch', { emailSearch: `%${emailSearch}%` });
        })
      );
    }

    const [user, mails, count] = await Promise.all([
      this.userRepository.findOne({ where: { id: userId } }),
      queryBuilder
        .orderBy('sequence.status', 'DESC')
        .addOrderBy('sequence.recreatedAt', 'DESC')
        .skip(limit && page ? (page - 1) * limit : 0)
        .take(limit || 10)
        .select([
          'sequence.id',
          'sequence.name',
          'sequence.status',
          'sequence.stepCount',
          'sequence.pendingCount',
          'sequence.sentCount',
          'sequence.repliedCount',
          'sequence.stopCount',
          'sequence.delivered',
          'sequence.dropped',
          'sequence.bounced',
          'sequence.blocked',
          'sequence.deferred',
          'sequence.spamReport',
          'sequence.createdAt',
          'sequence.updatedAt',
          'sequence.isMarkedAsCompleted',
          'sequence.opened',
          'sequence.linkClicked',
          'sequence.isStared',
          'sequence.scheduleInformation',
          // 'sequence.recipients',
          'sequence.recreatedAt',
        ])
        .getMany(),
      queryBuilder.getCount(),
    ]);

    const seqIds = mails.map((item) => item.id);

    const results = await this.dataSource.query(
      `
      WITH RECURSIVE descendants AS (
        -- Start with the root item
        SELECT
          id AS root_id,
          id AS descendant_id,
          status,
          is_marked_as_completed,
          sent_count,
          opened,
          link_clicked,
          replied_count,
          0 AS depth
        FROM sequences
          WHERE id = ANY($1)

        UNION ALL

        -- Recursively find descendants
        SELECT
          d.root_id,
          s.id AS descendant_id,
          s.status,
          s.is_marked_as_completed,
          s.sent_count,
          s.opened,
          s.link_clicked,
          s.replied_count,
          d.depth + 1 AS depth
        FROM descendants d
        JOIN sequences s ON s.parent_id::text = d.descendant_id::text
      )
      -- Combine the results: count descendants, find the deepest one, and sum sent_count, opened, replied_count, and link_clicked
      SELECT
        root_id,
        MAX(depth) AS total_descendants, -- Track the maximum depth
        (ARRAY_AGG(descendant_id ORDER BY depth DESC))[1] AS latest_descendant_id, -- Latest descendant's ID
        (ARRAY_AGG(status ORDER BY depth DESC))[1] AS latest_status, -- Latest descendant's status
        (ARRAY_AGG(is_marked_as_completed ORDER BY depth DESC))[1] AS latest_is_marked_as_completed, -- Latest descendant's completion status
        SUM(sent_count) AS total_sent_count, -- Sum of sent_count for root and its descendants
        SUM(opened) AS total_opened, -- Sum of opened for root and its descendants
        SUM(link_clicked) AS total_link_clicked, -- Sum of link_clicked for root and its descendants
        SUM(replied_count) AS total_replied_count -- Sum of replied_count for root and its descendants
      FROM descendants
      GROUP BY root_id; -- Include root_id in GROUP BY
    `,
      [seqIds]
    );

    const childrenMap = results.reduce((acc, item) => {
      acc[item.root_id] = item;
      return acc;
    }, {});

    return this.formatOutputData(
      { key: 'GET_ALL_MAIL_CONFIG' },
      {
        data: {
          mails: mails.map((item) => {
            // const listRecipients = item.recipients?.split(',');
            // const actionCount = listRecipients.length * item.stepCount;
            // Calculate rates
            const sentCount = childrenMap[item.id]?.total_sent_count || item.sentCount || 0;
            const opened = childrenMap[item.id]?.total_opened || item.opened || 0;
            const linkClicked = childrenMap[item.id]?.total_link_clicked || item.linkClicked || 0;
            const repliedCount = childrenMap[item.id]?.total_replied_count || item.repliedCount || 0;
            const replyRate = ((Number(repliedCount) / Number(sentCount)) * 100).toFixed(2);
            const openRate = ((Number(opened) / Number(sentCount)) * 100).toFixed(2);
            return {
              ...item,
              sender: user.fullName,
              replyRate,
              openRate,
              sentCount,
              opened,
              linkClicked,
              repliedCount,
              hasChildren: !!childrenMap[item.id]?.total_descendants,
              numOfChildren: childrenMap[item.id]?.total_descendants || 0,
              ...(childrenMap[item.id]?.total_descendants && {
                childStatus: childrenMap[item.id]?.latest_status,
                childIsMarkedAsCompleted: childrenMap[item.id]?.latest_is_marked_as_completed,
              }),
            };
          }),
          count,
        },
      }
    );
  }

  async getSequenceDetail(sequenceId: string) {
    const mappingStatus = {
      PENDING: 'PENDING',
      SCHEDULED: 'PENDING',
      STOP: 'STOPPED',
      OFF: 'PENDING',
      SENT: 'SENT',
      REPLIED: 'STOPPED',
      ERROR: 'SENT',
      LINKEDIN_REACH_LIMIT: 'SENT',
      NOT_SENT: 'NOT_SENT',
    };

    const sequenceSteps = await this.sequenceStepRepository
      .createQueryBuilder('ss')
      .innerJoin('sequences', 's', 's.id = ss.sequence_id')
      .innerJoin('sequence_instances', 'si', 'si.sequence_step_id = ss.id AND version = 0')
      .select(
        `ss.id AS id,
        ss.type AS type,
        ss.subject AS subject,
        ss.content AS content,
        ss.name AS name,
        ss.step_index AS step_index,
        ss.delays AS delays,
        ss.thread_id AS thread_id,
        ss.attachments AS file_list,
        si.status AS status,
        si.repliers AS repliers`
      )
      .where('ss.sequence_id = :sequenceId', {
        sequenceId,
      })
      .orderBy('ss.step_index', 'ASC')
      .getRawMany();

    let listEmailConfig = sequenceSteps.map((step) => ({
      id: step.id,
      type: step.type,
      stepIndex: step.step_index,
      subject: step.subject,
      content: step.content,
      delays: step.delays,
      threadId: step.thread_id,
      name: step.name,
      status:
        step.status === SequenceStepStatus.SENT && Array.isArray(step.repliers) && step.repliers.length > 0
          ? mappingStatus.REPLIED
          : mappingStatus[step.status],
      repliers: step.repliers,
      fileList: step?.file_list || [],
    }));
    // console.log('sequenceSteps: ', sequenceSteps);
    // console.log('listEmailConfig: ', listEmailConfig);
    const rawData = await this.sequenceInstanceRepository
      .createQueryBuilder('sequenceInstance')
      .innerJoin('sequence_steps', 'sequenceStep', 'sequenceStep.id = sequenceInstance.sequence_step_id')
      .select([
        'sequenceInstance.recipients AS recipients',
        'sequenceInstance.hotlist_ids AS hotlistIds',
        'sequenceInstance.contact_list_ids AS contactListIds',
        'sequenceInstance.short_list_ids AS shortListIds',
        'sequenceInstance.version AS version',
        'sequenceInstance.status AS status',
        'sequenceInstance.executed_at AS executed_at',
        'sequenceStep.step_index AS step_index',
        'sequenceStep.type AS type',
      ])
      .where('sequenceInstance.sequence_id = :sequenceId', { sequenceId })
      .orderBy('sequenceStep.step_index', 'DESC')
      .addOrderBy('sequenceInstance.version', 'DESC')
      .getRawMany();

    const { recipients } = rawData.reduce(
      (acc, { version: siVer, ...item }) => {
        // Get the latest steps of each version
        if (
          !acc.versions.includes(siVer) &&
          // LinkedIn connection request has its own recipients
          item.type !== SequenceStepType.LINKEDIN_CONNECTION_REQUEST
        ) {
          acc.versions.push(siVer);
          acc.recipients.push({
            shortListIds: item?.shortlistids || [],
            recipients: item.recipients,
            hotlistIds: item.hotlistids || [],
            contactListIds: item.contactlistids || [],
            status: item.status,
          });
        }

        return acc;
      },
      { recipients: [], versions: [] }
    );

    const stepRecipients = rawData.reduce((acc, item) => {
      if (!acc[item.step_index]) {
        acc[item.step_index] = {
          recipients: [],
          hotlistIds: [],
          contactListIds: [],
          shortListIds: [],
        };
      }
      const uniqueRecipients = new Map(
        [...acc[item.step_index].recipients, ...(item.recipients || [])].map((r) => [r.email, r])
      );
      acc[item.step_index].recipients = Array.from(uniqueRecipients.values());
      acc[item.step_index].hotlistIds = [...new Set([...acc[item.step_index].hotlistIds, ...(item.hotlistids || [])])];
      acc[item.step_index].contactListIds = [
        ...new Set([...acc[item.step_index].contactListIds, ...(item.contactlistids || [])]),
      ];
      acc[item.step_index].shortListIds = [
        ...new Set([...acc[item.step_index]?.shortListIds, ...(item?.shortlistids || [])]),
      ];

      return acc;
    }, {});

    const stepsExecutedAt = rawData.reduce((acc, item) => {
      if (!acc[item.step_index]) {
        acc[item.step_index] = item.executed_at;
      }

      return acc;
    }, {});

    listEmailConfig = listEmailConfig.map((step) =>
      [SequenceStepType.LINKEDIN_CONNECTION_REQUEST, SequenceStepType.TASK].includes(step.type)
        ? {
            ...step,
            ...stepRecipients[step.stepIndex],
            executedAt: stepsExecutedAt[step.stepIndex],
          }
        : {
            ...step,
            executedAt: stepsExecutedAt[step.stepIndex],
          }
    );

    return { listEmailConfig, recipients };
  }

  async getChildSequences(sequence: any) {
    if (!sequence?.id) {
      return null;
    }

    const childSequence: any = await this.sequenceRepository.findOne({
      where: { parentId: sequence.id },
      order: { createdAt: 'DESC' },
    });
    if (!childSequence) {
      return null;
    }

    const { listEmailConfig: childListEmailConfig, recipients: childRecipients } = await this.getSequenceDetail(
      childSequence.id
    );
    const child = await this.getChildSequences(childSequence);
    if (child) {
      childSequence.child = child;
    }

    return {
      id: childSequence.id,
      sequence: childSequence,
      steps: childListEmailConfig,
      recipients: childRecipients,
    };
  }

  async getDetailEmailConfigFromSequence(sequenceId: string) {
    const emailSequence: any = await this.sequenceRepository.findOne({
      where: { id: sequenceId },
      relations: {
        user: true,
      },
    });

    if (!emailSequence) {
      throw new NotFoundException(`Sequence ${sequenceId} not found`);
    }

    const { listEmailConfig, recipients } = await this.getSequenceDetail(sequenceId);
    const job = await this.getJobFromSequence(emailSequence);

    let newJob = job;
    if (job?.jobtype) {
      newJob = {
        ...job,
        jobtype: convertCamelToNormalCase(
          standardizeJobType(job.jobtype?.replace('{', '').replace('}', '').replace(/\"/g, '')).join(', ')
        ),
      };
    }

    const child = await this.getChildSequences(emailSequence);
    if (child) {
      emailSequence.child = child;
    }

    return this.formatOutputData(
      { key: 'GET_DETAIL_EMAIL_CONFIG_FROM_SEQUENCE' },
      { data: { job: newJob, mails: listEmailConfig, sequence: emailSequence, steps: listEmailConfig, recipients } }
    );
  }

  async getDataReportSequence(sequenceId: string) {
    const [emailSequence] = await Promise.all([
      this.sequenceRepository.findOne({
        where: { id: sequenceId },
      }),
    ]);

    if (!emailSequence) {
      throw new NotFoundException(`Sequence ${sequenceId} not found`);
    }

    const childIds = await this.getChildSequenceIds(emailSequence);
    const seqIds = [sequenceId, ...childIds];

    const [data, stepCount] = await Promise.all([
      this.sequenceActivityLogRepository
        .createQueryBuilder('activity')
        .select('DATE(activity.created_at)', 'dateTime')
        .addSelect("SUM(CASE WHEN activity.type = 'replied' THEN 1 ELSE 0 END)", 'replyRate')
        .addSelect("SUM(CASE WHEN activity.type = 'opened' THEN 1 ELSE 0 END)", 'openRate')
        // .addSelect('SUM(CASE WHEN activity.type = \'sent\' THEN 1 ELSE 0 END)', 'sequenceStarted') - not need now
        .addSelect("SUM(CASE WHEN activity.type = 'link_clicked' THEN 1 ELSE 0 END)", 'clickRate')
        .addSelect("SUM(CASE WHEN activity.type = 'bounce' THEN 1 ELSE 0 END)", 'bounceRate')
        .where('activity.sequence_id IN (:...seqIds)', { seqIds })
        .groupBy('DATE(activity.created_at)')
        .getRawMany(),
      this.sequenceStepRepository.count({
        where: { sequence: { id: sequenceId } },
      }),
    ]);

    const listRecipients = emailSequence.recipients.split(',');
    const actionCount = listRecipients.length * stepCount;

    // Calculate rates
    const replyRate = Number(((Number(emailSequence.repliedCount) / actionCount) * 100).toFixed(2));
    const openRate = Number(((Number(emailSequence.opened) / actionCount) * 100).toFixed(2));
    // const sequenceStarted = (Number(total.totalSequenceStarted) / actionCount) * 100; - not need now
    const clickRate = Number(((Number(emailSequence.linkClicked) / actionCount) * 100).toFixed(2));
    const bounceRate = Number(((Number(emailSequence.bounced) / actionCount) * 100).toFixed(2));
    const rate = { replyRate, openRate, clickRate, bounceRate };

    return this.formatOutputData({ key: 'GET_DATA_REPORT_SEQUENCE' }, { data: { data, rate } });
  }

  async getJobFromSequence(sequence: SequenceEntity): Promise<{
    jobtitle: string;
    description: string;
    jobtype: string;
    company: string;
    logoCompany: string;
    salary: string | number;
    posted: Date;
    joblocationcity: string;
    source: string;
    address_city: string;
    address_country: string;
    address_line_1: string;
    address_line_2: string;
    employment_type: string;
    companyBhId?: string;
  }> {
    const { jobBoardId, externalJobId } = sequence;
    // Get original jobs
    if (jobBoardId && !externalJobId) {
      const jobBoardFromOpenSearch = this.elasticsearchService.getById(JOBS_INDEX, jobBoardId);
      const jobBoardFromDb = this.jobBoardRepository.findOneBy({ job_id: jobBoardId });
      const data = await Promise.all([jobBoardFromOpenSearch, jobBoardFromDb]);

      let osJob: any;
      let dbJob: any;
      if (data[0]) {
        osJob = {
          jobtitle: data[0].jobtitle,
          description: data[0].description,
          jobtype: data[0].jobtype,
          company: data[0].company,
          logoCompany: data[0].logoCompany,
          salary: data[0].salary,
          posted: data[0].posted,
          joblocationcity: data[0].joblocationcity,
          source: data[0].source,
          address_city: data[0].joblocationcity,
          address_country: data[0].country,
          address_line_1: '',
          address_line_2: '',
          employment_type: data[0].jobtype,
        };
      }

      if (data[1]) {
        dbJob = {
          jobtitle: data[1].jobtitle,
          description: data[1].description,
          jobtype: data[1].jobtype,
          company: data[1].company,
          logoCompany: data[1].logoCompany,
          salary: data[1].salary,
          posted: data[1].posted,
          joblocationcity: data[1].joblocationcity,
          source: data[1].source,
          address_city: data[1].joblocationcity,
          address_country: data[1].country,
          address_line_1: '',
          address_line_2: '',
          employment_type: data[1].jobtype,
        };
      }

      return osJob || dbJob || {};
    }

    // Get from job leads
    if (jobBoardId && !externalJobId) {
      const jobLeadData = await this.jobLeadRepository.findOne({
        where: [{ job_lead_external_id: externalJobId }, { job_board_id: jobBoardId }],
      });

      if (jobLeadData) {
        return {
          jobtitle: jobLeadData.title,
          description: jobLeadData.description,
          jobtype: jobLeadData.employment_type,
          company: jobLeadData.company_name,
          logoCompany: jobLeadData.logoCompany,
          salary: jobLeadData.salary,
          posted: jobLeadData.date_added,
          joblocationcity: [jobLeadData.address_city, jobLeadData.address_country].filter(Boolean).join(', '),
          source: jobBoardId ? JOB_BOARDS.find((board) => jobBoardId.startsWith(board)) : '',
          address_city: jobLeadData.address_city,
          address_country: jobLeadData.address_country,
          address_line_1: jobLeadData.address_line_1,
          address_line_2: jobLeadData.address_line_2,
          employment_type: jobLeadData.employment_type,
        };
      }
    }

    // Get from BH
    if (externalJobId) {
      const { bhRestToken, corporateRestUrl } = (await this.getBhToken(sequence.user?.organizationId)) || {};
      const jobVacancyData = await getJobVacancy(bhRestToken, corporateRestUrl, externalJobId);
      if (jobVacancyData) {
        return {
          jobtitle: jobVacancyData.title,
          description:
            jobVacancyData.description?.split('<br> Source: Zileo<br>')?.at(-1) || jobVacancyData.description,
          jobtype: jobVacancyData.employmentType,
          company: jobVacancyData.clientCorporation?.name,
          companyBhId: jobVacancyData.clientCorporation?.id,
          logoCompany: jobVacancyData.logoCompany,
          salary: jobVacancyData.salary,
          posted: jobVacancyData.dateAdded,
          joblocationcity: jobVacancyData.address?.city,
          source: '',
          address_city: jobVacancyData.address?.city,
          address_country: jobVacancyData.address?.countryName,
          address_line_1: jobVacancyData.address?.address1,
          address_line_2: jobVacancyData.address?.address2,
          employment_type: jobVacancyData.employmentType,
        };
      }
    }

    return null;
  }

  async getJobLead(sequence) {
    let jobLead: any;

    const jobFromBH = await this.getJobFromSequence(sequence);
    if (jobFromBH) {
      jobLead = {
        title: jobFromBH.jobtitle,
        description: jobFromBH.description,
        jobType: jobFromBH.jobtype,
        company_name: jobFromBH.company,
        logoCompany: jobFromBH.logoCompany,
        salary: jobFromBH.salary as number,
        date_added: jobFromBH.posted,
        address_city: jobFromBH.address_city,
        address_country: jobFromBH.address_country,
        address_line_1: jobFromBH.address_line_1,
        address_line_2: jobFromBH.address_line_2,
        employment_type: jobFromBH.employment_type,
      } as JobLead;
    }

    return jobLead;
  }

  async getSequence(id: string) {
    const emailSequence = await this.sequenceRepository.findOne({
      where: [{ id }, { externalJobId: id }, { jobBoardId: id }],
    });

    return this.formatOutputData({ key: 'GET_SEQUENCE' }, { data: { sequence: emailSequence } });
  }

  async deleteEmailConfigFromSequence(sequenceId: string) {
    const ids = await this.getChildSequenceIds({ id: sequenceId });
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const emailSequence = await queryRunner.manager.findOne(SequenceEntity, { where: { id: sequenceId } });

      if (!emailSequence) {
        throw new NotFoundException(`Sequence ${sequenceId} not found`);
      }

      ids.unshift(sequenceId);

      await queryRunner.manager.delete(SequenceStepEntity, { sequence: { id: In(ids) } });
      await queryRunner.manager.delete(SequenceActivityLogEntity, { sequence: { id: In(ids) } });
      await queryRunner.manager.delete(SequenceEntity, { id: In(ids) });

      await queryRunner.commitTransaction();

      return this.formatOutputData({ key: 'DELETE_EMAIL_CONFIG_FROM_SEQUENCE' }, { data: {} });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async deleteManySequences(sequenceIds: string[]) {
    await this.sequenceRepository.delete({ id: In(sequenceIds) });

    return this.formatOutputData({ key: 'DELETE_MANY_SEQUENCE' }, { data: {} });
  }

  async updateMarkSequence(sequenceIds: string[]) {
    const sequencesToUpdate = await this.sequenceRepository.find({
      where: { id: In(sequenceIds), status: Not(SequenceStatus.STOP) },
      select: ['id'],
    });

    await this.sequenceRepository.update(
      { id: In(sequenceIds) },
      { isMarkedAsCompleted: true, status: SequenceStatus.STOP }
    );
    const salEntities = sequencesToUpdate.map((sequence) =>
      this.sequenceActivityLogRepository.create({
        type: SequenceActivityType.STOPPED_BY_USER,
        sequence: { id: sequence.id },
        content: {
          reason: 'MARKED_AS_COMPLETED',
        },
        occurredAt: new Date().toISOString(),
      })
    );

    await this.sequenceActivityLogRepository.insert(salEntities);

    return this.formatOutputData({ key: 'UPDATE_MARK_SEQUENCE' }, { data: {} });
  }

  async bulkStopSequence(sequenceIds: string[]) {
    await this.sequenceRepository.update({ id: In(sequenceIds) }, { status: SequenceStatus.STOP });
    const seqActivityLogsEntities = sequenceIds.map((sequenceId) =>
      this.sequenceActivityLogRepository.create({
        type: SequenceActivityType.STOPPED_BY_USER,
        sequence: { id: sequenceId },
        content: {
          reason: 'BULK_STOP_SEQUENCE',
        },
        occurredAt: new Date().toISOString(),
      })
    );

    await this.sequenceActivityLogRepository.insert(seqActivityLogsEntities);

    return this.formatOutputData({ key: 'BULK_STOP_SEQUENCE' }, { data: {} });
  }

  async updateDetailEmailConfigFromSequence(sequenceId: string, updateMailDto: UpdateSequenceDto) {
    const emailSequence = await this.sequenceRepository.findOne({
      where: { id: sequenceId },
    });

    if (!emailSequence) {
      throw new NotFoundException(`Sequence ${sequenceId} not found`);
    }

    if (emailSequence.status === SequenceStatus.DRAFT) {
      if (updateMailDto.status !== SequenceStatusEnum.LIVE) {
        throw new BadRequestException('Draft sequence only can be live');
      }

      const sequenceSteps = await this.sequenceStepRepository.find({
        where: { sequence: { id: sequenceId } },
        relations: ['sequenceInstances'],
      });

      if (sequenceSteps.length === 0) {
        throw new BadRequestException('Sequence must have at least one step to be live');
      }

      if (
        sequenceSteps.some(
          ({
            sequenceInstances = [],
            // Draft should have only 1 instance
          }) => !sequenceInstances.length || !sequenceInstances[0].recipients?.length
        )
      ) {
        throw new BadRequestException('Sequence steps must have at least one recipient to be live');
      }

      if (
        sequenceSteps.some(({ type, subject, content }) => type === SequenceStepType.EMAIL && (!subject || !content))
      ) {
        throw new BadRequestException('Sequence email steps must have subject and content to be live');
      }
    }

    const updateSeqData: any = {
      status: updateMailDto.status,
      isStared: updateMailDto.isStared,
    };

    await this.sequenceRepository.update(sequenceId, updateSeqData);

    if (updateMailDto.status === SequenceStatusEnum.STOP) {
      const emailActivityLogEntity = this.sequenceActivityLogRepository.create({
        type: SequenceActivityType.STOPPED_BY_USER,
        sequence: { id: emailSequence.id },
        occurredAt: new Date().toISOString(),
      });

      await Promise.all([
        this.sequenceInstanceRepository.update(
          { sequence: { id: sequenceId }, status: SequenceStepStatus.PENDING },
          { status: SequenceStepStatus.OFF }
        ),
        this.sequenceActivityLogRepository.save(emailActivityLogEntity),
      ]);
    }

    if (updateMailDto.status === SequenceStatusEnum.LIVE) {
      const emailActivityLogEntity = this.sequenceActivityLogRepository.create({
        type: SequenceActivityType.LIVED,
        sequence: { id: emailSequence.id },
        occurredAt: new Date().toISOString(),
      });

      await Promise.all([
        this.sequenceInstanceRepository.update(
          { sequence: { id: sequenceId }, status: In([SequenceStepStatus.OFF, SequenceStepStatus.STOP]) },
          { status: SequenceStepStatus.PENDING }
        ),
        this.sequenceActivityLogRepository.save(emailActivityLogEntity),
      ]);
    }

    // Re-calculate pendingCount
    const seqInstancesEmails = await this.sequenceInstanceRepository
      .createQueryBuilder('si')
      .innerJoin('sequence_steps', 'ss', 'ss.id = si.sequence_step_id')
      .select(
        'si.recipients AS recipients, si.status AS status, ss.step_index AS step_index, si.skip_recipients as skip_recipients'
      )
      .where('si.sequence_id = :seqId AND ss.type = :type', {
        // status: SequenceStepStatus.SENT,
        seqId: sequenceId,
        type: SequenceStepType.EMAIL,
      })
      .orderBy('ss.step_index', 'ASC')
      .getRawMany();

    const { pendingCount, stopCount } = seqInstancesEmails.reduce(
      (acc, seqInstancesEmail) => {
        if (seqInstancesEmail.status === SequenceStepStatus.PENDING) {
          acc.pendingCount += seqInstancesEmail.recipients.length;
        } else if ([SequenceStepStatus.STOP, SequenceStepStatus.OFF].includes(seqInstancesEmail.status)) {
          acc.stopCount += seqInstancesEmail.recipients.length;
        }
        acc.stopCount += seqInstancesEmail.skip_recipients?.length || 0;

        return acc;
      },
      { pendingCount: 0, stopCount: 0 }
    );

    await this.sequenceRepository.update(sequenceId, {
      pendingCount,
      stopCount,
    });

    return this.formatOutputData({ key: 'UPDATE_DETAIL_EMAIL_CONFIG_FROM_SEQUENCE' }, { data: {} });
  }

  async addContact(addContactDto: AddContactDto) {
    const { emailSeqIds, recipients } = addContactDto;

    await Promise.all(
      emailSeqIds.map(async (id) => {
        const emails = await this.sequenceInstanceRepository.find({
          where: {
            sequence: { id },
          },
        });

        if (emailSeqIds.length === 0) {
          return;
        }

        const existContact = emails?.[0]?.recipients;
        recipients.forEach((newContact) => {
          const isContactExist = ((existContact as { name: string; email: string }[]) || []).some(
            (contact) => contact.email === newContact.email
          );
          if (!isContactExist) {
            (existContact as { name: string; email: string }[]).push(newContact);
          }
        });

        return this.sequenceInstanceRepository.update(
          {
            sequence: { id },
          },
          {
            recipients: existContact,
          }
        );
      })
    );

    return this.formatOutputData({ key: 'ADD_CONTACT' }, { data: {} });
  }

  async test(sequenceId, execute = null, runAt = '2024-10-10T08:00:00.000Z') {
    const mapping = {
      '1e899dba-302f-4b48-b2ed-e989885b2ef7': '3549',
      'ed6671d2-4935-4ccb-a970-0b1a6e9abf8b': '3550',
      '5fc11793-7a93-4d75-9554-29d3ad8fe422': '3551',
    };
    if (!mapping[sequenceId]) {
      return this.formatOutputData({ key: 'RUN_SEQ_STEP' }, { data: { success: false } });
    }

    const hotListId = mapping[sequenceId];
    const userId = '4c2912a1-7814-4f1f-9201-00431dfc21a2';

    const sequenceStep: SequenceStepEntity = await this.sequenceStepRepository.findOne({
      where: { sequence: { id: sequenceId } },
      relations: {
        sequence: true,
        sequenceInstances: true,
      },
    });

    const sentActivityLogs = await this.sequenceActivityLogRepository.find({
      where: { sequence: { id: sequenceId }, type: SequenceActivityType.SENT },
    });
    const sentRecipients = sentActivityLogs.flatMap((item) => (item.content as any)?.recipients);
    const formattedSentRecipients = sentRecipients.map((item) => ({ email: item.email || item }));

    // return { sentRecipients, length: sentRecipients.length };

    const currentUser = await this.userRepository.findOneBy({ id: userId });
    const contactHotList = await this.findHotListContact(hotListId, currentUser);
    const optOutContacts = [];

    if (contactHotList?.length) {
      const hotlistRecipients = filterOutByKey(
        removeDuplicatesByKey(contactHotList.flat(), (item: any) => item?.email?.toLowerCase()),
        formattedSentRecipients, //remove sent recipients
        (item: any) => item?.email?.toLowerCase()
      );

      const { validRecipients, optOutRecipients } = hotlistRecipients.reduce(
        (acc, recipient: any) => {
          if (!recipient?.email?.includes('@')) {
            return acc;
          }
          if (recipient.massMailOptOut || recipient.isUnsubscribeEmail) {
            acc.optOutRecipients.push(recipient);
          } else {
            acc.validRecipients.push(recipient);
          }

          return acc;
        },
        { validRecipients: [], optOutRecipients: [] }
      );
      optOutContacts.push(...optOutRecipients);

      if (execute !== 'true') {
        return validRecipients;
      }

      const delayFrom = new Date(runAt).getTime() - new Date().getTime();
      if (Number.isNaN(new Date(runAt).getTime()) || delayFrom < 0) {
        // Over expected time,
        return this.formatOutputData({ key: 'RUN_SEQ_STEP' }, { data: { success: false, reason: 'Overtime' } });
      }

      const chunkHotlistRecipients = chunkArray(validRecipients, RECIPIENT_PER_TASK);
      const stepTasks = [];
      chunkHotlistRecipients.forEach((chunk, chunkIndex) => {
        stepTasks.push(
          this.sequenceStepTaskQueue.add(
            BULL_JOB_NAMES.SEQUENCE_STEP_TASK,
            {
              recipients: chunk,
              user: currentUser,
              sequence: sequenceStep.sequence,
              sequenceStep: sequenceStep,
              sequenceInstance: sequenceStep.sequenceInstances[0],
              scheduledAt: new Date(),
              type: SequenceStepType.EMAIL,
              sourceType: 'HOTLIST',
            },
            {
              jobId: `${sequenceStep.sequenceInstances[0].id}#H${chunkIndex}`,
              attempts: BULL_JOB_ATTEMPTS,
              delay: delayFrom + 10 * 1000 * chunkIndex,
            }
          )
        );
      });

      await Promise.all(stepTasks);
    }
  }

  async getMailConfig(rawJobId: string, userId: string, type: JobType = JobType.INTERNAL) {
    try {
      const jobId = decodeURIComponent(rawJobId);
      let query: FindManyOptions = {
        where: {
          user: { id: userId },
        },
        order: {
          stepIndex: 'ASC',
        },
        relations: {
          sequence: true,
        },
      };

      if (type === JobType.INTERNAL) {
        set(query, 'where.jobBoardId', jobId);
      } else {
        set(query, 'where.externalJobId', `bullhorn-${jobId}`);
      }

      const mails = await this.sequenceStepRepository.find(query);

      return this.formatOutputData({ key: 'GET_EMAIL' }, { data: { mails } });
    } catch (error) {
      console.log(error);
      this.logger.error(error.message);

      throw error;
    }
  }

  async validateUserSendMail(
    user: IJwtPayload,
    emailSeqId: string,
    jobBoardId: string,
    externalJobId: string
  ): Promise<ISendMail> {
    const { currentUser, jobId, externalId, existedMails, jobLead } = await this.findEssentialInfo(
      user,
      emailSeqId,
      jobBoardId,
      externalJobId
    );

    if (!currentUser.grantId && !currentUser.grantUnipileId) {
      throw new BadRequestException(
        `Your email has not been granted permission to send sequence email. Please select Mailbox tab, link your email then try again`
      );
    }

    return { currentUser, jobId, externalId, existedMails, jobLead };
  }

  async bulkInsertTasks(records: any[]) {
    const chunkRecords = chunkArray(records, 1000);

    await BBPromise.all(
      chunkRecords.map(async (items) => {
        // await this.sequenceStepTaskRepository.insert(items);
        await this.sequenceStepTaskRepository
          .createQueryBuilder()
          .insert()
          .into(SequenceStepTaskEntity)
          .values(items)
          .orUpdate(['scheduled_at'], ['id'])
          // .orIgnore()
          .execute();
        return true;
      }),
      { concurrency: 1 }
    );
  }

  async sendFirstMail({
    currentUser,
    existedPrimaryMail,
    jobLead,
    currentDate,
    isLastStep = false,
  }: {
    currentUser: UserEntity;
    existedPrimaryMail: SequenceStepEntity;
    jobLead: JobLead;
    currentDate: Date;
    isLastStep?: boolean;
  }) {
    const { jobBoardId, externalJobId } = existedPrimaryMail;

    let hotlistContacts = [];
    let contactListContacts = [];
    let shortListContacts = [];
    const firstStepInstance = existedPrimaryMail.sequenceInstances[0];
    if (!firstStepInstance) {
      return;
    }

    const { recipients, hotlistIds, contactListIds, shortListIds } = firstStepInstance;

    if (!jobLead && (externalJobId || jobBoardId)) {
      jobLead = externalJobId
        ? await this.jobLeadRepository.findOneBy({ job_lead_external_id: externalJobId })
        : await this.jobLeadRepository.findOneBy({ job_board_id: jobBoardId });
    }

    if (hotlistIds?.length) {
      hotlistContacts = await this.findHotListContact(hotlistIds, currentUser);
    }

    if (contactListIds?.length) {
      contactListContacts = await this.findContactFromContactList(contactListIds);
    }

    if (shortListIds?.length) {
      shortListContacts = await this.findShortListCandidate(shortListIds);
    }

    let hotListSentId = '';
    let sentIds: string[];
    let sentCount = 0;
    let sequenceChanges;
    let hasStepTask = false;
    if (existedPrimaryMail.type === SequenceStepType.EMAIL) {
      const optOutContacts = [];
      const invalidContacts = [];
      const stepRecipients = recipients.reduce(
        (acc, recipient: any) => {
          if (!recipient?.email?.includes('@')) {
            return acc;
          }
          if (recipient.massMailOptOut || recipient.isUnsubscribeEmail) {
            acc.optOutRecipients.push(recipient);
          } else {
            acc.validRecipients.push(recipient);
          }

          return acc;
        },
        { validRecipients: [], optOutRecipients: [] }
      );
      const chunkRecipients = chunkArray(stepRecipients.validRecipients, RECIPIENT_PER_TASK);
      optOutContacts.push(...stepRecipients.optOutRecipients);
      // const stepTasks = chunkRecipients.map((chunk, chunkIndex) =>
      //   this.sequenceStepTaskQueue.add(
      //     BULL_JOB_NAMES.SEQUENCE_STEP_TASK,
      //     {
      //       recipients: chunk,
      //       user: currentUser,
      //       sequence: existedPrimaryMail.sequence,
      //       sequenceStep: existedPrimaryMail,
      //       sequenceInstance: firstStepInstance,
      //       scheduledAt: new Date(),
      //       type: SequenceStepType.EMAIL,
      //     },
      //     {
      //       jobId: `${firstStepInstance.id}#${chunkIndex}`,
      //       attempts: BULL_JOB_ATTEMPTS,
      //       delay: 30 * 1000 * chunkIndex,
      //     }
      //   )
      // );
      const stepTasks = chunkRecipients.map((chunk) =>
        this.sequenceStepTaskRepository.create({
          recipients: chunk,
          user: currentUser,
          sequence: existedPrimaryMail.sequence,
          sequenceStep: existedPrimaryMail,
          sequenceInstance: firstStepInstance,
          scheduledAt: new Date(),
          type: SequenceStepType.EMAIL,
          extraInformation: {
            hostname: os.hostname(),
          },
        })
      );

      if (contactListContacts?.length) {
        const contactListRecipients = filterOutByKey(
          removeDuplicatesByKey(contactListContacts.flat(), (item: any) => item?.email?.toLowerCase()),
          [...recipients, ...(hotlistContacts || [])].flat(),
          'email'
        );

        const { validRecipients, optOutRecipients } = contactListRecipients.reduce(
          (acc, recipient: any) => {
            if (!recipient?.email?.includes('@')) {
              return acc;
            }
            if (recipient.massMailOptOut || recipient.isUnsubscribeEmail) {
              acc.optOutRecipients.push(recipient);
            } else {
              acc.validRecipients.push(recipient);
            }

            return acc;
          },
          { validRecipients: [], optOutRecipients: [] }
        );
        const chunkContactListRecipients = chunkArray(validRecipients, RECIPIENT_PER_TASK);
        optOutContacts.push(...optOutRecipients);

        chunkContactListRecipients.forEach((chunk, chunkIndex) => {
          // stepTasks.push(
          //   this.sequenceStepTaskQueue.add(
          //     BULL_JOB_NAMES.SEQUENCE_STEP_TASK,
          //     {
          //       recipients: chunk,
          //       user: currentUser,
          //       sequence: existedPrimaryMail.sequence,
          //       sequenceStep: existedPrimaryMail,
          //       sequenceInstance: firstStepInstance,
          //       scheduledAt: new Date(),
          //       type: SequenceStepType.EMAIL,
          //       sourceType: 'CONTACTLIST',
          //     },
          //     {
          //       jobId: `${firstStepInstance.id}#C${chunkIndex}`,
          //       attempts: BULL_JOB_ATTEMPTS,
          //       delay: 30 * 1000 * chunkIndex,
          //     }
          //   )
          // );
          stepTasks.push(
            this.sequenceStepTaskRepository.create({
              recipients: chunk,
              user: currentUser,
              sequence: existedPrimaryMail.sequence,
              sequenceStep: existedPrimaryMail,
              sequenceInstance: firstStepInstance,
              scheduledAt: new Date(),
              type: SequenceStepType.EMAIL,
              sourceType: 'CONTACTLIST',
              extraInformation: {
                hostname: os.hostname(),
              },
            })
          );
        });
      }

      if (hotlistContacts?.length) {
        const hotlistRecipients = filterOutByKey(
          removeDuplicatesByKey(hotlistContacts.flat(), (item: any) => item?.email?.toLowerCase()),
          recipients,
          'email'
        );

        const { validRecipients, invalidRecipients, optOutRecipients } = hotlistRecipients.reduce(
          (acc, recipient: any) => {
            if (existedPrimaryMail.sequence?.skippedEmails?.includes(recipient?.email?.toLowerCase())) {
              acc.invalidRecipients.push(recipient);

              return acc;
            }
            if (recipient.massMailOptOut || recipient.isUnsubscribeEmail) {
              acc.optOutRecipients.push(recipient);
            } else {
              acc.validRecipients.push(recipient);
            }

            return acc;
          },
          { validRecipients: [], invalidRecipients: [], optOutRecipients: [] }
        );
        optOutContacts.push(...optOutRecipients);
        invalidContacts.push(...invalidRecipients);
        const chunkHotlistRecipients = chunkArray(validRecipients, RECIPIENT_PER_TASK);

        chunkHotlistRecipients.forEach((chunk, chunkIndex) => {
          // stepTasks.push(
          //   this.sequenceStepTaskQueue.add(
          //     BULL_JOB_NAMES.SEQUENCE_STEP_TASK,
          //     {
          //       recipients: chunk,
          //       user: currentUser,
          //       sequence: existedPrimaryMail.sequence,
          //       sequenceStep: existedPrimaryMail,
          //       sequenceInstance: firstStepInstance,
          //       scheduledAt: new Date(),
          //       type: SequenceStepType.EMAIL,
          //       sourceType: 'HOTLIST',
          //     },
          //     {
          //       jobId: `${firstStepInstance.id}#H${chunkIndex}`,
          //       attempts: BULL_JOB_ATTEMPTS,
          //       delay: 10 * 1000 * chunkIndex,
          //     }
          //   )
          // );
          stepTasks.push(
            this.sequenceStepTaskRepository.create({
              recipients: chunk,
              user: currentUser,
              sequence: existedPrimaryMail.sequence,
              sequenceStep: existedPrimaryMail,
              sequenceInstance: firstStepInstance,
              scheduledAt: new Date(),
              type: SequenceStepType.EMAIL,
              sourceType: 'HOTLIST',
              extraInformation: {
                hostname: os.hostname(),
              },
            })
          );
        });
      }

      if (shortListContacts?.length) {
        const shortListRecipients = filterOutByKey(
          removeDuplicatesByKey(shortListContacts.flat(), (item: any) => item?.email?.toLowerCase()),
          [...recipients, ...(hotlistContacts || [])].flat(),
          'email'
        );

        const { validRecipients, optOutRecipients } = shortListRecipients.reduce(
          (acc, recipient: any) => {
            if (!recipient?.email?.includes('@')) {
              return acc;
            }
            if (recipient.massMailOptOut || recipient.isUnsubscribeEmail) {
              acc.optOutRecipients.push(recipient);
            } else {
              acc.validRecipients.push(recipient);
            }
            return acc;
          },
          { validRecipients: [], optOutRecipients: [] }
        );

        const chunkShortListRecipients = chunkArray(validRecipients, RECIPIENT_PER_TASK);
        optOutContacts.push(...optOutRecipients);

        chunkShortListRecipients.forEach((chunk, chunkIndex) => {
          stepTasks.push(
            this.sequenceStepTaskRepository.create({
              recipients: chunk,
              user: currentUser,
              sequence: existedPrimaryMail.sequence,
              sequenceStep: existedPrimaryMail,
              sequenceInstance: firstStepInstance,
              scheduledAt: new Date(),
              type: SequenceStepType.EMAIL,
              sourceType: 'SHORTLIST',
              extraInformation: {
                hostname: os.hostname(),
              },
            })
          );
        });
      }

      await this.bulkInsertTasks(stepTasks);
      hasStepTask = stepTasks.length > 0;
      if (optOutContacts.length) {
        await this.sequenceActivityLogRepository.insert({
          type: SequenceActivityType.SKIPPED,
          sequence: { id: existedPrimaryMail.sequence.id },
          sequenceStep: { id: existedPrimaryMail.id },
          content: {
            sequenceInstance: firstStepInstance.id,
            type: existedPrimaryMail.type,
            emails: optOutContacts.map((item) => item.email),
            recipients: optOutContacts.map(({ email, name }) => ({ email, name })),
            reason: 'OPT_OUT',
          },
          occurredAt: new Date().toISOString(),
        });
      }
      if (invalidContacts.length) {
        await this.sequenceActivityLogRepository.insert({
          type: SequenceActivityType.SKIPPED,
          sequence: { id: existedPrimaryMail.sequence.id },
          sequenceStep: { id: existedPrimaryMail.id },
          content: {
            sequenceInstance: firstStepInstance.id,
            type: existedPrimaryMail.type,
            emails: invalidContacts.map((item) => item.email),
            recipients: invalidContacts.map(({ email, name }) => ({ email, name })),
            reason: 'INVALID_EMAIL',
          },
          occurredAt: new Date().toISOString(),
        });
      }

      // Is last step && no recipients
      if (isLastStep && !stepTasks.length) {
        // await this.sequenceRepository.update(
        //   { id: existedPrimaryMail.sequence?.id },
        //   { isMarkedAsCompleted: true, status: SequenceStatus.STOP }
        // );
        sequenceChanges = { isMarkedAsCompleted: true, status: SequenceStatus.STOP };
      }
    } else if (existedPrimaryMail.type === SequenceStepType.NOTE) {
      // Send note
      sentIds = await this.sendFirstNote({
        jobLead,
        currentUser,
        firstStep: existedPrimaryMail,
        clientContacts: removeDuplicatesByKey(
          [...recipients, ...hotlistContacts].flat(),
          (item: any) => item?.email?.toLowerCase(),
          'id'
        ).filter(({ id }) => id),
      });

      // Is last step && no recipients
      if (isLastStep) {
        // await this.sequenceRepository.update(
        //   { id: existedPrimaryMail.sequence?.id },
        //   { isMarkedAsCompleted: true, status: SequenceStatus.STOP }
        // );
        sequenceChanges = { isMarkedAsCompleted: true, status: SequenceStatus.STOP };
      }
    } else if (existedPrimaryMail.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
      // Send LinkedIn request
      const { clientContacts, noLinkedInProfiles, optOutContacts } = removeDuplicatesByKey(
        [...recipients, ...hotlistContacts, ...contactListContacts].flat(),
        (item: any) => item?.email?.toLowerCase(),
        'linkedinProfileUrl'
      ).reduce(
        (acc, recipient) => {
          if (recipient.massMailOptOut) {
            acc.optOutContacts.push(recipient);
            return acc;
          }

          const linkedinProfileUrl =
            recipient.linkedinProfileUrl || recipient.customText1 || recipient.linkedInProfileUrl;
          if (unipileClient.getLinkedInIdentifier(linkedinProfileUrl)) {
            acc.clientContacts.push(recipient);
          } else {
            acc.noLinkedInProfiles.push(recipient);
          }
          return acc;
        },
        {
          clientContacts: [],
          noLinkedInProfiles: [],
          optOutContacts: [],
        }
      );
      if (noLinkedInProfiles.length) {
        await this.sequenceActivityLogRepository.insert({
          type: SequenceActivityType.SKIPPED,
          sequence: { id: existedPrimaryMail.sequence.id },
          sequenceStep: { id: existedPrimaryMail.id },
          content: {
            sequenceInstance: firstStepInstance.id,
            type: existedPrimaryMail.type,
            emails: noLinkedInProfiles.map((item) => item.email),
            recipients: noLinkedInProfiles.map(({ email, name }) => ({ email, name })),
            displayText: "No valid LinkedIn URL was found so we couldn't send to {{contact_name}}.",
            reason: 'NO_LINKEDIN_PROFILE',
          },
          occurredAt: new Date().toISOString(),
        });
      }
      if (optOutContacts.length) {
        await this.sequenceActivityLogRepository.insert({
          type: SequenceActivityType.SKIPPED,
          sequence: { id: existedPrimaryMail.sequence.id },
          sequenceStep: { id: existedPrimaryMail.id },
          content: {
            sequenceInstance: firstStepInstance.id,
            type: existedPrimaryMail.type,
            emails: optOutContacts.map((item) => item.email),
            recipients: optOutContacts.map(({ email, name }) => ({ email, name })),
            reason: 'OPT_OUT',
          },
          occurredAt: new Date().toISOString(),
        });
      }

      const chunkRecipients = chunkArray(clientContacts, RECIPIENT_PER_TASK);
      // const stepTasks = chunkRecipients.map((chunk, chunkIndex) =>
      //   this.sequenceStepTaskQueue.add(
      //     BULL_JOB_NAMES.SEQUENCE_STEP_TASK,
      //     {
      //       recipients: chunk,
      //       user: currentUser,
      //       sequence: existedPrimaryMail.sequence,
      //       sequenceStep: existedPrimaryMail,
      //       sequenceInstance: firstStepInstance,
      //       scheduledAt: new Date(),
      //       type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
      //     },
      //     {
      //       jobId: `${firstStepInstance.id}#${chunkIndex}`,
      //       attempts: BULL_JOB_ATTEMPTS,
      //       delay: 3 * 1000 * chunkIndex,
      //       backoff: {
      //         type: 'fixed',
      //         delay: UNIPILE_REQUEST_LIMIT_TIMEOUT * 60 * 60 * 1000,
      //       },
      //     }
      //   )
      // );

      const actionLinkedIn = safeParseJSON(existedPrimaryMail.content);
      const stepTasks = chunkRecipients.map((chunk) =>
        this.sequenceStepTaskRepository.create({
          recipients: chunk,
          user: currentUser,
          sequence: existedPrimaryMail.sequence,
          sequenceStep: existedPrimaryMail,
          sequenceInstance: firstStepInstance,
          scheduledAt: new Date(),
          type: SequenceStepType.LINKEDIN_CONNECTION_REQUEST,
          action: actionLinkedIn?.map((action: any) => action.type)?.join(','),
          extraInformation: {
            hostname: os.hostname(),
          },
        })
      );

      await this.bulkInsertTasks(stepTasks);

      // Is last step && no recipients
      if (isLastStep && !stepTasks.length) {
        // await this.sequenceRepository.update(
        //   { id: existedPrimaryMail.sequence?.id },
        //   { isMarkedAsCompleted: true, status: SequenceStatus.STOP }
        // );

        sequenceChanges = { isMarkedAsCompleted: true, status: SequenceStatus.STOP };
      }
      hasStepTask = stepTasks.length > 0;
    } else if (existedPrimaryMail.type === SequenceStepType.TASK) {
      if (isLastStep) {
        sequenceChanges = { isMarkedAsCompleted: true, status: SequenceStatus.STOP };
      }
    }

    const savedEmailSequenceEntity = { ...existedPrimaryMail.sequence, ...sequenceChanges };
    savedEmailSequenceEntity.sentCount += sentCount;
    savedEmailSequenceEntity.pendingCount -= sentCount;

    const emailActivityLogEntity = this.sequenceActivityLogRepository.create({
      type: SequenceActivityType.SENT,
      sequence: { id: savedEmailSequenceEntity.id },
      sequenceStep: { id: existedPrimaryMail.id },
      content: {
        type: existedPrimaryMail.type || SequenceStepType.EMAIL,
        sequenceInstance: firstStepInstance.id,
        count: sentCount,
      },
      occurredAt: currentDate,
    });

    let instanceStatus = SequenceStepStatus.SCHEDULED;
    if ([SequenceStepType.NOTE, SequenceStepType.TASK].includes(existedPrimaryMail.type)) {
      instanceStatus = SequenceStepStatus.SENT;
    } else if (!hasStepTask) {
      instanceStatus = SequenceStepStatus.NOT_SENT;
    }

    const processList = [
      this.sequenceInstanceRepository.update(firstStepInstance.id, {
        status: instanceStatus,
        sentIds,
        scheduledAt: currentDate,
        // LINKEDIN_CONNECTION_REQUEST only update executedAt when it's sent success
        ...(existedPrimaryMail.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST
          ? {}
          : { executedAt: currentDate }),
      }),
      this.sequenceRepository.save(savedEmailSequenceEntity),
    ];
    if (sequenceChanges?.isMarkedAsCompleted) {
      processList.push(
        this.sequenceActivityLogRepository.insert({
          type: SequenceActivityType.STOPPED,
          sequence: { id: savedEmailSequenceEntity.id },
          content: {
            reason: 'AUTO_COMPLETED',
            explain:
              existedPrimaryMail.type === SequenceStepType.NOTE
                ? 'The sequence only has one NOTE step'
                : 'The sequence only has one step & there is no vaild recipients to send',
            stepIndex: existedPrimaryMail.stepIndex,
          },
          occurredAt: currentDate,
        })
      );
      const ancestorIds = await this.getSequenceAncestorsIds(savedEmailSequenceEntity.id);
      processList.push(
        this.sequenceRepository.update(
          { id: In(ancestorIds) },
          { isMarkedAsCompleted: true, status: SequenceStatus.STOP }
        )
      );
    }

    if (
      existedPrimaryMail.type !== SequenceStepType.TASK &&
      (sentCount ||
        ![SequenceStepType.EMAIL, SequenceStepType.LINKEDIN_CONNECTION_REQUEST].includes(existedPrimaryMail.type))
    ) {
      this.sequenceActivityLogRepository.insert(emailActivityLogEntity);
    }

    if (sequenceChanges?.isMarkedAsCompleted) {
      processList.push(this.sendEmailSequenceStop(currentUser, existedPrimaryMail.sequence, true));
    }

    await Promise.all(processList);
  }

  private async sendFirstNote({
    currentUser,
    clientContacts,
    firstStep,
    jobLead,
  }: {
    currentUser: UserEntity;
    clientContacts: any;
    firstStep: SequenceStepEntity;
    jobLead: JobLead;
  }) {
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(currentUser?.organizationId)) || {};
    const isLicenseConnected = currentUser?.licenseType === UserLicenseType.CONNECTED;

    const crmContactsUnordered = await this.crmBullhornService.findAndSyncBullhornContacts(
      clientContacts,
      currentUser.organizationId,
      currentUser.licenseType
    );
    const crmContactsMap = crmContactsUnordered.reduce((acc, contact) => {
      acc[contact.id] = contact;
      if (contact.bullhornId) {
        acc[contact.bullhornId] = contact;
      }

      return acc;
    }, {});

    const dataPayloads = await BBPromise.map(
      clientContacts,
      async (recipient) => {
        let crmContact = crmContactsMap[recipient.id];

        const emailInfo = {
          sender: {
            name: currentUser?.fullName,
            email: currentUser?.email,
          },
          recipient,
          job: jobLead,
          currentSentDateTime: new Intl.DateTimeFormat('en-GB', {
            dateStyle: 'full',
            timeStyle: 'long',
          }).format(),
        };

        let { content } = firstStep;

        MERGE_TAGS_PLACE_HOLDERS?.forEach((rawPlaceHolder) => {
          const purePlaceHolder = rawPlaceHolder.split('{{').join('').split('}}').join('');
          const replacedContent = getValueByPath(emailInfo, MergeTagMappingEnum[purePlaceHolder], '');
          content = content
            ?.split(rawPlaceHolder)
            ?.join(Array.isArray(replacedContent) ? replacedContent.join(', ') : replacedContent);
        });

        content = content?.replace(/\$(first_name|first name)\$/g, recipient.name?.split(' ')[0]);

        return {
          content,
          recipient,
          crmPayload: crmContact
            ? {
                resourceName: CrmNoteResourceName.CONTACT,
                resourceId: crmContact.id,
                action: firstStep.subject,
                title: firstStep.subject,
                content,
                creator: currentUser,
                contact: {
                  id: crmContact.id,
                },
                company: {
                  id: crmContact.companyId,
                },
              }
            : null,
          bullhornPayload: {
            comments: content,
            personReference: {
              id: recipient.id,
              searchEntity: 'ClientContact',
            },
            action: firstStep.subject,
            clientContacts: [{ id: recipient.id }],
            commentingPerson: { id: currentUser?.consultantId || 1 },
          },
        };
      },
      { concurrency: 20 }
    );

    // Bulk insert CRM notes in chunks of 1000
    const crmPayloads = dataPayloads.map((info) => info.crmPayload).filter(Boolean);
    for (let i = 0; i < crmPayloads.length; i += 1000) {
      const chunk = crmPayloads.slice(i, i + 1000);
      await this.dataSource.getRepository(CrmNoteEntity).insert(chunk);
    }

    let results = [];
    if (isLicenseConnected) {
      results = await BBPromise.map(
        dataPayloads,
        async (info) => {
          try {
            const result = await sendNoteToBullhorn(bhRestToken, corporateRestUrl, info.bullhornPayload);
            return result;
          } catch (error) {
            console.log('Error sending note to Bullhorn', error);

            return null;
          }
        },
        { concurrency: 20 }
      );
    }

    // sentIds
    return results.reduce((ids, item) => (item ? [...ids, item?.changedEntityId] : ids), []);
  }

  async createSequenceTasks({
    user,
    clientContacts,
    sequenceStep,
    sequence,
    jobLead,
    dueDate = new Date(),
  }: {
    user: UserEntity;
    clientContacts: any;
    sequenceStep: SequenceStepEntity;
    sequence?: SequenceEntity;
    jobLead?: any;
    dueDate?: Date;
  }) {
    const currentDate = new Date();
    const sequenceId = sequence?.id || sequenceStep.sequence?.id;
    const sequenceStepId = sequenceStep.id;
    if (!clientContacts?.length) {
      await this.sequenceActivityLogRepository.insert({
        type: SequenceActivityType.SKIPPED,
        sequence: { id: sequenceId },
        sequenceStep: { id: sequenceStepId },
        content: {
          type: SequenceStepType.TASK,
          displayText: 'No contacts were set, skip creating the task',
        },
        occurredAt: currentDate,
      });

      return [];
    }

    const tasks: any[] = [];
    clientContacts.forEach((recipient) => {
      const mergeTags = {
        sender: {
          name: user.fullName,
          email: user.email,
        },
        recipient: {
          ...recipient,
          firstName: recipient?.firstName?.trim() ? recipient?.firstName : recipient?.name,
          clientCorporation: { name: recipient?.clientCorporation?.name || recipient?.companyName },
          occupation: recipient?.occupation || recipient?.contactTitle,
        },
        job: {
          ...jobLead,
          company_name: sequence?.companyName,
          owner: {
            firstName: user.fullName.split(' ')[0] ?? '',
            fullName: user.fullName,
            email: user.email,
          },
        },
        sequence: {
          ...sequence,
        },
        currentSentDateTime: new Intl.DateTimeFormat('en-GB', {
          dateStyle: 'full',
          timeStyle: 'long',
        }).format(),
      };

      tasks.push({
        participant: recipient,
        content: getMergeTagsContent(sequenceStep.content, mergeTags),
        type: sequenceStep.subject,
        creatingType: SequenceTaskCreatingType.SEQUENCE,
        sequenceStep: {
          id: sequenceStep.id,
        },
        sequence: {
          id: sequence?.id || sequenceStep.sequence?.id,
        },
        dueDate,
        status: SequenceTaskStatusEnum.PENDING,
        groupId: sequence?.id || sequenceStep.sequence?.id, // sequence Id
        name: sequence?.name,
        userId: user.id,
      });
    });

    // Existing tasks
    const existingTasks = await this.sequenceTaskRepository
      .createQueryBuilder('sequenceTask')
      .select(['sequenceTask.id', 'sequenceTask.participant'])
      .where('sequenceTask.sequence_id = :sequenceId', { sequenceId })
      .andWhere('sequenceTask.sequence_step_id = :sequenceStepId', { sequenceStepId })
      .getMany();

    const existingTasksMap = new Map<string, SequenceTaskEntity>();
    existingTasks.forEach((task) => {
      const email = task.participant?.email;
      if (email) {
        existingTasksMap.set(email, task);
      }
    });

    // Step 3: Prepare the tasks for bulk update and bulk insert
    const tasksToUpdate: Partial<SequenceTaskEntity>[] = [];
    const tasksToInsert: Partial<SequenceTaskEntity>[] = [];

    tasks.forEach((task) => {
      const email = task.participant?.email;
      if (!email) return; // Skip tasks without an email

      const existingTask = existingTasksMap.get(email);

      if (existingTask) {
        // Prepare the task for update (set the id to target the existing record)
        tasksToUpdate.push({
          ...task,
          id: existingTask.id,
        });
      } else {
        // Prepare the task for insert
        tasksToInsert.push(task);
      }
    });

    if (tasksToUpdate.length > 0) {
      const chunkRecords = chunkArray(tasksToUpdate, 1000);

      await BBPromise.all(
        chunkRecords.map(async (items) => {
          await this.dataSource
            .createQueryBuilder()
            .insert()
            .into(SequenceTaskEntity)
            .values(items)
            .orUpdate(['type', 'content'], ['id'])
            .execute();

          return true;
        }),
        { concurrency: 1 }
      );
    }

    // Bulk Insert new tasks
    if (tasksToInsert.length > 0) {
      const chunkRecords = chunkArray(tasksToInsert, 1000);

      const results = await BBPromise.all(
        chunkRecords.map(async (items) => {
          const result = await this.sequenceTaskRepository.insert(items);
          await this.sequenceActivityLogRepository.insert(
            items.map((task) => ({
              type: SequenceActivityType.CREATED,
              sequence: { id: sequenceId },
              sequenceStep: { id: sequenceStepId },
              content: {
                type: SequenceStepType.TASK,
                recipient: task.participant,
                count: 1,
              },
              occurredAt: currentDate,
            }))
          );

          return result.identifiers.map(({ id }) => id);
        }),
        { concurrency: 1 }
      );

      return results.flat();
    }

    if (sequence?.name) {
      this.sequenceTaskRepository.update(
        {
          sequence: {
            id: sequence?.id,
          },
        },
        {
          name: sequence.name,
        }
      );
    }

    return existingTasks.map(({ id }) => id);
  }

  async createAllSequenceTasks(sequenceId: string) {
    const sequence = await this.sequenceRepository.findOne({
      where: { id: sequenceId },
      relations: ['user'],
    });
    const jobLead = this.getJobLead(sequence);

    const currentDate = new Date();
    const sequenceSteps = await this.sequenceStepRepository.find({
      where: { sequence: { id: sequence.id } },
      relations: ['sequenceInstances'],
      order: {
        stepIndex: 'ASC',
      },
    });

    let nextScheduled = 0;
    await sequenceSteps.reduce(async (prev, step) => {
      await prev;
      nextScheduled += step.delays.reduce((acc, item) => acc + item.delay * this.DELAY_UNIT_MULTIPLE[item.unit], 0);

      if (step.type !== SequenceStepType.TASK) {
        return;
      }

      const recipients = step.sequenceInstances.reduce((acc, seqIns) => [...acc, ...(seqIns.recipients || [])], []);

      await this.createSequenceTasks({
        jobLead,
        user: sequence.user,
        sequenceStep: step,
        sequence,
        dueDate: new Date(currentDate.getTime() + nextScheduled),
        clientContacts: removeDuplicatesByKey(recipients, (item: any) => item?.email?.toLowerCase(), 'id').filter(
          ({ id }) => id
        ),
      });

      await this.sequenceInstanceRepository.update(
        {
          id: In(step.sequenceInstances.map(({ id }) => id)),
        },
        {
          status: SequenceStepStatus.SENT,
          sentIds: [],
          executedAt: currentDate,
        }
      );

      return;
    }, Promise.resolve());
  }

  async send(
    user: IJwtPayload | UserEntity | ISimpleUser,
    sendEmailDto:
      | SendEmailDto
      | {
          emailSeqId: string;
          jobBoardId?: string;
          externalJobId?: string;
          createdFrom?: string[];
        },
    dataFindEssentialInfo: ISendMail = null
  ) {
    sgMail.setApiKey(SendGridConfig.apiKeyEmailExplorerCandiate);

    try {
      const { jobBoardId, externalJobId, emailSeqId, createdFrom } = sendEmailDto;

      const { currentUser, jobId, externalId, existedMails, jobLead } = dataFindEssentialInfo?.existedMails?.length
        ? dataFindEssentialInfo
        : await this.findEssentialInfo(user, emailSeqId, jobBoardId, externalJobId);
      let isRegistered = true;

      if (!currentUser.grantId && !currentUser.grantUnipileId) {
        isRegistered = false;
        throw new BadRequestException(
          `Your email has not been granted permission to send sequence email. Please select Mailbox tab, link your email then try again`
        );
      }

      if (!existedMails?.length) {
        throw new BadRequestException('There are no steps found. Please double check!');
      }

      const existedPrimaryMail = existedMails.find((mail) => mail.stepIndex === 0);

      if (
        existedPrimaryMail?.stepIndex === 0 &&
        existedPrimaryMail.sequenceInstances?.some(({ status }) => status === SequenceStepStatus.SENT)
      ) {
        throw new BadRequestException(`Email already sent`);
      }

      let createFromStr = null;
      if (createdFrom && createdFrom?.length > 0) {
        createFromStr = createdFrom.join(', ');
      }

      const stepLinkedInInvitation = existedMails.find((mail) => {
        if (mail.type !== SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
          return false;
        }
        const actionLinkedIn = safeParseJSON(mail.content);

        return !!actionLinkedIn.find(({ type }) => type === 'INVITATION');
      });

      const currentDate = new Date();
      let nextScheduled = 0;
      if (!existedPrimaryMail?.delays?.length) {
        console.log(`[SEQUENCE] Send first mail ${emailSeqId}`);
        await this.sendFirstMail({
          currentUser,
          jobLead,
          existedPrimaryMail,
          currentDate,
          // Is last steps or next steps is all TASK
          isLastStep:
            existedMails.length === 1 ||
            existedMails.every((step, i) => i === 0 || step.type === SequenceStepType.TASK),
        });
        existedMails.shift();
      } else {
        console.log(`[SEQUENCE] Delay first mail ${emailSeqId}`);
      }

      if (existedMails.length > 0) {
        const updatedFollowUpMail = existedMails.flatMap((mail) => {
          const delayTime = mail.delays.reduce(
            (acc, item) => acc + item.delay * this.DELAY_UNIT_MULTIPLE[item.unit],
            0
          );
          nextScheduled += Number.isNaN(delayTime) ? 0 : delayTime;

          return mail.sequenceInstances.map((seqInstance) => {
            seqInstance.scheduledAt = new Date(currentDate.getTime() + nextScheduled);

            return seqInstance;
          });
        });
        await Promise.all(
          updatedFollowUpMail.map(
            (mail) => mail && this.sequenceInstanceRepository.update(mail.id, { scheduledAt: mail.scheduledAt })
          )
        );
      }

      await this.sequenceRepository.update(emailSeqId, { isTriggered: true });
      await this.createAllSequenceTasks(emailSeqId);
      return this.formatOutputData({ key: 'SEND_EMAIL' }, { data: { isRegistered } });
    } catch (error) {
      console.error('Error in send', error);
      console.error(error.response?.body);
      if (error.code === HttpStatus.BAD_REQUEST) {
        throw new BadRequestException('Invalid email address');
      } else if (error.message.includes('fk_signature_email')) {
        throw new NotFoundException('Signature can not be found');
      }
      throw error;
    }
  }

  async sendPersonalMail(user: IJwtPayload, sendPersonMailPayload: SendPersonalMailDto) {
    const { recipients, subject, content, replyTo, replyToMessageId } = sendPersonMailPayload;

    const currentUser = await this.userRepository.findOne({ where: { id: user.id } });

    if (!currentUser.grantMailboxId) {
      throw new PreconditionFailedException('Not have permission');
    }

    const { data: mailInfo } = await this.nylas.grants.find({
      grantId: currentUser.grantMailboxId,
    });

    const payload = {
      identifier: currentUser.grantMailboxId,
      requestBody: {
        from: [
          {
            email: mailInfo.email,
            name: currentUser.fullName,
          },
        ],
        to: recipients,
        body: content,
        subject,
      },
    };

    set(payload, 'requestBody.to', recipients);

    if (replyTo) {
      set(payload, 'requestBody.replyTo', recipients);
      set(payload, 'requestBody.replyToMessageId', replyToMessageId);
    }

    const data = await this.nylas.messages.send(payload);

    return this.formatOutputData({ key: 'SEND_PERSONAL_EMAIL' }, { data: data });
  }

  async upsertSequenceEmail(
    user: ISimpleUser,
    upsertEmailDto: UpsertEmailDto | UpsertEmailDraftDto | UpsertEmailChildDto,
    { parent = {}, isDraft = false }: { isDraft?: boolean; parent?: any } = {}
  ) {
    try {
      const {
        mails,
        externalJobId,
        jobBoardId,
        crmLeadId,
        name,
        emailSeqId,
        rawSequence,
        createdFrom,
        placeHolders,
        stopRules,
        companyId,
        jobBoardName,
        companyName,
        country,
        scheduleInformation = {},
        sequenceTemplateId,
        timeZone,
        participants,
        skippedEmails,
        childSequence,
      } = upsertEmailDto as any;

      const { currentUser, jobId, externalId, existedMails, emailSeq } = await this.findEssentialInfo(
        user,
        emailSeqId,
        jobBoardId,
        externalJobId
      );

      if (childSequence && emailSeq) {
        // Only handle child sequence if parent sequence exists
        const existChildSeq = await this.sequenceRepository.findOneBy({ parentId: emailSeqId });
        const sequenceData: UpsertEmailDto = {
          ...childSequence,
          emailSeqId: existChildSeq?.id,
          externalJobId: emailSeq.externalJobId,
          jobBoardId: emailSeq.jobBoardId,
          crmLeadId: emailSeq.crmLeadId,
          name: emailSeq.name,
          createdFrom: emailSeq.createdFrom?.split(', '),
          companyId: emailSeq.companyId,
          companyName: emailSeq.companyName,
          country: emailSeq.country,
        };

        if (!existChildSeq && (emailSeq?.status !== SequenceStatus.STOP || !emailSeq.isMarkedAsCompleted)) {
          throw new BadRequestException('Only completed sequence can create child sequence');
        }

        return this.upsertSequenceEmail(user, sequenceData, { parent: emailSeq });
      }

      const parentId = parent?.id;
      let createFromStr = null;
      if (createdFrom && createdFrom?.length > 0) {
        createFromStr = createdFrom.join(', ');
      }

      if (!isDraft) {
        // Check recipients
        if (
          !participants?.recipients?.length &&
          !participants?.contactListIds?.length &&
          !participants?.hotlistIds?.length &&
          !participants?.shortListIds?.length
        ) {
          throw new BadRequestException('Please add at least one recipient, hotlist or contact list');
        }

        if (
          mails?.some(
            (mail) => [SequenceStepType.TASK].includes(mail.type) && mail.recipients && !mail.recipients.length
          )
        ) {
          throw new BadRequestException('Task steps must have at least one recipient');
        }

        // Check steps & subject
        if (
          !mails.length ||
          mails?.some((mail) => mail.type === SequenceStepType.EMAIL && (!mail.content || !mail.subject))
        ) {
          throw new BadRequestException('Please fill in email subject & content fields');
        }
      }

      // Check if no email was created, create them
      let seqId = null;
      if (!emailSeq) {
        try {
          const { seqId: createdSeqId } = await this.createSequenceEmail(
            mails,
            name,
            currentUser,
            externalJobId ? `bullhorn-${externalJobId}` : externalId ?? undefined,
            jobBoardId ?? jobId ?? undefined,
            crmLeadId,
            rawSequence,
            placeHolders,
            createFromStr ?? SequenceEmailCreatedFrom.VACANCY,
            stopRules,
            companyId,
            jobBoardName,
            companyName,
            country,
            scheduleInformation,
            sequenceTemplateId,
            timeZone,
            participants,
            skippedEmails,
            isDraft,
            parentId
          );

          seqId = createdSeqId;
        } catch (createErr) {
          if (
            createErr.driverError?.code === '23505' &&
            createErr.driverError?.table?.includes('sequence_steps') &&
            createErr.driverError?.constraint
          ) {
            throw new Error('Your request data is conflicting. Please refresh and try again!');
          }

          throw createErr;
        }
      } else {
        if (isDraft && emailSeq.status !== SequenceStatus.DRAFT) {
          throw new Error('Only draft sequence can not be updated as draft');
        }
        if (emailSeq.parentId && !parentId) {
          throw new Error('Cannot upsert child sequence directly');
        }
        const updatedSeq = await this.updateSequenceEmail({
          existedMails,
          mails,
          name,
          currentUser,
          jobBoardId: jobBoardId ?? jobId ?? undefined,
          externalJobId: externalJobId ? `bullhorn-${externalJobId}` : externalId ?? undefined,
          crmLeadId,
          rawSequence,
          emailSeq,
          placeHolders,
          createdFrom: createFromStr ?? SequenceEmailCreatedFrom.VACANCY,
          stopRules,
          companyId,
          companyName,
          country,
          scheduleInformation,
          timeZone,
          participants,
          skippedEmails,
          isDraft,
          parentId,
        });

        seqId = updatedSeq.seqId;
      }

      const existedPrimaryMail = existedMails.find((mail) => mail.stepIndex === 0);
      const isSent =
        existedPrimaryMail?.stepIndex === 0 &&
        existedPrimaryMail.sequenceInstances?.some(({ status }) => status === SequenceStepStatus.SENT);

      const isTriggeredNow = scheduleInformation.isTriggeredNow;
      if (
        !isDraft &&
        emailSeq?.status !== SequenceStatus.DRAFT &&
        isTriggeredNow &&
        !isSent &&
        !mails[0]?.delays?.length
      ) {
        const sendEmailData = {
          emailSeqId: seqId,
          // Keep jobBoardId as request payload to avoid issue deleted OS job
          jobBoardId: jobBoardId ?? undefined,
          externalJobId: externalJobId ? `bullhorn-${externalJobId}` : externalId ?? undefined,
        };
        if (!sendEmailData.externalJobId && jobId) {
          sendEmailData.jobBoardId = jobId;
        }

        await this.send({ id: user.id }, sendEmailData);
      }

      return this.formatOutputData(
        { key: 'CREATE_SEQUENCE_EMAIL' },
        { data: parentId ? { emailSeqId: parentId, childSequenceId: seqId } : { emailSeqId: seqId } }
      );
    } catch (error) {
      this.logger.error(error.message);

      if (error.message.includes('fk_signature_email')) {
        throw new NotFoundException('Signature can not be found');
      }

      throw error;
    }
  }

  async upsertFreeSequenceEmail(user: IJwtPayload, upsertEmailDto: UpsertEmailDto) {
    try {
      const { mails, emailSeqId, name, createdFrom, participants, stopRules, timeZone } = upsertEmailDto;
      const { recipients = [] } = participants as ParticipantsDto;
      const currentUser = await this.userRepository.findOne({ where: { id: user.id } });

      if (!emailSeqId) {
        await this.createSequenceEmail(mails, name, currentUser, recipients);
        return this.formatOutputData({ key: 'CREATE_FREE_SEQUENCE_EMAIL' }, { data: {} });
      }

      let createFromStr = null;
      if (createdFrom && createdFrom?.length > 0) {
        createFromStr = createdFrom.join(', ');
      }

      const existedMails = await this.sequenceStepRepository.find({
        where: { sequence: { id: emailSeqId } },
        relations: { sequence: true },
      });

      await this.updateSequenceEmail({
        existedMails,
        mails,
        name,
        currentUser,
        createdFrom: createFromStr,
        participants,
        stopRules,
        timeZone,
      });
      return this.formatOutputData({ key: 'UPDATE_FREE_SEQUENCE_EMAIL' }, { data: {} });
    } catch (error) {
      this.logger.error(error.message);

      throw error;
    }
  }

  async getChildSequenceIds(sequence: any) {
    if (!sequence?.id) {
      return [];
    }

    const childSequence: any = await this.sequenceRepository.findOne({
      where: { parentId: sequence.id },
      order: { createdAt: 'DESC' },
    });
    if (!childSequence) {
      return [];
    }

    const ids = await this.getChildSequenceIds(childSequence);

    return [childSequence.id, ...ids];
  }

  // need to adjust upsert seq(create,edit, stop/live), send, webhook(reply)
  async getEmailActivityLog(sequenceId: string, queryParams: SequenceActivityQueryDto) {
    const { page = 1, limit = 1000, activityType, stepId, stepType, searchText } = queryParams;
    const ids = await this.getChildSequenceIds({ id: sequenceId });
    const qb = this.sequenceActivityLogRepository
      .createQueryBuilder('log')
      .leftJoinAndSelect('log.sequence', 'sequence')
      .leftJoinAndSelect('log.sequenceStep', 'sequence_step');

    // Build conditions for (sequence.id IN (...) OR sequence.parentId = ...)
    qb.where(
      new Brackets((qb1) => {
        qb1.where('sequence.id IN (:...ids)', { ids: [sequenceId, ...ids] })
          .orWhere('sequence.parentId = :sequenceId', { sequenceId });
      })
    );

    // Filter by type (either exact or not BOUNCE)
    if (activityType) {
      qb.andWhere('log.type = :activityType', { activityType });
    } else {
      qb.andWhere('log.type IS DISTINCT FROM :bounceType', {
        bounceType: SequenceActivityType.BOUNCE,
      });
    }

    // SequenceStep filters (stepId, stepType, searchText in step.content)
    if (stepId) {
      qb.andWhere('sequence_step.id = :stepId', { stepId });
    }

    if (stepType) {
      qb.andWhere('sequence_step.type = :stepType', { stepType });
    }

    if (searchText) {
      qb.andWhere(
        new Brackets((qb1) => {
          // Search in sequence step content
          qb1.where('sequence_step.content ILIKE :searchText', {
            searchText: `%${searchText}%`,
          })
            // Search in activity log content
            .orWhere('log.content::text ILIKE :searchText', {
              searchText: `%${searchText}%`,
            });
        }),
      );
    }

    // Pagination and sorting
    qb.orderBy('log.occurredAt', 'ASC')
      .take(limit)
      .skip((page - 1) * limit);

    const [data, count] = await qb.getManyAndCount();

    const result = data.map((item) => ({ ...item, isChildren: ids.includes(item.sequence?.id) }));

    return this.formatOutputData({ key: 'GET_EMAIL_LOGS_ACTIVITY' }, {
      data: {
        items: result,
        total: count,
        currentPage: page,
        pageSize: limit,
      }
    });
  }

  async getMailBox(userId: string, pageToken: string, limit: string) {
    const currentUser = await this.userRepository.findOne({ where: { id: userId } });
    if (!currentUser.grantMailboxId) {
      throw new NotFoundException('Not have permission');
    }

    const mailBox = await this.nylas.threads.list({
      identifier: currentUser.grantMailboxId,
      queryParams: {
        limit: parseInt(limit),
        pageToken: pageToken ?? '',
        in: ['INBOX'],
      },
    });

    return this.formatOutputData({ key: 'GET_MAILBOX' }, { data: { ...mailBox } });
  }

  async getMailByThread(user: IJwtPayload, messageIds: string[]) {
    const currentUser = await this.userRepository.findOne({ where: { id: user.id } });

    if (!currentUser.grantMailboxId) {
      throw new NotFoundException('Not have permission');
    }

    const messages = await Promise.all(
      messageIds.map((messageId) =>
        this.nylas.messages.find({ identifier: currentUser.grantMailboxId, messageId: messageId })
      )
    );

    return this.formatOutputData({ key: 'GET_THREAD_MAIL' }, { data: { messages } });
  }

  // TODO: manage in a transaction
  async generateEmail(user: IJwtPayload, generateEmailDto: EmailGenerationDto) {
    try {
      const { jobBoardId, recipients } = generateEmailDto;

      const jobBoard = await this.jobBoardRepository.findOne({ where: { job_id: jobBoardId } });
      if (!jobBoard) {
        throw new NotFoundException(`Job board ${jobBoardId} not found`);
      }

      const keyPoints = await this.openAIService.generateKeyPointFromJobDescription(jobBoard.description);

      const emailSequenceEntity = new SequenceEntity();
      // leave it hard code for now because we don't have a UI to create a sequence yet
      // emailSequenceEntity.name = 'seuqence_name';
      // emailSequenceEntity.jobBoard = jobBoard;
      await this.sequenceRepository.save(emailSequenceEntity);

      const emailEntities = [];
      const sequenceInstances = [];
      const currentUser = await this.userRepository.findOne({ where: { id: user.id } });
      for (const key in keyPoints) {
        const seqStepEntity = new SequenceStepEntity();
        seqStepEntity.subject = `${jobBoard.jobtitle} - Immediately Available for interviews`;
        seqStepEntity.sequence = emailSequenceEntity;
        seqStepEntity.content = this.buildCandidateExplorationEmailContent(
          jobBoard.jobtitle,
          jobBoard.joblocationcity.split(',')[0],
          jobBoard.company,
          keyPoints[0] as any, // change function response
          currentUser.username || '[Your user name]',
          currentUser.email || '[Your email]',
          '[Your phone number]'
        )
          .replace(/\n/g, '')
          .trim();
        emailEntities.push(seqStepEntity);
        const seqInstance = this.sequenceInstanceRepository.create({
          sequence: emailSequenceEntity,
          sequenceStep: seqStepEntity,
          recipients,
          user: currentUser,
          version: 0,
        });
        sequenceInstances.push(seqInstance);
      }

      const data = emailEntities.map((email) => ({
        subject: email.subject,
        content: email.content,
      }));

      const [sequenceStepResults] = await this.sequenceStepRepository.save(emailEntities);

      sequenceStepResults.identifiers.forEach((step, index) => {
        sequenceInstances[index].sequenceStep = step;
      });
      await this.sequenceInstanceRepository.insert(sequenceInstances);

      return this.formatOutputData({ key: 'GET_ALL_EMAILS' }, { data });
    } catch (error) {
      this.logger.error(error);
      return await this.throwCommonMessage('GENERATE_EMAIL', error);
    }
  }

  async generateSubject(jobBoardId: string) {
    try {
      const jobBoard = await this.elasticsearchService.getById(JOBS_INDEX, jobBoardId);
      if (!jobBoard) {
        throw new NotFoundException(`Job board ${jobBoardId} not found`);
      }

      const generatedSubject = await this.openAIService.generatedSubject(jobBoard.jobtitle);

      return this.formatOutputData(
        { key: 'GET_SUBJECT_EMAIL' },
        { data: { subject: generatedSubject.split('"').join('') } }
      );
    } catch (error) {
      this.logger.error(error);
      return await this.throwCommonMessage('GENERATE_SUBJECT_EMAIL', error);
    }
  }

  async generateContent(user: IJwtPayload, generateEmailDto: EmailGenerationFromUnfinished) {
    try {
      const { jobBoardId, unfinishedContent } = generateEmailDto;

      const jobBoard = await this.elasticsearchService.getById(JOBS_INDEX, jobBoardId);
      if (!jobBoard) {
        throw new NotFoundException(`Job board ${jobBoardId} not found`);
      }

      const keyPoint = await this.openAIService.generateKeyPointFromUnfinishedContent(
        jobBoard.description,
        unfinishedContent,
        jobBoard.joblocationcity.split(',')[0],
        jobBoard.jobtitle
      );

      const currentUser = await this.userRepository.findOne({ where: { id: user.id } });

      const content = this.buildCandidateExplorationEmailContent(
        jobBoard.jobtitle,
        jobBoard.joblocationcity.split(',')[0],
        jobBoard.company,
        keyPoint,
        currentUser.username || '[Your user name]',
        currentUser.email || '[Your email]',
        '[Your phone number]'
      )
        .replace(/\n/g, '')
        .trim();

      return this.formatOutputData({ key: 'GET_CONTENT_EMAILS' }, { data: { content } });
    } catch (error) {
      this.logger.error(error);
      return await this.throwCommonMessage('GENERATE_CONTENT_EMAIL', error);
    }
  }

  async generateContentByChildContent(user: IJwtPayload, generateEmailDto: EmailGenerationFromChild) {
    try {
      const keyPoint = await this.openAIService.generateKeyPointFromUnfinishedContent(
        generateEmailDto?.content,
        null,
        generateEmailDto?.address,
        generateEmailDto?.title
      );

      const currentUser = await this.userRepository.findOne({ where: { id: user.id } });

      const content = this.buildCandidateExplorationEmailContent(
        generateEmailDto?.title,
        generateEmailDto?.address,
        generateEmailDto?.company,
        keyPoint,
        currentUser.username || '[Your user name]',
        currentUser.email || '[Your email]',
        '[Your phone number]'
      )
        .replace(/\n/g, '')
        .trim();

      // const generatedSubject = await this.openAIService.generatedSubject(generateEmailDto.title);
      return this.formatOutputData({ key: 'GET_EMAILS_BY_CHILD_CONTENT' }, { data: { content: content } });
    } catch (error) {
      this.logger.error(error);
      return await this.throwCommonMessage('GET_EMAILS_BY_CHILD_CONTENT', error);
    }
  }

  async generateContentByChildSubject(user: IJwtPayload, generateEmailDto: EmailGenerationFromChild) {
    try {
      const generatedSubject = await this.openAIService.generatedSubject(generateEmailDto.title);
      return this.formatOutputData(
        { key: 'GET_EMAILS_BY_CHILD_SUBJECT' },
        { data: { subject: generatedSubject.split('"').join('') } }
      );
    } catch (error) {
      this.logger.error(error);
      return await this.throwCommonMessage('GET_EMAILS_BY_CHILD_SUBJECT', error);
    }
  }

  async generateFreeSubject(generateEmailDto: FreeEmailGenerationDTO) {
    try {
      const generatedSubject = await this.openAIService.generateFreeSubject(generateEmailDto);
      return this.formatOutputData({ key: 'GET_FREE_SUBJECT' }, { data: { subject: generatedSubject } });
    } catch (error) {
      this.logger.error(error);
      return await this.throwCommonMessage('GET_FREE_SUBJECT', error);
    }
  }

  async generateFreeContent(user: IJwtPayload, generateEmailDto: FreeEmailGenerationDTO) {
    try {
      const currentUser = await this.userRepository.findOne({ where: { id: user.id } });
      const generatedFreeContent = await this.openAIService.generateFreeContent(generateEmailDto, currentUser.fullName);
      return this.formatOutputData(
        { key: 'GET_FREE_CONTENT' },
        {
          data: {
            content: generatedFreeContent,
          },
        }
      );
    } catch (error) {
      this.logger.error(error);
      return await this.throwCommonMessage('GET_FREE_CONTENT', error);
    }
  }

  // webhook
  async checkIncomingEmail(req) {
    try {
      const rawMessage = await simpleParser(req.body.email);

      const rawMessageId = rawMessage.inReplyTo;
      if (!rawMessageId) {
        return;
      }

      const regex = /<([^@>]+)@/;
      const messageId = rawMessageId.match(regex)[1];
      const { envelope } = req.body;
      const { from: sender } = safeParseJSON(envelope);
      const mail = await this.sequenceInstanceRepository.findOne({
        where: {
          sentIds: Raw((sentIdsCol) => `${sentIdsCol} @> :sentId`, { sentId: JSON.stringify([messageId]) }),
          status: SequenceStepStatus.SENT,
        },
        relations: {
          user: true,
          sequence: true,
          sequenceStep: true,
        },
      });

      if (!mail) {
        return;
      }

      await Promise.all([
        this.sequenceInstanceRepository.update(mail.id, {
          repliers: (mail?.repliers ?? []).push(sender) as any,
          status: SequenceStepStatus.REPLIED,
        }),
        this.sequenceInstanceRepository.update(
          {
            user: { id: mail.user.id },
            sequence: { id: mail.sequence.id },
            id: Not(mail.id),
            status: SequenceStepStatus.PENDING,
          },
          {
            status: SequenceStepStatus.STOP,
          }
        ),
        this.sequenceRepository
          .createQueryBuilder()
          .update(SequenceEntity)
          .set({ repliedCount: () => `"replied_count" + 1` })
          .where('id = :id', { id: mail.sequence.id })
          .execute(),
      ]);

      return true;
    } catch (error) {
      this.logger.error(error.message);
      throw error;
    }
  }

  private async updateFollowUpEmails(data: any, userId: string, seqId: string, instanceVersion?: number) {
    await this.sequenceInstanceRepository.update(
      {
        user: { id: userId },
        sequence: { id: seqId },
        status: SequenceStepStatus.PENDING,
        ...(typeof instanceVersion === 'number' ? { version: instanceVersion } : {}),
      },
      data
    );
  }

  async checkEmailEvent(events) {
    return Promise.all(
      events.map(async (eventBody) => {
        const {
          'smtp-id': rawId,
          sg_message_id,
          event,
          timestamp,
          type,
          email: recipient,
          reason,
          attempt = 0,
        } = eventBody;
        let rawMessageId = rawId;
        if (!rawId && sg_message_id) {
          [rawMessageId] = sg_message_id.split('.');
        }

        if (!rawMessageId || attempt > 0 || !['open', 'click', 'bounce', 'dropped'].includes(event)) {
          return this.formatOutputData(
            { key: 'SG_WEBHOOK_EVENT' },
            {
              data: {
                skip: true,
              },
            }
          );
        }

        const regex = /<([^@>]+)@/;
        const messageId = rawMessageId.match(regex)?.[1] || rawMessageId;

        await this.webhookSendgridEventQueue.add(BULL_JOB_NAMES.WEBHOOK_SENDGRID_EVENT, eventBody, {
          jobId: `${messageId}#${event}`,
          attempts: 1,
        });

        return this.formatOutputData(
          { key: 'SG_WEBHOOK_EVENT' },
          {
            data: {
              skip: false,
            },
          }
        );
      })
    );
  }

  // nylas function
  async saveGrantId(code, state, isMailbox: boolean = false) {
    if (!code) {
      throw new BadRequestException('No authorization code returned');
    }

    const codeExchangePayload = {
      clientSecret: NYLAS_CONFIG.apiKey,
      clientId: NYLAS_CONFIG.clientId,
      redirectUri: isMailbox ? NYLAS_CONFIG.callbackMailboxUri : NYLAS_CONFIG.callbackUri,
      code,
    };

    try {
      const response = await this.nylas.auth.exchangeCodeForToken(codeExchangePayload);
      const { grantId, email: grantEmail } = response;

      const user = await this.userRepository.findOne({
        where: {
          id: state,
        },
      });

      if (!isMailbox) {
        // grant sequence
        if (user.grantId) {
          return this.formatOutputData(
            { key: 'GRANT_SEQUENCE_PERMISSION' },
            { data: { message: 'account was already registered' } }
          );
        }

        await this.userRepository.update(user.id, { grantId, grantEmail });

        return this.formatOutputData({ key: 'GRANT_SEQUENCE_PERMISSION' }, { data: {} });
      }

      // grant mailbox
      if (user.grantMailboxId) {
        return this.formatOutputData(
          { key: 'GRANT_MAILBOX_PERMISSION' },
          { data: { message: 'account was already registered' } }
        );
      }

      await this.userRepository.update(user.id, { grantMailboxId: grantId });

      return this.formatOutputData({ key: 'GRANT_MAILBOX_PERMISSION' }, { data: {} });
    } catch (error) {
      this.logger.error(error.message);
      throw error;
    }
  }

  async authUrl(id: string, isMailbox: boolean = false) {
    const authUrl = this.nylas.auth.urlForOAuth2({
      clientId: NYLAS_CONFIG.clientId,
      redirectUri: isMailbox ? NYLAS_CONFIG.callbackMailboxUri : NYLAS_CONFIG.callbackUri,
      state: id,
    });

    return authUrl;
  }

  async sendGrantLink(userId: string) {
    try {
      const { fromEmail } = SendGridConfig;
      const currentUser = await this.userRepository.findOne({
        where: {
          id: userId,
        },
      });

      const msg = {
        to: currentUser.email,
        from: {
          name: 'Zileo',
          email: fromEmail,
        },
        subject: 'GRANT SEQUENCE PERMISSION',
        html: `Please use this link to grant permission: <a clicktracking='off' href='${APP_CONFIG.API_URL}/emails/auth-via-email?id=${currentUser.id}'>Grant Permission</a>`,
      };
      await sgMail.send(msg);

      return this.formatOutputData({ key: 'SEND_INVITATION_EMAIL_BY_EMAILS' }, { data: {} });
    } catch (error) {
      this.logger.error(error.message);
      throw error;
    }
  }

  async destroyGrantId(userId: string, grantType = 'grantMailboxId') {
    const currentUser = await this.userRepository.findOne({ where: { id: userId } });
    await this.deleteLinkedGrantId(currentUser, grantType);

    return this.formatOutputData({ key: 'DESTROY_GRANT_MAILBOX' }, { data: { type: grantType } });
  }

  async updateThread(user: IJwtPayload, threadId: string, updatedData: UpdateThreadMailDto) {
    const currentUser = await this.userRepository.findOne({ where: { id: user.id } });

    if (!currentUser.grantId) {
      throw new PreconditionFailedException('Not have permission');
    }

    await this.nylas.threads.update({
      identifier: currentUser.grantMailboxId,
      threadId,
      requestBody: updatedData,
    });

    return this.formatOutputData({ key: 'UPDATE_THREAD_MAILBOX' }, { data: {} });
  }

  async deleteThreads(user: IJwtPayload, threadIds: string[]) {
    const currentUser = await this.userRepository.findOne({ where: { id: user.id } });

    if (!currentUser.grantId) {
      throw new PreconditionFailedException('Not have permission');
    }
    await Promise.all(
      threadIds.map((threadId) =>
        this.nylas.threads.destroy({
          identifier: currentUser.grantMailboxId,
          threadId,
        })
      )
    );

    return this.formatOutputData({ key: 'DELETE_THREAD_MAILBOX' }, { data: {} });
  }

  // private function
  private buildCandidateExplorationEmailContent(
    job_position: string,
    job_location_city: string,
    company_name: string,
    keyPoint: string,
    username: string,
    email: string,
    phone: string
  ): string {
    return `
        <div>
          ${keyPoint}

        <p>This candidate is actively interviewing in ${
          job_location_city ? job_location_city : company_name
        } and looks potentially very strong for the position you are actively hiring for.</p>

        <p>What would be the best time to discuss this candidate for the ${job_position} role?</p>

        <p>Best Regards,</p>
      </div>
    `;
  }

  private async createSequenceEmail(
    mails,
    name,
    currentUser,
    externalJobId = null,
    jobBoardId = null,
    crmLeadId = null,
    rawSequence = null,
    placeholders = [],
    createdFrom = null,
    stopRules = [],
    companyId = null,
    jobBoardName = null,
    companyName = null,
    country = null,
    scheduleInformation = null,
    sequenceTemplateId = null,
    timeZone = null,
    participants = {},
    skippedEmails = [],
    isDraft = false,
    parentId = null
  ) {
    // Init queryRunner
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let {
        recipients = [],
        hotlistIds = [],
        contactListIds = [],
        shortListIds = [],
      } = participants as ParticipantsDto;

      if (!mails || !mails.length) {
        mails = [];
      }
      if (!recipients || !recipients.length) {
        recipients = [];
      }

      let sequenceName = name || '';

      const pendingCount = mails.filter((mail) => mail.type === SequenceStepType.EMAIL).length * recipients.length;

      const sequenceEmailEntity = this.sequenceRepository.create({
        name: sequenceName,
        status: isDraft ? SequenceStatus.DRAFT : SequenceStatus.LIVE,
        stepCount: mails.length,
        pendingCount,
        user: currentUser,
        rawSequence,
        externalJobId,
        jobBoardId,
        crmLeadId,
        createdFrom: createdFrom ?? SequenceEmailCreatedFrom.VACANCY,
        recipients: recipients?.map((item: { email: string }) => item?.email).join(', '),
        contactNames: recipients?.map((item: { name: string }) => item?.name).join(', '),
        companyName,
        country,
        companyId,
        scheduleInformation,
        sequenceTemplateId,
        timeZone,
        stopRules,
        participants,
        skippedEmails,
        parentId,
      });

      // Use queryRunner.manager instead of repository
      const createdSequence = await queryRunner.manager.save(SequenceEntity, sequenceEmailEntity);
      await queryRunner.manager.update(
        SequenceEntity,
        { id: createdSequence.id },
        {
          triggerAt: () => `'${getSequenceTriggerAt(scheduleInformation)})'::timestamptz`,
          recreatedAt: createdSequence.createdAt,
        }
      );

      const { sequenceSteps, sequenceInstances } = mails.reduce(
        (acc, mail, index) => {
          const seqStep = this.sequenceStepRepository.create({
            type: mail.type,
            subject: mail.subject,
            content: mail.content,
            name: mail?.name,
            // status: SequenceStepStatus.PENDING,
            delays: mail.delays,
            // recipients,
            stepIndex: index,
            user: currentUser,
            externalJobId,
            jobBoardId,
            crmLeadId,
            sequence: sequenceEmailEntity,
            userSignature: { id: mail?.signatureId },
            // tearsheetId,
            placeholders,
            stopRules,
            // contactListId,
            threadId: mail?.threadId,
            id: mail?.id || mail?.key,
            attachments: mail?.fileList || [],
          });
          acc.sequenceSteps.push(seqStep);

          const stepInsParticipants = {
            recipients,
            hotlistIds,
            contactListIds,
            shortListIds,
          };
          if (mail.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
            stepInsParticipants.recipients = mail.recipients || stepInsParticipants.recipients;
            stepInsParticipants.hotlistIds = mail.hotlistIds || stepInsParticipants.hotlistIds;
            stepInsParticipants.contactListIds = mail.contactListIds || stepInsParticipants.contactListIds;
            stepInsParticipants.shortListIds = mail.shortListIds || stepInsParticipants.shortListIds;
          } else if (mail.type === SequenceStepType.TASK) {
            stepInsParticipants.recipients = mail.recipients || stepInsParticipants.recipients;
          }

          const seqInstance = this.sequenceInstanceRepository.create({
            sequence: sequenceEmailEntity,
            sequenceStep: seqStep,
            user: currentUser,
            version: 0,
            ...stepInsParticipants,
          });
          acc.sequenceInstances.push(seqInstance);

          return acc;
        },
        {
          sequenceSteps: [],
          sequenceInstances: [],
        }
      );

      const emailActivityLogEntity = this.sequenceActivityLogRepository.create({
        type: SequenceActivityType.CREATED,
        sequence: { id: sequenceEmailEntity.id },
        occurredAt: new Date().toISOString(),
      });

      const [sequenceStepResults] = await Promise.all([
        queryRunner.manager.insert(SequenceStepEntity, sequenceSteps),
        queryRunner.manager.insert(SequenceActivityLogEntity, emailActivityLogEntity),
      ]);

      sequenceStepResults.identifiers.forEach((step, index) => {
        sequenceInstances[index].sequenceStep = step;
      });
      await queryRunner.manager.insert(SequenceInstanceEntity, sequenceInstances);

      if (!isDraft) {
        this.sendEmailSequenceCreated(currentUser, createdSequence);
      }

      if (parentId) {
        const rootSequenceId = await this.getSequenceAncestorsIds(sequenceEmailEntity.id, true);
        await queryRunner.manager.update(
          SequenceEntity,
          {
            id: rootSequenceId,
            parentId: IsNull(),
          },
          {
            status: SequenceStatus.LIVE,
            recreatedAt: new Date(),
          }
        );
      }

      // Commit transaction
      await queryRunner.commitTransaction();

      return { seqId: sequenceEmailEntity.id };
    } catch (error) {
      // Rollback
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release queryRunner
      await queryRunner.release();
    }
  }

  private detectChanges<T>(oldArray: T[], newArray: T[], key?: keyof T): { added: T[]; removed: T[]; updated: T[] } {
    if (key) {
      const oldMap = new Map(oldArray.map((item) => [(item as any)[key], item]));
      const newMap = new Map(newArray.map((item) => [(item as any)[key], item]));

      const added = newArray.filter((item) => !oldMap.has((item as any)[key]));
      const removed = oldArray.filter((item) => !newMap.has((item as any)[key]));
      const updated = newArray.filter((item) => oldMap.has((item as any)[key]));

      return { added, removed, updated };
    } else {
      const oldSet = new Set(oldArray);
      const newSet = new Set(newArray);

      const added = newArray.filter((item) => !oldSet.has(item));
      const removed = oldArray.filter((item) => !newSet.has(item));
      const updated = newArray.filter((item) => oldSet.has(item));

      return { added, removed, updated };
    }
  }

  //TODO: update interface here
  private async updateSequenceEmail({
    existedMails,
    mails,
    name,
    currentUser,
    externalJobId = undefined,
    jobBoardId = undefined,
    crmLeadId = null,
    rawSequence = undefined,
    emailSeq = undefined,
    placeHolders,
    createdFrom = undefined,
    stopRules = [],
    companyId = undefined,
    companyName = undefined,
    country = null,
    scheduleInformation = null,
    timeZone = null,
    participants = {},
    skippedEmails = [],
    isDraft = false,
    parentId = null,
  }: {
    existedMails: any;
    mails: any;
    name: any;
    currentUser: any;
    externalJobId?: any;
    jobBoardId?: any;
    crmLeadId?: string;
    rawSequence?: any;
    emailSeq?: any;
    placeHolders?: any;
    createdFrom?: any;
    stopRules?: any;
    companyId?: any;
    companyName?: any;
    country?: string;
    scheduleInformation?: ScheduleInformationDto;
    timeZone?: string;
    participants?: ParticipantsDto;
    skippedEmails?: string[];
    isDraft?: boolean;
    parentId?: string;
  }) {
    // Init queryRunner
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (
        existedMails.some((mail) =>
          mail.sequenceInstances?.some((seqInstance) => seqInstance.status === SequenceStepStatus.REPLIED)
        )
      ) {
        throw new BadRequestException('Mail is already replied');
      }

      let {
        recipients = [],
        hotlistIds = [],
        contactListIds = [],
        shortListIds = [],
      } = participants as ParticipantsDto;

      if (!mails || !mails.length) {
        mails = [];
      }
      if (!recipients || !recipients.length) {
        recipients = [];
      }

      const keys = mails.map(({ key, id }) => (!id ? key : null)).filter(Boolean);
      let existingIds = [];
      if (keys.length) {
        const existing = await this.dataSource.getRepository(SequenceStepEntity).find({
          where: {
            id: In(keys),
            sequence: {
              id: emailSeq.id,
            },
          },
          select: ['id'],
        });
        existingIds = existing.map((item) => item.id);
      }

      const existedStepIds = new Set([...existedMails.map((mail) => mail.id), ...existingIds]);
      const mailsUpdated = [...mails].map((mail, stepIndex) => ({
        ...mail,
        stepIndex,
        id: [mail.id, mail.key].find((id) => existedStepIds.has(id)),
      }));
      // if (
      //   existedMails.some((step, i) => step.type !== (mailsUpdated[i]?.type || SequenceStepType.EMAIL)) ||
      //   mailsUpdated.length < existedMails.length
      // ) {
      //   throw new BadRequestException('Cannot remove or update type of previous steps');
      // }

      const emailSequence = cloneDeep(emailSeq);

      // find the latest mail which sent, then remove them from update
      // const numberOfMails = existedMails.length;
      const firstStep = existedMails[0] || {};
      // const editableStepIndex = existedMails.findIndex((mail) =>
      //   mail.sequenceInstances.every((seqInstance) => seqInstance.status === SequenceStepStatus.PENDING)
      // );
      // const editableStepIndex = 0; // All steps now are editable

      // const closestUnsentMailIndex = editableStepIndex >= 0 ? editableStepIndex : numberOfMails;
      const addSteps = mailsUpdated.filter((step) => !step.id);
      const updateSteps = mailsUpdated.filter((step) => step.id);
      const updateStepIds = updateSteps.map((step) => step.id);
      const deleteSteps = existedMails.filter((step) => !updateStepIds.includes(step.id));
      let mustUpdateSchedule = existedMails.some((step) =>
        step.sequenceInstances?.some((seqInstance) => seqInstance.status !== SequenceStepStatus.PENDING)
      );

      // if number of payload excluded the update
      // const notUpdateMails = mails.slice(0, closestUnsentMailIndex);
      // const notUpdateMailsLength = notUpdateMails.length;
      // if (notUpdateMailsLength !== closestUnsentMailIndex) {
      //   throw new BadRequestException('Invalid payload');
      // }

      const { version: prevVersion = -1 } = firstStep.sequenceInstances?.sort((a, b) => b.version - a.version)[0] || {};

      const prevRecipients =
        firstStep.sequenceInstances?.reduce((acc, stepIns) => [...acc, ...stepIns.recipients], []) || [];

      let { added: addedRecipients, updated: updateRecipients } = this.detectChanges(
        emailSeq.participants?.recipients || prevRecipients,
        recipients,
        'email'
      );
      let { added: addedHotlistIds, updated: updateHotlistIds } = this.detectChanges(
        emailSeq.participants?.hotlistIds || [],
        hotlistIds
      );
      let { added: addedContactListIds, updated: updateContactListIds } = this.detectChanges(
        emailSeq.participants?.contactListIds || [],
        contactListIds
      );
      let { added: addedShortListIds, updated: updateShortListIds } = this.detectChanges(
        emailSeq.participants?.shortListIds || [],
        shortListIds
      );

      const isSeqDraft = emailSeq.status === SequenceStatus.DRAFT;
      if (isSeqDraft) {
        addedRecipients = [];
        updateRecipients = recipients;
        addedHotlistIds = [];
        updateHotlistIds = hotlistIds;
        addedContactListIds = [];
        updateContactListIds = contactListIds;
        addedShortListIds = [];
        updateShortListIds = shortListIds;
      }

      // create email sequence
      let emailSeqEntity = { ...emailSequence, scheduleInformation, stopRules, participants, skippedEmails, parentId };
      if (emailSeqEntity) {
        emailSeqEntity.name = name || emailSeqEntity.name;
        // emailSeqEntity.pendingCount = mails.length - notUpdateMailsLength;
        emailSeqEntity.stepCount = mails.length;
        emailSeqEntity.rawSequence = rawSequence;
        emailSeqEntity.jobBoardId = jobBoardId;
        emailSeqEntity.externalJobId = externalJobId;
        emailSeqEntity.crmLeadId = crmLeadId;
        emailSeqEntity.recipients = recipients?.map((item) => item?.email).join(', ');
        emailSeqEntity.contactNames = recipients?.map((item) => item?.email).join(', ');
      } else {
        const pendingCount = mails.reduce(
          (acc, mail) => acc + (mail.type === SequenceStepType.EMAIL ? mail?.recipients?.length || 0 : 0),
          0
        );
        emailSeqEntity = this.sequenceRepository.create({
          name: name || '',
          status: SequenceStatus.LIVE,
          stepCount: mails.length,
          pendingCount,
          user: currentUser,
          rawSequence,
          jobBoardId,
          externalJobId,
          crmLeadId,
          createdFrom: createdFrom ?? SequenceEmailCreatedFrom.VACANCY,
          recipients: recipients?.map((item) => item?.email).join(', '),
          companyId,
          country,
        });
      }
      const updatedSequence = await queryRunner.manager.save(SequenceEntity, emailSeqEntity);

      let sequenceTriggerAt;
      if (
        emailSeq.scheduleInformation?.isTriggeredNow !== scheduleInformation?.isTriggeredNow ||
        !scheduleInformation?.isTriggeredNow
      ) {
        sequenceTriggerAt = getSequenceTriggerAt(scheduleInformation);
        await queryRunner.manager.update(
          SequenceEntity,
          { id: updatedSequence.id },
          {
            triggerAt: () => `'${sequenceTriggerAt}'::timestamptz`,
          }
        );
        mustUpdateSchedule = true;
      }

      const updateSequenceSteps = [];
      const updateSequenceInstances = [];

      const mappingStartTimeReschedule = {};
      firstStep.sequenceInstances?.forEach((stepInstance) => {
        if (stepInstance.status !== SequenceStepStatus.PENDING && firstStep.id === mailsUpdated[0].id) {
          mappingStartTimeReschedule[stepInstance.version] = stepInstance.scheduledAt;

          return;
        }

        mappingStartTimeReschedule[stepInstance.version] = sequenceTriggerAt ? new Date(sequenceTriggerAt) : new Date();
      });

      const currentVScheduledAt = {};
      const mappingRescheduleAt = mailsUpdated.reduce((m, step) => {
        // Pre-defined scheduledAt for all steps of all instances (and new version maybe)
        Array(prevVersion + 2)
          .fill(null)
          .map((_, ver) => {
            const vStart = mappingStartTimeReschedule[ver] || new Date();
            const stepDelay = step.delays.reduce(
              (acc, item) => acc + (item.delay || 0) * this.DELAY_UNIT_MULTIPLE[item.unit],
              0
            );
            currentVScheduledAt[ver] = (currentVScheduledAt[ver] || 0) + stepDelay;

            if (!m[ver]) {
              m[ver] = [];
            }

            m[ver].push(new Date(vStart.getTime() + currentVScheduledAt[ver]));
          });

        return m;
      }, {});

      updateSteps.forEach((step) => {
        updateSequenceSteps.push({
          id: step.id,
          type: step.type,
          subject: step.subject,
          content: step.content,
          delays: step.delays,
          stepIndex: step.stepIndex,
          name: step?.name,
          ...(step.signatureId ? { userSignature: { id: step.signatureId } } : {}),
          ...(placeHolders ? { placeholders: placeHolders } : {}),
          ...(stopRules ? { stopRules } : {}),
        });
        const dbStep = existedMails.find((mail) => mail.id === step.id);
        dbStep.sequenceInstances?.forEach((stepInstance) => {
          if (
            ![SequenceStepStatus.PENDING, SequenceStepStatus.STOP, SequenceStepStatus.OFF].includes(stepInstance.status)
          ) {
            return;
          }
          stepInstance.scheduledAt = mustUpdateSchedule
            ? mappingRescheduleAt[stepInstance.version][step.stepIndex]
            : stepInstance.scheduledAt;

          const stepInsParticipants: any = {
            recipients: updateRecipients.filter((recipient) =>
              isSeqDraft
                ? true
                : stepInstance.recipients.some((stepRecipient) => recipient.email === stepRecipient.email)
            ),
            contactListIds: updateContactListIds.filter((contactListId) =>
              isSeqDraft
                ? true
                : stepInstance.contactListIds.some((stepContactListId) => contactListId === stepContactListId)
            ),
            shortListIds: updateShortListIds.filter((shortListId) =>
              isSeqDraft ? true : stepInstance.shortListIds.some((stepShortListId) => shortListId === stepShortListId)
            ),
            hotlistIds: updateHotlistIds.filter((hotlistId) =>
              isSeqDraft ? true : stepInstance.hotlistIds.some((stepHotlistId) => hotlistId === stepHotlistId)
            ),
          };
          if (step.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
            stepInsParticipants.recipients = step.recipients || stepInsParticipants.recipients;
            stepInsParticipants.hotlistIds = step.hotlistIds || stepInsParticipants.hotlistIds;
            stepInsParticipants.contactListIds = step.contactListIds || stepInsParticipants.contactListIds;
            stepInsParticipants.shortListIds = step.shortListIds || stepInsParticipants.shortListIds;
          } else if (step.type === SequenceStepType.TASK) {
            stepInsParticipants.recipients = step.recipients || stepInsParticipants.recipients;
          }

          updateSequenceInstances.push({
            id: stepInstance.id,
            ...stepInsParticipants,
            ...(mustUpdateSchedule
              ? {
                  scheduledAt: stepInstance.scheduledAt,
                }
              : {}),
          });
        });
      });

      const addSequenceSteps = [];
      const addSequenceInstances = [];
      const addSequenceInstancesPrevSteps = [];

      addSteps.forEach((step) => {
        const seqStep = this.sequenceStepRepository.create({
          type: step.type || SequenceStepType.EMAIL,
          subject: step.subject,
          content: step.content,
          delays: step.delays,
          stepIndex: step.stepIndex,
          name: step?.name,
          user: currentUser,
          externalJobId: externalJobId || existedMails[0]?.externalJobId,
          jobBoardId: jobBoardId || existedMails[0]?.jobBoardId,
          crmLeadId: crmLeadId || existedMails[0]?.crmLeadId,
          sequence: emailSeqEntity,
          ...(step.signatureId ? { userSignature: { id: step.signatureId } } : {}),
          ...(placeHolders ? { placeholders: placeHolders } : {}),
          ...(stopRules ? { stopRules } : {}),
          ...(uuidValidate(step.key) ? { id: step.key } : {}),
        });
        addSequenceSteps.push(seqStep);
        const seqInstances: any = Array(prevVersion + 1)
          .fill(null)
          .map((_, ver) => {
            const stepInsParticipants: any = {
              recipients,
              hotlistIds,
              contactListIds,
              shortListIds,
            };
            if (step.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
              stepInsParticipants.recipients = step.recipients || stepInsParticipants.recipients;
              stepInsParticipants.hotlistIds = step.hotlistIds || stepInsParticipants.hotlistIds;
              stepInsParticipants.contactListIds = step.contactListIds || stepInsParticipants.contactListIds;
              stepInsParticipants.shortListIds = step.shortListIds || stepInsParticipants.shortListIds;
            } else if (step.type === SequenceStepType.TASK) {
              stepInsParticipants.recipients = step.recipients || stepInsParticipants.recipients;
            }

            return this.sequenceInstanceRepository.create({
              sequence: emailSeqEntity,
              sequenceStep: seqStep,
              user: currentUser,
              version: ver,
              scheduledAt: mappingRescheduleAt[ver][step.stepIndex],
              ...stepInsParticipants,
            });
          });
        if (
          addedRecipients.length ||
          addedHotlistIds.length ||
          addedContactListIds.length ||
          addedShortListIds.length
        ) {
          const stepInsParticipants: any = {
            recipients: addedRecipients.length ? addedRecipients : [],
            hotlistIds: addedHotlistIds.length ? addedHotlistIds : [],
            contactListIds: addedContactListIds.length ? addedContactListIds : [],
            shortListIds: addedShortListIds.length ? addedShortListIds : [],
          };
          if (step.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
            stepInsParticipants.recipients = step.recipients || stepInsParticipants.recipients;
            stepInsParticipants.hotlistIds = step.hotlistIds || stepInsParticipants.hotlistIds;
            stepInsParticipants.contactListIds = step.contactListIds || stepInsParticipants.contactListIds;
            stepInsParticipants.shortListIds = step.shortListIds || stepInsParticipants.shortListIds;
          } else if (step.type === SequenceStepType.TASK) {
            stepInsParticipants.recipients = step.recipients || stepInsParticipants.recipients;
          }

          seqInstances.push(
            this.sequenceInstanceRepository.create({
              sequence: emailSeqEntity,
              sequenceStep: seqStep,
              user: currentUser,
              version: prevVersion + 1,
              scheduledAt: mappingRescheduleAt[prevVersion + 1][step.stepIndex],
              ...stepInsParticipants,
            })
          );
        }
        addSequenceInstances.push(seqInstances);
      });

      if (addedRecipients.length || addedHotlistIds.length || addedContactListIds.length || addedShortListIds.length) {
        existedMails.forEach((step, index) => {
          if (!updateStepIds.includes(step.id)) {
            return;
          }
          const stepInsParticipants: any = {
            recipients: addedRecipients.length ? addedRecipients : [],
            hotlistIds: addedHotlistIds.length ? addedHotlistIds : [],
            contactListIds: addedContactListIds.length ? addedContactListIds : [],
            shortListIds: addedShortListIds.length ? addedShortListIds : [],
          };
          if (step.type === SequenceStepType.LINKEDIN_CONNECTION_REQUEST) {
            stepInsParticipants.recipients = step.recipients || stepInsParticipants.recipients;
            stepInsParticipants.hotlistIds = step.hotlistIds || stepInsParticipants.hotlistIds;
            stepInsParticipants.contactListIds = step.contactListIds || stepInsParticipants.contactListIds;
            stepInsParticipants.shortListIds = step.shortListIds || stepInsParticipants.shortListIds;
          } else if (step.type === SequenceStepType.TASK) {
            stepInsParticipants.recipients = step.recipients || stepInsParticipants.recipients;
          }

          const seqInstance = this.sequenceInstanceRepository.create({
            sequence: emailSeqEntity,
            sequenceStep: step,
            user: currentUser,
            version: prevVersion + 1,
            ...(mustUpdateSchedule
              ? {
                  scheduledAt: mappingRescheduleAt[prevVersion + 1][step.stepIndex],
                }
              : {}),
            ...stepInsParticipants,
          });
          addSequenceInstancesPrevSteps.push(seqInstance);
        });
      }

      const emailActivityLogEntity = this.sequenceActivityLogRepository.create({
        type: SequenceActivityType.UPDATED,
        sequence: { id: emailSeqEntity.id },
        content: { addedRecipients, newParticipants: participants, prevParticiapnts: emailSeq.participants },
        occurredAt: new Date().toISOString(),
      });

      await Promise.all([
        ...updateSequenceSteps.map(({ id, ...data }) => queryRunner.manager.update(SequenceStepEntity, id, data)),
        ...updateSequenceInstances.map(({ id, ...data }) =>
          queryRunner.manager.update(SequenceInstanceEntity, id, data)
        ),
      ]);
      const [sequenceStepResults] = await Promise.all([
        queryRunner.manager.insert(SequenceStepEntity, addSequenceSteps),
        queryRunner.manager.insert(SequenceInstanceEntity, addSequenceInstancesPrevSteps),
        queryRunner.manager.insert(SequenceActivityLogEntity, emailActivityLogEntity),
        deleteSteps.length
          ? queryRunner.manager.delete(
              SequenceStepEntity,
              deleteSteps.map((step) => step.id)
            )
          : Promise.resolve(),
      ]);

      sequenceStepResults.identifiers.forEach((step, index) => {
        addSequenceInstances[index].forEach((instance) => {
          instance.sequenceStep = step;
        });
      });
      await queryRunner.manager.insert(SequenceInstanceEntity, addSequenceInstances.flat());

      // Re-calculate pendingCount
      const seqInstancesEmails = await this.sequenceInstanceRepository
        .createQueryBuilder('si')
        .innerJoin('sequence_steps', 'ss', 'ss.id = si.sequence_step_id')
        .select(
          'si.recipients AS recipients, si.status AS status, ss.step_index AS step_index, si.skip_recipients as skip_recipients'
        )
        .where('si.sequence_id = :seqId AND ss.type = :type', {
          // status: SequenceStepStatus.SENT,
          seqId: emailSeqEntity.id,
          type: SequenceStepType.EMAIL,
        })
        .orderBy('ss.step_index', 'ASC')
        .getRawMany();

      const { pendingCount, stopCount } = seqInstancesEmails.reduce(
        (acc, seqInstancesEmail) => {
          if (seqInstancesEmail.status === SequenceStepStatus.PENDING) {
            acc.pendingCount += seqInstancesEmail.recipients.length;
          } else if ([SequenceStepStatus.STOP, SequenceStepStatus.OFF].includes(seqInstancesEmail.status)) {
            acc.stopCount += seqInstancesEmail.recipients.length;
          }
          acc.stopCount += seqInstancesEmail.skip_recipients?.length || 0;

          return acc;
        },
        { pendingCount: 0, stopCount: 0 }
      );

      await queryRunner.manager.update(
        SequenceEntity,
        { id: emailSeqEntity.id },
        {
          pendingCount,
          stopCount,
          ...(isSeqDraft && !isDraft ? { status: SequenceStatus.LIVE } : {}),
        }
      );

      if (addSteps.length) {
        const rootSequenceId = await this.getSequenceAncestorsIds(emailSeqEntity.id, true);
        await queryRunner.manager.update(
          SequenceEntity,
          {
            id: In([rootSequenceId, emailSeqEntity.id].filter(Boolean)),
            status: SequenceStatus.STOP,
            isMarkedAsCompleted: true,
          },
          {
            status: SequenceStatus.LIVE,
            isMarkedAsCompleted: false,
            createdAt: new Date(),
          }
        );
      }

      // Commit transaction
      await queryRunner.commitTransaction();

      if (!(isSeqDraft && !isDraft) && (addSteps.length || addedRecipients.length || updateSteps.length)) {
        await this.createAllSequenceTasks(updatedSequence.id).catch((error) => {
          // Do nothing
        });
      }

      return { seqId: updatedSequence.id };
    } catch (error) {
      // Rollback
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Relase queryRunner
      await queryRunner.release();
    }
  }

  private async findEssentialInfo(user, emailSeqId = null, jobBoardId = null, externalJobId = null) {
    let jobId: string;
    let externalId: string;
    let jobLead: JobLead;

    if (jobBoardId) {
      const jobBoard = await this.elasticsearchService.getById(JOBS_INDEX, jobBoardId);

      if (!jobBoard && !externalJobId) {
        throw new NotFoundException(`Job board ${jobBoardId} not found`);
      }

      if (jobBoard) {
        jobLead = await this.jobLeadRepository.findOne({
          where: {
            job_board_id: jobBoardId,
          },
        });

        if (jobLead) {
          externalId = jobLead.job_lead_external_id;
        }
      }
    }

    if (externalJobId && !jobLead) {
      // check whether external job is exited in the db
      jobLead = await this.jobLeadRepository.findOne({
        where: {
          job_lead_external_id: `bullhorn-${externalJobId}`,
        },
      });

      if (jobLead) {
        jobId = jobLead.job_board_id;
      }
    }

    const [currentUser, existedMails, emailSeq] = await Promise.all([
      this.userRepository.findOne({ where: { id: user.id } }),
      emailSeqId
        ? this.sequenceStepRepository.find({
            where: {
              sequence: {
                id: emailSeqId,
              },
              user: {
                id: user.id,
              },
            },
            order: {
              stepIndex: 'ASC',
            },
            relations: {
              sequence: true,
              sequenceInstances: true,
            },
          })
        : this.findSequenceByJob(user.id, jobBoardId, externalId),
      emailSeqId
        ? this.sequenceRepository.findOne({
            where: { id: emailSeqId },
          })
        : null,
    ]);

    if (!jobLead && (emailSeq?.externalJobId || emailSeq?.jobBoardId)) {
      jobLead = emailSeq?.externalJobId
        ? await this.jobLeadRepository.findOneBy({ job_lead_external_id: emailSeq.externalJobId })
        : await this.jobLeadRepository.findOneBy({ job_board_id: emailSeq.jobBoardId });
    }

    return { jobId, externalId, currentUser, existedMails, emailSeq, jobLead };
  }

  private async findSequenceByJob(userId, jobBoardId, externalJobId) {
    let query: FindManyOptions = {
      where: {
        user: { id: userId },
      },
      order: {
        stepIndex: 'ASC',
      },
      relations: {
        sequence: true,
      },
    };

    if (jobBoardId) {
      set(query, 'where.jobBoardId', jobBoardId);
    } else {
      set(query, 'where.externalJobId', `bullhorn-${externalJobId}`);
    }

    return this.sequenceStepRepository.find(query);
  }

  async updateCorrespondingField({ field, mail, emailSeq, eventTime, recipient, recipientSend, url, reason }) {
    const mappingEvents = {
      open: 'opened',
      click: 'link_clicked',
    };
    const mappingFields = {
      open: 'opened',
      click: 'linkClicked',
      bounce: 'bounced',
    };
    const mappingRawFields = {
      open: 'opened',
      click: 'link_clicked',
      bounce: 'bounced',
    };

    const eventEvent = mappingEvents[field] || field;
    const eventField = mappingFields[field] || field;
    const dbRawField = mappingRawFields[field] || field;
    const activityLogEntity = this.sequenceActivityLogRepository.create({
      //TODO: to check if this includes null as type
      type: eventEvent,
      sequence: { id: mail?.sequence.id },
      occurredAt: new Date().toISOString(),
      sequenceStep: { id: mail.sequenceStep?.id },
      content: {
        email: { content: recipient },
        urlClick: url,
        contact: recipientSend,
        sequenceInstance: mail.id,
        recipient,
        ...(reason && { reason }),
      },
    });

    await Promise.all([
      this.sequenceRepository
        .createQueryBuilder()
        .update(SequenceEntity)
        .set({ [eventField]: () => `"${dbRawField}" + 1` })
        .where('id = :id', { id: mail?.sequence.id })
        .execute(),

      this.sequenceActivityLogRepository.insert(activityLogEntity),
    ]);
  }

  async findMailByRawId(rawId) {
    if (!rawId) {
      return null;
    }

    const regex = /<([^@>]+)@/;
    const messageId = rawId.match(regex)?.[1] || rawId;

    return this.sequenceInstanceRepository.findOne({
      where: {
        sentIds: Raw((sentIdsCol) => `${sentIdsCol} @> :sentId`, { sentId: JSON.stringify([messageId]) }),
        status: SequenceStepStatus.SENT,
      },
      relations: {
        user: true,
        sequence: true,
        sequenceStep: {
          sequenceStepTasks: true,
        },
      },
    });
  }

  private async findMailById(id: string) {
    if (!id) {
      return null;
    }

    return this.sequenceInstanceRepository.findOne({
      where: {
        sentIds: Raw((sentIdsCol) => `${sentIdsCol} @> :sentId`, { sentId: JSON.stringify([id]) }),
        status: SequenceStepStatus.SENT,
      },
      relations: {
        user: true,
        sequence: true,
        sequenceStep: true,
      },
    });
  }

  async findContactFromContactList(contactListIds: string | string[]) {
    const ids = Array.isArray(contactListIds) ? contactListIds : [contactListIds];
    const dataContacts = await this.contactRepository.find({ where: { contactList: { id: In(ids) } } });

    return dataContacts;
  }

  async findHotListContact(tearSheetIds: string[] | number[], user: any = {}, email = '') {
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(user?.organizationId)) || {};
    const entityName = 'ClientContact';

    return (
      await BBPromise.all(
        tearSheetIds.map(async (tearSheetId) => {
          let data = await queryClientContacts(
            {
              entityName,
              tearSheetId,
              start: '0',
              query: '',
              email,
              count: '10000',
            },
            entityName,
            bhRestToken,
            corporateRestUrl
          );

          if (!data?.length) {
            const entity = 'Candidate';
            data = await queryClientContacts(
              {
                entityName: entity,
                tearSheetId,
                start: '0',
                query: '',
                email,
                count: '10000',
              },
              entity,
              bhRestToken,
              corporateRestUrl
            );
          }

          return data;
        }),
        { concurrency: 10 }
      )
    ).flat();
  }

  async findShortListCandidate(shortListIds: string[] | number[], user: any = {}) {
    const { bhRestToken, corporateRestUrl } = (await this.getBhToken(user?.organizationId)) || {};

    return (
      await BBPromise.all(
        shortListIds.map(async (shortListId) => {
          const response = await queryGetListShortList(bhRestToken, corporateRestUrl, 0, shortListId, null);

          return response?.data?.flatMap((item) => (Array.isArray(item.candidate) ? item.candidate : [item.candidate]));
        }),
        { concurrency: 10 }
      )
    )
      .flat()
      .filter(Boolean);
  }

  private async deleteLinkedGrantId(currentUser: UserEntity, type: string) {
    if (!currentUser[type]) {
      throw new PreconditionFailedException('Not have permission');
    }

    if (currentUser.grantId === currentUser.grantMailboxId) {
      return this.userRepository.update(currentUser.id, {
        [`${type}`]: null,
      });
    }

    return Promise.all([
      this.userRepository.update(currentUser.id, {
        [`${type}`]: null,
        ...(type === 'grantId' ? { grantEmail: null } : {}),
      }),
      this.nylas.grants.destroy({ grantId: currentUser[type] }),
    ]);
  }

  async getSignatureAsset(signatureId, useSendGrid = false) {
    const signature = signatureId
      ? await this.userSignatureRepository.findOne({
          where: {
            id: signatureId,
          },
        })
      : null;

    const images: any = signature
      ? // signature?.imgMetadata?.length > 0
        //   ? await Promise.all(
        //       signature?.imgMetadata?.map((metadata) =>
        //         S3.getObject({
        //           Bucket: SesConfig.COMMON_API_S3_BUCKET_NAME,
        //           Key: metadata.imgId,
        //         }).promise()
        //       )
        //     ).then((res) => {
        //       return res.map((image, index) => ({
        //         filename: signature?.imgMetadata?.[index]?.imgId,
        //         contentType: image.ContentType,
        //         isInline: true,
        //         contentId: signature?.imgMetadata?.[index]?.imgId,
        //         content: image.Body.toString('base64'),
        //       }));
        //     })
        //   :
        signature.sigContent
          .replace(new RegExp('\r', 'g'), '')
          .replace(new RegExp('\n', 'g'), '')
          .match(/<img[^>]+src="([^">]+)"/gm)
          ?.map((x) => x.replace(/.*src="([^"]*)".*/, '$1'))
          ?.map((content, index) => {
            const imgId = `SIGNATURE-${index}`;
            signature.sigContent = signature.sigContent
              .replace(new RegExp('\r', 'g'), '')
              .replace(new RegExp('\n', 'g'), '')
              .replace(content, `cid:${imgId}`);
            const splittedContent = content.split(',');
            const imageValue = splittedContent?.length > 0 ? splittedContent[1] : '';
            return {
              filename: `${imgId}.jpeg`,
              contentType: `image/jpeg`,
              content: imageValue,
              ...(useSendGrid ? { disposition: 'inline', content_id: imgId } : { isInline: true, contentId: imgId }),
            };
          })
          ?.filter((img: any) => img.content) || []
      : [];
    return { signature, images };
  }

  private convertUtcToTimeZone = (utc: string) => {
    if (!utc) return null;
    const offset = parseInt(utc.replace('UTC', ''), 10);
    return `Etc/GMT${-offset}`;
  };

  standardizeSentEmail({
    recipient,
    detailEmail,
    currentUser,
    job,
    signature,
    fromHotList,
    lastSent,
    timeZone = null,
    utc = null,
    sourceType = false,
    sourceId = '',
  }: {
    recipient?: RecipientInfo;
    detailEmail: MailInfo;
    currentUser: UserEntity;
    job: CoreJobLead;
    signature: UserSignature;
    fromHotList: boolean;
    lastSent?: any;
    timeZone?: string;
    utc?: string;
    sourceType?: any;
    sourceId?: string;
  }) {
    const cloneDetailEmail = cloneDeep(detailEmail);
    const finalTimeZone =
      utc && utc !== 'UTC-12:00' ? this.convertUtcToTimeZone(utc) : timeZone || this.convertUtcToTimeZone('UTC+0');

    const emailInfo: {
      sender: object;
      recipient: RecipientInfo;
      job: CoreJobLead;
      sentDateTime: string;
      currentSentDateTime: string;
    } = {
      sender: {
        name: currentUser.fullName,
        email: currentUser.email,
      },
      recipient,
      job: {
        ...job,
        owner: {
          firstName: currentUser.fullName.split(' ')[0] ?? '',
          fullName: currentUser.fullName,
          email: currentUser.email,
        },
      },
      currentSentDateTime: new Intl.DateTimeFormat('en-GB', {
        dateStyle: 'full',
        timeStyle: 'long',
        timeZone: finalTimeZone,
      }).format(),
      sentDateTime: lastSent,
    };

    MERGE_TAGS_PLACE_HOLDERS?.forEach((rawPlaceHolder) => {
      const purePlaceHolder = rawPlaceHolder.split('{{').join('').split('}}').join('');
      const replacedContent =
        fromHotList && rawPlaceHolder.includes('RECIPIENT') // from hot list, do not specify recipient's info
          ? ''
          : getValueByPath(emailInfo, MergeTagMappingEnum[purePlaceHolder], '');

      cloneDetailEmail.content = cloneDetailEmail?.content
        ?.split(rawPlaceHolder)
        ?.join(Array.isArray(replacedContent) ? replacedContent.join(', ') : replacedContent);
    });
    cloneDetailEmail.subject = getMergeTagsContent(cloneDetailEmail.subject, emailInfo);

    cloneDetailEmail.content = processingContent({
      emailContent: cloneDetailEmail?.content?.replace(
        /\$(first_name|first name)\$/g,
        fromHotList ? '' : recipient?.name?.split(' ')[0]
      ),
      organizationId: currentUser.organizationId || 'null',
      signature: signature?.sigContent,
      email: recipient.email,
      sourceType,
      sourceId,
    });

    return cloneDetailEmail;
  }

  async sendEmail({
    recipients,
    detailEmail,
    currentUser,
    signatureId = '',
    job,
    fromHotList,
    sequenceId,
    threadId,
    timeZone = null,
    utc = null,
    useSendGrid = false,
    sourceType = false,
  }: {
    recipients: RecipientInfo[];
    detailEmail: MailInfo;
    currentUser: UserEntity;
    signatureId?: string;
    job?: CoreJobLead;
    fromHotList: boolean;
    sequenceId?: string;
    threadId?: string;
    timeZone?: string;
    utc?: string;
    useSendGrid?: boolean;
    sourceType?: any;
  }) {
    const sigItem: any = await this.userSignatureServices.getSignatureDefault(currentUser.id);
    const { images, signature } = await this.getSignatureAsset(sigItem?.result?.id, useSendGrid);
    const lastSentItem =
      sequenceId && threadId
        ? await this.sequenceActivityLogRepository.findOne({
            where: {
              sequence: { id: sequenceId },
              sequenceStep: { id: threadId },
              type: SequenceActivityType.SENT,
            },
            order: {
              createdAt: 'DESC',
            },
          })
        : null;

    const finalTimeZone =
      utc && utc !== 'UTC-12:00' ? this.convertUtcToTimeZone(utc) : timeZone || this.convertUtcToTimeZone('UTC+0');

    const lastSent = lastSentItem?.createdAt
      ? new Intl.DateTimeFormat('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          timeZoneName: 'short',
          timeZone: finalTimeZone,
        }).format(lastSentItem.createdAt)
      : null;
    return Promise.all(
      recipients.map(async (recipient: any) => {
        const standardEmail = this.standardizeSentEmail({
          detailEmail,
          recipient: {
            ...recipient,
            firstName: recipient?.firstName?.trim() ? recipient?.firstName : recipient?.name?.split(' ')?.[0],
            clientCorporation: { name: recipient?.clientCorporation?.name || recipient?.companyName },
            occupation: recipient?.occupation || recipient?.contactTitle,
          },
          currentUser,
          signature,
          job,
          fromHotList,
          lastSent,
          timeZone,
          sourceType,
          sourceId: recipient?.id,
        });
        let result: any;
        if (useSendGrid) {
          const msg = {
            to: {
              email: recipient.email,
              name: recipient.name,
            },
            from: {
              email: currentUser.email,
              name: currentUser.fullName,
            },
            subject: standardEmail?.subject,
            html: standardEmail?.content,
            replyTo: {
              email: currentUser.grantEmail || currentUser.email,
              name: currentUser.fullName,
            },
            trackingSettings: {
              clickTracking: {
                // Click tracking need to update Brand Links (SendGrid)
                // https://app.sendgrid.com/settings/sender_auth/links
                enable: true,
              },
              openTracking: {
                enable: true,
              },
            },
          };
          if (images.length > 0) {
            let attachments = [...images];

            if (standardEmail?.attachments?.length > 0) {
              const mailAttachments = await Promise.all(
                standardEmail?.attachments?.map(async ({ name, type, fileId, uid }) => {
                  try {
                    const link = await this.fileUploadService.getSignedUrl(fileId);
                    if (!link) {
                      return null;
                    }

                    const image = await axios.get(link, { responseType: 'arraybuffer' });
                    const imageValue = Buffer.from(image.data).toString('base64');
                    return {
                      contentId: uid,
                      filename: name,
                      contentType: type,
                      content: imageValue,
                      // isInline: false,
                    };
                  } catch (err) {
                    console.log('Error getting attachment:', fileId, err);

                    return null;
                  }
                })
              );

              attachments = [...attachments, ...(mailAttachments || []).filter(Boolean)];
            }
            set(msg, 'attachments', attachments);
          }

          sgMail.setApiKey(SendGridConfig.apiKeyEmailExplorerCandiate);
          result = await sgMail.send(msg);

          return {
            data: {
              messageId: result[0].headers['x-message-id'],
            },
          };
        } else {
          const msg = {
            to: [
              {
                email: recipient.email,
                name: recipient.name,
              },
            ],
            from: [
              {
                email: currentUser.grantEmail || currentUser.email,
                name: currentUser.fullName,
              },
            ],
            subject: standardEmail?.subject,
            body: standardEmail?.content,
            replyTo: [
              {
                email: currentUser.grantEmail || currentUser.email,
                name: currentUser.fullName,
              },
            ],
            trackingOptions: {
              links: true,
              opens: true,
              thread_replies: true,
            },
            customHeaders: [
              {
                name: 'List-Unsubscribe-Post',
                value: 'List-Unsubscribe=One-Click',
              },
              {
                name: 'List-Unsubscribe',
                value:
                  '<mailto: <EMAIL>?subject=unsubscribe>,  <http://mailinglist.example.com/unsubscribe.html>',
              },
            ],
          };
          let nylasMessageClient: any = this.nylas.messages;
          if (images.length > 0) {
            let attachments = [...images];

            if (standardEmail?.attachments?.length > 0) {
              const mailAttachments = await Promise.all(
                standardEmail?.attachments?.map(async ({ name, type, fileId, uid }) => {
                  const link = await this.fileUploadService.getSignedUrl(fileId);
                  const image = await axios.get(link, { responseType: 'arraybuffer' });
                  const imageValue = Buffer.from(image.data);
                  return {
                    // contentId: uid,
                    filename: name,
                    contentType: type,
                    content: imageValue,
                    // isInline: false,
                  };
                })
              );
              attachments = [...attachments, ...(mailAttachments || [])];
              nylasMessageClient = nylasClient;
            }

            set(msg, 'attachments', attachments);
          }

          result = await nylasMessageClient.send({
            identifier: currentUser.grantId,
            requestBody: msg,
          });

          if (result.data?.id) {
            const emailWithHeader = await this.nylas.messages.find({
              identifier: currentUser.grantId,
              messageId: result.data.id,
              queryParams: {
                fields: MessageFields.INCLUDE_HEADERS,
              },
            });
            const rawId = (emailWithHeader?.data?.headers || []).find(
              (item) => item?.name?.toLowerCase() === 'message-id'
            )?.value;
            const regex = /<([^@>]+)@/;
            const messageId = `${rawId}`.match(regex)[1];
            result.data.messageId = messageId;
          }

          return result;
        }
      })
    );
  }

  async sendMailToHotList({
    recipients,
    detailEmail,
    currentUser,
    signatureId = '',
    job,
  }: {
    recipients: RecipientInfo[];
    detailEmail: MailInfo;
    currentUser: UserEntity;
    signatureId?: string;
    job: CoreJobLead;
  }) {
    const sigItem: any = await this.userSignatureServices.getSignatureDefault(currentUser.id);
    const { images, signature } = await this.getSignatureAsset(sigItem?.result?.id);
    const standardEmail = this.standardizeSentEmail({ detailEmail, currentUser, job, signature, fromHotList: true });
    const msg = {
      to: recipients,
      from: [
        {
          email: currentUser.grantEmail || currentUser.email,
          name: currentUser.fullName,
        },
      ],
      subject: standardEmail?.subject,
      body: standardEmail?.content,
      replyTo: [
        {
          email: currentUser.grantEmail || currentUser.email,
          name: currentUser.fullName,
        },
      ],
    };

    if (images.length > 0) {
      const attachments = [...images];

      set(msg, 'attachments', attachments);
    }

    const result: any = await this.nylas.messages.send({
      identifier: currentUser.grantId,
      requestBody: msg,
    });

    if (result.data?.id) {
      const emailWithHeader = await this.nylas.messages.find({
        identifier: currentUser.grantId,
        messageId: result.data.id,
        queryParams: {
          fields: MessageFields.INCLUDE_HEADERS,
        },
      });
      const rawId = (emailWithHeader?.data?.headers || []).find(
        (item) => item?.name?.toLowerCase() === 'message-id'
      )?.value;
      const regex = /<([^@>]+)@/;
      const messageId = `${rawId}`.match(regex)[1];
      result.data.messageId = messageId;
    }

    return result;
  }

  async validEmail(email: string) {
    try {
      const data = {
        email: email,
        source: 'Newsletter',
      };

      const request: any = {
        url: `/v3/validations/email`,
        method: 'POST',
        body: data,
      };

      const response = await sgMailClient.request(request);
      return this.formatOutputData({ key: 'VALID_EMAIL' }, { data: response[0].body });
    } catch (error) {
      console.log('Error in validEmail', error);
      return this.throwCommonMessage('VALID_EMAIL_FAIL', error);
    }
  }

  async retryRequest(url: string, email: string, retries: number = 1) {
    try {
      const { data } = await axios.get(url);
      const result = data.deliverability === 'DELIVERABLE' ? 'Valid' : 'Invalid';
      return { email, result };
    } catch (err) {
      if (retries > 0) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return await this.retryRequest(url, email, retries - 1);
      }
      return { email, result: 'Undefined' };
    }
  }

  async validListEmail(body: ValidListEmailDto) {
    const emailFormDb = await this.emailValidationResultRepository.find({
      where: { email: In(body.emails) },
    });

    const results = emailFormDb.map((em) => ({
      email: em.email,
      result: em.status,
    }));

    const emailFormDbArr = new Set(emailFormDb.map((em) => em.email));
    const emailsToCheck = body.emails.filter((email) => !emailFormDbArr.has(email));

    if (emailsToCheck.length > 0) {
      const newResults = await Promise.all(
        emailsToCheck.map((email) => {
          const urlCheckValidEmail = `${process.env.EMAIL_VALIDATION_API_URL}?api_key=${process.env.EMAIL_VALIDATION_API_KEY}&email=${email}`;
          return this.retryRequest(urlCheckValidEmail, email);
        })
      );

      results.push(...newResults);

      const bulkInsertData = [];
      const insertedEmails = [];
      newResults.forEach((result) => {
        if (!insertedEmails.includes(result.email) && result.status !== 'Undefined') {
          insertedEmails.push(result.email);
          bulkInsertData.push({
            email: result.email,
            status: result.result,
          });
        }
      });

      console.log(bulkInsertData, 'bulkInsertData');

      if (bulkInsertData.length > 0) {
        await this.emailValidationResultRepository.insert(bulkInsertData);
      }
    }

    return this.formatOutputData({ key: 'VALID_EMAIL' }, { data: results });
  }

  async getCalendarConnection(req) {
    const user = await this.userRepository.findOne({ where: { id: req.user.id } });
    const identifier = user.grantId;
    if (!identifier) {
      throw new BadRequestException(
        `Your email has not been granted permission to view calendar. Please select Mailbox tab, link your email then try again`
      );
    }

    const { data: calendars } = await this.nylas.calendars.list({
      identifier,
    });

    const primaryCalendar = calendars.find((calendar) => calendar.isPrimary && calendar.isOwnedByUser);
    if (!primaryCalendar) {
      throw new BadRequestException(`You don't have an own primary calendar. Please check again!`);
    }

    return { calendarId: primaryCalendar.id, identifier };
  }

  async getCalendarEvents(req, query: CalendarEventsQueryDto) {
    const { calendarId, identifier } = await this.getCalendarConnection(req);
    let events = [];
    let hasMore = true;
    let maxItems = query.limit || Infinity;

    try {
      const currentDate = new Date();
      const startDay = query.start
        ? new Date(query.start)
        : new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      startDay.setHours(0, 0, 0, 0);
      const endDay = query.end
        ? new Date(query.end)
        : new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      endDay.setHours(23, 59, 59, 999);

      const queryParams: ListEventQueryParams = {
        calendarId,
        start: String(Math.floor(startDay.getTime() / 1000)),
        end: String(Math.floor(endDay.getTime() / 1000)),
        limit: 200,
      };

      while (hasMore) {
        queryParams.limit = Math.min(200, maxItems - events.length);
        const { data, nextCursor } = await this.nylas.events.list({
          identifier,
          queryParams,
        });

        events = events.concat(data);
        hasMore = nextCursor && data.length && events.length < maxItems;
        queryParams.pageToken = nextCursor;

        // Exit loop if no items are fetched or the requested count is reached
        if (!data.length || events.length >= maxItems) {
          break;
        }
      }

      return this.formatOutputData({ key: 'GET_CALENDAR_EVENTS' }, { data: events });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('GET_CALENDAR_EVENTS_FAILED', error);
    }
  }

  async createCalendarEvent(req, event: CalendarEventDto) {
    const { calendarId, identifier } = await this.getCalendarConnection(req);
    try {
      const startTime = new Date(event.startTime).getTime() / 1000;
      const endTime = new Date(event.endTime).getTime() / 1000;
      if (startTime > endTime) {
        throw new BadRequestException(`The start time must less than the end time.`);
      }
      const { data } = await this.nylas.events.create({
        identifier,
        requestBody: {
          title: event.title,
          description: event.description,
          location: event.location,
          busy: event.busy,
          when: {
            startTime,
            endTime,
          },
          participants: event.participants,
        },
        queryParams: {
          calendarId,
        },
      });

      return this.formatOutputData({ key: 'CREATE_CALENDAR_EVENT' }, { data });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('CREATE_CALENDAR_EVENT_FAILED', error);
    }
  }

  async getCalendarEvent(req, eventId: string) {
    const { calendarId, identifier } = await this.getCalendarConnection(req);
    try {
      const { data } = await this.nylas.events.find({
        identifier,
        eventId,
        queryParams: {
          calendarId,
        },
      });

      return this.formatOutputData({ key: 'GET_CALENDAR_EVENT' }, { data });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('GET_CALENDAR_EVENT_FAILED', error);
    }
  }

  async getCalendars(userId) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const { grantId } = user;
      if (!grantId) {
        throw new PreconditionFailedException('You need to grant sequence to use this feature');
      }

      const nylasApiKey = NYLAS_CONFIG.apiKey;
      const configurationUrl = `${NYLAS_CONFIG.apiUri}/v3/grants/${grantId}/calendars?limit=5`;
      const { data } = await this.httpService.axiosRef.get(configurationUrl, {
        headers: {
          Authorization: `Bearer ${nylasApiKey}`,
        },
      });

      return this.formatOutputData({ key: 'GET_CALENDAR_EVENTS' }, { data });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('GET_CALENDAR_EVENTS_FAILED', error);
    }
  }

  async getMyEvents(userId: string) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const { grantId } = user;
      if (!grantId) {
        throw new PreconditionFailedException('You need to grant sequence to use this feature');
      }

      const nylasApiKey = NYLAS_CONFIG.apiKey;
      const configurationUrl = `${NYLAS_CONFIG.apiUri}/v3/grants/${grantId}/scheduling/configurations`;
      const { data } = await this.httpService.axiosRef.get(configurationUrl, {
        headers: {
          Authorization: `Bearer ${nylasApiKey}`,
        },
      });

      return this.formatOutputData({ key: 'GET_MY_EVENTS' }, { data });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('GET_MY_EVENTS_FAILED', error);
    }
  }

  async createConfiguration(userId: string, configuration: any) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const { grantId } = user;
      if (!grantId) {
        throw new PreconditionFailedException('You need to grant sequence to use this feature');
      }

      const nylasApiKey = NYLAS_CONFIG.apiKey;
      const configurationUrl = `${NYLAS_CONFIG.apiUri}/v3/grants/${grantId}/scheduling/configurations`;
      const { data } = await this.httpService.axiosRef.post(
        configurationUrl,
        { ...configuration },
        {
          headers: {
            Authorization: `Bearer ${nylasApiKey}`,
          },
        }
      );

      return this.formatOutputData({ key: 'CREATE_EVENT' }, { data });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('CREATE_EVENT_FAILED', error);
    }
  }

  async getConfigurationDetail(userId: string, configurationId: string) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const { grantId } = user;
      if (!grantId) {
        throw new PreconditionFailedException('You need to grant sequence to use this feature');
      }

      const nylasApiKey = NYLAS_CONFIG.apiKey;
      const configurationUrl = `${NYLAS_CONFIG.apiUri}/v3/grants/${grantId}/scheduling/configurations/${configurationId}`;
      const { data } = await this.httpService.axiosRef.get(configurationUrl, {
        headers: {
          Authorization: `Bearer ${nylasApiKey}`,
        },
      });

      return this.formatOutputData({ key: 'GET_DETAIL_EVENT' }, { data });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('GET_DETAIL_EVENT_FAILED', error);
    }
  }

  async deleteConfiguration(userId: string, configurationId: string) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const { grantId } = user;
      if (!grantId) {
        throw new PreconditionFailedException('You need to grant sequence to use this feature');
      }

      const nylasApiKey = NYLAS_CONFIG.apiKey;
      const configurationUrl = `${NYLAS_CONFIG.apiUri}/v3/grants/${grantId}/scheduling/configurations/${configurationId}`;
      const { data } = await this.httpService.axiosRef.delete(configurationUrl, {
        headers: {
          Authorization: `Bearer ${nylasApiKey}`,
        },
      });

      return this.formatOutputData({ key: 'DELETE_EVENT' }, { data });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('DELETE_EVENT_FAILED', error);
    }
  }

  async updateConfiguration(userId: string, configurationId: string, payload: any) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const { grantId } = user;
      if (!grantId) {
        throw new PreconditionFailedException('You need to grant sequence to use this feature');
      }

      const nylasApiKey = NYLAS_CONFIG.apiKey;
      const configurationUrl = `${NYLAS_CONFIG.apiUri}/v3/grants/${grantId}/scheduling/configurations/${configurationId}`;
      const { data } = await this.httpService.axiosRef.put(configurationUrl, payload, {
        headers: {
          Authorization: `Bearer ${nylasApiKey}`,
        },
      });

      return this.formatOutputData({ key: 'UPDATE_EVENT' }, { data });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('UPDATE_EVENT_FAILED', error);
    }
  }

  async triggerSequence(userId: string, sequenceId: string) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const sendMailData = { emailSeqId: sequenceId };
      await this.send(user, sendMailData);

      return this.formatOutputData({ key: 'TRIGGER_SEQUENCE' }, { data: {} });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('TRIGGER_SEQUENCE_FAILED', error);
    }
  }

  async getLiveFeed(userId: string, query: LiveFeedDto) {
    const { fromDate, toDate, consultant, country, limit = 20, type, offset = 0 } = query;
    const standardConsultant = consultant ?? userId;
    const whereConditions = [["(jsonb_extract_path_text(sal.content, 'contact', 'name') IS NOT NULL)", {}]];

    if (country) {
      whereConditions.push(['(s.country = :country)', { country }]);
    }
    if (standardConsultant) {
      whereConditions.push(['(s.user_id = :consultant)', { consultant: standardConsultant }]);
    }
    if (fromDate) {
      whereConditions.push(['(sal.created_at >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(sal.created_at <= :toDate)', { toDate }]);
    }
    if (type) {
      if (type === 'auto_replied') {
        whereConditions.push(['(sal.type IN (:...types))', { types: ['auto_replied', 'replied'] }]);
      } else {
        whereConditions.push(['(sal.type = :type)', { type }]);
      }
    }

    const queryBuilder = this.sequenceActivityLogRepository
      .createQueryBuilder('sal')
      .innerJoin('sequences', 's', 's.id = sal.sequence_id')
      .select(
        `
        jsonb_extract_path_text(sal.content, 'contact', 'id') AS contact_id,
        jsonb_extract_path_text(sal.content, 'contact', 'email') AS contact_email,
        jsonb_extract_path_text(sal.content, 'contact', 'name') AS contact_name,
        jsonb_extract_path_text(sal.content, 'link_onclick') AS link_onclick,
        sal.sequence_id AS sequence_id,
        s.name AS sequence_name,
        sal.created_at AS created_at,
        sal.type AS type,
        s.user_id AS user_id,
        COUNT(*) OVER(PARTITION BY jsonb_extract_path_text(sal.content, 'contact', 'email'), sal.type,  sal.sequence_id) AS times
        `
      )
      .limit(limit)
      .offset(offset * limit)
      .orderBy('sal.created_at', 'DESC');

    whereConditions.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });
    const data = await queryBuilder.getRawMany();

    return this.formatOutputData(
      { key: 'GET_LIVE_FEED' },
      {
        data: {
          items: data,
        },
      }
    );
  }

  async countEmailSent(userId: string, filter: any) {
    const { country, fromDate, toDate } = filter;
    const whereConditions = [];

    whereConditions.push(
      ['(si.status = :status)', { status: SequenceStepStatus.SENT }],
      ['(ss.type = :type)', { type: SequenceStepType.EMAIL }]
    );

    if (userId) {
      whereConditions.push(['(s.user_id = :userId)', { userId }]);
    }
    if (country) {
      whereConditions.push(['(s.country = :country)', { country }]);
    }
    if (fromDate) {
      whereConditions.push(['(si.executed_at >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(si.executed_at <= :toDate)', { toDate }]);
    }

    const queryBuilder = this.sequenceInstanceRepository
      .createQueryBuilder('si')
      .innerJoin('sequence_steps', 'ss', 'ss.id = si.sequence_step_id')
      .innerJoin('sequences', 's', 's.id = si.sequence_id')
      .leftJoin(
        (qb) => {
          qb.getQuery = () => `LATERAL jsonb_array_elements(si.sent_ids)`;
          qb.setParameters({});
          return qb;
        },
        'sent_id',
        `true`
      )
      .select('COUNT(sent_id)', 'count');

    whereConditions.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });

    const totalData = await queryBuilder.getRawOne();

    const monthlyCounts = await queryBuilder
      .clone()
      .select([`DATE_TRUNC('month', si.executed_at) as month`, 'COUNT(sent_id) as count'])
      .groupBy(`DATE_TRUNC('month', si.executed_at)`)
      .orderBy(`DATE_TRUNC('month', si.executed_at)`, 'ASC')
      .getRawMany<{
        month: string;
        count: number;
      }>();

    const dataByMonth = monthlyCounts.map((item) => ({
      month: item.month,
      count: item.count,
    }));

    return this.formatOutputData(
      { key: 'STATS_EMAIL_SENT' },
      {
        data: {
          totalCount: totalData.count,
          monthData: dataByMonth,
        },
      }
    );
  }

  async unsubscribeEmail(email: string, organizationId: string, source?: string) {
    try {
      let result = '';
      if (!email) {
        throw new NotFoundException('User not found');
      }

      let organizationIdToCheck = organizationId === 'null' ? null : organizationId;

      if (source) {
        await this.contactRepository.update({ email: email }, { isUnsubscribeEmail: true });
        result = 'Unsubscribed Successfull!';
      } else {
        const { bhRestToken, corporateRestUrl } = (await this.getBhToken(organizationIdToCheck)) ?? {};
        const where = `email = '${email}'`;
        const apiUrl = `${corporateRestUrl}query/ClientContact?BhRestToken=${bhRestToken}&fields=id&where=${encodeURIComponent(
          where
        )}`;

        const { data } = await this.httpService.axiosRef.get(apiUrl);

        if (data?.data?.length > 0) {
          const unsubcribePayload = { massMailOptOut: true };
          await Promise.all(
            data?.data?.map((item) => {
              const unsubcribedUserId = item?.id;
              const unsubcribeUrl = `${corporateRestUrl}/entity/ClientContact/${unsubcribedUserId}?BhRestToken=${bhRestToken}`;

              return this.httpService.axiosRef.post(unsubcribeUrl, unsubcribePayload);
            })
          );
          result = 'Unsubscribed Successfull!';
        } else {
          result = 'Something went wrong!';
        }
      }

      // Unsubscribe from SendGrid
      sgMailClient
        .request({
          method: 'POST',
          url: '/v3/asm/suppressions/global',
          body: {
            recipient_emails: [email],
          },
        })
        .catch((error) => {
          console.log('[UNSUBSCRIBE] Unsubscribe from SendGrid error: ', error?.response?.body?.errors || error);
        });

      return result;
    } catch (error) {
      console.log('[UNSUBSCRIBE] Unsubscribe error: ', error?.response?.body?.errors || error);
    }
  }

  async sendTestEmail(userId: string, data: sendTestEmailDto) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const result = await this.sendEmail({
        currentUser: user,
        recipients: [
          {
            id: user.id,
            email: user.email,
            name: user.fullName,
            firstName: user.fullName,
            companyDetail: data?.companyDetail,
            clientCorporation: { name: data?.companyDetail?.name },
          },
        ],
        detailEmail: { ...data.mail, attachments: data?.mail?.fileList },
        job: data.job,
        fromHotList: false,
        useSendGrid: true,
      });

      return this.formatOutputData({ key: 'SEND_TEST_EMAIL_SUCCEED' }, { data: result?.[0] });
    } catch (error) {
      this.logger.error(error);

      return this.throwCommonMessage('SEND_TEST_EMAIL_FAILED', error);
    }
  }

  async isLastSequenceStep(sequenceId: string, sequenceStepId: string) {
    // Create the subquery to get the maximum stepIndex for the sequence
    const subQuery = this.sequenceStepRepository
      .createQueryBuilder('innerStep')
      .select('MAX(innerStep.step_index)', 'maxStepIndex')
      .where('innerStep.sequence_id = :sequenceId', { sequenceId })
      .andWhere('innerStep.type NOT IN (:...type)', { type: [SequenceStepType.TASK] })
      .getQuery();

    // Use the subquery in the main query's WHERE clause
    const result = await this.sequenceStepRepository
      .createQueryBuilder('step')
      .select('step.id')
      .where('step.id = :sequenceStepId', { sequenceStepId })
      .andWhere('step.sequence_id = :sequenceId', { sequenceId })
      .andWhere(`step.step_index = (${subQuery})`, { sequenceId, type: [SequenceStepType.TASK] })
      .getOne();

    return !!result;
  }

  async validateContactsInActiveSequences(userId: string, bodyDto: ValidateContactsInActiveSequenceDto) {
    const { contactEmails } = bodyDto;
    const rawParticipants = await this.sequenceRepository
      .createQueryBuilder('s')
      .where('s.user_id = :userId and s.status = :status', { userId, status: SequenceStatus.LIVE })
      .select(`array_to_string(array_agg(participants), '***') as participants`)
      .getRawOne<{ participants: string }>();

    const participants = [
      ...new Set(
        rawParticipants.participants
          ?.split('***')
          ?.flatMap((item) => safeParseJSON(item)?.recipients?.map((item) => item?.email))
          ?.filter(Boolean)
      ),
    ];

    const joinedParticipants = participants.join(',');
    const warningParticipants = contactEmails.filter((item) => joinedParticipants.includes(item));
    return this.formatOutputData({ key: 'VALIDATE_CONTACT' }, { data: warningParticipants });
  }

  async getListTimezones() {
    const timezones = await getListTimezones();
    const data = timezones
      .filter(({ windows_zone }) => windows_zone)
      .sort((a: any, b: any) => a.offset - b.offset)
      .map((timezone: any, i: number) => ({
        id: timezone.id,
        timezone: timezone.id,
        offset: timezone.offset,
        dst: timezone.dst,
        period: timezone.period,
        name: timezone.windows_zone.name,
        mapName: MAPPING_TIMEZONE_NAMES[timezone.windows_zone.name],
        text: `(UTC${timezone.formatted_offset}) ${
          MAPPING_TIMEZONE_NAMES[timezone.windows_zone.name] || timezone.windows_zone.name
        }`,
        utc: `UTC${timezone.formatted_offset}`, // For old data
        order: i + 1,
      }));

    return this.formatOutputData({ key: 'GET_LIST_TIMEZONES' }, { data });
  }

  async getLinkedinMessageInChat(userId: string, chatId: string, cursor?: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const accountId = user.unipileAccountId;
      if (!accountId) {
        return this.formatOutputData({ key: 'GET_LINKEDIN_MESSAGE' }, { data: [] });
      }

      const currentProfile = await unipileClient.retrieveOwnProfile(accountId);

      let listAttendeeData = await this.unipileRecordRepository.find({
        where: { recordId: In([chatId]) },
      });

      const data = await unipileClient.getLinkedinMessageInChat(accountId, chatId, cursor);

      const listMessage = await data.items.map((item) => {
        const secondUser = listAttendeeData.find((u) => u?.data?.id == item?.sender_attendee_id);
        const secondUserProfile = {
          name: secondUser?.data?.name || '',
          headline: secondUser?.data?.specifics?.occupation || '',
          profileUrl: secondUser?.data?.picture_url || '',
        };
        const firstUserProfile = {
          name: currentProfile?.first_name + ' ' + currentProfile?.last_name,
          headline: currentProfile?.specifics?.occupation || '',
          profileUrl: currentProfile?.profile_picture_url || '',
        };
        return {
          id: item?.id,
          seen: item?.seen,
          object: item?.object,
          file: item?.attachments?.map((it) => ({
            id: it?.id,
            type: it?.type,
            url: it?.url,
          })),
          sender: item?.is_sender == 0 ? secondUserProfile : firstUserProfile,
          userInChat: [firstUserProfile, secondUserProfile],
          chat_id: chatId,
          text: item?.text,
          author: secondUserProfile,
          // isGroupChat: safeParseJSON(item.original)?.conversation?.groupChat,
          // chatTitle: safeParseJSON(item.original)?.conversation?.title,
          createdAt: item?.timestamp,
        };
      });

      return this.formatOutputData({ key: 'GET_LINKEDIN_MESSAGE_IN_CHAT' }, { data: { ...data, items: listMessage } });
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('GET_LINKEDIN_MESSAGE_IN_CHAT', e);
    }
  }

  private mappedLinkedInListChat(data: any[]) {
    return data.map((item) => {
      const sender = item?.userInChat?.find((u) => u?.data?.id === item?.lastMessage?.sender_attendee_id);
      const author = item?.userInChat?.[0];
      return {
        chat_id: item?.id,
        author: {
          name: author?.data?.name || '',
          headline: author?.data?.specifics?.occupation || '',
          profileUrl: author?.data?.picture_url || '',
          linkedinUrl: author?.data?.profile_url
        },
        userInChat: item?.userInChat?.map((user) => ({
          name: user?.data?.name || '',
          headline: user?.data?.specifics?.occupation || '',
          profileUrl: user?.data?.picture_url || '',
          linkedinUrl: user?.data?.profile_url
        })),
        inmail: false,
        seen: item?.unread,
        list_item: [
          {
            id: item?.lastMessage?.id,
            seen: item?.unread,
            object: item?.object,
            file: [],
            sender: {
              name: sender?.data?.name || '',
              headline: sender?.data?.specifics?.occupation || '',
              profileUrl: sender?.data?.picture_url || '',
              linkedinUrl: sender?.data?.profile_url
            },
            createdAt: item?.timestamp,
            text: item?.lastMessage?.text,
          },
        ],
      };
    });
  }

  async getLinkedinMessage(userId: string, cursor?: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const accountId = user.unipileAccountId;
      if (!accountId) {
        return this.formatOutputData({ key: 'GET_LINKEDIN_MESSAGE' }, { data: [] });
      }

      const currentProfilePromise = unipileClient.retrieveAccount(accountId);
      const updateTotalLinkedinMessagePromise = this.userRepository.update(user.id, {
        totalLinkedinMessage: 0,
      });

      const [currentProfile] = await Promise.all([currentProfilePromise, updateTotalLinkedinMessagePromise]);

      const ACoId = currentProfile?.connection_params?.im?.id;

      const data = await unipileClient.getLinkedinListChat(accountId, cursor);
      const cursorIndex = data?.cursor;
      const results = await Promise.all(data?.items.map((item) => unipileClient.getLinkedinListChatDetail(item.id)));
      // const listAttendees = await Promise.all(data?.items.map((item) => unipileClient.getListAttendeesFromAChat(item.id)));
      const listSenderId = results.map((item) => item.id);

      // check list data in unipile record
      let listAttendeeData = await this.unipileRecordRepository.find({
        where: { recordId: In(listSenderId) },
      });

      const listExistedId = listAttendeeData.map((item) => item.recordId);
      const listSenderSearch = listSenderId.filter((item) => !listExistedId.includes(item));
      if (listSenderSearch.length > 0) {
        const listAttendeesInChat = await Promise.all(
          listSenderSearch.map(async (item) => ({
            chatId: item,
            data: await unipileClient.getListAttendeesFromAChat(item),
          }))
        );

        const transformedArray = listAttendeesInChat.flatMap(({ chatId, data }) =>
          data.items.map((item) => ({
            recordId: chatId,
            data: item,
            recordType: RecordTypeEnum.USER,
          }))
        );
        await this.unipileRecordRepository.insert(transformedArray);
        listAttendeeData = transformedArray;
      }

      const mappedArr = results.map((item) => ({
        ...item,
        userInChat: listAttendeeData.filter((user) => user.recordId === item.id),
      }));

      return this.formatOutputData(
        { key: 'GET_LINKEDIN_MESSAGE' },
        { data: { items: this.mappedLinkedInListChat(mappedArr), cursorIndex } }
      );
    } catch (e) {
      console.log('GET_LINKEDIN_MESSAGE_ERR', e);
      this.logger.error(e);
      return this.throwCommonMessage('GET_LINKEDIN_MESSAGE', e);
    }
  }


  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async retryGetMessageDetail(client, messageId, maxRetries = 5, delayMs = 500) {
    let attempts = 0;

    while (attempts < maxRetries) {
      try {
        const messageDetail = await client.getMessageDetail(messageId);
        return messageDetail;
      } catch (error) {
        attempts++;
        console.error(`Attempt ${attempts} failed:`, error);

        if (attempts >= maxRetries) {
          throw new Error(`Failed to retrieve message details after ${maxRetries} attempts.`);
        }
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    }
  }

  async syncAccountDataMessaging(userId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const accountId = user.unipileAccountId;
      if (!accountId) {
        return this.formatOutputData({ key: 'SYNC_ACCOUNT_DATA_MESSAGING' }, { data: [] });
      }

      const data = await unipileClient.syncAccountDataMessaging(accountId);
      return this.formatOutputData({ key: 'SYNC_ACCOUNT_DATA_MESSAGING' }, { data: data });
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('SYNC_ACCOUNT_DATA_MESSAGING', e);
    }
  }

  async retrieveOwnProfile(userId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const accountId = user.unipileAccountId;
      if (!accountId) {
        return this.formatOutputData({ key: 'RETRIEVE_OWN_PROFILE' }, { data: [] });
      }

      const data = await unipileClient.retrieveOwnProfile(accountId);
      return this.formatOutputData({ key: 'RETRIEVE_OWN_PROFILE' }, { data: data });
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('RETRIEVE_OWN_PROFILE', e);
    }
  }

  async sendMessagesToChat(userId: string, chatId: string, content: string, attachments?: any[]) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const accountId = user.unipileAccountId;
      if (!accountId) {
        return this.formatOutputData({ key: 'SEND_MESSAGES_TO_CHAT' }, { data: [] });
      }

      const data = await unipileClient.sendMessagesToChat(accountId, chatId, content, attachments);
      const message_id = data?.message_id;

      console.log('Data create messages', data);

      const currentProfile = await unipileClient.retrieveAccount(accountId);
      let messageDetail: any = {};
      try {
        messageDetail = await this.retryGetMessageDetail(unipileClient, message_id);
        console.log('Message Detail', messageDetail);
      } catch (error) {
        console.error('Failed to retrieve message detail:', error);
      }
      const ACoId = currentProfile?.connection_params?.im?.id;
      const conversation = safeParseJSON(messageDetail.original)?.conversation;
      const participant = conversation?.conversationParticipants?.[0]?.participantType?.member;
      const participantSecond = conversation?.conversationParticipants?.[1]?.participantType?.member;
      const sender = safeParseJSON(messageDetail.original)?.sender?.participantType?.member;

      const participantInfo = {
        name: `${participant?.firstName?.text || ''} ${participant?.lastName?.text || ''}`.trim(),
        headline: participant?.headline?.text || '',
        linkedinUrl: participant?.profileUrl,
        profileUrl:
          participant?.profilePicture?.rootUrl +
          (participant?.profilePicture?.artifacts?.[participant?.profilePicture?.artifacts?.length - 1]
            ?.fileIdentifyingUrlPathSegment || ''),
      };

      const participantSecondInfo = {
        name: `${participantSecond?.firstName?.text || ''} ${participantSecond?.lastName?.text || ''}`.trim(),
        headline: participantSecond?.headline?.text || '',
        linkedinUrl: participantSecond?.profileUrl,
        profileUrl:
          participantSecond?.profilePicture?.rootUrl +
          (participantSecond?.profilePicture?.artifacts?.[participantSecond?.profilePicture?.artifacts?.length - 1]
            ?.fileIdentifyingUrlPathSegment || ''),
      };

      const senderInfo = {
        name: `${sender?.firstName?.text || ''} ${sender?.lastName?.text || ''}`.trim(),
        headline: sender?.headline?.text || '',
        profileUrl:
          sender?.profilePicture?.rootUrl +
          (sender?.profilePicture?.artifacts?.[sender?.profilePicture?.artifacts?.length - 1]
            ?.fileIdentifyingUrlPathSegment || ''),
      };

      const messageData = {
        title: safeParseJSON(messageDetail.original)?.conversation?.title,
        seen: messageDetail?.seen,
        object: messageDetail?.object,
        file: messageDetail?.attachments?.map((it) => ({
          id: it?.id,
          type: it?.type,
        })),
        sender: senderInfo,
        userInChat: [participantInfo, participantSecondInfo],
        chat_id: messageDetail?.chat_id,
        text: messageDetail?.text,
        author:
          participantInfo?.linkedinUrl && participantInfo.linkedinUrl.includes(ACoId)
            ? participantSecondInfo
            : participantInfo,
        isGroupChat: safeParseJSON(messageDetail.original)?.conversation?.groupChat,
        chatTitle: safeParseJSON(messageDetail.original)?.conversation?.title,
        createdAt: messageDetail?.timestamp,
      };
      return this.formatOutputData({ key: 'SEND_MESSAGES_TO_CHAT' }, { data: { ...messageData } });
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('SEND_MESSAGES_TO_CHAT', e);
    }
  }

  async getFileFromMessage(userId: string, messageId: string, attachmentId: string, res, type: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const accountId = user.unipileAccountId;
      if (!accountId) {
        return this.formatOutputData({ key: 'GET_FILE_FROM_CHAT' }, { data: [] });
      }

      const url = `${unipileConfig.UNIPILE_BASE_URL}/api/v1/messages/${messageId}/attachments/${attachmentId}`;
      const response = await lastValueFrom(
        this.httpService.get(url, {
          responseType: 'arraybuffer',
          headers: {
            'X-API-KEY': unipileConfig.UNIPILE_KEY,
          },
        })
      );
      if (type === 'img') {
        res.set('Content-Type', 'image/jpeg');
        res.send(response.data);
      } else {
        res.set('Content-Type', 'application/pdf');
        res.set('Content-Disposition', 'attachment; filename="downloaded_file.pdf"');
        res.send(response.data);
      }
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('GET_FILE_FROM_CHAT', e);
    }
  }

  async updateSequenceName(id: string, bodyDto: SequencePartialDto) {
    const { name } = bodyDto;
    await this.sequenceRepository.update(id, { name });

    return this.formatOutputData({ key: 'UPDATE_SEQUENCE_NAME' }, { data: {} });
  }

  async handleReactAPost(postId: string, userId: string, reactIcon: LinkedInReactIconEnum) {
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const accountId = user.unipileAccountId;
    if (!accountId) {
      return this.formatOutputData({ key: 'GET_LINKEDIN_MESSAGE' }, { data: [] });
    }

    const postDetail = await unipileClient.retrieveAPost(accountId, postId);
    const postSocicalId = await unipileClient.reactAPost(accountId, postDetail?.social_id, reactIcon);
    return postSocicalId;
  }

  async handleFollowAnUser(userId: string, userFollowIdentifier: string) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const accountId = user.unipileAccountId;
      if (!accountId) {
        return this.formatOutputData({ key: 'GET_LINKEDIN_MESSAGE' }, { data: [] });
      }

      const providerUser = await this.linkedinFinderService.getRetrieveProfile(userFollowIdentifier);
      const providerId = providerUser.provider_id;

      const followUser = await unipileClient.followUser(providerId, accountId);
      return this.formatOutputData({ key: 'FOLLOW_AN_USER' }, { data: followUser });
    } catch (e) {
      return this.throwCommonMessage('FOLLOW_AN_USER', e);
    }
  }

  async handleLikeRandomPost(
    userId: string,
    userFollowIdentifier: string,
    reactIcon?: LinkedInReactIconEnum,
    random = true
  ) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const accountId = user.unipileAccountId;
      if (!accountId) {
        return this.formatOutputData({ key: 'LIKE_RANDOM_POST' }, { data: [] });
      }

      const providerUser = await this.linkedinFinderService.getRetrieveProfile(userFollowIdentifier);
      const providerId = providerUser.provider_id;

      const getListPostOfUser = await unipileClient.listAllPostOfUser(accountId, providerId);

      const listSocialId = getListPostOfUser?.items?.map((item) => item?.social_id);

      if (listSocialId?.length === 0) {
        return this.formatOutputData({ key: 'LIKE_RANDOM_POST' }, { data: [] });
      }

      let randomItem = '';

      if (random) {
        randomItem = listSocialId[Math.floor(Math.random() * listSocialId.length)];
      } else {
        randomItem = listSocialId[0];
      }

      const postSocicalData = await unipileClient.reactAPost(
        accountId,
        randomItem,
        reactIcon || LinkedInReactIconEnum.LIKE
      );

      return this.formatOutputData(
        { key: 'LIKE_RANDOM_POST' },
        {
          data: {
            reactData: postSocicalData,
            postData: getListPostOfUser?.items?.find((item) => item.social_id === randomItem),
          },
        }
      );
    } catch (e) {
      console.log('FOLLOW_AN_USER', e);
      return this.throwCommonMessage('LIKE_RANDOM_POST', e);
    }
  }

  async getAttendeeProfileImage(userId: string, attendeeId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const accountId = user.unipileAccountId;
      if (!accountId) {
        return this.formatOutputData({ key: 'GET_LINKEDIN_MESSAGE' }, { data: [] });
      }

      const url = `${unipileConfig.UNIPILE_BASE_URL}/api/v1/chat_attendees/${attendeeId}/picture`;
      const response = await lastValueFrom(
        this.httpService.get(url, {
          responseType: 'arraybuffer',
          headers: {
            'X-API-KEY': unipileConfig.UNIPILE_KEY,
          },
        })
      );
      return response.data;
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('GET_ATTENDEE_PROFILE_IMAGE', e);
    }
  }

  async linkedInIncomingMessageWebhook(body: any) {
    try {
      const { account_id } = body;
      const user = await this.userRepository.findOne({
        where: {
          unipileAccountId: account_id,
        },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (user.linkedinLastMessage?.message_id === body?.message_id) {
        return;
      }

      await this.userRepository.update(user.id, {
        totalLinkedinMessage: user.totalLinkedinMessage + 1,
        linkedinLastMessage: body,
      });
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('LINKEDIN_INCOMING_MESSAGE_WEBHOOK', e);
    }
  }

  async getTotalLinkedInMessage(userId: string) {
    try {
      if (!['staging', 'prod'].includes(this.appEnv)) {
        return this.formatOutputData({ key: 'GET_TOTAL_LINKEDIN_MESSAGE' }, { data: { totalLinkedinMessage: 0 } });
      }
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const totalLikedinMessage = user.totalLinkedinMessage;

      return this.formatOutputData(
        { key: 'GET_TOTAL_LINKEDIN_MESSAGE' },
        { data: { totalLinkedinMessage: totalLikedinMessage } }
      );
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('GET_TOTAL_LINKEDIN_MESSAGE', e);
    }
  }

  async createSequenceTask(userId: string, body: CreateSequenceTaskDto) {
    try {
      const today = new Date();
      today.setHours(23, 59, 59, 999);

      const groupId = body.groupId || uuid();
      const tasks: any[] = [];
      body.participants.forEach((participant: any) => {
        const mergeTags = {
          recipient: {
            ...participant,
            firstName: participant?.firstName?.trim() ? participant?.firstName : participant?.name,
            clientCorporation: { name: participant?.clientCorporation?.name || participant?.companyName },
            occupation: participant?.occupation || participant?.contactTitle,
          },
        };

        tasks.push({
          participant,
          content: getMergeTagsContent(body.content, mergeTags),
          type: body.type,
          sequenceStep: body.sequenceStepId,
          status: body.status,
          name: body.name,
          dueDate: body.dueDate || today,
          creatingType: body.creatingType || SequenceTaskCreatingType.MANUAL,
          groupId,
          userId,
        });
      });
      const result = await this.sequenceTaskRepository.insert(tasks);

      return this.formatOutputData({ key: 'CREATE_SEQUENCE_TASK' }, { data: result.identifiers.map(({ id }) => id) });
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('CREATE_SEQUENCE_TASK', e);
    }
  }

  async getListSequenceTasks(userId: string, query: GetDataSequenceTaskDto) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }
      const offset = (query?.page - 1) * query.limit;

      const queryBuilder = this.sequenceRepository
        .createQueryBuilder('sequence')
        .leftJoin('sequence.user', 'user')
        .innerJoinAndSelect('sequence.sequenceTasks', 'tasks')
        .select([
          'sequence.id',
          'sequence.name',
          'sequence.isMarkedAsCompleted',
          'sequence.status',
          'sequence.createdAt',
          'sequence.updatedAt',
          'sequence.triggerAt',
          'tasks',
        ])
        .groupBy('sequence.id')
        .addGroupBy('tasks.id')
        .having('COUNT(tasks.id) > 0')
        .where('user.id = :userId', { userId })
        .take(query.limit || 10)
        .skip(offset || 0);

      if (query.sequenceName) {
        queryBuilder.andWhere('sequence.name ILIKE :sequenceName', { sequenceName: `%${query.sequenceName}%` });
      }

      if (query.status) {
        queryBuilder.andWhere('tasks.status = :status', { status: query.status });
      }

      if (query.type) {
        queryBuilder.andWhere('tasks.type = :type', { type: query.type });
      }

      if (query.dueDate) {
        queryBuilder.andWhere('tasks.dueDate <= :dueDate', { dueDate: query.dueDate });
      }

      const [sequences, total] = await queryBuilder.getManyAndCount();

      return this.formatOutputData(
        { key: 'GET_LIST_TASK_BY_USER' },
        {
          data: {
            items: sequences,
            pagination: {
              page: query.page,
              limit: query.limit,
              total,
              totalPages: Math.ceil(total / query.limit) || 0,
            },
          },
        }
      );
    } catch (e) {
      console.log('GET_LIST_TASK_BY_USER', e);
      return this.throwCommonMessage('GET_LIST_TASK_BY_USER', e);
    }
  }

  async getSequenceTasks(userId: string, query: GetSequenceTasksDto) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }
      const offset = (query?.page - 1) * query.limit;

      const groupIdsQuery = this.sequenceTaskRepository
        .createQueryBuilder('task')
        .select(['task.group_id AS group_id'])
        .where('(task.user_id::text = :userId AND task.group_id IS NOT NULL)', { userId: userId })
        .groupBy('task.group_id')
        .orderBy('MAX(task.due_date)', 'ASC')
        .addOrderBy('task.group_id', 'DESC')
        .take(query.limit || 10)
        .skip(offset || 0);

      const totalCountQuery = this.sequenceTaskRepository
        .createQueryBuilder('task')
        .select('COUNT(DISTINCT task.group_id)', 'total')
        .where('(task.user_id::text = :userId AND task.group_id IS NOT NULL)', { userId: userId });

      if (query.name) {
        groupIdsQuery.andWhere('task.name ILIKE :name', { name: `%${query.name}%` });
        totalCountQuery.andWhere('task.name ILIKE :name', { name: `%${query.name}%` });
      }

      if (query.status) {
        groupIdsQuery.andWhere('task.status = :status', { status: query.status });
        totalCountQuery.andWhere('task.status = :status', { status: query.status });
      }

      if (query.type) {
        groupIdsQuery.andWhere('task.type = :type', { type: query.type });
        totalCountQuery.andWhere('task.type = :type', { type: query.type });
      }

      if (query.creatingType) {
        groupIdsQuery.andWhere('task.creatingType = :type', { type: query.creatingType });
        totalCountQuery.andWhere('task.creatingType = :type', { type: query.creatingType });
      }

      if (query.dueDate) {
        groupIdsQuery.andWhere('task.dueDate <= :dueDate', { dueDate: query.dueDate });
        totalCountQuery.andWhere('task.dueDate <= :dueDate', { dueDate: query.dueDate });
      }

      const [groupIds, totalResult] = await Promise.all([groupIdsQuery.getRawMany(), totalCountQuery.getRawOne()]);

      if (!groupIds.length) {
        return { groups: [], total: 0 };
      }
      const total = totalResult?.total || 0;

      const tasksByGroup = await Promise.all(
        groupIds.map(async (group) => {
          const queryBuilder = this.sequenceTaskRepository
            .createQueryBuilder('st')
            .where('(st.group_id = :groupId)', { groupId: group.group_id });

          if (query.name) {
            queryBuilder.andWhere('st.name ILIKE :name', { name: `%${query.name}%` });
          }

          if (query.status) {
            queryBuilder.andWhere('st.status = :status', { status: query.status });
          }

          if (query.type) {
            queryBuilder.andWhere('st.type = :type', { type: query.type });
          }

          if (query.creatingType) {
            queryBuilder.andWhere('st.creatingType = :type', { type: query.creatingType });
          }

          if (query.dueDate) {
            queryBuilder.andWhere('st.dueDate <= :dueDate', { dueDate: query.dueDate });
          }

          const tasks = await queryBuilder.getMany();

          return {
            groupId: group.group_id,
            tasks,
          };
        })
      );

      return this.formatOutputData(
        { key: 'GET_LIST_TASKS_BY_USER' },
        {
          data: {
            items: tasksByGroup,
            pagination: {
              page: query.page,
              limit: query.limit,
              total: Number(total),
              totalPages: Math.ceil(total / (query.limit || 10)) || 0,
            },
          },
        }
      );
    } catch (e) {
      console.log('GET_LIST_TASK_BY_USER', e);
      return this.throwCommonMessage('GET_LIST_TASKS_BY_USER', e);
    }
  }

  async getTaskDetail(taskId: string) {
    try {
      const item = await this.sequenceTaskRepository.findOne({
        where: { id: taskId },
      });

      return this.formatOutputData(
        { key: 'GET_TASK_DETAIL' },
        {
          data: item,
        }
      );
    } catch (e) {
      this.logger.error(e);
      return this.throwCommonMessage('GET_TASK_DETAIL', e);
    }
  }

  async updateSequenceTask(userId: string, taskId: string, body: UpdateSequenceTaskDto) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const task = await this.sequenceTaskRepository.findOne({ where: { id: taskId } });
      if (!task) {
        throw new NotFoundException('Task not found');
      }

      await this.sequenceTaskRepository.update(taskId, body);

      return this.formatOutputData(
        { key: 'UPDATE_SEQUENCE_TASK' },
        {
          data: {},
        }
      );
    } catch (e) {
      console.log('UPDATE_SEQUENCE_TASK', e);
      return this.throwCommonMessage('UPDATE_SEQUENCE_TASK', e);
    }
  }

  async updateTasksByGroupId(userId: string, groupId: string, body: UpdateSequenceTaskDto) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      await this.sequenceTaskRepository.update({ groupId }, body);

      return this.formatOutputData(
        { key: 'UPDATE_TASK_BY_GROUP_ID' },
        {
          data: {},
        }
      );
    } catch (e) {
      console.log('UPDATE_TASK_BY_GROUP_ID', e);
      return this.throwCommonMessage('UPDATE_TASK_BY_GROUP_ID', e);
    }
  }

  async deleteSequenceTask(userId: string, taskId: string) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const task = await this.sequenceTaskRepository.findOne({ where: { id: taskId } });
      if (!task) {
        throw new NotFoundException('Task not found');
      }

      this.sequenceTaskRepository.delete({ id: task.id });

      return this.formatOutputData(
        { key: 'DELETE_SEQUENCE_TASK' },
        {
          data: {},
        }
      );
    } catch (e) {
      console.log('DELETE_SEQUENCE_TASK', e);
      return this.throwCommonMessage('DELETE_SEQUENCE_TASK', e);
    }
  }

  async getTasksTotalByType(userId: string, status?: string) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const data = await this.sequenceTaskRepository
        .createQueryBuilder('task')
        .leftJoin('task.sequenceStep', 'sequenceStep')
        .leftJoin('sequenceStep.sequence', 'sequence')
        .leftJoin('sequence.user', 'user')
        .where('task.user_id = :userId', { userId })
        .andWhere('task.group_id IS NOT NULL')
        .andWhere(status ? 'task.status = :status' : '1=1', { status })
        .select('task.type', 'type')
        .addSelect('COUNT(task.id)', 'count')
        .groupBy('task.type')
        .getRawMany();

      return this.formatOutputData(
        { key: 'GET_TASK_TOTAL_BY_TYPE' },
        {
          data: data,
        }
      );
    } catch (e) {
      console.log('GET_TASK_TOTAL_BY_TYPE', e);
      return this.throwCommonMessage('GET_TASK_TOTAL_BY_TYPE', e);
    }
  }

  async getLinkedinData(userId: string, linkedinUrl: string) {
    try {
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const linkedInIdentifier = await unipileClient.getLinkedInIdentifier(linkedinUrl);
      const linkedInProfile = await this.linkedinFinderService.getRetrieveProfile(linkedInIdentifier);

      return this.formatOutputData(
        { key: 'GET_LINKEDIN_DATA' },
        {
          data: linkedInProfile,
        }
      );
    } catch (e) {
      console.log('GET_LINKEDIN_DATA', e);
      return this.throwCommonMessage('GET_LINKEDIN_DATA', e);
    }
  }

  async bulkSendOnboardingEmails(onboardingEmailRequests: { password: string; toEmail: string }[]) {
    const { fromEmail, invitationSubject } = SendGridConfig;
    try {
      const baseUrl = process.env.CLIENT_URL;
      const loginUrl = `${baseUrl}/login`;
      const messages = onboardingEmailRequests.map(({ password, toEmail }) => ({
        to: toEmail,
        from: {
          name: 'Zileo',
          email: fromEmail,
        }, // Set your verified sender email
        subject: invitationSubject,
        html: `<div
  style="
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
      Ubuntu, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: -0.005em;
    color: #091e42;
    line-height: 20px;
    background: #ffffff;
    height: 100%;
    width: 100%;
  "
>
  <table
    width="100%"
    border="0"
    cellspacing="0"
    cellpadding="0"
    style="border-collapse: collapse"
  >
    <tbody>
      <tr>
        <td align="center">
          <div style="max-width: 520px; margin: 0 auto">
            <div
              style="
                vertical-align: top;
                text-align: left;
                font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto,
                  Oxygen, Ubuntu, Fira Sans, Droid Sans, Helvetica Neue,
                  sans-serif;
                font-size: 14px;
                font-weight: 400;
                letter-spacing: -0.005em;
                color: #091e42;
                line-height: 20px;
              "
            >
              <div
                style="
                  border: solid #ebecf0 1px;
                  border-bottom: none;
                  border-radius: 4px 4px 0 0;
                  max-width: 520px;
                "
              >
                <table
                  style="
                    width: 100%;
                    border-radius: 4px 4px 0 0;
                    border-collapse: collapse;
                  "
                  width="100%"
                >
                  <tbody>
                    <tr>
                      <td>
                        <img
                          src="${baseUrl}/assets/logo-9bb5a7de.png"
                          height="45"
                          style="
                            border: 0;
                            line-height: 100%;
                            outline: none;
                            text-decoration: none;
                            height: 100%;
                            max-height: 45px;
                            padding: 27px 0px 20px 40px;
                          "
                          border="0"
                          role="presentation"
                          alt="Zileo logo"
                          class="CToWUd"
                          data-bit="iit"
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div
                style="
                  margin-bottom: 32px;
                  background: #fafbfc;
                  padding: 40px;
                  border-radius: 0 0 4px 4px;
                  border: solid #ebecf0 1px;
                  border-top: none;
                "
              >
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  style="border-collapse: collapse"
                >
                  <tbody>
                    <tr>
                      <td align="center">
                        <div style="max-width: 520px; margin: 0 auto">
                          <table style="border-collapse: collapse">
                            <tbody>
                              <tr>
                                <td>
                                  <h1 style="margin: 0px; text-align: left">
                                    <div style="line-height: 33px">
                                      Admin invited you to join them in Zileo
                                    </div>
                                  </h1>
                                </td>
                              </tr>
                              <tr>
                                <td>
                                  <div
                                    style="
                                      font-family: -apple-system,
                                        BlinkMacSystemFont, Segoe UI, Roboto,
                                        Oxygen, Ubuntu, Fira Sans, Droid Sans,
                                        Helvetica Neue, sans-serif;
                                      font-size: 14px;
                                      font-weight: 400;
                                      letter-spacing: -0.005em;
                                      color: #091e42;
                                      line-height: 20px;
                                      margin-top: 16px;
                                      text-align: left;
                                    "
                                  >
                                    Get started with Zileo to simplify hiring
                                    and discover top talent faster. Below is your logins and please note when you first login, you will be required to update your password.
                                  </div>
                                </td>
                              </tr>
                              <tr>
                                <td>
                                  <div style="display: flex; margin-top: 24px">
                                    <div>Username: <strong>${toEmail}</strong></div>
                                  </div>
                                </td>
                              </tr>
                              <tr>
                                <td>
                                  <div style="display: flex; margin-top: 24px">
                                    <div>Password: <strong>${password}</strong></div>
                                  </div>
                                </td>
                              </tr>
                              <tr>
                                <td>
                                  <div style="display: flex; margin-top: 24px">
                                    <tr>
                                      <td
                                        style="border-radius: 6px"
                                        bgcolor="#1a82e2"
                                        align="center"
                                      >
                                        <a
                                          style="
                                            display: inline-block;
                                            padding: 16px 36px;
                                            font-family: 'Source Sans Pro',
                                              Helvetica, Arial, sans-serif;
                                            font-size: 16px;
                                            color: rgb(
                                              255,
                                              255,
                                              255
                                            ) !important;
                                            text-decoration: none;
                                            border-radius: 6px;
                                          "
                                          data-auth="NotApplicable"
                                          rel="noopener noreferrer"
                                          target="_blank"
                                          href="${loginUrl}"
                                          title="${loginUrl}"
                                          data-linkindex="0"
                                          >Login Now</a
                                        >
                                      </td>
                                    </tr>
                                  </div>
                                </td>
                              </tr>
                              <tr>
                                <td>
                                  <div
                                    style="
                                      font-family: -apple-system,
                                        BlinkMacSystemFont, Segoe UI, Roboto,
                                        Oxygen, Ubuntu, Fira Sans, Droid Sans,
                                        Helvetica Neue, sans-serif;
                                      font-size: 14px;
                                      font-weight: 400;
                                      letter-spacing: -0.005em;
                                      color: #091e42;
                                      line-height: 20px;
                                      margin-top: 24px;
                                    "
                                  >
                                    <span
                                      style="
                                        font-family: -apple-system,
                                          BlinkMacSystemFont, Segoe UI, Roboto,
                                          Oxygen, Ubuntu, Fira Sans, Droid Sans,
                                          Helvetica Neue, sans-serif;
                                        font-size: 14px;
                                        font-weight: 600;
                                        letter-spacing: -0.003em;
                                        color: #172b4d;
                                        line-height: 16px;
                                      "
                                      >What is Zileo?</span
                                    >
                                    A platform that streamlines hiring with Job
                                    Aggregation & Alerts, ATS Integrations, and
                                    Corporate Data Mining
                                  </div>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`,
      }));
      await sgMail.send(messages);
      return true;
    } catch (error) {
      this.logger.error(error.message);
      console.error(error.response.body);
      if (error.code === HttpStatus.BAD_REQUEST) {
        throw new BadRequestException('Invalid email address');
      }
      throw error;
    }
  }


  async countActiveSequence(userId: string, filter: any) {
    const { country, fromDate, toDate } = filter;
    const whereConditions = [];

    if (userId) {
      whereConditions.push(['(s.user_id = :userId)', { userId }]);
    }
    if (fromDate) {
      whereConditions.push(['(s.created_at >= :fromDate)', { fromDate }]);
    }
    if (toDate) {
      whereConditions.push(['(s.created_at <= :toDate)', { toDate }]);
    }

    const queryBuilder = this.sequenceRepository
      .createQueryBuilder('s')
      .where("s.status =: status", {status: SequenceStatus.LIVE})
      .select('COUNT(s.id)', 'count');

    whereConditions.forEach(([condition, variables], i) => {
      if (i === 0) {
        queryBuilder.where(condition, variables);
      } else {
        queryBuilder.andWhere(condition, variables);
      }
    });

    const totalData = await queryBuilder.getRawOne();

    const monthlyCounts = await queryBuilder
      .clone()
      .select([`DATE_TRUNC('month', s.created_at) as month`, 'COUNT(id) as count'])
      .groupBy(`DATE_TRUNC('month', s.created_at)`)
      .orderBy(`DATE_TRUNC('month', s.created_at)`, 'ASC')
      .getRawMany<{
        month: string;
        count: number;
      }>();

    const dataByMonth = monthlyCounts.map((item) => ({
      month: item.month,
      count: item.count,
    }));

    return this.formatOutputData(
      { key: 'STATS_SEQUENCE_ACTIVE' },
      {
        data: {
          totalCount: totalData.count,
          monthData: dataByMonth,
        },
      }
    );
  }
}

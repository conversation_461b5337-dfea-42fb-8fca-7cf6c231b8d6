import {
  BaseEntity,
  Column,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  <PERSON>in<PERSON>olum<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from 'src/modules/user/entities/user.entity';

export enum EmailSequenceStatus {
  LIVE = 'LIVE',
  STOP = 'STOP',
}

@Entity({ name: 'email_sequences' })
export class EmailSequenceEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'name', nullable: true })
  name: string;

  @Column({ name: 'status', nullable: true, enum: EmailSequenceStatus })
  status: EmailSequenceStatus;

  @Column({ name: 'step_count', nullable: true })
  stepCount: number;

  @Column({ name: 'pending_count', nullable: true })
  pendingCount: number;

  @Column({ name: 'sent_count', nullable: true, default: 0 })
  sentCount: number;

  @Column({ name: 'replied_count', nullable: true, default: 0 })
  repliedCount: number;

  @Column({ name: 'stop_count', nullable: true, default: 0 })
  stopCount: number;

  @Column({ name: 'delivered', nullable: true, default: 0 })
  delivered: number;

  @Column({ name: 'dropped', nullable: true, default: 0 })
  dropped: number;

  @Column({ name: 'bounce', nullable: true, default: 0 })
  bounce: number;

  @Column({ name: 'blocked', nullable: true, default: 0 })
  blocked: number;

  @Column({ name: 'deferred', nullable: true, default: 0  })
  deferred: number;

  @Column({ name: 'spam_report', nullable: true, default: 0  })
  spamReport: number;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  // @OneToMany(() => EmailEntity, (email) => email.emailSequences, {
  //   cascade: false,
  //   onDelete: 'SET NULL',
  // })
  // @JoinColumn({ referencedColumnName: 'email_sequence_id' })
  // email: EmailEntity[];

  @ManyToOne(() => UserEntity, (email) => email)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}

import {
  BaseEntity,
  Column,
  <PERSON>reateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SequenceEntity } from './sequence.entity';
import { UserSignature } from '../../user/entities/user-signature.entity';
import { StopRulesDto } from '../dto/upsert-email.dto';
import { SequenceInstanceEntity } from './sequence-instance.entity';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { RecipientInfo, SequenceStepDelay } from '../dto/send-email.dto';
import { SequenceStepTaskEntity } from './sequence-step-task.entity';
import { ParticipantsDto } from '../dto/sequence.dto';
import { SequenceStepEntity } from './sequence-step.entity';

export enum SequenceTaskTypeEnum {
  CALL = 'Call',
  EMAIL = 'Email',
  LINKEDIN = 'LinkedIn',
  MESSAGE = 'Message',
  RESEARCH = 'Research',
  QUALIFY = 'Qualify',
  PROSPECT = 'Prospect',
  OTHER = 'Other'
}

export enum SequenceTaskStatusEnum {
  PENDING = "Pending",
  COMPLETED = "Completed",
  DECLINED = "Declined",
}

export enum SequenceTaskCreatingType {
  MANUAL = 'Manual',
  EXTENDED = 'Extended',
  SEQUENCE = 'Sequence',
}

@Entity({ name: 'sequence_tasks' })
export class SequenceTaskEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'participant', type: 'jsonb', nullable: true })
  participant: RecipientInfo;

  @Column({ name: 'name', nullable: true })
  name: string;

  @Column({ name: 'content', nullable: true })
  content: string;

  @Column({ name: 'type', type: 'enum', enum: SequenceTaskTypeEnum, nullable: true })
  type: SequenceTaskTypeEnum;

  @Column({ name: 'creating_type', type: 'enum', enum: SequenceTaskCreatingType, nullable: true })
  creatingType: SequenceTaskCreatingType;

  @ManyToOne(() => SequenceStepEntity, (sequenceStep) => sequenceStep, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_step_id', foreignKeyConstraintName: 'fk_seq_stss' })
  sequenceStep: SequenceStepEntity;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ name: 'status', type: 'enum', enum: SequenceTaskStatusEnum, nullable: true })
  status: SequenceTaskStatusEnum;

  @ManyToOne(() => SequenceEntity, (sequence) => sequence, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_id'})
  sequence: SequenceEntity;

  @Column({ name: 'due_date', nullable: true, type: 'timestamptz' })
  dueDate: Date;

  @Column({ name: 'group_id', nullable: true })
  groupId: string;

  @Column({ name: 'user_id', nullable: true })
  userId: string;
}

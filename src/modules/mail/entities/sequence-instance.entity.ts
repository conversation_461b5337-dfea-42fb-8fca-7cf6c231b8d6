import {
  BaseEntity,
  Column,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { SequenceEntity } from './sequence.entity';
import { SequenceStepEntity } from './sequence-step.entity';
import { RecipientInfo } from '../dto/send-email.dto';
import { UserEntity } from 'src/modules/user/entities/user.entity';

export enum SequenceStepStatus {
  PENDING = 'PENDING',
  SCHEDULED = 'SCHEDULED',
  SENT = 'SENT',
  NOT_SENT = 'NOT_SENT',
  ERROR = 'ERROR',
  STOP = 'STOP',
  REPLIED = 'REPLIED',
  OFF = 'OFF',
  LINKEDIN_REACH_LIMIT = 'LINKEDIN_REACH_LIMIT'
}

export enum SequenceStepFlag {
  INVITATION_SENT = 'INVITATION_SENT',
  INVITATION_ACCEPTED = 'INVITATION_ACCEPTED',
  INVITATION_OVERTIME = 'INVITATION_OVERTIME',
}

@Entity({ name: 'sequence_instances' })
@Index('idx_sequence_instance_sent_ids', { synchronize: false })
@Index('idx_sequence_instance_status', ['status'])
export class SequenceInstanceEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => SequenceEntity, (sequence) => sequence, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_id', foreignKeyConstraintName: 'fk_seq_is' })
  sequence: SequenceEntity;

  @ManyToOne(() => SequenceStepEntity, (sequenceStep) => sequenceStep, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_step_id', foreignKeyConstraintName: 'fk_seq_iss' })
  sequenceStep: SequenceStepEntity;

  @Column({ name: 'status', type: 'enum', enum: SequenceStepStatus, default: SequenceStepStatus.PENDING })
  status: SequenceStepStatus;

  @Column({ name: 'recipients', type: 'jsonb', nullable: true })
  recipients: RecipientInfo[];

  @Column({ name: 'hotlist_ids', type: 'jsonb', nullable: true })
  hotlistIds: string[];

  @Column({ name: 'short_list_ids', type: 'jsonb', nullable: true })
  shortListIds: string[];

  @Column({ name: 'contact_list_ids', type: 'jsonb', nullable: true })
  contactListIds: string[];

  @Column({ name: 'skip_recipients', type: 'jsonb', nullable: true })
  skipRecipients: { email?: string, type: string, domain?: string }[];

  @Column({ name: 'repliers', type: 'jsonb', nullable: true })
  repliers: string[];

  @Column({ name: 'sent_ids', type: 'jsonb', nullable: true })
  sentIds: string[];

  @Column({ name: 'version', nullable: false })
  version: number;

  @Column({ name: 'flag', type: 'enum', enum: SequenceStepFlag, nullable: true })
  flag: SequenceStepFlag;

  @ManyToOne(() => UserEntity, (email) => email)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({ name: 'scheduled_at', nullable: true, type: 'timestamptz' })
  scheduledAt: Date;

  @Column({ name: 'executed_at', nullable: true, type: 'timestamptz' })
  executedAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

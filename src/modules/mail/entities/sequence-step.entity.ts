import {
  <PERSON><PERSON><PERSON>ty,
  <PERSON>umn,
  <PERSON>reateDate<PERSON><PERSON><PERSON>n,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SequenceEntity } from './sequence.entity';
import { UserSignature } from '../../user/entities/user-signature.entity';
import { StopRulesDto } from '../dto/upsert-email.dto';
import { SequenceInstanceEntity } from './sequence-instance.entity';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { SequenceStepDelay } from '../dto/send-email.dto';
import { SequenceStepTaskEntity } from './sequence-step-task.entity';
import { SequenceTaskEntity } from './sequence-tasks.entity';
import { CrmContactSequenceStepEntity } from '../../crm/entities/crm-contact-sequence-step.entity';

export enum SequenceStepStatus {
  PENDING = 'PENDING',
  SCHEDULED = 'SCHEDULED',
  SENT = 'SENT',
  NOT_SENT = 'NOT_SENT',
  ERROR = 'ERROR',
  STOP = 'STOP',
  REPLIED = 'REPLIED',
  OFF = 'OFF'
}

export enum SequenceStepType {
  EMAIL = 'EMAIL',
  NOTE = 'NOTE',
  LINKEDIN_CONNECTION_REQUEST = 'LINKEDIN_CONNECTION_REQUEST',
  TASK = 'TASK'
}

export enum SequenceStepDelayUnit {
  DAY = 'DAY',
  HOUR = 'HOUR',
}

@Entity({ name: 'sequence_steps' })
export class SequenceStepEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => SequenceEntity, (sequence) => sequence, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_id', foreignKeyConstraintName: 'fk_seq_sss' })
  sequence: SequenceEntity;

  @OneToMany(() => SequenceInstanceEntity, sequenceInstance => sequenceInstance.sequenceStep)
  sequenceInstances: SequenceInstanceEntity[];

  @OneToMany(() => SequenceStepTaskEntity, (task) => task.sequenceStep, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'sequence_step_id' })
  sequenceStepTasks: SequenceStepTaskEntity[];

  @Column({ name: 'type', type: 'enum', enum: SequenceStepType, default: SequenceStepType.EMAIL })
  type: SequenceStepType;

  @Column({ name: 'subject', nullable: true })
  subject: string;

  @Column({ name: 'content', nullable: true })
  content: string;

  @Column({ name: 'delays', type: 'jsonb', nullable: true })
  delays: SequenceStepDelay[];

  @Column({ name: 'step_index', nullable: false })
  stepIndex: number;

  // If in the future we want to add a template for each email, this field will be useful
  @Column({ name: 'template_name', default: 'candidate_exploration.template.ts' })
  templateName: string;

  @ManyToOne(() => UserSignature, (userSignature) => userSignature)
  @JoinColumn({ name: 'user_signature', foreignKeyConstraintName: 'fk_seq_ssus' })
  userSignature: UserSignature;

  @Column({ name: 'job_board_id', nullable: true })
  jobBoardId: string;

  @Column({ name: 'external_job_id', nullable: true })
  externalJobId: string;

  @Column({ name: 'crm_lead_id', type: 'uuid', nullable: true })
  crmLeadId: string;

  @Column({ name: 'placeholders', type: 'jsonb', nullable: true })
  placeholders: object;

  @Column({ name: 'stop_rules', type: 'jsonb', nullable: true })
  stopRules: StopRulesDto[];

  @ManyToOne(() => UserEntity, (email) => email)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ name: 'thread_id', nullable: true })
  threadId: string;

  @Column({ name: 'attachments', type: 'jsonb', nullable: true })
  attachments: any[];

  @Column({ name: 'name', nullable: true })
  name: string;

  @OneToMany(() => SequenceTaskEntity, (task) => task.sequenceStep, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'sequence_task_id' })
  sequenceTasks: SequenceTaskEntity[];

  @OneToMany(() => CrmContactSequenceStepEntity, (contactSequenceStep) => contactSequenceStep.sequenceStep)
  contactSequenceSteps: CrmContactSequenceStepEntity[];
}

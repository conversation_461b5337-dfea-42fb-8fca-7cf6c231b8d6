import {
  <PERSON><PERSON><PERSON>ty,
  <PERSON>umn,
  <PERSON>reateDate<PERSON><PERSON><PERSON>n,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { ScheduleInformationDto, StopRulesDto } from '../dto/upsert-email.dto';
import { SequenceTemplateEntity } from './sequence-template.entity';
import { ParticipantsDto } from '../dto/sequence.dto';
import { SequenceTaskEntity } from './sequence-tasks.entity';
import { CrmContactSequenceEntity } from '../../crm/entities/crm-contact-sequence.entity';
import { CrmLeadEntity } from 'src/modules/crm/entities/crm-lead.entity';

export enum SequenceStatus {
  LIVE = 'LIVE',
  STOP = 'STOP',
  DRAFT = 'DRAFT',
}

export enum SequenceEvent {
  MESSAGE_OPENED = 'message.opened',
  MESSAGE_LINK_CLICKED = 'message.link_clicked',
  THREAD_REPLIED = 'thread_required',
  MESSAGE_BOUNCE_DETECTED = 'message.bounce_detected',
}

@Entity({ name: 'sequences' })
export class SequenceEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'name', nullable: true })
  name: string;

  @Column({ name: 'status', nullable: true, enum: SequenceStatus })
  status: SequenceStatus;

  @Column({ name: 'is_marked_as_completed', default: false, nullable: true })
  isMarkedAsCompleted: boolean;

  @Column({ name: 'step_count', nullable: true })
  stepCount: number;

  @Column({ name: 'pending_count', nullable: true })
  pendingCount: number;

  @Column({ name: 'sent_count', nullable: true, default: 0 })
  sentCount: number;

  @Column({ name: 'replied_count', nullable: true, default: 0 })
  repliedCount: number;

  @Column({ name: 'stop_count', nullable: true, default: 0 })
  stopCount: number;

  @Column({ name: 'delivered', nullable: true, default: 0 })
  delivered: number;

  @Column({ name: 'dropped', nullable: true, default: 0 })
  dropped: number;

  @Column({ name: 'bounced', nullable: true, default: 0 })
  bounced: number;

  @Column({ name: 'blocked', nullable: true, default: 0 })
  blocked: number;

  @Column({ name: 'opened', nullable: true, default: 0 })
  opened: number;

  @Column({ name: 'link_clicked', nullable: true, default: 0 })
  linkClicked: number;

  @Column({ name: 'deferred', nullable: true, default: 0 })
  deferred: number;

  @Column({ name: 'spam_report', nullable: true, default: 0 })
  spamReport: number;

  @ManyToOne(() => UserEntity, (email) => email)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({ name: 'raw_sequence', type: 'jsonb', nullable: true })
  rawSequence: object;

  @Column({ name: 'job_board_id', nullable: true })
  jobBoardId: string;

  @Column({ name: 'external_job_id', nullable: true })
  externalJobId: string;

  @Column({ name: 'crm_lead_id', type: 'uuid', nullable: true })
  crmLeadId: string;

  // @ManyToOne(() => CrmLeadEntity, { onDelete: 'SET NULL' })
  // @JoinColumn({ name: 'crm_lead_id' })
  // crmLead: CrmLeadEntity;

  @Column({ name: 'company_id', nullable: true })
  companyId: string;

  @Column({ name: 'created_from', nullable: true })
  createdFrom: string;

  @Column({ name: 'recipients', nullable: true })
  recipients: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ name: 'recreated_at', type: 'timestamptz', nullable: true })
  recreatedAt: Date;

  @Column({ name: 'is_stared', nullable: true, default: false })
  isStared: boolean;

  @Column({ name: 'company_name', nullable: true })
  companyName: string;

  @Column({ name: 'contact_names', nullable: true })
  contactNames: string;

  @Column({ name: 'country', nullable: true })
  country: string;

  @Column({ name: 'schedule_information', type: 'jsonb', nullable: true })
  scheduleInformation: ScheduleInformationDto;

  @Column({ name: 'trigger_at', nullable: true, type: 'timestamptz' })
  triggerAt: Date;

  @Column({ name: 'is_triggered', default: false })
  isTriggered: boolean;

  @Column({ name: 'sequence_template_id', nullable: true })
  sequenceTemplateId: string;

  @ManyToOne(() => SequenceTemplateEntity, (sequenceTemplate) => sequenceTemplate, {
    cascade: false,
    onDelete: 'CASCADE',
    nullable: true,
  })
  @JoinColumn({ name: 'sequence_template_id', foreignKeyConstraintName: 'fk_seq_stps' })
  sequenceTemplate: SequenceTemplateEntity;

  @Column({ name: 'time_zone', nullable: true })
  timeZone: string;

  @Column({ name: 'stop_rules', type: 'jsonb', nullable: true })
  stopRules: StopRulesDto[];

  //TODO: top participants of sequence
  @Column({ name: 'participants', type: 'jsonb', nullable: true })
  participants: ParticipantsDto;

  @Column({ name: 'skipped_emails', type: 'jsonb', nullable: true })
  skippedEmails: string[];

  @Column({ name: 'parent_id', nullable: true })
  parentId: string;

  @OneToMany(() => SequenceTaskEntity, (task) => task.sequence, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'sequence_task_id' })
  sequenceTasks: SequenceTaskEntity[];

  @OneToMany(() => CrmContactSequenceEntity, (contactSequence) => contactSequence.sequence)
  contactSequences: CrmContactSequenceEntity[];
}

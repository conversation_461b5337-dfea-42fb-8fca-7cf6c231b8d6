import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EmailSequenceEntity } from './email-sequence.entity';
import { UserEntity } from '../../user/entities/user.entity';

export enum EmailStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  ERROR = 'ERROR',
  STOP = 'STOP',
  REPLIED = 'REPLIED',
  OFF = 'OFF'
}

@Entity({ name: 'emails' })
export class EmailEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'subject', nullable: false })
  subject: string;

  @Column({ name: 'content', nullable: false })
  content: string;

  @Column({ name: 'recipient', type: 'jsonb', nullable: false })
  recipients: object;

  @Column({ name: 'reply_by', nullable: true })
  replyBy: string;

  @Column({ name: 'status', type: 'enum', enum: EmailStatus, default: EmailStatus.PENDING })
  status: EmailStatus;

  @Column({ name: 'delay', nullable: true, default: 1 })
  delay: number;

  @Column({ name: 'sequence', nullable: true })
  sequence: number;

  // If in the future we want to add a template for each email, this field will be useful
  @Column({ name: 'template_name', default: 'candidate_exploration.template.ts' })
  templateName: string;

  // unique mail id after sending email (created by sendgrid)
  @Column({ name: 'sent_id', nullable: true })
  sentId: string;

  @Column({ name: 'sent_at', nullable: true, type: 'timestamptz' })
  sentAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @ManyToOne(() => EmailSequenceEntity, (emailSequence) => emailSequence, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'email_sequence_id', foreignKeyConstraintName: 'fk_na' })
  emailSequences: EmailSequenceEntity;

  @ManyToOne(() => UserEntity, (email) => email)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({ name: 'job_board_id', nullable: true })
  jobBoardId: string;

  @Column({ name: 'external_job_id', nullable: true })
  externalJobId: string;
}

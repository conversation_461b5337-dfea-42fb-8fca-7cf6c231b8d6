import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity({ name: 'sequence_templates' })
export class SequenceTemplateEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: false})
  name: string;

  @Column({ type: 'varchar', length: 255, name: "organization_id", nullable: true})
  organizationId: string;

  @Column({ type: 'varchar'})
  content: string;

  @Column({ type: 'varchar', name: "modified_by"})
  modifiedBy: string;

  @Column({ type: 'varchar', name: "created_by"})
  createdBy: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

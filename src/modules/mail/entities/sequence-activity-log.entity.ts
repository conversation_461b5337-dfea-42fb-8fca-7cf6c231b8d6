import {
  BaseEntity,
  Column,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SequenceEntity } from './sequence.entity';
import { SequenceStepEntity } from './sequence-step.entity';

export enum SequenceActivityType {
  DEFERRED = 'deferred',
  BOUNCE = 'bounce',
  DROPPED = 'dropped',
  SPAM = 'spamreport',
  DELIVERED = 'delivered',
  SENT = 'sent',
  UPDATED = 'updated',
  CREATED = 'created',
  REPLIED = 'replied',
  STOPPED = 'stopped',
  LIVED = 'lived',
  BLOCKED = 'blocked',
  STOPPED_BY_USER = 'off',
  REPLIED_BY_HOTLIST = 'replied_by_hotlist',
  OPENED = 'opened',
  LINK_CLICKED = 'link_clicked',
  FAILED = 'failed',
  SKIPPED = 'skipped',
  AUTO_REPLIED = 'auto_replied',
  LINKEDIN_INVITATION_ACCEPTED = 'linkedin_invitation_accepted',
  MESSAGE = 'message'
}

@Entity({ name: 'sequence_activity_logs' })
export class SequenceActivityLogEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  //TODO: revert after work of Oliver
  // @Column({ name: 'type', type: 'enum', enum: SequenceActivityType, nullable: true })
  @Column({ name: 'type', nullable: true })
  type: SequenceActivityType;

  @ManyToOne(() => SequenceEntity, (sequence) => sequence, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_id', foreignKeyConstraintName: 'fk_seq_als' })
  sequence: SequenceEntity;

  @ManyToOne(() => SequenceStepEntity, (sequenceStep) => sequenceStep, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_step_id', foreignKeyConstraintName: 'fk_seq_alss' })
  sequenceStep: SequenceStepEntity;

  @Column({ name: 'content', type: 'jsonb', nullable: true })
  content: object;

  @Column({ name: 'occurred_at', nullable: true, type: 'timestamptz' })
  occurredAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

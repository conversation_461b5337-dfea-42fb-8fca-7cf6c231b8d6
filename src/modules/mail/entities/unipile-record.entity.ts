import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  <PERSON>inColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum RecordTypeEnum {
  USER = "USER",
  CHAT = 'CHAT',
}

@Entity({ name: 'unipile_records' })
export class UnipileRecordEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'record_id', nullable: true })
  recordId: string;

  @Column({ name: 'data ', type: 'jsonb', nullable: true })
  data: any;

  @Column({ name: 'record_type ', nullable: true, enum: RecordTypeEnum })
  recordType: RecordTypeEnum;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

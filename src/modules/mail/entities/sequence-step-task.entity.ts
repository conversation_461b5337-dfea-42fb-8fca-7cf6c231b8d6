import {
  BaseEntity,
  BeforeInsert,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SequenceEntity } from './sequence.entity';
import { SequenceStepEntity } from './sequence-step.entity';
import { SequenceInstanceEntity } from './sequence-instance.entity';
import { RecipientInfo } from '../dto/send-email.dto';
import { UserEntity } from 'src/modules/user/entities/user.entity';
import { createHash } from 'crypto';
import { v5 as uuidv5 } from 'uuid';

export enum SequenceStepStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  ERROR = 'ERROR',
  STOP = 'STOP',
  REPLIED = 'REPLIED',
  OFF = 'OFF'
}

@Entity({ name: 'sequence_step_tasks' })
@Index('idx_sequence_step_task_user_created', ['userId', 'createdAt'])
@Index('idx_sequence_step_task_execution', ['executedAt', 'scheduledAt'])
@Index('idx_sequence_step_task_type', ['type'])
@Index('idx_sequence_step_task_linkedin', ['type', 'action'])
@Index('idx_sequence_step_task_recipients', { synchronize: false })
export class SequenceStepTaskEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => SequenceEntity, (sequence) => sequence, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_id', foreignKeyConstraintName: 'fk_seq_sts' })
  sequence: SequenceEntity;

  @ManyToOne(() => SequenceStepEntity, (sequenceStep) => sequenceStep, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_step_id', foreignKeyConstraintName: 'fk_seq_stss' })
  sequenceStep: SequenceStepEntity;

  @ManyToOne(() => SequenceInstanceEntity, (sequenceInstance) => sequenceInstance, {
    cascade: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'sequence_instance_id', foreignKeyConstraintName: 'fk_seq_sti' })
  sequenceInstance: SequenceInstanceEntity;

  @Column({ name: 'recipients', type: 'jsonb', nullable: true })
  recipients: RecipientInfo[];

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => UserEntity, (email) => email)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({ name: 'scheduled_at', nullable: true, type: 'timestamptz' })
  scheduledAt: Date;

  @Column({ name: 'executed_at', nullable: true, type: 'timestamptz' })
  executedAt: Date;

  @Column({ name: 'attempts_made', nullable: true, default: 0 })
  attemptsMade: number;

  @Column({ name: 'type', nullable: true })
  type: string;

  @Column({ name: 'source_type', nullable: true })
  sourceType: string;

  @Column({ name: 'action', nullable: true })
  action: string;

  @Column({ name: 'extra_information', type: 'jsonb', nullable: true })
  extraInformation: any;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @BeforeInsert()
  generateId() {
    const data = `${this.sequence?.id}-${this.sequenceStep?.id}-${this.sequenceInstance?.id}-${JSON.stringify(this.recipients)}-${this.attemptsMade}`;
    
    // Create SHA-256 hash
    const hash = createHash('sha256').update(data).digest('hex');

    // Generate a UUID v5 from the hash (using a namespace)
    this.id = uuidv5(hash, uuidv5.URL);
  }
}

import { Body, Controller, Post, Req, UseGuards } from "@nestjs/common";
import { ApiBadRequestResponse, ApiNotFoundResponse, ApiTags, ApiUnauthorizedResponse } from "@nestjs/swagger";
import { SkipThrottle } from "@nestjs/throttler";
import { ResponseMessage } from "src/common/constants/common.constant";
import { BaseErrorResponseDto } from "src/common/dto/common.dto";
import { AuthenticationGuard } from "../auth/guards/auth.guard";
import { OpenAIService } from "../openai/openai.service";
import { GeminiService } from "./gemini.services";
import { EmailAnalyzeContent } from "./dto/email-analyze.dto";

@ApiTags('Gemini')
@Controller('gemini')
@SkipThrottle()
@ApiBadRequestResponse({
  description: ResponseMessage.Common.BAD_REQUEST,
  type: BaseErrorResponseDto,
})
@ApiNotFoundResponse({
  description: ResponseMessage.Common.NOT_FOUND,
  type: BaseErrorResponseDto,
})
@ApiUnauthorizedResponse({
  description: ResponseMessage.Common.UNAUTHORIZED,
  type: BaseErrorResponseDto,
})
// @UseGuards(AuthenticationGuard)
export class GeminiController {
  constructor(private readonly geminiService: GeminiService) {}

  @Post('mail-analytics')
  async generateChoices(@Body() body: EmailAnalyzeContent) {
    const pay = {
        emailContent: body.emailContent,
        emailSubject: body.emailSubject,
    }
    return this.geminiService.emailAnalyzeContent(pay);
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { GeminiConfig, OpenAIConfig } from 'src/configs/configs.constants';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { EMAIL_ANALYZE_RULES, EMAIL_SUBJECT_RULES, GEMINI_MODAL } from './gemini.constants';
import { EmailAnalyzeContent } from './dto/email-analyze.dto';

@Injectable()
export class GeminiService extends BaseAbstractService {
  private readonly logger = new Logger(GeminiService.name);

  private geminiOpenAI: GoogleGenerativeAI;
  private model;

  constructor(readonly i18nService: I18nService) {
    super(i18nService);

    this.geminiOpenAI = new GoogleGenerativeAI(GeminiConfig.apiKey);
    this.model = this.geminiOpenAI.getGenerativeModel({ model: GEMINI_MODAL });
  }

  async emailAnalyzeContent(body: EmailAnalyzeContent) {
    const prompt = `
        You are an assistant specialized in improving emails to prevent them from being marked as spam based on the initial email content provided:
            - Email subject: "${body.emailSubject}"
            - Email content: "${body.emailContent}"
        Output:
        An improved email based on the initial email.
        The output should be in JSON format:
        {
            "data": [
                {
                    "key": "subject_length",
                    "pass": "Pass or Fail. Mail subject length must be from 3 to 5 words, if within this range it will pass, otherwise it will fail. Note: The word is a single word, not a compound word, and is separated by spaces ... "
                    "why": "Reason why the message"
                },
                {
                    "key": 'subject_mobile_friendly',
                    "pass": 'Pass or Fail. If Subject Length Aim for 40 characters or less for optimal visibility it will pass',
                    "why": "Reason why the message",
                },
                {
                    "key": "message_length",
                    "pass": "Pass or Fail. Mail content length must be from 20 to 100 words, if within this range it will pass, otherwise it will fail. Note: The word is a single word, not a compound word, and is separated by spaces ... "
                    "why": "Reason why the message"
                },
                {
                    "key": "concise",
                    "pass": "Pass or Fail. If Content should be communicated in a focus on job vacancies, career skills ... then it will Pass otherwise it will Fail"
                    "why": "Reason why the message"
                },
                {
                    "key": "reading_level",
                    "pass": "Pass or Fail. If Content should be appropriate to the reading level of technical people. ... then it will Pass otherwise it will Fail"
                    "why": "Reason why the message"
                },
                {
                    "key": "long_sentences",
                    "pass": "Pass or Fail. If all sentences in the mail content have at most 15 words, it passes. If at least 1 sentence has more than 15 words, it fails. Note: The word is a single word, not a compound word, and is separated by spaces ..."
                    "why": "Reason why the message"
                },
                {
                  "key": "adverb",
                  "pass": "Pass or Fail. If the email content uses 3 or less adverbs and uses them sparingly then pass otherwise fail"
                  "why": "Reason why the message"
                },
                {
                    "key": "pain_point",
                    "pass": "Pass or Fail. If the email content shows empathy and understanding of the recipient's challenges, it passes, otherwise it fails."
                    "why": "Reason why the message"
                },
                {
                    "key": "value_proposition",
                    "pass": "Pass or Fail. If the email content communicates the unique benefits or advantages of your product or service, pass. Otherwise, forget it."
                    "why": "Reason why the message"
                },
                {
                  "key": "personalized",
                  "pass": "Pass or Fail. If the email content Focus on the recipient's needs, interests, and preferences then pass, otherwise fail"
                  "why": "Reason why the message"
                },
                {
                  "key": "social_proof",
                  "pass": "Pass or Fail. If the email content utilizes testimonials or examples of how others had positive experiences, it passes, otherwise it fails."
                  "why": "Reason why the message"
                },
            ],
            "rewrite_content": "",
            "rewrite_subject": "
            "pass_percentage": 75,
            "reading_time": "1 minute(s)"
        }

        Requirements:
            - Revise the initial email to avoid being marked as spam.
            - Ensure the email is written professionally and friendly.
            - Act as the person sending the email to the candidate.
            - Rewrite the email content to comply with the rules in ${JSON.stringify(EMAIL_ANALYZE_RULES)}.
            - Rewrite the email subject to comply with the rules in ${JSON.stringify(EMAIL_SUBJECT_RULES)}.
            - The rewritten email content must comply with the rules in ${JSON.stringify(EMAIL_ANALYZE_RULES)} and be formatted as plaintext.

        "data": Input data analysis results based on rules.
        "pass_percentage: The percentage of rules passed ( pass / (pass + fail)).
        "reading_time: The time required to read the improved email."
        "rewrite_content: The improved email content based on the initial email."
        "rewrite_subject: The improved email subject based on the initial email subject."
        `;
    const result = await this.model.generateContent(prompt);
    let data = result.response.text()
    if (data.includes('```json')) {
        data = data.replace(/```json/g, '');
        data = data.replace(/```/g, '');
    }

    return this.formatOutputData(
        { key: 'GENERATE_ANALYZE' },
        {
          data: JSON.parse(data)
        }
      );
  }
}

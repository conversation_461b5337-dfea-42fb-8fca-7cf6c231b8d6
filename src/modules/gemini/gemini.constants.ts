export const GEMINI_MODAL = 'gemini-2.0-flash-exp';

export const EMAIL_SUBJECT_RULES = [
  {
    key: 'subject_length',
    title: 'Subject length',
    description:
      'Mail subject length must be from 3 to 5 words, if within this range it will pass, otherwise it will fail. Note: The word is a single word, not a compound word, and is separated by spaces ... ',
  },
  {
    key: 'mobile_friendly',
    title: 'Mobile-friendly',
    description:
      'If Subject Length Aim for 40 characters or less for optimal visibility it will pass',
  },
]

export const EMAIL_ANALYZE_RULES = [
  {
    key: 'message_length',
    title: 'Message length',
    description:
      'Mail content length must be from 20 to 100 words, if within this range it will pass, otherwise it will fail. Note: The word is a single word, not a compound word, and is separated by spaces ...',
  },
  {
    key: 'concise',
    title: 'concise',
    description: 'Content should be communicated in a focus on job vacancies, career skills ...',
  },
  {
    key: 'reading_level',
    title: 'Reading level',
    description: 'The text should be appropriate to the reading level of technical people.',
  },
  {
    key: 'long_sentences',
    title: 'Long sentences',
    description: 'Aim for sentences that are around 15 words or less to increase readability.',
  },
  {
    key: 'adverb',
    title: 'adverbs',
    description: 'Aim for 3 adverbs or less and use them in moderation',
  },
  {
    key: 'pain_point',
    title: 'pain point',
    description: "Show empathy and understanding of the recipient's challenges",
  },
  {
    key: "value_proposition",
    title: 'value proposition',
    description: "Communicate the unique benefits or advantages of your product or service",
  },
  {
    key: "personalized",
    title: 'personalized',
    description: "Focus on the the recipient's needs, interests, and preferences",
  },
  {
    key: "social_proof",
    title: 'social proof',
    description: "Leverage testimonials or examples of how others had a positive experience",
  }
];

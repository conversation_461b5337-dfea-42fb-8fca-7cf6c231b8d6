import { Modu<PERSON> } from '@nestjs/common';
import { MyCacheModule } from '../cache/cache.module';
import { JobsModule } from '../jobs/jobs.module';
import { GeminiController } from './gemini.controller';
import { GeminiService } from './gemini.services';

@Module({
  imports: [MyCacheModule, JobsModule],
  controllers: [GeminiController],
  providers: [
    GeminiService,
  ],
  exports: [GeminiService],
})
export class GeminiModule {}

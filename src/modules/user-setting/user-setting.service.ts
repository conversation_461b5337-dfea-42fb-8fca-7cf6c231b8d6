import { BaseAbstractService } from 'src/base/base.abstract.service';
import { UserSettingRepository } from './user-setting.repository';
import { I18nService } from 'nestjs-i18n';
import { Injectable } from '@nestjs/common';
import { UpdateUserSettingDto } from './dto/update-user-setting.dto';
import { UserSettingEnum } from './entities/user-setting.entity';
import { BH_VACANCY_CHILDREN_PARENT_PARTS, BULLHORN_VACANCY_FORM_CONFIG } from './constants/bullhorn.const';
import { IJwtPayload } from '../auth/payloads/jwt-payload.payload';
import { BullhornConfigDto, BullhornVacancyChildDto, BullhornVacancyParentDto } from './dto/bullhorn-config.dto';
import { getMissingItems } from 'src/common/utils/helpers.util';

@Injectable()
export class UserSettingService extends BaseAbstractService {
  constructor(
    private readonly userSettingRepository: UserSettingRepository,
    private readonly i18nService: I18nService
  ) {
    super(i18nService);
  }

  async updateUserSetting(loginUserId: string, updateDto: UpdateUserSettingDto) {
    const userId = updateDto.updatedFor ?? loginUserId;
    try {
      const { name, value } = updateDto;
      const existedSetting = await this.userSettingRepository.findOneBy({ userId, name });

      if (existedSetting) {
        await this.userSettingRepository.update(existedSetting.id, { value, updatedBy: loginUserId });
        return this.formatOutputData({ key: 'UPDATE_USER_SETTING' }, { data: {} });
      }

      await this.userSettingRepository.insert(
        this.userSettingRepository.create({ ...updateDto, userId, updatedBy: loginUserId })
      );

      return this.formatOutputData({ key: 'UPDATE_USER_SETTING' }, { data: {} });
    } catch (error) {
      return this.throwCommonMessage('UPDATE_USER_SETTING', error);
    }
  }

  async getUserSetting(userId: string) {
    const data = await this.userSettingRepository.findBy({ userId });

    return this.formatOutputData({ key: 'GET_USER_SETTING' }, { data });
  }

  async getMyConfigByType(userId: string, name: UserSettingEnum, loginUser: IJwtPayload) {
    let data: any = await this.userSettingRepository.findBy({ userId, name });
    if (!data.length) {
      //Create if not exist for bullhorn_vacancy_form
      if (name === UserSettingEnum.BULLHORN_VACANCY_FORM) {
        const insertedItem = {
          userId,
          name,
          value: BULLHORN_VACANCY_FORM_CONFIG,
          updatedBy: loginUser.id,
        };
        data = [insertedItem];
        await this.userSettingRepository.insert(insertedItem);
      }
    }
    return this.formatOutputData({ key: 'GET_MY_CONFIG_BY_TYPE' }, { data });
  }

  async updateBHVacancyConfig(userId: string, loginUser: IJwtPayload, bodyDto: BullhornConfigDto) {
    const { data } = bodyDto;
    const isExisted = await this.userSettingRepository.findOneBy({
      userId,
      name: UserSettingEnum.BULLHORN_VACANCY_FORM,
    });

    const updatedData = this.standardizeUpdatedBHVacancyData(data);
    if (!isExisted) {
      const insertedItem = {
        value: [...BULLHORN_VACANCY_FORM_CONFIG, ...updatedData],
        userId,
        name: UserSettingEnum.BULLHORN_VACANCY_FORM,
        updatedBy: loginUser.id,
      };
      await this.userSettingRepository.insert(insertedItem);
    } else {
      console.log(data, 'data');
      await this.userSettingRepository.update({ userId }, { value: updatedData, updatedBy: loginUser.id });
    }

    return this.formatOutputData({ key: 'UPDATE_BH_VACANCY_CONFIG' }, { data: updatedData });
  }

  private standardizeUpdatedBHVacancyData(data: BullhornVacancyParentDto[]): BullhornVacancyParentDto[] {
    return data.map((item) => {
      const parentName = item.name;
      const childrenItems = item.children;
      const childrenItemNames = childrenItems.map((item) => item.name);

      const defaultChildrenNames = BH_VACANCY_CHILDREN_PARENT_PARTS[parentName];
      const missingChildrenNames = getMissingItems(defaultChildrenNames, childrenItemNames);
      const missingChildren: BullhornVacancyChildDto[] = missingChildrenNames.map((cItemName: string) => ({
        name: cItemName,
        on: false,
      }));

      return { ...item, children: [...childrenItems, ...missingChildren] };
    });
  }
}

import { Body, Controller, Get, Param, Post, Put, Query, Req, Request, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserSettingService } from './user-setting.service';
import { UpdateUserSettingDto } from './dto/update-user-setting.dto';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { SkipThrottle } from '@nestjs/throttler';
import { UserSettingEnum } from './entities/user-setting.entity';
import { IJwtPayload } from '../auth/payloads/jwt-payload.payload';
import { BullhornConfigDto } from './dto/bullhorn-config.dto';

@Controller('user-settings')
@ApiTags('UserSettings')
@UseGuards(AuthenticationGuard)
@SkipThrottle()
export class UserSettingController {
  constructor(private readonly userSettingService: UserSettingService) {}

  @Post()
  @ApiOperation({ description: 'Update user settings' })
  async updateMyUserSetting(@Request() req: any, @Body() updateDto: UpdateUserSettingDto) {
    const userId = req.viewAsUser.id;
    return this.userSettingService.updateUserSetting(userId, updateDto);
  }

  @Get()
  @ApiOperation({ description: 'Get user settings' })
  async getMyUserSetting(@Request() req: any) {
    const userId = req.viewAsUser.id;
    return this.userSettingService.getUserSetting(userId);
  }

  @Get('view-as/:userId')
  @ApiOperation({ description: 'Get user settings' })
  async getMyUserSettingForUserLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.userSettingService.getUserSetting(userId);
  }

  @Get('my-config-by-name/view-as/:userId')
  async getMyConfigByTypeLegacy(@Param('userId') userId: string, @Query('name') name: UserSettingEnum, @Request() req: any) {
    // TODO: Remove this after client migration is complete
    return this.userSettingService.getMyConfigByType(userId, name, <IJwtPayload>req.user);
  }

  @Get('my-config-by-name')
  async getMyConfigByType(@Query('name') name: UserSettingEnum, @Request() req: any) {
    const userId = req.viewAsUser.id;
    return this.userSettingService.getMyConfigByType(userId, name, <IJwtPayload>req.user);
  }

  @Put('update-bh-vacancy-config/view-as/:userId')
  async updateBHVacancyConfigLegacy(@Param('userId') userId: string, @Request() req: any, @Body() bodyDto: BullhornConfigDto) {
    // TODO: Remove this after client migration is complete
    return this.userSettingService.updateBHVacancyConfig(userId, <IJwtPayload>req.user, bodyDto);
  }

  @Put('update-bh-vacancy-config')
  async updateBHVacancyConfig(@Request() req: any, @Body() bodyDto: BullhornConfigDto) {
    const userId = req.viewAsUser.id;
    return this.userSettingService.updateBHVacancyConfig(userId, <IJwtPayload>req.user, bodyDto);
  }
}

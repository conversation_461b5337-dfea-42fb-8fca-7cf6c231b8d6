export const BULLHORN_VACANCY_FORM_CONFIG = [
  {
    name: 'Job Information',
    on: true,
    children: [
      {
        name: 'Employment Type',
        on: true,
      },
      {
        name: 'Job Type',
        on: true,
      },
      {
        name: 'Job Title',
        on: true,
      },
      {
        name: 'Description',
        on: true,
      },
      {
        name: 'Status',
        on: true,
      },
      {
        name: 'Consultant',
        on: true,
      },
      {
        name: 'Company',
        on: true,
      },
      {
        name: 'Contact',
        on: true,
      },
      {
        name: '# of Openings',
        on: true,
      },
      {
        name: 'Start Date',
        on: true,
      },
      {
        name: 'Scheduled End Date',
        on: true,
      },
      {
        name: 'Source',
        on: true,
      },
    ],
  },
  {
    name: 'Compensation & Fees',
    on: false,
    children: [
      {
        name: 'Perm fee (%)',
        on: true,
      },
      {
        name: 'Salary',
        on: true,
      },
      {
        name: 'Pay Unit',
        on: true,
      },
      {
        name: 'Pay Rate',
        on: true,
      },
      {
        name: 'Mark-up %',
        on: true,
      },
    ],
  },
  {
    name: 'Desired Experience',
    on: true,
    children: [
      {
        name: 'Industries',
        on: true,
      },
      {
        name: 'Categories',
        on: true,
      },
      {
        name: 'Required Skills',
        on: true,
      },
    ],
  },
  {
    name: 'Job Location',
    on: true,
    children: [
      {
        name: 'Address Line 1',
        on: true,
      },
      {
        name: 'City',
        on: true,
      },
      {
        name: 'County',
        on: true,
      },
      {
        name: 'Postcode',
        on: true,
      },
      {
        name: 'Country',
        on: true,
      },
    ],
  },
  {
    name: 'Job Board Publishing (Optional)',
    on: true,
    children: [
      {
        name: 'Published Description',
        on: true,
      },
    ],
  },
  {
    name: 'Email Notification',
    on: true,
    children: [
      {
        name: 'Internal User',
        on: true,
      },
    ],
  },
];

const childrenParentMapping: Record<string, string[]> = {};
BULLHORN_VACANCY_FORM_CONFIG.forEach((item) => {
  const { name, children } = item;
  childrenParentMapping[name] = children.map((child) => child.name);
});

export const BH_VACANCY_CHILDREN_PARENT_PARTS = childrenParentMapping;


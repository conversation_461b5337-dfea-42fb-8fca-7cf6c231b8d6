import { BaseEntity, Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

export enum UserSettingEnum {
  DASHBOARD = 'dashboard',
  BULLHORN_VACANCY_FORM = 'bullhorn_vacancy_form',
  CRM_LEAD = 'crm_lead',
  CRM_CONTACT = 'crm_contact',
  CRM_COMPANY = 'crm_company',
}

@Entity({ name: 'user_settings' })
export class UserSettingEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  //TODO: will be en enum in the future
  @Column({ type: 'enum', enum: UserSettingEnum, default: UserSettingEnum.DASHBOARD })
  name: UserSettingEnum;

  @Column({ type: 'jsonb' })
  value: object;

  @Column({ name: 'updated_by', length: 36, nullable: true })
  updatedBy: string;
}

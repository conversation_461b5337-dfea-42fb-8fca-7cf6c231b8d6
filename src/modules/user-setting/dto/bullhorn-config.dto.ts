import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, ValidateIf, ValidateNested } from 'class-validator';

export class BullhornVacancyChildDto {
  @ApiProperty()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  on: Boolean;
}

export class BullhornVacancyParentDto extends BullhornVacancyChildDto {
  @ApiProperty({ type: [BullhornVacancyChildDto] })
  @ValidateIf(value => value.length > 0)
  @Type(() => BullhornVacancyChildDto)
  @ValidateNested({ each: true })
  @IsOptional()
  children: BullhornVacancyChildDto[];
}

export class BullhornConfigDto {
  @ApiProperty({ type: [BullhornVacancyParentDto] })
  @Type(() => BullhornVacancyParentDto)
  @ValidateNested({ each: true })
  data: BullhornVacancyParentDto[];
}

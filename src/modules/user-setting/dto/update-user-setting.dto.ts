import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, ValidateNested } from "class-validator";
import { BaseUpdateDto } from "src/common/dto/update.dto";
import { UserSettingEnum } from "../entities/user-setting.entity";

export class UserSettingValueDto {
    @ApiProperty()
    @IsNotEmpty()
    id: number;

    @ApiProperty()
    @IsNotEmpty()
    name: string;

    @ApiProperty()
    @IsNotEmpty()
    x: number;

    @ApiProperty()
    @IsNotEmpty()
    y: number;

    @ApiProperty()
    @IsNotEmpty()
    w: number;

    @ApiProperty()
    @IsNotEmpty()
    h: number;
}


export class UpdateUserSettingDto extends BaseUpdateDto {
    @ApiProperty()
    @IsNotEmpty()
    name: UserSettingEnum;

    @ApiProperty()
    @IsNotEmpty()
    // @Type(() => UserSettingValueDto)
    // @ValidateNested({ each: true })
    value: object;
}
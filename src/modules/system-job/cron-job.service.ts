import { Promise as BlueBirdPromise } from 'bluebird';
import { JobSearchService } from './../jobs/service/job-search.service';
import { Brackets, DataSource, In, Is<PERSON>ull, <PERSON><PERSON><PERSON>, <PERSON>Than, More<PERSON>hanOrEqual, Not } from 'typeorm';
import * as sgMail from '@sendgrid/mail';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import {
  REDIS_KEYS,
  SendGridConfig,
} from 'src/configs/configs.constants';
import { UserEntity } from '../user/entities/user.entity';
import * as moment from 'moment';
import { STOP_BY_ACTOR } from 'src/common/constants/common.constant';
import { MailService } from '../mail/mail.service';
import { SequenceRepository } from '../mail/repositories/sequence.repostiory';
import { SequenceStatus } from '../mail/entities/sequence.entity';
import { chunkArray, queryAllByChunk } from 'src/common/utils/helpers.util';
import { OpenAIService } from '../openai/openai.service';
import { RedisService } from '../redis/redis.service';
import { OpensearchService } from '../opensearch/service/opensearch.service';
import { ReportedAgencyRepository } from '../jobs/repository/reported-agency.repository';
import { VerifiedCompanyRepository } from '../jobs/repository/verified_company.repository';
import { ContactEnrichmentTaskRepository } from '../email-finder/repositories/contact-enrichment-tasks.repository';
import { EnrichmentStatus } from '../email-finder/entities/contact-enrichment-tasks.entity';
import { UserService } from '../user/user.service';
import { JobSearchEntity } from '../jobs/entities/job-search.entity';

const VERIFIED_COMPANIES_AND_AGENCIES = {};
const VERIFIED_COMPANIES = {};

@Injectable()
export class CronJobService {
  private appEnv: string;
  constructor(
    private readonly dataSource: DataSource,
    private readonly jobSearchService: JobSearchService, // private readonly cacheService: CacheService
    private readonly mailService: MailService,
    private readonly sequenceRepository: SequenceRepository,
    private readonly reportedAgencyRepository: ReportedAgencyRepository,
    private readonly verifiedCompanyRepository: VerifiedCompanyRepository,
    private readonly openSearchService: OpensearchService,
    private readonly openAI: OpenAIService,
    private readonly redis: RedisService,
    private readonly contactEnrichmentTaskRepository: ContactEnrichmentTaskRepository,
    private readonly userService: UserService,
  ) {
    sgMail.setApiKey(SendGridConfig.apiKeyEmailExplorerCandiate);
    this.appEnv = process.env.APP_ENV;
  }

  //Every 3 minutes
  @Cron('* * * * *')
  async disabledSearchesForInactiveUsers() {
    if (!['dev', 'staging', 'prod'].includes(this.appEnv)) {
      return;
    }
    console.log('[CRON JOB] disabledSearchesForInactiveUsers is running');
    try {
      const INACTIVE_MINUTE = 60;
      const inactiveUsers = await this.dataSource
        .createQueryBuilder(UserEntity, 'u')
        .where({ lastActivity: LessThan(moment().subtract(INACTIVE_MINUTE, 'minutes').toDate()) })
        .select('id')
        .getRawMany<{ id: string }>();

      const inactiveUserIds = inactiveUsers.map((item) => item.id);
      if (!inactiveUserIds.length) {
        return null;
      }

      // const MAX_IN_ACTIVE_SEARCH_NUM = 10;
      // const inactiveSearches = await this.dataSource
      //   .createQueryBuilder(JobSearchEntity, 'js')
      //   .where({ dataKey: In(inactiveUserIds), stopScapingAt: IsNull(), isDeleted: false }) //inactive users and active searches
      //   // .limit(MAX_IN_ACTIVE_SEARCH_NUM)
      //   .getMany();
      // const inactiveSearchIds = inactiveSearches.map((item) => item.id);
      // console.log(inactiveSearchIds, 'inactiveSearchIds');

      // if (!inactiveSearchIds.length) {
      //   return null;
      // }

      await this.jobSearchService.updateByCondition(
        { dataKey: In(inactiveUserIds), stopScapingAt: IsNull(), isDeleted: false },
        {
          stopScapingAt: new Date(),
          activeSubprocessIds: null,
          stopBy: STOP_BY_ACTOR.SYSTEM,
        }
      );
      // await this.jobSearchService.updateByIds(inactiveSearchIds, {
      //   stopScapingAt: new Date(),
      //   activeSubprocessIds: null,
      //   stopBy: STOP_BY_ACTOR.SYSTEM,
      // });

      // await this.dataSource
      //   .createQueryBuilder(CrawlingJobEntity, 'cj')
      //   .delete()
      //   .where({ searchId: In(inactiveSearchIds) })
      //   .execute();

      // await Promise.all(
      //   inactiveSearches.map((item) => this.jobSearchService.callToKillCrawlProcess(item.activeSubprocessIds))
      // );
    } catch (error) {
      console.log('Error in disabledSearchesForInactiveUsers', error);
      return null;
    }

    console.log('[CRON JOB] disabledSearchesForInactiveUsers is completed');
  }

  // We disabled this cron on DEV to saving costs
  // @Cron('*/2 * * * *')
  async autoDetectAgencyCompanies() {
    console.log('[CRON JOB] autoDetectAgencyCompanies starting');

    if (!Object.keys(VERIFIED_COMPANIES_AND_AGENCIES).length) {
      // Prefill dataƒ
      // Agency companies
      const response = await this.openSearchService.getClient().search({
        index: 'reported_agency_country',
        size: 1000,
        body: {
          query: {
            match_all: {},
          },
          _source: ['country', 'reported_agencies.company_name'],
        },
      });
      response.body.hits.hits.forEach(({ _source: report }) => {
        report.reported_agencies.forEach(({ company_name }) => (VERIFIED_COMPANIES_AND_AGENCIES[company_name] = true));
      });

      // Verified companies
      const promptedCompanies = await this.redis.keys(`${REDIS_KEYS.VERIFIED_COMPANY}:*`);
      promptedCompanies.forEach((key: string) => {
        const [_, companyName] = key.split(':');
        VERIFIED_COMPANIES_AND_AGENCIES[companyName] = true;
      });
    }

    const verifiedCompanyBuilder = this.verifiedCompanyRepository
      .createQueryBuilder('verified_companies')
      .select('DISTINCT verified_companies.company_name');
    if (Object.keys(VERIFIED_COMPANIES).length) {
      // Get recently items only
      verifiedCompanyBuilder.where({
        updatedAt: MoreThan(moment().subtract(5, 'minutes').toDate()),
      });
    }

    (await queryAllByChunk(verifiedCompanyBuilder)).forEach(({ company_name: companyName }) => {
      VERIFIED_COMPANIES[companyName] = true;
    });

    const unverifiedCompanies: string[] = (await this.redis.keys(`${REDIS_KEYS.UNVERIFIED_COMPANY}:*`)).reduce(
      (acc: string[], key: string) => {
        const [_, companyName] = key.split(':');
        if (VERIFIED_COMPANIES_AND_AGENCIES[companyName] || VERIFIED_COMPANIES[companyName]) {
          return acc;
        }
        acc.push(companyName);

        return acc;
      },
      []
    );

    console.log('The processing data: ', unverifiedCompanies);

    try {
      const countryCompanies = [...new Set(unverifiedCompanies)];
      const chunks = chunkArray(countryCompanies, 25);

      await BlueBirdPromise.map(
        chunks,
        async (chunkCompanies: string[]) => {
          const agencyCompanies = await this.openAI.isCompanyRecruiter(chunkCompanies);
          if (agencyCompanies.length) {
            const reportedAgencies = agencyCompanies.map((companyName: string) =>
              this.reportedAgencyRepository.create({
                companyName,
                aliasCompanyNames: [companyName],
              })
            );

            await this.reportedAgencyRepository.insert(reportedAgencies);
            await this.openSearchService.pushNewAgenciesToReportedAgencies(agencyCompanies);
          }

          await BlueBirdPromise.map(chunkCompanies, async (company: string) => {
            VERIFIED_COMPANIES_AND_AGENCIES[company] = true;
            await this.redis.del(`${REDIS_KEYS.UNVERIFIED_COMPANY}:${company}`);
            if (!agencyCompanies.includes(company)) {
              await this.redis.set(`${REDIS_KEYS.VERIFIED_COMPANY}:${company}`, '1', { EX: 180 * 24 * 60 * 60 });
            }
          });
        },
        {
          concurrency: 20,
        }
      );
    } catch (error) {
      console.error('[CRON JOB] autoDetectAgencyCompanies error while updating reported agency', error);
    }

    console.log('[CRON JOB] autoDetectAgencyCompanies ends');
  }

  @Cron('* * * * *')
  async triggerSequence() {
    try {
      console.log('[CRON JOB] triggerSequence is running....');

      const currentDate = new Date();
      const inWorkingTimeUserIds = await this.userService.getInWorkingTimeUserIds(currentDate);

      const sequences = await this.sequenceRepository.find({
        where: {
          triggerAt: LessThan(new Date()),
          isTriggered: false,
          user: {
            id: In(inWorkingTimeUserIds),
          },
          status: SequenceStatus.LIVE,
        },
        relations: {
          user: {
            organization: true,
          },
        },
        select: {
          id: true,
          triggerAt: true,
          isTriggered: true,
          user: {
            id: true,
            organizationId: true,
          },
        },
      });

      console.log(sequences, 'sequences');

      const results = await BlueBirdPromise.allSettled(
        sequences.map(async (sequence) => {
          try {
            await this.mailService.triggerSequence(
              sequence.user.id,
              sequence.id,
            );

            return sequence.id;
          } catch (err) {
            if (err?.message === 'Email already sent' || err?.message === 'There are no steps found. Please double check!') {
              return sequence.id;
            }

            throw err;
          }
        }),
      );
      const successIds = [];
      results.forEach((result) => {
        if (!result.isFulfilled()) {
          console.error('Trigger sequence error: ', result.reason());

          return;
        }

        successIds.push(result.value());
      });
      await this.sequenceRepository.update(
        {
          id: In(successIds),
        },
        {
          isTriggered: true,
        },
      );
    } catch (error) {
      console.error('Error in triggerSequence', error);
    }
    console.log('[CRON JOB] triggerSequence ends');
  }

  @Cron('*/10 * * * *')
  async contactEnrichmentTimout() {
    console.log('[CRON JOB] contactEnrichmentTimout is running....');
    await this.contactEnrichmentTaskRepository.update({
      status: EnrichmentStatus.IN_PROGRESS,
      updatedAt: LessThan(new Date(new Date().getTime() - 20 * 60 * 1000)), // 20 minutes
    }, {
      status: EnrichmentStatus.ERROR,
    });

    console.log('[CRON JOB] contactEnrichmentTimout ends');
  }
}

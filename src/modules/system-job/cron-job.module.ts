import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CronJobService } from './cron-job.service';
import { JobBoardsRepository } from '../jobs/repository/job-boards.repository';
import { JobsModule } from '../jobs/jobs.module';
import { MailModule } from '../mail/mail.module';
import { SequenceStepRepository } from '../mail/repositories/sequence-step.repostiory';
import { MailService } from '@sendgrid/mail';
import { SequenceRepository } from '../mail/repositories/sequence.repostiory';
import { SequenceActivityLogRepository } from '../mail/repositories/sequence-activity-log.repository';
import { BullHornService } from '../../middlewares/bullhorn/bullhorn.service';
import { SentJobRepository } from '../jobs/repository/sent-job.repository';
import { JobLeadsRepository } from '../jobs/repository/job-leads.repository';
import { OpenAIService } from '../openai/openai.service';
import { RedisModule } from '../redis/redis.module';
import { HttpModule } from '@nestjs/axios';
import { OpensearchService } from '../opensearch/service/opensearch.service';
import { ReportedAgencyRepository } from '../jobs/repository/reported-agency.repository';
import { VerifiedCompanyRepository } from '../jobs/repository/verified_company.repository';
import { SequenceInstanceRepository } from '../mail/repositories/sequence-instance.repository';
import { SequenceStepTaskRepository } from '../mail/repositories/sequence-step-task.repository';
import { MyCacheModule } from '../cache/cache.module';
import { QueueModule } from '../queue/queue.module';
import { BullModule } from '@nestjs/bullmq';
import { BULL_QUEUES } from 'src/configs/configs.constants';
import { SequenceStepTaskProcessor } from '../queue/sequence-step-task.processor';
import { UserRepository } from '../user/repositories/user.repository';
import { ContactEnrichmentTaskRepository } from '../email-finder/repositories/contact-enrichment-tasks.repository';
import { UserService } from '../user/user.service';
import { RoleRepository } from '../user/repositories/role.repository';
import { NotificationRepository } from '../notification/repositories/notification.repository';
import { UserWorkingTimeRepository } from '../user/repositories/user-working-time.repository';
import { LinkedInFinderService } from '../linkedin-finder/linkedin-finder.service';
import { ApolloService } from '../employee-finder/services/apollo.service';
import { NotificationService } from '../notification/services/notification.service';
import { SubscriptionModule } from '../subscription/subscription.module';
import { UserQuotaRepository } from '../subscription/repositories/user-quota.repository';
import { OrganizationQuotaRepository } from '../subscription/repositories/organization-quota.repository';

@Module({
  imports: [
    MyCacheModule,
    JobsModule,
    MailModule,
    RedisModule,
    HttpModule,
    QueueModule,
    SubscriptionModule,
    BullModule.registerQueue({
      name: BULL_QUEUES.SEQUENCE_STEP_TASK,
      prefix: '{bull_sequence_step_task}',
    }),
    // MyCacheModule
  ],
  providers: [
    CronJobService,
    JobBoardsRepository,
    SequenceStepRepository,
    SequenceInstanceRepository,
    SequenceStepTaskRepository,
    SentJobRepository,
    MailService,
    SequenceRepository,
    SequenceActivityLogRepository,
    BullHornService,
    JobLeadsRepository,
    OpensearchService,
    OpenAIService,
    ReportedAgencyRepository,
    VerifiedCompanyRepository,
    SequenceStepTaskProcessor,
    UserRepository,
    ContactEnrichmentTaskRepository,
    UserService,
    RoleRepository,
    NotificationRepository,
    UserWorkingTimeRepository,
    LinkedInFinderService,
    ApolloService,
    NotificationService,
    UserQuotaRepository,
    OrganizationQuotaRepository
  ],
  exports: [CronJobService],
})
export class CronJobModule {}

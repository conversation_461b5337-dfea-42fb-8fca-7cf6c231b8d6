import { Injectable } from '@nestjs/common';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import { UserSignature } from '../entities/user-signature.entity';
import { ContactEntity } from '../entities/contact.entity';
import { ContactListEntity } from '../entities/contact-list.entity';

@Injectable()
export class ContactListRepository extends Repository<ContactListEntity> {
  constructor(private dataSource: DataSource) {
    super(ContactListEntity, dataSource.createEntityManager());
  }
}

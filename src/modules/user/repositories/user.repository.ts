import { Injectable } from '@nestjs/common';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import { UserEntity } from '../entities/user.entity';

@Injectable()
export class UserRepository extends Repository<UserEntity> {
  constructor(private dataSource: DataSource) {
    super(UserEntity, dataSource.createEntityManager());
  }

  async softDeleteUser(userId: string, queryRunner: QueryRunner) {
    try {
      const query = queryRunner.manager.createQueryBuilder(UserEntity, 'u');
      query.where('id = :userId', { userId });
      await query.softDelete().execute();
      return true;
    } catch (error) {
      throw new Error(error);
    }
  }
}

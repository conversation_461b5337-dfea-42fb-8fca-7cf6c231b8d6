import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { OrganizationEntity } from './entities/organization.entity';
import { SendGridConfig } from 'src/configs/configs.constants';
import * as sgMailClient from '@sendgrid/client';
import { BrandLinkDto, CheckDomainValidationDto, DomainAuthDto } from './dto/verify-domain.dto';
import { UserEntity } from './entities/user.entity';

// Interface for domain options
interface DomainOptions {
  customDkimSelector?: string;
  region?: 'eu' | 'global';
  subdomain?: string;
}

@Injectable()
export class DomainVerificationService extends BaseAbstractService {
  private readonly logger = new Logger(DomainVerificationService.name);

  constructor(
    private readonly i18nService: I18nService,
    private readonly dataSource: DataSource,
  ) {
    super(i18nService);
    sgMailClient.setApiKey(SendGridConfig.apiKey);
  }

  /**
   * Create or check domain authentication with SendGrid
   * @param organizationId Organization ID
   * @param domain Domain to authenticate
   * @param options Optional domain configuration options
   * @returns Authentication status and DNS records if not verified
   */
  async createOrCheckDomainAuth(
    organizationId: string,
    domain: string,
    options?: DomainOptions
  ): Promise<any> {
    try {
      // Find organization
      const organization = await this.dataSource
        .getRepository(OrganizationEntity)
        .findOne({ where: { id: organizationId } });

      if (!organization) {
        throw new NotFoundException(`Organization with ID ${organizationId} not found`);
      }

      // Check if domain is already verified in our database
      if (organization.emailDomainAuthentication?.domain === domain && organization.emailDomainAuthentication?.isVerified) {
        return {
          isVerified: true,
          organizationId,
          domain
        };
      }

      // Check if domain exists in SendGrid
      const existingDomainInfo = await this.getDomainInfoFromSendGrid(domain);

      if (existingDomainInfo) {
        // Domain exists, check if it's verified
        const verificationResult = await this.isDomainVerified(domain, existingDomainInfo.id);

        // Update organization with domain info
        organization.emailDomainAuthentication = {
          ...organization.emailDomainAuthentication,
          domain: verificationResult.domain,
          isVerified: verificationResult.isVerified,
          verificationId: verificationResult.verificationId,
          lastCheckedAt: new Date().toISOString()
        };

        if (verificationResult.isVerified) {
          // If verified, clear DNS records as they're no longer needed
          organization.emailDomainAuthentication.dnsRecords = null;
          await this.dataSource.getRepository(OrganizationEntity).save(organization);

          return {
            isVerified: true,
            organizationId,
            domain,
            verificationId: organization.emailDomainAuthentication.verificationId
          };
        } else {
          // Domain exists but not verified, return DNS records
          const dnsRecords = this.formatDnsRecords(existingDomainInfo.dns);

          // Save DNS records to organization
          organization.emailDomainAuthentication.dnsRecords = dnsRecords;
          await this.dataSource.getRepository(OrganizationEntity).save(organization);

          return {
            isVerified: false,
            dnsRecords,
            organizationId,
            domain,
            verificationId: organization.emailDomainAuthentication.verificationId
          };
        }
      } else {
        // Domain doesn't exist, create it and return DNS records
        const createdDomain = options
          ? await this.createDomainInSendGrid(
              domain,
              options.customDkimSelector,
              options.region,
              options.subdomain
            )
          : await this.createDomainInSendGrid(domain);

        const dnsRecords = this.formatDnsRecords(createdDomain.dns);

        // Update organization with domain info
        organization.emailDomainAuthentication = {
          domain,
          isVerified: false,
          verificationId: createdDomain.id.toString(),
          dnsRecords,
          lastCheckedAt: new Date().toISOString()
        };
        await this.dataSource.getRepository(OrganizationEntity).save(organization);

        return {
          isVerified: false,
          dnsRecords,
          organizationId,
          domain,
          verificationId: organization.emailDomainAuthentication.verificationId
        };
      }
    } catch (error) {
      this.logger.error(`Error verifying domain: ${error.message}`, error.stack);
      return {
        isVerified: false,
        dnsRecords: null,
        domain,
        organizationId,
        error: error.message
      };
    }
  }

  /**
   * Get domain information from SendGrid
   * @param domain Domain to check
   * @returns Domain information if exists, null otherwise
   */
  private async getDomainInfoFromSendGrid(domain: string): Promise<any> {
    try {
      const [response] = await sgMailClient.request({
        method: 'GET',
        url: '/v3/whitelabel/domains'
      });

      const domains = response.body as any[];
      return domains.find((d: any) => d.domain === domain);
    } catch (error) {
      this.logger.error(`Error getting domain info from SendGrid: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Check if domain is verified in SendGrid
   * @param domain Domain to check
   * @param domainId Domain ID in SendGrid
   * @returns Object containing verification status and domain information
   */
  private async isDomainVerified(domain: string, domainId?: number): Promise<{ isVerified: boolean; domain: string; verificationId: string }> {
    try {
      let id = domainId;

      if (!id) {
        const domainInfo = await this.getDomainInfoFromSendGrid(domain);
        if (!domainInfo) {
          return { isVerified: false, domain, verificationId: null };
        }
        id = domainInfo.id;
      }

      // Validate the domain
      const [validationResponse] = await sgMailClient.request({
        method: 'POST',
        url: `/v3/whitelabel/domains/${id}/validate`
      });

      const isVerified = (validationResponse.body as any).valid;
      return { isVerified, domain, verificationId: id?.toString() };
    } catch (error) {
      this.logger.error(`Error checking domain verification: ${error.message}`, error.stack);
      return { isVerified: false, domain, verificationId: domainId?.toString() };
    }
  }

  /**
   * Format DNS records from SendGrid format to our format
   * @param dns DNS records from SendGrid
   * @returns Formatted DNS records
   */
  private formatDnsRecords(dns: any): any {
    if (!dns) return {};

    const result: any = {
      cname: [],
      txt: []
    };

    if (dns.mail_cname) {
      result.cname.push({
        host: dns.mail_cname.host,
        data: dns.mail_cname.data
      });
    }

    if (dns.dkim1) {
      result.cname.push({
        host: dns.dkim1.host,
        data: dns.dkim1.data
      });
    }

    if (dns.dkim2) {
      result.cname.push({
        host: dns.dkim2.host,
        data: dns.dkim2.data
      });
    }

    if (dns.dmarc) {
      result.txt.push({
        host: dns.dmarc.host,
        data: dns.dmarc.data
      });
    }
    this.logger.debug(`Formatted DNS records: ${JSON.stringify(result)}`);

    return result;
  }

  /**
   * Create domain authentication for a user
   * @param userId User ID
   * @param data Domain authentication request
   * @returns Domain authentication response
   */
  async createDomainAuth(userId: string, data: DomainAuthDto): Promise<any> {
    try {
      // Get user's organization
      const user = await this.dataSource
        .createQueryBuilder(UserEntity, 'user')
        .select(['user.id', 'user.organizationId'])
        .where('user.id = :userId', { userId })
        .getOne();

      if (!user || !user.organizationId) {
        throw new NotFoundException('User not found or not associated with an organization');
      }

      // Use createOrCheckDomainAuth method with options
      return this.createOrCheckDomainAuth(
        user.organizationId,
        data.domain,
        {
          customDkimSelector: data.customDkimSelector,
          region: data.region,
          subdomain: data.subdomain
        }
      );
    } catch (error) {
      this.logger.error(`Error creating domain authentication: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check domain validation status
   * @param userId User ID
   * @returns Domain validation status
   */
  async checkDomainValidation(userId: string): Promise<any> {
    try {
      // Get user's organization
      const user = await this.dataSource
        .createQueryBuilder(UserEntity, 'user')
        .select(['user.id', 'user.organizationId'])
        .where('user.id = :userId', { userId })
        .getOne();

      if (!user || !user.organizationId) {
        throw new NotFoundException('User not found or not associated with an organization');
      }
      const organizationId = user.organizationId;

      // Find organization
      const organization = await this.dataSource
        .getRepository(OrganizationEntity)
        .findOne({ where: { id: organizationId } });

      if (!organization) {
        throw new NotFoundException(`Organization with ID ${organizationId} not found`);
      }

      if (!organization.emailDomainAuthentication?.domain) {
        return {
          isVerified: false,
          organizationId,
          domain: null,
          verificationId: null
        };
      }

      const domain = organization.emailDomainAuthentication.domain;

      // If already verified, return success
      if (organization.emailDomainAuthentication.isVerified) {
        return {
          isVerified: true,
          organizationId,
          domain,
          verificationId: organization.emailDomainAuthentication.verificationId
        };
      }

      // Check if domain is verified in SendGrid
      const verificationId = organization.emailDomainAuthentication.verificationId;
      if (verificationId) {
        const verificationResult = await this.isDomainVerified(domain, parseInt(verificationId));

        // Update organization with verification status
        organization.emailDomainAuthentication = {
          ...organization.emailDomainAuthentication,
          isVerified: verificationResult.isVerified,
          verificationId: verificationResult.verificationId,
          lastCheckedAt: new Date().toISOString()
        };

        await this.dataSource.getRepository(OrganizationEntity).save(organization);

        return {
          isVerified: verificationResult.isVerified,
          organizationId,
          domain,
          verificationId: organization.emailDomainAuthentication.verificationId,
          dnsRecords: verificationResult.isVerified ? null : organization.emailDomainAuthentication.dnsRecords
        };
      }

      // If no verification ID, create domain authentication
      return this.createOrCheckDomainAuth(organizationId, domain);
    } catch (error) {
      this.logger.error(`Error checking domain validation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create a domain in SendGrid
   * @param domain Domain to create
   * @param customDkimSelector Custom DKIM selector
   * @param region Region for the domain (eu or global)
   * @param subdomain Subdomain for the domain
   * @returns Created domain information
   */
  private async createDomainInSendGrid(
    domain: string,
    customDkimSelector?: string,
    region: 'eu' | 'global' = 'global',
    subdomain?: string,
  ): Promise<any> {
    try {
      const requestBody: any = {
        domain,
        automatic_security: true,
        ips: [],
        region: region || 'global',
      };

      // Add optional parameters if provided
      if (customDkimSelector) {
        requestBody.custom_dkim_selector = customDkimSelector; // SendGrid API expects snake_case
      }

      if (subdomain) {
        requestBody.subdomain = subdomain;
      }

      const [response] = await sgMailClient.request({
        method: 'POST',
        url: '/v3/whitelabel/domains?dmarc=true',
        body: requestBody,
      });

      return response.body;
    } catch (error) {
      this.logger.error(`Error creating domain in SendGrid: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create a brand link in SendGrid
   * @param domain Domain for the brand link
   * @param subdomain Subdomain for the brand link
   * @param region Region for the brand link (eu or global)
   * @returns Created brand link information
   */
  private async createBrandLinkInSendGrid(
    domain: string,
    subdomain?: string,
    region: 'eu' | 'global' = 'global',
  ): Promise<any> {
    try {
      const requestBody: any = {
        domain,
        default: false,
      };

      // Add optional parameters if provided
      if (subdomain) {
        requestBody.subdomain = subdomain;
      }

      if (region) {
        requestBody.region = region;
      }

      const [response] = await sgMailClient.request({
        method: 'POST',
        url: '/v3/whitelabel/links',
        body: requestBody,
      });

      return response.body;
    } catch (error) {
      this.logger.error(`Error creating brand link in SendGrid: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Verify a brand link in SendGrid
   * @param id Brand link ID to verify
   * @returns Verification result
   */
  private async verifyBrandLinkInSendGrid(id: number): Promise<any> {
    try {
      const [response] = await sgMailClient.request({
        method: 'POST',
        url: `/v3/whitelabel/links/${id}/validate`,
      });

      return response.body;
    } catch (error) {
      this.logger.error(`Error verifying brand link in SendGrid: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create and verify a brand link for a user
   * @param userId User ID
   * @param data Brand link data
   * @returns Brand link creation and verification result
   */
  async createAndVerifyBrandLink(userId: string, data: BrandLinkDto): Promise<any> {
    try {
      // Get user's organization
      const user = await this.dataSource
        .createQueryBuilder(UserEntity, 'user')
        .select(['user.id', 'user.organizationId'])
        .where('user.id = :userId', { userId })
        .getOne();

      if (!user || !user.organizationId) {
        throw new NotFoundException('User not found or not associated with an organization');
      }

      // Create brand link in SendGrid
      const createdBrandLink = await this.createBrandLinkInSendGrid(
        data.domain,
        data.subdomain,
        data.region,
      );

      // Verify the brand link
      const verificationResult = await this.verifyBrandLinkInSendGrid(createdBrandLink.id);

      return {
        brandLink: {
          id: createdBrandLink.id,
          domain: createdBrandLink.domain,
          subdomain: createdBrandLink.subdomain,
          region: createdBrandLink.region,
        },
        verification: {
          isValid: verificationResult.valid,
          validationResults: verificationResult.validation_results,
        },
      };
    } catch (error) {
      this.logger.error(`Error creating and verifying brand link: ${error.message}`, error.stack);
      throw error;
    }
  }
}

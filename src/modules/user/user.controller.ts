import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiConsumes,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { UserService } from './user.service';
import { SkipThrottle } from '@nestjs/throttler';

import { BaseErrorResponseDto } from '../../common/dto/common.dto';
import { ResponseMessage } from '../../common/constants/common.constant';
import { SendByEmailsDto } from '../mail/dto/send-by-emails.dto';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { UpdateMeRequestDto, UpdateUserDto } from './dto/update-user.dto';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from './entities/permission.entity';
import { IJwtPayload } from '../auth/payloads/jwt-payload.payload';
import { BaseUpdateDto } from 'src/common/dto/update.dto';
import { QuickCreateUserDto, UpdateBullhornConfigDto, UpsertWorkingTimeDto, GetDashboardMetricsQuery } from './dto/user.dto';
import { UpdateSolutionsDto } from './dto/update-consultant.dto';
import { UserSearchDto } from './dto/user-search.dto';
import { UpdatePasswordDto } from './dto/update-password.dto';
import { AddUserDto } from './dto/add-user.dto';
import {
  CreateDocumentFileDto,
  CreateOrganizationDto,
  OnboardingCompanyDto,
  SingleOnboardingUserDto,
} from './dto/create-origanization.dto';
import { CompleteOnboardingDto } from './dto/complete-onboarding.dto';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  MAX_FILE_SIZE_IN_BYTES,
  MAX_IMAGE_SIZE_IN_BYTES,
} from './constants/user-signature.constants';
import {
  LinkedConnectionDto,
  UnipileResendCheckpointDto,
  UnipileSolveCheckpointDto,
  UnipileUpdateAccountInApp,
} from './dto/linked-connection.dto';
import { JobLeadStatsDto, LinkedinMetricDto } from '../jobs/dto/job-lead/job-lead-stats.dto';
import { OrganizationStatusEnum } from './enums/organization.enum';
import { UpsertCreditManagementDto } from './dto/upsert-credit-management.dto';
import { TopupCreditsDto } from './dto/topup-credits.dto';
import { ConfirmTopupPaymentDto } from './dto/confirm-topup-payment.dto';
import { BrandLinkDto, DomainAuthDto } from './dto/verify-domain.dto';
import { DomainVerificationService } from './domain-verification.service';
import { RoleEnum } from './entities/role.entity';

const avatarFileFilter = (
  req: Request,
  file: Express.Multer.File,
  callback: (error: Error, acceptFile: boolean) => void,
) => {
  // if (
  //   !file.mimetype.match(/^image\/(jpg|jpeg|png|gif)$/i) &&
  //   file.mimetype !== 'application/octet-stream'
  // ) {
  //   callback(
  //     new HttpException(
  //       'Upload not allowed. Upload only files of type: image/jpg, image/jpeg, image/png, image/gif, or application/octet-stream',
  //       HttpStatus.BAD_REQUEST
  //     ),
  //     false
  //   );
  // }

  callback(null, true);
};

@ApiTags('Users')
@Controller('users')
@SkipThrottle()
@ApiBadRequestResponse({
  description: ResponseMessage.Common.BAD_REQUEST,
  type: BaseErrorResponseDto,
})
@ApiNotFoundResponse({
  description: ResponseMessage.Common.NOT_FOUND,
  type: BaseErrorResponseDto,
})
@ApiUnauthorizedResponse({
  description: ResponseMessage.Common.UNAUTHORIZED,
  type: BaseErrorResponseDto,
})
@ApiForbiddenResponse({
  description: ResponseMessage.Common.FORBIDDEN,
  type: BaseErrorResponseDto,
})
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly domainVerificationService: DomainVerificationService,
  ) {}

  @Get('permissions')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getPermissions() {
    return this.userService.getAllPermissions();
  }

  @Get('roles')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getRoles() {
    return this.userService.getAllRoles();
  }

  @Get('organizations')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getOrganizations() {
    return this.userService.getAllOrganizations();
  }

  @Get('/view-as/:userId/credit-management/organizations')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getCreditManagementOrganizations(@Req() req: any, @Param('userId') userId: string) {
    return this.userService.getCreditManagementOrganizations(userId);
  }

  @Post('organization')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async createOrganization(@Body() createOrganizationDto: CreateOrganizationDto, @Req() req) {
    return this.userService.createOrganization(createOrganizationDto, <IJwtPayload>req.user);
  }

  @Get('organization/:id')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getOrganization(@Param('id') id: string) {
    return this.userService.getDetailOrganization(id);
  }

  @Put('organization/:id')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async updateOrganization(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @Param('id') id: string,
    @Req() req,
  ) {
    return this.userService.updateOrganization(id, createOrganizationDto, <IJwtPayload>req.user);
  }

  @Post('organization/:id/documents')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async createDocument(
    @Body() body: CreateDocumentFileDto,
    @Req() req: any,
    @Param('id') id: string,
  ) {
    const userId = req.viewAsUser.id;
    return await this.userService.handleCreateDocument(body, userId, id);
  }

  @Delete('organization/:id/documents/:documentId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async deleteDocument(
    @Req() req: any,
    @Param('id') id: string,
    @Param('documentId') documentId: string,
  ) {
    const userId = req.viewAsUser.id;
    return await this.userService.handleDeleteDocument(documentId, userId, id);
  }

  @Delete('organization/:id')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async deleteOrganization(@Param('id') id: string) {
    return this.userService.deleteOrganization(id);
  }

  @Get('get-number-of-login-user')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getNumberOfLoginUser() {
    return this.userService.getNumberOfLoginUser();
  }

  @Get('me')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getMe(@Req() req) {
    return this.userService.getMe(<IJwtPayload>req.viewAsUser);
  }

  @Get('me/view-as/:userId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getMeViewAsLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.userService.getMeViewAs(userId);
  }

  @Get('me-view-as')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getMeViewAs(@Req() req) {
    const userId = req.viewAsUser.id;
    return this.userService.getMeViewAs(userId);
  }

  //TODO: remove after integrating with FE
  @Get('my-lead-value-range')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.JOB_LEAD].Read)
  async getMyLeadValueRange(@Req() req, @Query() query: JobLeadStatsDto) {
    return this.userService.getMyLeadValueRange(query?.userId || req.user.id, query);
  }

  @Get('lead-value/:id')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async getLeadValue(@Param('id') id: string, @Query() query: JobLeadStatsDto) {
    return this.userService.getLeadValueUser(query?.userId || id, query);
  }

  @Put('me')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async updateMe(@Req() req, @Body() updateMeDto: UpdateMeRequestDto) {
    return this.userService.updateMe(<IJwtPayload>req.user, updateMeDto);
  }

  @Put('me/view-as/:userId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async updateMeViewAsLegacy(
    @Param('userId') userId: string,
    @Body() updateMeDto: UpdateMeRequestDto,
  ) {
    // TODO: Remove this after client migration is complete
    return this.userService.updateMeViewAs(userId, updateMeDto);
  }

  @Put('me-view-as')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async updateMeViewAs(@Req() req, @Body() updateMeDto: UpdateMeRequestDto) {
    const userId = req.viewAsUser.id;
    return this.userService.updateMeViewAs(userId, updateMeDto);
  }

  @Patch('update-user/:id')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async updateUser(@Body() updateUserDto: UpdateUserDto, @Param('id') id: string, @Req() req) {
    return await this.userService.updateUser(
      id,
      { ...updateUserDto, updatedBy: req.user.id },
      req.user.role,
    );
  }

  @Post('invite-user')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Invite)
  async inviteUser(@Body() inviteUserDto: SendByEmailsDto, @Req() req) {
    return this.userService.inviteUser({ id: req.user.id }, inviteUserDto);
  }

  @Put('update-consultant/:userId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Invite)
  async updateConsultant(
    @Body() body: UpdateSolutionsDto,
    @Req() req,
    @Param('userId') userId: string,
  ) {
    return this.userService.updateConsultant(userId, body);
  }

  @Post('invite-user/view-as/:userId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Invite)
  async inviteUserViewAsLegacy(
    @Body() inviteUserDto: SendByEmailsDto,
    @Param('userId') userId: string,
  ) {
    // TODO: Remove this after client migration is complete
    return this.userService.inviteUser({ id: userId }, inviteUserDto);
  }

  @Post('invite-user-view-as')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Invite)
  async inviteUserViewAs(@Body() inviteUserDto: SendByEmailsDto, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userService.inviteUser({ id: userId }, inviteUserDto);
  }

  @Post('add-user')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async addUser(@Body() addUserDto: AddUserDto, @Req() req: any) {
    return this.userService.addUserToOrganization(addUserDto, req.viewAsUser);
  }

  @Get('organization/:organizationId/licenses')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getOrganizationLicenses(@Param('organizationId') organizationId: string, @Req() req: any) {
    return this.userService.getOrganizationLicenses(organizationId, req.viewAsUser);
  }

  @Delete(':userId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async deleteUserById(@Param('userId') userId: string, @Req() req) {
    return this.userService.deleteUserById(userId, req.user.id);
  }

  @Get(':id')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  // TODO - Only for user management - use /me instead
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async getUser(@Param('id') id: string) {
    return this.userService.findUserDetailById(id);
  }

  @Get('')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getUsers() {
    return this.userService.getUsers();
  }

  @Get('view-as/:userId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  // TODO - Only for user management - use /me instead
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async getUsersForUserLegacy(@Param('userId') userId: string, @Query() query: UserSearchDto) {
    // TODO: Remove this after client migration is complete
    return this.userService.getMyUsers(userId, query);
  }

  @Get('users-for-user')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getUsersForUser(@Req() req, @Query() query: UserSearchDto) {
    const userId = req.viewAsUser.id;
    return this.userService.getMyUsers(userId, query);
  }

  @Get('view-as/:userId/metrics')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getUserMetricsLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.userService.getUserMetrics(userId);
  }

  @Get('user-metrics')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getUserMetrics(@Req() req) {
    const userId = req.viewAsUser.id;
    return this.userService.getUserMetrics(userId);
  }

  @Get('view-as/:userId/working-times')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getUserWorkingTimes(@Param('userId') userId: string) {
    return this.userService.getWorkingTimes(userId);
  }

  @Post('view-as/:userId/working-times')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async updateUserWorkingTimes(
    @Param('userId') userId: string,
    @Body() data: UpsertWorkingTimeDto,
  ) {
    return this.userService.updateWorkingTimes(userId, data);
  }

  @Patch(':userId/update-bullhorn-config')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async updateBullhornConfig(
    @Param('userId') userId: string,
    @Body() bodyDto: UpdateBullhornConfigDto,
    @Req() req,
  ) {
    return this.userService.updateBullhornConfig(userId, bodyDto, req);
  }

  @Patch('view-as/:userId/update-password')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async updateUserPassword(@Param('userId') userId: string, @Body() bodyDto: UpdatePasswordDto) {
    return this.userService.updateUserPassword(userId, bodyDto);
  }

  @Post('upload-avatar')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        avatar: {
          type: 'string',
          format: 'binary',
        },
      },
      required: ['avatar'],
    },
  })
  @UseInterceptors(
    FileInterceptor('avatar', {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: avatarFileFilter,
    }),
  )
  async updateUserAvatar(@UploadedFile() avatar: Express.Multer.File) {
    return this.userService.uploadAvatar(avatar);
  }

  @Post('upload-file')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
      required: ['file'],
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: { fileSize: MAX_FILE_SIZE_IN_BYTES },
    }),
  )
  async uploadFiles(@UploadedFile() file: Express.Multer.File) {
    return this.userService.uploadFile(file);
  }

  @Get(':userId/get-bullhorn-config')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getBullhornConfig(@Param('userId') userId: string) {
    return this.userService.getBullhornConfig(userId);
  }

  @Get('/view-as/:userId/sequence-metrics')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  async getSequenceMetricsLegacy(@Param('userId') userId: string, @Query() query: JobLeadStatsDto) {
    // TODO: Remove this after client migration is complete
    return this.userService.getSequenceMetrics(query?.userId || userId, query);
  }

  @Get('/view-as/:userId/linkedin-metrics')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  async getLinkedInMetricsLegacy(
    @Param('userId') userId: string,
    @Query() query: LinkedinMetricDto,
  ) {
    return this.userService.getLinkedInMetrics(userId || userId, query);
  }

  @Get('/sequence-metrics')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SEQUENCE].Read)
  async getSequenceMetrics(@Req() req, @Query() query: JobLeadStatsDto) {
    const userId = req.viewAsUser.id;
    return this.userService.getSequenceMetrics(query?.userId || userId, query);
  }

  @Post('/view-as/:userId/linkedin-connection/')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async linkedInConnectionLegacy(
    @Body() body: LinkedConnectionDto,
    @Param('userId') userId: string,
  ) {
    // TODO: Remove this after client migration is complete
    return this.userService.linkedInConnection(body, userId);
  }

  @Post('/linkedin-connection/')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async linkedInConnection(@Body() body: LinkedConnectionDto, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userService.linkedInConnection(body, userId);
  }

  @Delete('/view-as/:userId/un-linkedin-connection/')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async unLinkedInConnectionLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.userService.unLinkedInConnection(userId);
  }

  @Delete('/un-linkedin-connection/')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async unLinkedInConnection(@Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userService.unLinkedInConnection(userId);
  }

  @Post('/view-as/:userId/resolve-unipile-checkpoint')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async resolveUnipileCheckpointLegacy(
    @Body() body: UnipileSolveCheckpointDto,
    @Param('userId') userId: string,
  ) {
    // TODO: Remove this after client migration is complete
    return this.userService.resolveUnipileCheckpoint(body, userId);
  }

  @Post('/resolve-unipile-checkpoint')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async resolveUnipileCheckpoint(@Body() body: UnipileSolveCheckpointDto, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userService.resolveUnipileCheckpoint(body, userId);
  }

  @Post('/view-as/:userId/resend-unipile-checkpoint')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async resendUnipileCheckpointLegacy(@Body() body: UnipileResendCheckpointDto) {
    // TODO: Remove this after client migration is complete
    return this.userService.resendUnipileCheckpoint(body);
  }

  @Post('/resend-unipile-checkpoint')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async resendUnipileCheckpoint(@Body() body: UnipileResendCheckpointDto) {
    return this.userService.resendUnipileCheckpoint(body);
  }

  @Get('/retrieve-uniplie-account/:accountId')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async retrieveUnipileAccount(@Param('accountId') accountId: string) {
    return this.userService.retrieveUnipileAccount(accountId);
  }

  @Get('/view-as/:userId/get-uniplie-account')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async getUnipileAccountLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.userService.getUnipileAccount(userId);
  }

  @Get('/get-uniplie-account')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  // TODO - Create a basic permission
  @Permission(PermissionResource[ResourceEnum.DASHBOARD].Read)
  async getUnipileAccount(@Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userService.getUnipileAccount(userId);
  }

  @Post('/view-as/:userId/update-uniplie-account-in-app')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async updateUniplileAccountInAppLegacy(
    @Param('userId') userId: string,
    @Body() body: UnipileUpdateAccountInApp,
  ) {
    // TODO: Remove this after client migration is complete
    return this.userService.updateUniplileAccountInApp(userId, body);
  }

  @Post('/update-uniplie-account-in-app')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async updateUniplileAccountInApp(@Body() body: UnipileUpdateAccountInApp, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userService.updateUniplileAccountInApp(userId, body);
  }

  @Post('/unipile/webhook')
  // This is a public webhook endpoint, no authentication or permission required
  async unipileWebhook(@Body() body: any) {
    return this.userService.unipileWebhook(body);
  }

  @Get('unipile/:userId/link')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async linkUnipileAccount(@Res() res, @Param('userId') userId: string) {
    const url = await this.userService.linkUnipileAccount(userId);

    return res.redirect(url);
  }

  @Delete('unipile/:userId/unlink')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async unlinkUnipileAccount(@Res() res, @Param('userId') userId: string) {
    return this.userService.unlinkUnipileAccount(userId);
  }

  @Post('/unipile/:userId/webhook')
  // This is a public webhook endpoint, no authentication or permission required
  async createUnipileWebhook(@Param('userId') userId: string, @Body() body: any) {
    return this.userService.saveUnipileLinkedAccount(userId, body);
  }

  @Get('/view-as/:userId/get-uniplie-profile')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getUnipileProfileLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.userService.getUnipileProfile(userId);
  }

  @Get('/get-uniplie-profile')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getUnipileProfile(@Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userService.getUnipileProfile(userId);
  }

  @Post('/view-as/:userId/reconnect-uniplie-account')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async reconnectUnipileAccountLegacy(
    @Body() body: LinkedConnectionDto,
    @Param('userId') userId: string,
  ) {
    // TODO: Remove this after client migration is complete
    return this.userService.reconnectAccount(body, userId);
  }

  @Post('/reconnect-uniplie-account')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async reconnectUnipileAccount(@Body() body: LinkedConnectionDto, @Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userService.reconnectAccount(body, userId);
  }

  // TODO: Remove view-as
  @Post('/view-as/:userId/onboarding-company')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.COMPANY_ONBOARDING].Write)
  async onboardingCompany(
    @Req() req: any,
    @Body() body: OnboardingCompanyDto,
    @Param('userId') userIdFromParam: string,
  ) {
    const userId = req?.viewAsUser?.id || userIdFromParam;
    return this.userService.onboardingCompany(body, userId);
  }

  // TODO Remove view-as
  @Put('/view-as/:userId/onboarding-company/status')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.COMPANY_APPROVAL].Write)
  async updateOnboardingCompanyStatus(
    @Req() req: any,
    @Body() body: { status: OrganizationStatusEnum; companyId: string },
    @Param('userId') userIdFromParam: string,
  ) {
    const userId = req?.viewAsUser?.id || userIdFromParam;
    return this.userService.updateOnboardingCompanyStatus(body, userId);
  }

  // TOOD: Remove view-as
  @Post('/view-as/:userId/single-onboarding-employee')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.COMPANY_ONBOARDING].Write)
  async singleOnboardingEmployee(
    @Req() req: any,
    @Body() body: SingleOnboardingUserDto,
    @Param('userId') userIdFromParam: string,
  ) {
    const userId = req?.viewAsUser?.id || userIdFromParam;
    return this.userService.singleOnboardingEmployee(body, userId);
  }

  @Get('credits/management')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getCreditManagement(@Req() req, @Query('organizationId') queryOrgId?: string) {
    const { id: userId, role } = req.viewAsUser;

    // Determine which organization to query
    let targetOrgId = null;

    // Super Admin/Sales can specify organizationId via query param or get system-wide data
    if (role === RoleEnum.SUPER_ADMIN || role === RoleEnum.SALES) {
      if (queryOrgId) {
        targetOrgId = queryOrgId; // Specific organization
      }
    }

    return this.userService.getCompanyCreditManagement(targetOrgId, userId);
  }

  @Get('credits/stats')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getCreditStats(@Req() req, @Query('organizationId') queryOrgId?: string) {
    const { id: userId, role } = req.viewAsUser;

    // Determine which organization to query
    let targetOrgId = null;

    // Super Admin/Sales can specify organizationId via query param or get system-wide data
    if (role === RoleEnum.SUPER_ADMIN || role === RoleEnum.SALES) {
      if (queryOrgId) {
        targetOrgId = queryOrgId; // Specific organization
      }
    }

    return this.userService.getCreditStats(targetOrgId, userId);
  }

  @Post('credits/topup')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async topupCredits(@Body() body: TopupCreditsDto, @Req() req) {
    const { id: userId, organizationId } = req.user;
    return this.userService.topupCredits(organizationId, userId, body);
  }

  @Post('credits/topup/confirm')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async confirmTopupPayment(@Body() body: ConfirmTopupPaymentDto, @Req() req) {
    const { id: userId, organizationId } = req.user;
    return this.userService.confirmTopupPayment(organizationId, userId, body.paymentIntentId);
  }

  @Get('credits/topup-history')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getTopupHistory(@Req() req) {
    const { id: userId, organizationId } = req.viewAsUser;
    return this.userService.getTopupHistory(organizationId, userId);
  }

  @Get('credits/packages')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getTopupPackages(@Req() req) {
    const { id: userId } = req.viewAsUser;
    return this.userService.getTopupPackages(userId);
  }

  @Put('organization-id/:organizationId/upsert-credit-management')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  async upsertCreditManagement(
    @Param('organizationId') organizationId: string,
    @Body() body: UpsertCreditManagementDto,
    @Req() req: any,
  ) {
    const userId = req.viewAsUser?.id;
    return this.userService.upsertCreditManagement(organizationId, userId, body);
  }

  @Post('domain-authentication/create')
  @UseGuards(AuthenticationGuard)
  async createDomainAuth(@Body() data: DomainAuthDto, @Req() req) {
    const userId = req.viewAsUser.id;
    return this.domainVerificationService.createDomainAuth(userId, data);
  }

  @Get('domain-authentication/verify')
  @UseGuards(AuthenticationGuard)
  async checkDomainValidation(@Req() req) {
    const userId = req.viewAsUser.id;
    return this.domainVerificationService.checkDomainValidation(userId);
  }

  @Post('brand-link/create')
  @UseGuards(AuthenticationGuard)
  async createBrandLink(@Body() data: BrandLinkDto, @Req() req) {
    const userId = req.viewAsUser.id;
    return this.domainVerificationService.createAndVerifyBrandLink(userId, data);
  }

  @Post('complete-onboarding')
  @UseGuards(AuthenticationGuard)
  async completeOnboarding(@Req() req: any, @Body() completeOnboardingDto: CompleteOnboardingDto) {
    const userId = req.user.id; // Not use view-as for onboarding
    return this.userService.completeOnboarding(userId, completeOnboardingDto);
  }

  @Post('quick-create-user')
  @UseGuards(AuthenticationGuard)
  async quickCreateUser(@Req() req: any, @Body() quickCreateUserDto: QuickCreateUserDto) {
    const userId = req.user.id; // Not use view-as for quick create user
    return this.userService.quickCreateUser(userId, quickCreateUserDto);
  }

  @Get('/metrics/list-contact-added')
  @UseGuards(AuthenticationGuard)
  async getContactAdded(@Req() req: any, @Query() query: GetDashboardMetricsQuery) {
    const userId = req.viewAsUser.id;
    return this.userService.getContactAdded(userId, query);
  }

  @Get('/metrics/list-company-added')
  @UseGuards(AuthenticationGuard)
  async getCompanyAdded(@Req() req: any, @Query() query: GetDashboardMetricsQuery) {
    const userId = req.viewAsUser.id;
    return this.userService.getCompanyAdded(userId, query);
  }
}

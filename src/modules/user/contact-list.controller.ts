import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { ContactListService } from './contact-list.service';
import {
  AddContactToContactListDto,
  BulkAddContactToListDto,
  CreateContactListDto,
  EditContactListDto,
  GenerateCSVDto,
  InsertContactToListDto,
  ToggleUpdateSubscribedDto,
  UpdateContactInListDto,
} from './dto/create-contact-list.dto';
import { ContactListQueryDto } from './dto/ContactListQueryDto';
import { FileInterceptor } from '@nestjs/platform-express';
import { MAX_IMAGE_SIZE_IN_BYTES } from './constants/user-signature.constants';
import { Response } from 'express';
import * as path from 'path';
import { csvFileFilter } from 'src/configs/file-configs.constants';
import { BulkDeleteContactsDto, BulkDeleteListDto } from './dto/contact.dto';
import { ContactListsQueryDto } from './dto/contact-lists.dto';

@ApiTags('Contact List')
@Controller('contact-list')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class ContactListController {
  constructor(private readonly contactListService: ContactListService) { }

  // @Post('view-as/:userId')
  // async createContactList(@Param('userId') userId: string, @Body() contactList: CreateContactListDto) {
  //   return this.contactListService.createContactList(userId, contactList);
  // }

  @Get('view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Read)
  async getContactListLegacy(@Param('userId') userId: string, @Query() query: ContactListsQueryDto) {
    // TODO: Remove this after client migration is complete
    return this.contactListService.getContactList(userId, query);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Read)
  async getContactList(@Req() req: any, @Query() query: ContactListsQueryDto) {
    const userId = req.viewAsUser.id;
    return this.contactListService.getContactList(userId, query);
  }

  @Get('view-as/:userId/list/:listId')
  async getDetailContactListLegacy(
    @Param('userId') userId: string,
    @Param('listId') listId: string,
    @Query() queryDto: ContactListQueryDto
  ) {
    // TODO: Remove this after client migration is complete
    return this.contactListService.getDetailContactList(userId, listId, queryDto);
  }

  @Get('list/:listId')
  async getDetailContactList(
    @Req() req: any,
    @Param('listId') listId: string,
    @Query() queryDto: ContactListQueryDto
  ) {
    const userId = req.viewAsUser.id;
    return this.contactListService.getDetailContactList(userId, listId, queryDto);
  }

  @Delete('view-as/:userId/list/:list')
  async deleteContactListLegacy(@Param('userId') userId: string, @Param('list') list: string) {
    // TODO: Remove this after client migration is complete
    return this.contactListService.deleteContactList(userId, list);
  }

  @Delete('list/:list')
  async deleteContactList(@Req() req: any, @Param('list') list: string) {
    const userId = req.viewAsUser.id;
    return this.contactListService.deleteContactList(userId, list);
  }

  @Delete('view-as/:userId/contact/:contactId')
  async deleteContactInListLegacy(@Param('userId') userId: string, @Param('contactId') contactId: string) {
    // TODO: Remove this after client migration is complete
    return this.contactListService.deleteContactInList(userId, contactId);
  }

  @Delete('contact/:contactId')
  async deleteContactInList(@Req() req: any, @Param('contactId') contactId: string) {
    const userId = req.viewAsUser.id;
    return this.contactListService.deleteContactInList(userId, contactId);
  }

  @Put('view-as/:userId/contact/:contactId')
  async updateContactInListLegacy(
    @Param('userId') userId: string,
    @Param('contactId') contactId: string,
    @Body() body: UpdateContactInListDto
  ) {
    // TODO: Remove this after client migration is complete
    return this.contactListService.updateContactInList(userId, contactId, body);
  }

  @Put('contact/:contactId')
  async updateContactInList(
    @Req() req: any,
    @Param('contactId') contactId: string,
    @Body() body: UpdateContactInListDto
  ) {
    const userId = req.viewAsUser.id;
    return this.contactListService.updateContactInList(userId, contactId, body);
  }

  @Post('view-as/:userId/toggle-update-subscribed')
  async toggleUpdateSubscribedLegacy(
    @Param('userId') userId: string,
    @Param('contactId') contactId: string,
    @Body() body: ToggleUpdateSubscribedDto
  ) {
    // TODO: Remove this after client migration is complete
    return this.contactListService.toggleUpdateSubscribed(body.email, body.unsubscribed);
  }

  @Post('toggle-update-subscribed')
  async toggleUpdateSubscribed(
    @Req() req: any,
    @Body() body: ToggleUpdateSubscribedDto
  ) {
    return this.contactListService.toggleUpdateSubscribed(body.email, body.unsubscribed);
  }

  @Put('view-as/:userId/list/:list')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  async editContactListLegacy(
    @Param('userId') userId: string,
    @Param('list') list: string,
    @Body() contactList: EditContactListDto
  ) {
    // TODO: Remove this after client migration is complete
    return this.contactListService.editContactList(userId, list, contactList);
  }

  @Put('list/:list')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  async editContactList(
    @Req() req: any,
    @Param('list') list: string,
    @Body() contactList: EditContactListDto
  ) {
    const userId = req.viewAsUser.id;
    return this.contactListService.editContactList(userId, list, contactList);
  }

  @Post('view-as/:userId/add-contact-to-list')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  async addContactToListLegacy(@Param('userId') userId: string, @Body() contactListDto: AddContactToContactListDto) {
    // TODO: Remove this after client migration is complete
    return this.contactListService.addContactToList(userId, contactListDto);
  }

  @Post('add-contact-to-list')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  async addContactToList(@Req() req: any, @Body() contactListDto: AddContactToContactListDto) {
    const userId = req.viewAsUser.id;
    return this.contactListService.addContactToList(userId, contactListDto);
  }

  @Post('view-as/:userId/add-contact-to-list/bulk')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  async bulkAddContactToListLegacy(@Param('userId') userId: string, @Body() data: BulkAddContactToListDto) {
    // TODO: Remove this after client migration is complete
    return this.contactListService.bulkAddContactToList(userId, data);
  }

  @Post('add-contact-to-list/bulk')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  async bulkAddContactToList(@Req() req: any, @Body() data: BulkAddContactToListDto) {
    const userId = req.viewAsUser.id;
    return this.contactListService.bulkAddContactToList(userId, data);
  }

  @Post('view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        csvFile: {
          type: 'string',
          format: 'binary',
        },
        name: {
          type: 'string',
        },
      },
      required: ['name'],
    },
  })
  @UseInterceptors(
    FileInterceptor('csvFile', {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: csvFileFilter,
    })
  )
  async createContactListLegacy(
    @UploadedFile() csvFile: Express.Multer.File,
    @Body() body: CreateContactListDto,
    @Param('userId') userId: string
  ) {
    // TODO: Remove this after client migration is complete
    return await this.contactListService.createContactListWithCsv(csvFile, body, userId);
  }

  @Post()
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        csvFile: {
          type: 'string',
          format: 'binary',
        },
        name: {
          type: 'string',
        },
      },
      required: ['name'],
    },
  })
  @UseInterceptors(
    FileInterceptor('csvFile', {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: csvFileFilter,
    })
  )
  async createContactList(
    @UploadedFile() csvFile: Express.Multer.File,
    @Body() body: CreateContactListDto,
    @Req() req: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.contactListService.createContactListWithCsv(csvFile, body, userId);
  }

  @Post('/view-as/:userId/insert-to-list')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        csvFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('csvFile', {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: csvFileFilter,
    })
  )
  async insertToListLegacy(
    @UploadedFile() csvFile: Express.Multer.File,
    @Body() body: InsertContactToListDto,
    @Param('userId') userId: string
  ) {
    // TODO: Remove this after client migration is complete
    return await this.contactListService.insertCsvContactList(csvFile, body, userId);
  }

  @Post('/insert-to-list')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        csvFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('csvFile', {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: csvFileFilter,
    })
  )
  async insertToList(
    @UploadedFile() csvFile: Express.Multer.File,
    @Body() body: InsertContactToListDto,
    @Req() req: any
  ) {
    const userId = req.viewAsUser.id;
    return await this.contactListService.insertCsvContactList(csvFile, body, userId);
  }

  @Post('/download-csv-with-data')
  @Permission(PermissionResource[ResourceEnum.CONTACT_LIST].Read)
  downloadCsv(@Res() res: Response, @Body() body: GenerateCSVDto) {
    const data = body.data;
    const csvStream = this.contactListService.generateCsv(data);

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="errors.csv"');
    csvStream.pipe(res);
  }

  @Get('/download-example-csv')
  downloadExampleCSV(@Res() res: Response) {
    const filePath = path.join(process.cwd(), 'public', 'example.csv');

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="example.csv"');
    res.download(filePath, 'example.csv', (err) => {
      if (err) {
        console.error('Error while sending file:', err);
        res.status(500).send('Error while sending the file');
      }
    });
  }

  @Delete('bulk-delete-list')
  bulkDeleteList(@Body() bodyDto: BulkDeleteListDto) {
    return this.contactListService.bulkDeleteList(bodyDto)
  }

  @Delete('bulk-delete-contacts')
  bulkDeleteContacts(@Body() bodyDto: BulkDeleteContactsDto) {
    return this.contactListService.bulkDeleteContacts(bodyDto);
  }

  @Get('simple-list')
  getSimpleListByIds(@Query('ids') ids: string[]) {
    return this.contactListService.getSimpleListByIds(ids);
  }

  @Get('/contacts')
  getContacts(@Query('companyName') companyName: string) {
    return this.contactListService.getContacts({ companyName });
  }
}

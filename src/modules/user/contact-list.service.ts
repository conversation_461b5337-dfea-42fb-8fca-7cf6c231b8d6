import { Promise } from 'bluebird';
import { I18nService } from 'nestjs-i18n';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { UserRepository } from './repositories/user.repository';
import { BaseAbstractService } from '../../base/base.abstract.service';
import { ContactListRepository } from './repositories/contact-list.repository';
import { ContactRepository } from './repositories/contact.repository';
import {
  AddContactToContactListDto,
  BulkAddContactToListDto,
  CreateContactListDto,
  EditContactListDto,
  InsertContactToListDto,
  UpdateContactInListDto,
} from './dto/create-contact-list.dto';
import { ContactListEntity } from './entities/contact-list.entity';
import { DataSource, FindManyOptions, In } from 'typeorm';
import { ContactListQueryDto } from './dto/ContactListQueryDto';
import { Readable } from 'stream';
import { UserEntity } from './entities/user.entity';
import { handleProcessCSV } from 'src/common/utils/processCsv.util';
import { BulkDeleteContactsDto, BulkDeleteListDto } from './dto/contact.dto';
import { getLinkedInIdentifier, getProfile } from '../jobs/utils/unipile-service.utils';
import { FileUploadService } from '../files-upload/file-upload.service';
import { convertArrayToObject } from 'src/common/utils/helpers.util';
import { ContactListsQueryDto, QUERY_CONTACTLIST_TYPE } from './dto/contact-lists.dto';
import { LinkedInFinderService } from '../linkedin-finder/linkedin-finder.service';
import { StatsEntityDataRepository } from '../jobs/repository/stats-entity-data.repository';
import { StatisticItemEntity } from '../jobs/entities/statistic-item.entity';

@Injectable()
export class ContactListService extends BaseAbstractService {
  private readonly logger = new Logger(ContactListService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly contactListRepository: ContactListRepository,
    private readonly contactRepository: ContactRepository,
    readonly i18nService: I18nService,
    private readonly dataSource: DataSource,
    private readonly fileUploadService: FileUploadService,
    private readonly linkedinFinderService: LinkedInFinderService,
    private readonly statsEntityDataRepository: StatsEntityDataRepository,
    
  ) {
    super(i18nService);
  }

  private async handleInsertContactList(user: UserEntity, body: CreateContactListDto) {
    const contactList = {
      name: body.name,
      contactCount: 0,
      user: user,
      contactListDescription: body?.contactListDescription,
      location: body?.location,
      organizationId: user?.organizationId || null,
    };

    const dataInsert = await this.contactListRepository.insert(contactList);
    return { id: dataInsert.identifiers[0].id };
  }

  async createContactList(userId: string, body: CreateContactListDto) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const contactList = {
        name: body.name,
        contactCount: 0,
        user: user,
        contactListDescription: body?.contactListDescription,
        location: body?.location,
      };

      await this.contactListRepository.insert(contactList);

      return this.formatOutputData({ key: 'CREATE_NEW_CONTACT_LIST' }, { data: {} });
    } catch (error) {
      console.log('Error in createContactList', error);
      return this.throwCommonMessage('CREATE_NEW_CONTACT_LIST', error);
    }
  }

  async getContactList(userId: any, query: ContactListsQueryDto) {
    const { type } = query;

    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const orgId = user?.organizationId || null;

      const queryBuilder = this.contactListRepository
        .createQueryBuilder('cl')
        .leftJoinAndSelect('cl.contacts', 'contacts')
        .select([
          'cl.id',
          'cl.name',
          'cl.contactCount',
          'cl.contactListDescription',
          'cl.organizationId',
          'cl.location',
          'cl.updatedAt',
          'contacts.id',
          'contacts.name',
        ])
        .orderBy('cl.createdAt', 'DESC');

      if (orgId && type === QUERY_CONTACTLIST_TYPE.COMPANY_CONTACTLIST) {
        queryBuilder.where('( cl.user_id != :userId AND cl.organizationId = :orgId )', {
          userId,
          orgId,
        });
      } else {
        // If the user is viewing their own contact list
        queryBuilder.where('cl.user_id = :userId', { userId });
      }

      const contactList = await queryBuilder.getMany();

      return this.formatOutputData({ key: 'GET_CONTACT_LIST' }, { data: contactList });
    } catch (error) {
      console.log('Error in getContactList', error);
      return this.throwCommonMessage('GET_CONTACT_LIST', error);
    }
  }

  async getDetailContactList(userId: string, listId: string, param: ContactListQueryDto) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const { searchText, page = 1, limit = 10 } = param;
      const getContactQuery = this.contactRepository
        .createQueryBuilder('c')
        .where('c.contact_list = :listId', { listId })
        .orderBy('c.created_at', 'DESC');

      if (searchText) {
        getContactQuery.andWhere('(c.name ilike :searchText or c.contact_title ilike :searchText)', {
          searchText: `%${param.searchText}%`,
        });
      }

      if (param.listContactIds && param.listContactIds.length > 0) {
        const listContactIds = param.listContactIds.split(',');
        getContactQuery.andWhere('c.id IN (:...listContactIds)', { listContactIds: listContactIds });
      }

      getContactQuery.take(limit).skip((page - 1) * limit);
      const [contactDetail, count] = await getContactQuery.getManyAndCount();

      return this.formatOutputData({ key: 'GET_DETAIL_CONTACT_LIST' }, { data: { items: contactDetail, count } });
    } catch (error) {
      console.log('Error in getDetailContactList', error);
      return this.throwCommonMessage('GET_DETAIL_CONTACT_LIST', error);
    }
  }

  async deleteContactList(userId: string, listId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const contactList = await this.contactListRepository.findOne({ where: { id: listId, user: { id: userId } } });
      if (!contactList) {
        throw new BadRequestException('contactList not found');
      }

      await this.contactRepository.delete({ contactList: { id: listId } });
      await this.contactListRepository.delete(listId);

      return this.formatOutputData({ key: 'DELETE_CONTACT_LIST' }, { data: {} });
    } catch (error) {
      console.log('Error in deleteContactList', error);
      return this.throwCommonMessage('DELETE_CONTACT_LIST', error);
    }
  }

  async deleteContactInList(userId: string, contactId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const contactIntList = await this.contactRepository.findOne({
        where: { id: contactId },
        relations: { contactList: true },
      });
      if (!contactIntList) {
        throw new BadRequestException('contactInList not found');
      }

      await this.contactRepository.delete({ id: contactId });
      await this.contactListRepository.update(contactIntList.contactList.id, {
        contactCount: contactIntList.contactList.contactCount - 1,
      });

      return this.formatOutputData({ key: 'DELETE_CONTACT_IN_LIST' }, { data: {} });
    } catch (error) {
      console.log('Error in deleteContactInList', error);
      return this.throwCommonMessage('DELETE_CONTACT_IN_LIST', error);
    }
  }

  async updateContactInList(userId: string, contactId: string, body: UpdateContactInListDto) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const contactIntList = await this.contactRepository.findOne({
        where: { id: contactId },
        relations: { contactList: true },
      });
      if (!contactIntList) {
        throw new BadRequestException('contactInList not found');
      }

      const { linkedInProfileUrl, companyName } = body;
      const linkedInIdentifier = getLinkedInIdentifier(linkedInProfileUrl);
      const linkedInProfile = await this.linkedinFinderService.getRetrieveProfile(linkedInIdentifier);
      const linkedInProfileImageKey = await this.fileUploadService.uploadFileByUrl(
        linkedInProfile?.profile_picture_url,
        'assets',
        `${linkedInIdentifier}-${new Date().getTime()}`
      );

      const dataUpdateContact = {
        name: body.name,
        email: body.email?.toLowerCase(),
        phone: body.phone,
        contactTitle: body.contactTitle,
        contactLocation: body.contactLocation,
        linkedInProfileUrl: body?.linkedInProfileUrl,
        linkedInProfileImageUrl: linkedInProfileImageKey,
        companyName: companyName ? companyName : null,
        linkedInFirstName: linkedInProfile?.first_name,
        linkedInLastName: linkedInProfile?.last_name,
      };

      await this.contactRepository.update(contactId, dataUpdateContact);

      return this.formatOutputData({ key: 'UPDATE_CONTACT_IN_LIST' }, { data: dataUpdateContact });
    } catch (error) {
      console.log('Error in updateContactInList', error);
      return this.throwCommonMessage('UPDATE_CONTACT_IN_LIST', error);
    }
  }

  async toggleUpdateSubscribed(email: string, unsubscribed: boolean) {
    try {
      await this.contactRepository
        .createQueryBuilder()
        .update()
        .set({ isUnsubscribeEmail: unsubscribed })
        .where('email = :email', { email })
        .execute();

      return this.formatOutputData({ key: 'TOGGLE_UPDATE_SUBSCRIBED' }, { data: {} });
    } catch (error) {
      console.log('Error in toggleUpdateSubscribed', error);
      return this.throwCommonMessage('TOGGLE_UPDATE_SUBSCRIBED', error);
    }
  }

  async addContactToList(userId: string, body: AddContactToContactListDto) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const contactList = await this.contactListRepository.find({
        where: { id: In(body.listIds), user: { id: user.id } },
      });

      if (contactList.length === 0) {
        throw new BadRequestException('Cannot find Contact Lists');
      }

      const { linkedinUrl, companyName } = body;
      let linkedInIdentifier = '';
      let linkedInProfile: any = {};
      let linkedInProfileImageKey = "";
      try {
        linkedInIdentifier = getLinkedInIdentifier(linkedinUrl);
        linkedInProfile = await this.linkedinFinderService.getRetrieveProfile(linkedInIdentifier);
        linkedInProfileImageKey = await this.fileUploadService.uploadFileByUrl(
          linkedInProfile.profile_picture_url,
          'assets',
          `${linkedInIdentifier}-${new Date().getTime()}`
        );
      } catch (err) {
        linkedInProfileImageKey = null;
        linkedInProfile = null;
        linkedInProfileImageKey = null;
      }

      const dataCreate = contactList?.map((item) => {
        return {
          name: body?.name ? body?.name : body?.firstName + '  ' + body?.lastName,
          phone: body.phone,
          email: body.email?.toLowerCase(),
          contact_apollo_id: body.contactApolloId,
          contactList: item,
          contactTitle: body.contactTitle,
          contactLocation: body.contactLocation,
          linkedInProfileUrl: body?.linkedinUrl,
          firstName: body?.firstName,
          lastName: body?.lastName,
          linkedInProfileImageUrl: linkedInProfileImageKey,
          linkedInFirstName: linkedInProfile?.public_identifier === "undefined" ? "" : (linkedInProfile?.first_name || ""),
          linkedInLastName: linkedInProfile?.public_identifier === "undefined" ? "" : (linkedInProfile?.last_name || ""),
          companyName: companyName ? companyName : null,
          isUnsubscribeEmail: body?.unsubscribed || false,
        };
      });

      await this.dataSource
        .createQueryBuilder()
        .update(ContactListEntity)
        .set({ contactCount: () => 'contact_count + 1' })
        .where('id IN (:...ids)', { ids: body.listIds })
        .execute();

      await this.contactRepository.insert(dataCreate);

      return this.formatOutputData({ key: 'ADD_CONTACT_TO_LIST' }, { data: { dataCreate } });
    } catch (error) {
      console.log('Error in addContactToList', error);
      return this.throwCommonMessage('ADD_CONTACT_TO_LIST', error);
    }
  }

  async getLinkedInProfile(unipileAccountId: string, linkedInUrl: string) {
    try {
      const linkedInIdentifier = getLinkedInIdentifier(linkedInUrl);
      const linkedInProfile = await this.linkedinFinderService.getRetrieveProfile(linkedInIdentifier);
      const linkedInProfileImageKey = await this.fileUploadService.uploadFileByUrl(
        linkedInProfile.profile_picture_url,
        'assets',
        `${linkedInIdentifier}-${new Date().getTime()}`
      );

      return { ...linkedInProfile, linkedInProfileImageKey };
    } catch (e) {
      console.log('Error get profile image', e);

      return null;
    }
  }

  async bulkAddContactToList(userId: string, data: BulkAddContactToListDto) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const { listIds } = data;
      const contacts = data.contacts.filter(
        (contact) => contact.email && contact.email.toLowerCase() !== '<EMAIL>'
      );

      const contactList = await this.contactListRepository.find({
        where: { id: In(listIds), user: { id: user.id } },
      });

      if (contactList.length === 0) {
        throw new BadRequestException('Cannot find Contact Lists');
      }

      const countByList: any = {};

      const dataContacts = (
        await Promise.map(
          contactList,
          async (item) => {
            const linkedInProfiles = await Promise.all(
              contacts.map(async (item) => {
                const linkedInProfile = await this.getLinkedInProfile(user.unipileAccountId, item.linkedinUrl);

                return { email: item.email?.toLowerCase(), linkedInProfile };
              })
            );

            const linkedInProfileMapping = convertArrayToObject(linkedInProfiles, 'email');

            const existingContacts = await this.contactRepository.find({
              where: {
                contactList: { id: item.id },
                email: In(contacts.map((contact) => contact.email?.toLowerCase())),
              },
            });
            const existingEmails = existingContacts.map((contact) => contact.email);

            const insertContacts = contacts
              .filter((contact) => !existingEmails.includes(contact.email))
              .map((contact) => ({
                name: contact?.name ? contact?.name : `${contact?.firstName} ${contact?.lastName}`,
                phone: contact.phone,
                email: contact.email.toLowerCase(),
                contact_apollo_id: contact.contactApolloId,
                contactList: item,
                contactTitle: contact.contactTitle,
                contactLocation: contact.contactLocation,
                linkedInProfileUrl: contact?.linkedinUrl,
                firstName: contact?.firstName,
                lastName: contact?.lastName,
                linkedInProfileImageUrl:
                  linkedInProfileMapping[contact.email]?.linkedInProfile?.linkedInProfileImageKey,
                linkedInFirstName: linkedInProfileMapping[contact.email]?.linkedInProfile?.first_name,
                linkedInLastName: linkedInProfileMapping[contact.email]?.linkedInProfile?.last_name,
                companyName: contact.companyName ? contact.companyName : null,
              }));
            if (insertContacts.length) {
              countByList[item.id] = insertContacts.length;
            }

            return insertContacts;
          },
          { concurrency: 20 }
        )
      ).flat();

      if (dataContacts.length) {
        await Promise.all([
          this.contactRepository.insert(dataContacts),
          Object.keys(countByList).map((listId) =>
            this.dataSource
              .createQueryBuilder()
              .update(ContactListEntity)
              .set({ contactCount: () => `contact_count + ${countByList[listId]}` })
              .where('id = :listId', { listId })
              .execute()
          ),
        ]);
      }

      // add to stats

      const date = new Date().toISOString().split('T')[0];
      const statsType = `BULK_ADD_CONTACT_TO_LIST`;
      const statsEntity = await this.statsEntityDataRepository.findOne({
        where: {
            type: statsType,
            date,
            user_id: userId,
            country: "",
          },
      });

       if (statsEntity?.id) {
                // If exists, increment the count
                await this.statsEntityDataRepository
                  .createQueryBuilder()
                  .update(StatisticItemEntity)
                  .set({ count: () => 'count + 1' })
                  .where('id = :id', { id: statsEntity.id })
                  .execute();
              } else {
                // If not, create a new entry with count 1
                await this.statsEntityDataRepository.insert({
                  type: statsType,
                  date,
                  user_id: userId,
                  country: '',
                  count: dataContacts.length,
                });
              }

      console.info(
        `[CONTACT_LIST] @RequestId: ${data?.requestId}, @Status: SUCCESS, @Payload: ${JSON.stringify(
          dataContacts
        )}, @Response: ${JSON.stringify(data)}`
      );
      return this.formatOutputData({ key: 'BULK_ADD_CONTACT_TO_LIST' }, { data: {} });
    } catch (error) {
      console.error(`[CONTACT_LIST] @RequestId: ${data?.requestId}, @Status: FAIL, @Payload: ${JSON.stringify(data)}`);
      console.log('Error in bulkAddContactToList', error);

      return this.throwCommonMessage('BULK_ADD_CONTACT_TO_LIST', error);
    }
  }

  async editContactList(userId: string, listId: string, body: EditContactListDto) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const contactList = await this.contactListRepository.findOne({ where: { id: listId, user: { id: userId } } });
      if (!contactList) {
        throw new BadRequestException('contactList not found');
      }

      await this.contactListRepository.update(listId, {
        name: body.name,
        contactListDescription: body?.contactListDescription,
        location: body?.location,
      });

      return this.formatOutputData({ key: 'EDIT_CONTACT_LIST' }, { data: {} });
    } catch (error) {
      console.log('Error in editContactList', error);
      return this.throwCommonMessage('EDIT_CONTACT_LIST', error);
    }
  }

  async createContactListWithCsv(csvFile: Express.Multer.File, body: CreateContactListDto, userId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      let csvData: any = {};
      if (csvFile) {
        csvData = await handleProcessCSV(csvFile);
      }

      const dataInsert = await this.handleInsertContactList(user, body);

      if (csvData?.data?.length > 0 && dataInsert?.id) {
        try {
          const dataContact = await Promise.all(
            csvData?.data.map(async (item: any) => {
              let linkedInProfileImageKey: string | undefined;
              let linkedInProfile: any;

              if (item?.linkedInProfileUrl) {
                try {
                  const linkedInIdentifier = getLinkedInIdentifier(item?.linkedInProfileUrl);
                  linkedInProfile = await this.linkedinFinderService.getRetrieveProfile(linkedInIdentifier);

                  if (linkedInProfile?.profile_picture_url) {
                    linkedInProfileImageKey = await this.fileUploadService.uploadFileByUrl(
                      linkedInProfile.profile_picture_url,
                      'assets',
                      `${linkedInIdentifier}-${new Date().getTime()}`
                    );
                  }
                } catch (e) {
                  console.error('Error getting profile image', e);
                }
              }

              return {
                name: `${item?.firstName || ''} ${item?.lastName || ''}`.trim(),
                email: item?.email?.toLowerCase(),
                phone: item?.phoneNumber,
                contactTitle: item?.jobTitle,
                linkedInProfileUrl: item?.linkedInProfileUrl,
                firstName: item?.firstName,
                lastName: item?.lastName,
                contactList: dataInsert?.id,
                linkedInProfileImageUrl: linkedInProfileImageKey || null, // Ensure it's set
              };
            })
          );

          await this.dataSource
            .createQueryBuilder()
            .update(ContactListEntity)
            .set({ contactCount: () => `contact_count + ${dataContact.length}` })
            .where('id = :id', { id: dataInsert.id })
            .execute();

          await this.contactRepository.insert(dataContact);
        } catch (error) {
          console.error('Error processing CSV data:', error);
        }
      }

      return this.formatOutputData({ key: 'CREATE_CONTACT_LIST' }, { data: { ...csvData, dataCreate: dataInsert } });
    } catch (error) {
      console.log('Error in createContactListWithCsv', error);
      return this.throwCommonMessage('CREATE_CONTACT_LIST', error);
    }
  }

  async insertCsvContactList(csvFile: Express.Multer.File, body: InsertContactToListDto, userId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const contactList = await this.contactListRepository.findOne({ where: { id: body.id } });
      if (!contactList) {
        throw new BadRequestException('Could not find contact');
      }
      let dataCreate = {};
      let csvData: any = {};
      if (csvFile) {
        csvData = await handleProcessCSV(csvFile);
      }

      if (csvData?.data?.length > 0) {
        try {
          const dataContact = await Promise.all(
            csvData?.data.map(async (item: any) => {
              let linkedInProfileImageKey: string | undefined;
              let linkedInProfile: any;
              // Handle LinkedIn Profile Image if URL exists
              if (item?.linkedInProfileUrl) {
                try {
                  const linkedInIdentifier = getLinkedInIdentifier(item?.linkedInProfileUrl);
                  linkedInProfile = await this.linkedinFinderService.getRetrieveProfile(linkedInIdentifier);
                  if (linkedInProfile?.profile_picture_url) {
                    linkedInProfileImageKey = await this.fileUploadService.uploadFileByUrl(
                      linkedInProfile.profile_picture_url,
                      'assets',
                      `${linkedInIdentifier}-${new Date().getTime()}`
                    );
                  }
                } catch (e) {
                  console.error('Error getting profile image', e);
                }
              }

              return {
                name: `${item?.firstName || ''} ${item?.lastName || ''}`.trim(),
                email: item?.email?.toLowerCase(),
                phone: item?.phoneNumber,
                contactTitle: item?.jobTitle,
                linkedInProfileUrl: item?.linkedInProfileUrl,
                firstName: item?.firstName,
                lastName: item?.lastName,
                contactList: contactList.id,
                linkedInProfileImageUrl: linkedInProfileImageKey || null, // Include the profile image URL if available
                companyName: item?.companyName || null,
              };
            })
          );
          // Update contact count in ContactListEntity
          await this.dataSource
            .createQueryBuilder()
            .update(ContactListEntity)
            .set({ contactCount: () => `contact_count + ${dataContact.length}` })
            .where('id = :id', { id: contactList.id })
            .execute();

          // Insert data into the contact repository
          dataCreate = await this.contactRepository.insert(dataContact);
        } catch (error) {
          console.error('Error processing CSV data:', error);
        }
      }

      return this.formatOutputData(
        { key: 'INSERT_CSV_CONTACT_LIST' },
        { data: { ...csvData, dataCreate: dataCreate } }
      );
    } catch (error) {
      console.log('Error in insertCsvContactList', error);
      return this.throwCommonMessage('INSERT_CSV_CONTACT_LIST', error);
    }
  }

  generateCsv(data: any[]): Readable {
    const readable = new Readable({
      read() { },
    });
    readable.push('Id,firstName,lastName,linkedInProfileUrl,jobTitle,email,phoneNumber,errors\n');
    data.forEach((item) => {
      const index = item.index;
      const errors = item.errors.split(', ').join('\n');
      const firstName = item?.firstName;
      const lastName = item?.lastName;
      const linkedInProfileUrl = item?.linkedInProfileUrl;
      const jobTitle = item?.jobTitle;
      const email = item?.email;
      const phoneNumber = item?.phoneNumber;
      readable.push(
        `${index},"${firstName}","${lastName}","${linkedInProfileUrl}","${jobTitle}","${email}","${phoneNumber}","${errors}"\n`
      );
    });
    readable.push(null);
    return readable;
  }

  async bulkDeleteList(bodyDto: BulkDeleteListDto) {
    const { ids } = bodyDto;
    await this.contactRepository.delete({ contactList: { id: In(ids) } });
    await this.contactListRepository.delete({ id: In(ids) });

    return this.formatOutputData({ key: 'BULK_DELETE_LIST' }, { data: {} });
  }

  //assuming that these contacts belong to one list only
  async bulkDeleteContacts(bodyDto: BulkDeleteContactsDto) {
    const { ids } = bodyDto;
    const firstContact = await this.contactRepository
      .createQueryBuilder('c')
      .where('id = :id', { id: ids[0] })
      .select('c.contact_list as "contactListId" ')
      .getRawOne<{ contactListId: string }>();
    const result = await this.contactRepository.delete({ id: In(ids) });
    const deletedNumber = result.affected;

    await this.contactListRepository.update(firstContact.contactListId, {
      contactCount: () => `contact_count - ${deletedNumber}`,
    });

    return this.formatOutputData({ key: 'BULK_DELETE_CONTACTS' }, { data: {} });
  }

  async getSimpleListByIds(ids: string[]) {
    const data = await this.contactListRepository.find({ where: { id: In(ids) }, select: { id: true, name: true } });
    return this.formatOutputData({ key: 'GET_SIMPLE_LIST_BY_IDS' }, { data });
  }

  async getContacts(query: { companyName: string }) {
    const { companyName } = query;

    const contacts = await this.contactRepository.find({
      where: { companyName },
    });
    return this.formatOutputData({ key: 'GET_CONTACTS' }, { data: contacts });
  }
}

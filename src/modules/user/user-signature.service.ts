import { v4 as uuid } from 'uuid';
import { I18nService } from 'nestjs-i18n';
import { Injectable, Logger, InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { BaseAbstractService } from '../../base/base.abstract.service';
import { FileUploadService } from '../files-upload/file-upload.service';
import { UserRepository } from './repositories/user.repository';
import { UserSignatureRepository } from './repositories/user-signature.repository';
import { UserSignatureDto } from './dto/user-signature.dto';
import { In, Not } from 'typeorm';
import axios from 'axios';

@Injectable()
export class UserSignatureServices extends BaseAbstractService {
  private readonly logger = new Logger(UserSignatureServices.name);

  constructor(
    private readonly userRepository: UserRepository,
    readonly i18nService: I18nService,
    private readonly userSignatureRepository: UserSignatureRepository,
    private fileUploadService: FileUploadService
  ) {
    super(i18nService);
  }

  private async uploadSignatures(
    signatures: Array<Express.Multer.File>,
    userId: string
  ): Promise<{ name: string; imgId: string }[]> {
    if (signatures?.length) {
      return Promise.all(
        signatures.map(async ({ buffer, originalname: name }, i) => {
          const extension = name.split('.').pop();
          const imgId = await this.fileUploadService.uploadPublicFile(
            buffer,
            `signatures/${userId}/${uuid()}.${extension}`
          );
          return { name, imgId };
        })
      );
    }

    return [];
  }

  async getSignatures(userId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }
      const signatures = await this.userSignatureRepository.find({
        where: { user: { id: userId }, createdByEmail: false },
        order: { createdAt: 'DESC' },
      });

      // Get signed url for signature images
      const data = await Promise.all(
        signatures.map(async ({ imgMetadata, ...signature }) => {
          const images = await Promise.all(
            imgMetadata.map(async ({ name, imgId }) => {
              const link = await this.fileUploadService.getSignedViewUrl(imgId);

              return { name, imgId, link };
            })
          );
          return {
            ...signature,
            imgMetadata: images,
          };
        })
      );

      return this.formatOutputData({ key: 'GET_SIGNATURES' }, { data });
    } catch (error) {
      console.log('Error in createSignature', error);
      return this.throwCommonMessage(
        'GET_SIGNATURES_FAILED',
        new InternalServerErrorException('Get signatures failed')
      );
    }
  }

  async getSignatureDefault(userId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const signature = await this.userSignatureRepository.findOne({
        where: { user: { id: userId }, isDefault: true },
      });

      if (!signature) {
        return this.formatOutputData({ key: 'GET_SIGNATURE_DEFAULT' }, { data: {} });
      }
      const images = await Promise.all(
        signature.imgMetadata.map(async ({ name, imgId }) => {
          const link = await this.fileUploadService.getSignedUrl(imgId);

          return { name, imgId, link };
        })
      );

      return this.formatOutputData(
        { key: 'GET_SIGNATURE_DEFAULT' },
        { data: { ...signature, originalImages: signature.imgMetadata, imgMetadata: images } }
      );
    } catch (error) {
      console.log('Error in get Signature Default', error);
      return this.throwCommonMessage(
        'GET_SIGNATURES_DEFAULT_FAILED',
        new InternalServerErrorException('Get Signature default failed')
      );
    }
  }

  async createSignature(userId: string, signatureDto: UserSignatureDto, signatures?: Array<Express.Multer.File>) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const imgMetadata = await this.uploadSignatures(signatures, userId);
      const sigContent = imgMetadata.reduce(
        (acc, { imgId }, i) => acc.replace(`{{IMAGE_${i + 1}}}`, `cid:${imgId}`),
        signatureDto.content
      );

      const savedSignature = await this.userSignatureRepository.insert({
        name: signatureDto.name,
        sigContent: sigContent,
        imgMetadata,
        user,
        createdByEmail: signatureDto?.createdByEmail,
      });

      return this.formatOutputData({ key: 'CREATE_USER_SIGNATURE' }, { data: savedSignature });
    } catch (error) {
      console.log('Error in createSignature', error);
      return this.throwCommonMessage(
        'CREATE_SIGNATURE_FAILED',
        new InternalServerErrorException('Create signature failed')
      );
    }
  }

  async updateSignature(
    userId: string,
    signatureId: string,
    signatureDto: UserSignatureDto,
    signatures?: Array<Express.Multer.File>
  ) {
    try {
      const user = await this.userSignatureRepository.findOne({ where: { id: signatureId, user: { id: userId } } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_SIGNATURE_NOT_EXIST'));
      }

      const imgMetadata = await this.uploadSignatures(signatures, userId);
      const sigContent = imgMetadata.reduce(
        (acc, { imgId }, i) => acc.replace(`{{IMAGE_${i + 1}}}`, `cid:${imgId}`),
        signatureDto.content
      );

      await this.userSignatureRepository.update(
        { id: signatureId },
        {
          name: signatureDto.name,
          sigContent: signatureDto.content,
          imgMetadata,
        }
      );

      return this.formatOutputData({ key: 'UPDATE_USER_SIGNATURE' }, { data: {} });
    } catch (error) {
      console.log('Error in updateSignature', error);
      return this.throwCommonMessage(
        'UPDATE_SIGNATURE_FAILED',
        new InternalServerErrorException('Update signature failed')
      );
    }
  }

  async deleteSignature(userId: string, signatureId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      const userSignature = await this.userSignatureRepository.findOne({
        where: { id: signatureId },
      });

      if (!userSignature) {
        throw new BadRequestException('Signature can not be found');
      }

      await Promise.all([
        ...userSignature.imgMetadata.map((signature) => {
          this.fileUploadService.deleteFileByKey(signature.imgId);
        }),
        this.userSignatureRepository.delete({
          id: signatureId,
        }),
      ]);

      return this.formatOutputData({ key: 'DELETE_SIGNATURE' }, { data: {} });
    } catch (error) {
      console.log('Error in deleteSignature', error);
      return this.throwCommonMessage('DELETE_SIGNATURE_FAILED', error);
    }
  }

  async setDefaultSignature(userId: string, signatureId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      await Promise.all([
        this.userSignatureRepository.update(signatureId, {
          isDefault: true,
        }),
        this.userSignatureRepository.update(
          {
            id: Not(signatureId),
            user: { id: userId },
          },
          {
            isDefault: false,
          }
        ),
      ]);

      return this.formatOutputData({ key: 'SET_DEFAULT_SIGNATURE' }, { data: {} });
    } catch (error) {
      console.log('Error in setDefaultSignature', error);
      return this.throwCommonMessage('SET_DEFAULT_SIGNATURE_FAILED', error);
    }
  }

  async removeDefaultSignature(userId: string, signatureId: string) {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
      }

      await this.userSignatureRepository.update(signatureId, {
        isDefault: false,
      });

      return this.formatOutputData({ key: 'REMOVE_DEFAULT_SIGNATURE' }, { data: {} });
    } catch (error) {
      console.log('Error in removeDefaultSignature', error);
      return this.throwCommonMessage('REMOVE_DEFAULT_SIGNATURE_FAILED', error);
    }
  }

  async bulkDeleteSignature(userId: string, signatureIds: string[]) {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException(await this.i18nService.t('translate.USER_NOT_EXIST'));
    }

    const userSignatures = await this.userSignatureRepository.find({
      where: {
        id: In(signatureIds),
        user: { id: user.id },
        createdByEmail: false,
      },
    });

    const signatureObjKey = userSignatures.reduce((acc, currentSig) => {
      if (currentSig?.imgMetadata?.length > 0) {
        currentSig?.imgMetadata?.forEach((metadata) => {
          acc.push(metadata.imgId);
        });
      }

      return acc;
    }, []);

    return Promise.all([
      this.fileUploadService.bulkDeleteFiles(signatureObjKey),
      this.userSignatureRepository.delete({
        id: In(signatureIds),
        user: { id: user.id },
        createdByEmail: false,
      }),
    ]);
  }

  async urlToBase64(url: string, isUploadToS3: boolean = false) {
    try {
      if (isUploadToS3) {
        const imageKey = await this.fileUploadService.uploadFileByUrl(url, 'assets', `${uuid()}`);
        return this.formatOutputData({ key: 'URL_TO_BASE64' }, { data: imageKey });
      } else {
        const image = await axios.get(url, { responseType: 'arraybuffer' });
        const base64File = Buffer.from(image.data).toString('base64');
        return this.formatOutputData({ key: 'URL_TO_BASE64' }, { data: base64File });
      }
    } catch (error) {
      console.log('Error in urlToBase64', error);
      return this.throwCommonMessage('URL_TO_BASE64_FAILED', error);
    }
  }
}

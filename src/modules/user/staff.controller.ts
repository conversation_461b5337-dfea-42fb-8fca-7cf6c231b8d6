import { Controller, Get, Param, Req, UseGuards } from "@nestjs/common";
import { AuthenticationGuard } from "../auth/guards/auth.guard";
import { PermissionGuard } from "src/guards/permission.guard";
import { ApiTags } from "@nestjs/swagger";
import { SkipThrottle } from "@nestjs/throttler";
import { StaffService } from "./staff.service";
import { ResourceEnum } from "./entities/permission.entity";
import { PermissionResource } from "src/common/constants/permission.constant";
import { Permission } from "src/common/decorators/permissions.decorator";

@ApiTags("Staff")
@Controller("staffs")
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class StaffController {
  constructor(private readonly staffService: StaffService) { }

  @Get()
  @Permission(PermissionResource[ResourceEnum.STAFF_PERFORMANCE].Read)
  getMyStaff(@Req() req: any) {
    const userId = req.viewAsUser?.id || req.user.id;
    return this.staffService.getMyStaff(userId);
  }

  @Get('view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.STAFF_PERFORMANCE].Read)
  getMyStaffForUserLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.staffService.getMyStaff(userId);
  }
}
import { forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { AuthModule } from '../auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from './entities/user.entity';
import { UserRepository } from './repositories/user.repository';
import { MailModule } from '../mail/mail.module';
import { RoleRepository } from './repositories/role.repository';
import { UserPermissionRepository } from './repositories/user-permission.repository';
import { StaffController } from './staff.controller';
import { StaffService } from './staff.service';
import { UserSignatureServices } from './user-signature.service';
import { UserSignatureController } from './user-signature.controller';
import { UserSignatureRepository } from './repositories/user-signature.repository';
import { ContactRepository } from './repositories/contact.repository';
import { ContactListRepository } from './repositories/contact-list.repository';
import { ContactListController } from './contact-list.controller';
import { ContactListService } from './contact-list.service';
import { SequenceRepository } from '../mail/repositories/sequence.repostiory';
import { SequenceInstanceRepository } from '../mail/repositories/sequence-instance.repository';
import { NotificationRepository } from '../notification/repositories/notification.repository';
import { SequenceActivityLogRepository } from '../mail/repositories/sequence-activity-log.repository';
import { UserWorkingTimeRepository } from './repositories/user-working-time.repository';
import { LinkedInFinderService } from '../linkedin-finder/linkedin-finder.service';
import { HttpModule } from '@nestjs/axios';
import { MyCacheModule } from '../cache/cache.module';
import { ApolloService } from '../employee-finder/services/apollo.service';
import { NotificationService } from '../notification/services/notification.service';
import { NotificationController } from '../notification/notification.controller';
import { SubscriptionModule } from '../subscription/subscription.module';
import { UserQuotaRepository } from '../subscription/repositories/user-quota.repository';
import { OrganizationQuotaRepository } from '../subscription/repositories/organization-quota.repository';
import { DomainVerificationService } from './domain-verification.service';
import { StatsEntityDataRepository } from '../jobs/repository/stats-entity-data.repository';
import { JobsModule } from '../jobs/jobs.module';

@Module({
  providers: [
    UserService,
    UserSignatureServices,
    NotificationService,
    UserRepository,
    RoleRepository,
    UserPermissionRepository,
    StaffService,
    UserSignatureRepository,
    ContactRepository,
    ContactListRepository,
    ContactListService,
    SequenceRepository,
    SequenceInstanceRepository,
    SequenceActivityLogRepository,
    NotificationRepository,
    UserWorkingTimeRepository,
    LinkedInFinderService,
    ApolloService,
    UserQuotaRepository,
    OrganizationQuotaRepository,
    DomainVerificationService,
    StatsEntityDataRepository,
  ],
  controllers: [
    UserController,
    StaffController,
    UserSignatureController,
    ContactListController,
    NotificationController,
  ],
  imports: [
    TypeOrmModule.forFeature([UserEntity]),
    JwtModule,
    forwardRef(() => AuthModule),
    MailModule,
    HttpModule,
    MyCacheModule,
    SubscriptionModule,
    forwardRef(() => JobsModule),
  ],
  exports: [UserService, UserRepository, UserSignatureServices, UserSignatureRepository],
})
export class UserModule {}

import { <PERSON><PERSON>nti<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from "typeorm";
import { PermissionEntity } from "./permission.entity";
import { RoleEntity } from "./role.entity";

@Entity('role_default_permissions')
export class RoleDefaultPermissionEntity extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'uuid', nullable: true })
    roleId: string;

    @ManyToOne(() => RoleEntity)
    @JoinColumn({ name: 'roleId' })
    role: RoleEntity;

    @Column({ type: 'uuid', nullable: true  })
    permissionId: string;

    @ManyToOne(() => PermissionEntity)
    @JoinColumn({ name: 'permissionId' })
    permission: PermissionEntity;

    @Column({ default: true })
    allowRead: boolean;

    @Column({ default: true })
    allowWrite: boolean;
}

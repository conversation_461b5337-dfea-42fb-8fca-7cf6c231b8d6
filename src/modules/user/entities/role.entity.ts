import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { UserEntity } from './user.entity';
import { PermissionEntity } from './permission.entity';
import { RoleDefaultPermissionEntity } from './role-default-permission.entity';

export enum RoleEnum {
    SUPER_ADMIN = "SUPER_ADMIN",
    SALES = "SALES",
    ADMIN = "ADMIN",
    MANAGEMENT = "MANAGEMENT",
    BASIC_USER = "BASIC_USER",
    // TODO: remove these in the future, kept for backward compatibility
    COMPANY_ADMIN = "COMPANY_ADMIN",
    USER = "USER"
}

@Entity('roles')
export class RoleEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'enum', enum: RoleEnum })
  keyCode: RoleEnum;

  @OneToMany(() => UserEntity, (user) => user.role)
  @JoinColumn({ referencedColumnName: 'roleId' })
  users: UserEntity[];

  @OneToMany(() => RoleDefaultPermissionEntity, (rolePermission) => rolePermission.role)
  rolePermissions: RoleDefaultPermissionEntity[];

  // Virtual property to get permissions with allowRead and allowWrite
  get permissions(): any[] {
    if (!this.rolePermissions) return [];

    return this.rolePermissions
      .filter(rp => rp.permission) // Filter out null permissions
      .map(rp => ({
        ...rp.permission,
        allowRead: rp.allowRead,
        allowWrite: rp.allowWrite
      }));
  }
}

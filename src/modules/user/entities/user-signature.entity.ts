import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserSignatureImageDto } from '../dto/user-signature.dto';
import { UserEntity } from './user.entity';

@Entity({ name: 'user_signatures' })
export class UserSignature extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'sig_content', nullable: true })
  sigContent: string;

  @Column({ name: 'name', nullable: false })
  name: string;

  @Column({ name: 'is_default', nullable: true, default: false })
  isDefault: boolean;

  @Column({ name: 'img_metadata', type: 'jsonb', nullable: false })
  imgMetadata: UserSignatureImageDto[];

  @Column({ name: 'created_by_email', nullable: true, default: false })
  createdByEmail: boolean;

  @ManyToOne(() => UserEntity, (email) => email)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

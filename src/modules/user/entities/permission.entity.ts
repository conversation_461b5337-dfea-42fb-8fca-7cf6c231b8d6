import { <PERSON>Entity, <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { RoleEntity } from './role.entity';
import { UserPermissionEntity } from './user-permission.entity';

export enum ResourceEnum {
    REPORTED_AGENCIES = "REPORTED_AGENCIES",
    JOB_SEARCH = "JOB_SEARCH",
    JOB_LEAD = "JOB_LEAD",
    JOB_SYNC = "JOB_SYNC",
    EMAIL_FINDER = "EMAIL_FINDER",
    EMAIL_VERIFICATION = "EMAIL_VERIFICATION",
    DASHBOARD = "DASHBOARD",
    USER_MANAGEMENT = "USER_MANAGEMENT",
    STAFF_PERFORMANCE = "STAFF_PERFORMANCE",
    COMPANY_ONBOARDING = "COMPANY_ONBOARDING",
    COMPANY_APPROVAL = "COMPANY_APPROVAL",
    REPORTING = "REPORTING",
    TASK = "TASK",
    SEARCH = "SEARCH",
    SYNC = "SYNC",
    MY_LEADS = "MY_LEADS",
    MAILBOX = "MAILBOX",
    SEQUENCE = "SEQUENCE",
    MANUAL_LEADS = "MANUAL_LEADS",
    CONTACT_FINDER = "CONTACT_FINDER",
    CONSULTANT = "CONSULTANT",
    MANAGEMENT = "MANAGEMENT",
    CONTRACT = "CONTRACT",
    SETTINGS = "SETTINGS",
    DUPLICATED_JOBS = "DUPLICATED_JOBS",
    TEAM_MANAGEMENT = "TEAM_MANAGEMENT",
    ACCOUNTS = "ACCOUNTS",
    CRM = "CRM",
    CONTACT_LIST = "CONTACT_LIST",
    VIEW_AS = "VIEW_AS",
    CREDIT_MANAGEMENT = "CREDIT_MANAGEMENT",
    SUBSCRIPTION = "SUBSCRIPTION"
}

@Entity('permissions')
export class PermissionEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'enum', enum: ResourceEnum })
  keyCode: ResourceEnum;

  @OneToMany(() => UserPermissionEntity, (userPermission) => userPermission.permission, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'permissionId' })
  userPermissions?: UserPermissionEntity[];

  @ManyToMany(() => RoleEntity, role => role.permissions)
  roles: RoleEntity[];
}

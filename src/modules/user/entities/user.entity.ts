import {
  BaseEntity,
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { RoleEntity } from './role.entity';
import { OrganizationEntity } from './organization.entity';
import { UserPermissionEntity } from './user-permission.entity';
import { UserTrackingEntity } from '../../../modules/user-tracking/entities/user-tracking.entity';
import { ReportedAgency } from '../../../modules/jobs/entities/reported_agency.entity';
import { CommentEntity } from 'src/modules/comments/entities/comment.entity';
import { NotificationEntity } from 'src/modules/notification/entities/notification.entity';
import { VerifiedCompanyEntity } from 'src/modules/jobs/entities/verified_company.entity';
import { JobLead } from 'src/modules/jobs/entities/job-leads.entity';
import { IsOptional } from 'class-validator';
import { UserWorkingTimeEntity } from './user-working-time.entity';
import { CrmContactEntity } from 'src/modules/crm/entities/crm-contact.entity';
import { CrmNoteEntity } from 'src/modules/crm/entities/crm-note.entity';
import { UserLimitEntity } from './user-limit.entity';
import { FeatureEnum } from 'src/common/constants/feature-permission.constant';

export enum UnipileAccountStatus {
  CONNECTED = 'CONNECTED',
  DISCONNECTED = 'DISCONNECTED',
}

export enum LicenseType {
  CONNECTED = 'CONNECTED',
  STANDARD = 'STANDARD',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  DEACTIVATED = 'DEACTIVATED',
}

class UnipileLimitRequest {
  INMAIL?: string | boolean;
  INVITATION?: string | boolean;
  NORMAL_MESSAGE?: string | boolean;
}
export class UnipileRequestStats {
  @IsOptional()
  limit_requests?: UnipileLimitRequest;
}

@Entity({ name: 'users' })
@Index('IDX_users_email_unique_soft_delete', ['email'], { unique: true, where: 'is_deleted = false' })
@Index('IDX_users_username_unique_soft_delete', ['username'], { unique: true, where: 'is_deleted = false' })
export class UserEntity extends BaseEntity {
  @BeforeInsert()
  @BeforeUpdate()
  transformEmail() {
    if (this.email) {
      this.email = this.email.toLowerCase();
    }
  }
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'email', nullable: false })
  email: string;

  @Column({ name: 'password', nullable: true, select: false })
  password: string;

  @Column({ nullable: false, default: false, name: 'is_deleted' })
  isDeleted: boolean;

  @Column({ type: 'uuid', nullable: true })
  roleId: string;

  @Column({ type: 'uuid', nullable: true })
  organizationId: string;

  @Column({ name: 'expired_refresh_token_date', type: 'timestamptz', default: null })
  expiredRefreshTokenDate: Date;

  @Column({ name: 'potential_lead_value', default: 0 })
  potentialLeadValue: number;

  @Column({ name: 'username', nullable: false })
  username: string;

  @Column({ name: 'fullname', nullable: true })
  fullName: string;

  @Column({ name: 'grant_id', nullable: true })
  grantId: string;

  @Column({ name: 'grant_unipile_id', nullable: true })
  grantUnipileId: string;

  @Column({ name: 'unipile_account_id', nullable: true })
  unipileAccountId: string;

  @Column({ name: 'unipile_account_status', type: 'enum', enum: UnipileAccountStatus, nullable: true, default: null })
  unipileAccountStatus: UnipileAccountStatus | null;

  @Column({ name: 'unipile_request_stats', type: 'jsonb', nullable: true })
  unipileRequestStats: UnipileRequestStats;

  @Column({ name: 'avatar_id', nullable: true })
  avatarId: string;

  @Column({ name: 'linkedin_avatar_url', nullable: true })
  linkedinAvatarUrl: string;

  @Column({ name: 'linkedin_public_identifier', nullable: true })
  linkedinPublicIdentifier: string;

  @Column({ name: 'grant_email', nullable: true, comment: 'The email was granted access - A pair with grant_id' })
  grantEmail: string;

  @Column({ name: 'grant_mailbox_id', nullable: true })
  grantMailboxId: string;

  @Column({ name: 'updated_by', length: 36, nullable: true })
  updatedBy: string;

  @Column({ name: 'last_activity', type: 'timestamptz', default: null })
  lastActivity: Date;

  @Column({ name: 'consultant_id', nullable: true })
  consultantId: string;

  @Column({ name: 'consultant_name', nullable: true })
  consultantName: string;

  @Column({ name: 'linkedin_country_code', nullable: true })
  linkedInCountryCode: string;

  @Column({ name: 'linkedin_ip', nullable: true })
  linkedInIP: string;

  @Column({ name: 'license_type', type: 'enum', enum: LicenseType, nullable: true, default: LicenseType.CONNECTED })
  licenseType: LicenseType;

  @Column({ name: 'license_expiry_date', type: 'timestamptz', nullable: true })
  licenseExpiryDate: Date;

  @ManyToOne(() => RoleEntity, (role) => role)
  @JoinColumn({ name: 'roleId' })
  role: RoleEntity;

  @ManyToOne(() => OrganizationEntity, (organization) => organization)
  @JoinColumn({ name: 'organizationId' })
  organization: OrganizationEntity;

  @OneToMany(() => UserPermissionEntity, (userPermission) => userPermission.user, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'userId' })
  userPermissions: UserPermissionEntity[];

  @OneToMany(() => UserTrackingEntity, (userTracking) => userTracking.user, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'user_id' })
  userTrackings: UserPermissionEntity[];

  @OneToMany(() => UserTrackingEntity, (userTracking) => userTracking.user, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'data_key' })
  reportedAgencies: ReportedAgency[];

  @OneToMany(() => CommentEntity, (comment) => comment.creator, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'creator_id' })
  comments: CommentEntity[];

  @OneToMany(() => NotificationEntity, (notification) => notification.creator, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'creator_id' })
  creatorNotifications: NotificationEntity[];

  @OneToMany(() => NotificationEntity, (notification) => notification.user, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'creator_id' })
  myNotifications: NotificationEntity[];

  @JoinColumn({ referencedColumnName: 'user_id' })
  verifiedCompany: VerifiedCompanyEntity[];

  @OneToMany(() => JobLead, (jobLead) => jobLead.assigner, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ referencedColumnName: 'assigner_id' })
  myAssigneeJobLeads: JobLead[];

  // @OneToOne(() => UserWorkingTimeEntity, (user) => user.userId, { onDelete: 'CASCADE' })
  // @JoinColumn({ name: 'id' })
  // workingTime: UserWorkingTimeEntity;

  @Column({ name: 'total_linkedin_message', type: 'int', default: 0 })
  totalLinkedinMessage: number;

  @Column({ name: 'linkedin_last_message', type: 'jsonb', nullable: true })
  linkedinLastMessage: Record<string, any> | null;

  @OneToMany(() => CrmContactEntity, (contact) => contact.creator)
  crmContacts: CrmContactEntity[];

  @OneToMany(() => CrmNoteEntity, (contact) => contact.creator)
  crmNotes: CrmNoteEntity[];

  @Column({ name: 'status', type: 'enum', enum: UserStatus, nullable: true, default: UserStatus.ACTIVE })
  status: UserStatus;

  @Column({ name: 'initial_password', nullable: true })
  initialPassword: string;

  @Column({ nullable: false, default: false, name: 'disable_account' })
  disableAccount: boolean;

  @Column({ name: 'linkedin_url', nullable: true })
  linkedinUrl: string;

  @Column({ name: 'job_title', nullable: true })
  jobTitle: string;

  @Column({ name: 'timezone', nullable: true })
  timezone: string;

  // Virtual properties for feature permissions
  features?: FeatureEnum[];
  permissions?: string[];
}

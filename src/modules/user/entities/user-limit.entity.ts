import { BaseEntity, Column, Entity, JoinColumn, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { UserEntity } from './user.entity';
import { UserLimitType } from '../enums/user-limit.enum';

@Entity('user_limits')
export class UserLimitEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', nullable: true })
  userId: string;

  @Column()
  balance: number;

  @Column()
  used: number;

  @Column()
  originAmount: number;

  @Column({ name: 'type', enum: UserLimitType, nullable: true })
  type: UserLimitType;
}

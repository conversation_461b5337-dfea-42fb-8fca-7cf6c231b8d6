import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserSignatureImageDto } from '../dto/user-signature.dto';
import { UserEntity } from './user.entity';
import { ContactEntity } from './contact.entity';

@Entity({ name: 'contact_lists' })
export class ContactListEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'name', nullable: false })
  name: string;

  @Column({ name: 'contact_count', nullable: false, default: 0 })
  contactCount: number;

  @ManyToOne(() => UserEntity, (email) => email)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @OneToMany(() => ContactEntity, (contact) => contact.contactList, {
    cascade: true,
  })
  @JoinColumn({ referencedColumnName: 'contactId' })
  contacts: ContactEntity[];

  @Column({ name: 'contact_list_description', nullable: true })
  contactListDescription: string;

  @Column({ name: 'location', nullable: true })
  location: string;

  @Column({ type: 'varchar', length: 255, name: 'organization_id', nullable: true })
  organizationId: string;
}

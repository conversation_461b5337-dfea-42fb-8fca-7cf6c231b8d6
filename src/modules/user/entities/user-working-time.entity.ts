import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';
import { UserEntity } from './user.entity';

@Entity('user_working_times')
export class UserWorkingTimeEntity {
  @PrimaryColumn({ name: 'user_id' })
  userId: string; // Foreign key and primary key

  @OneToOne(() => UserEntity, (user) => user.id)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column({ name: 'working_hours', type: 'json' })
  workingHours: Record<string, { startTime: string; endTime: string }>;
  // Example: { MON: { startTime: '09:00', endTime: '17:00' }, TUE: { startTime: '10:00', endTime: '18:00' } }

  @Column({ name: 'non_working_days', type: 'jsonb', nullable: true })
  nonWorkingDays: Record<string, { start: string; end: string, name?: string }>[];

  @Column({ name: 'timezone', type: 'varchar' })
  timezone: string; // e.g., 'Asia/Ho_Chi_Minh'

  @Column({ name: 'created_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ name: 'updated_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}

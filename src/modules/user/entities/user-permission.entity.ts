import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from "typeorm";
import { PermissionEntity } from "./permission.entity";
import { UserEntity } from "./user.entity";

@Entity('user_permissions')
export class UserPermissionEntity extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({type: 'uuid'})
    userId: string;

    @Column({ type: 'uuid', nullable: true })
    permissionId: string;

    @Column({ default: true })
    allowRead: boolean;

    @Column({ default: true })
    allowWrite: boolean;

    @ManyToOne(() => UserEntity, (user) => user.userPermissions, { onDelete: 'CASCADE' })
    @JoinColumn({ name: "userId" })
    user: UserEntity;

    @ManyToOne(() => PermissionEntity, (permission) => permission.userPermissions, { onDelete: 'CASCADE' })
    @JoinColumn({ name: "permissionId" })
    permission?: PermissionEntity;
}

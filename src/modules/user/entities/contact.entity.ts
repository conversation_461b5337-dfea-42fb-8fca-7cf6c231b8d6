import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserSignatureImageDto } from '../dto/user-signature.dto';
import { UserEntity } from './user.entity';
import { ContactListEntity } from './contact-list.entity';

@Entity({ name: 'contacts' })
export class ContactEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'name', nullable: false })
  name: string;

  @Column({ type: 'varchar', name: "created_by", length: 100, nullable: true })
  createdBy: string;

  @Column({ name: 'first_name', nullable: true })
  firstName: string;

  @Column({ name: 'last_name', nullable: true })
  lastName: string;

  @Column({ name: 'linkedin_profile_url', nullable: true })
  linkedInProfileUrl: string;

  @Column({ name: 'linkedin_profile_image_url', nullable: true })
  linkedInProfileImageUrl: string;

  @Column({ name: 'linkedin_first_name', nullable: true })
  linkedInFirstName: string;

  @Column({ name: 'linkedin_last_name', nullable: true })
  linkedInLastName: string;

  @Column({ name: 'email', nullable: false })
  email: string;

  @Column({ name: 'phone', nullable: true })
  phone: string;

  @Column({ name: 'contact_apollo_id', nullable: true })
  contactApolloId: string;

  @ManyToOne(() => ContactListEntity, (list) => list.contacts)
  @JoinColumn({ name: "contact_list" })
  contactList: ContactListEntity;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ name: 'contact_title', nullable: true })
  contactTitle: string;

  @Column({ name: 'contact_location', nullable: true })
  contactLocation: string;

  @Column({ name: 'company_name', nullable: true })
  companyName: string;

  @Column({ name: 'is_unsubscribe_email', nullable: true, default: false })
  isUnsubscribeEmail: boolean;
}

import { <PERSON>Entity, Column, CreateDate<PERSON><PERSON>umn, DeleteDateColumn, Entity, <PERSON>in<PERSON><PERSON>umn, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { UserEntity } from './user.entity';
import { OrganizationCredentialDto } from '../dto/organization-credential.dto';
import { CrmIndustryEntity } from 'src/modules/crm/entities/crm-industry.entity';
import { CrmTagEntity } from 'src/modules/crm/entities/crm-tag.entity';
import { CrmSkillsEntity } from 'src/modules/crm/entities/crm-skill.entity';
import { OrganizationStatusEnum, PlanTypeEnum } from '../enums/organization.enum';

@Entity({ name: 'organizations' })
export class OrganizationEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, nullable: false })
  name: string;

  @Column({ name: 'bh_client_id', nullable: true })
  bhClientId: string;

  @<PERSON>umn({ name: 'bh_client_secret', nullable: true })
  bhClientSecret: string;

  @Column({ name: 'bh_username', nullable: true })
  bhUsername: string;

  @Column({ name: 'bh_password', nullable: true })
  bhPassword: string;

  @Column({ name: 'bh_token', type: 'jsonb', nullable: true })
  bhToken: OrganizationCredentialDto;

  @Column({ name: 'email_domain_authentication', type: 'jsonb', nullable: true })
  emailDomainAuthentication?: any;

  @Column({ name: 'company_size', nullable: true })
  companySize: string;

  @Column({ name: 'company_owner', nullable: true })
  companyOwner: string;

  @Column({ name: 'company_website', nullable: true })
  companyWebsite: string;

  @Column({ name: 'company_industries', nullable: true })
  companyIndustries: string;

  @Column({ name: 'company_types', nullable: true })
  companyTypes: string;

  @Column({ name: 'company_admins', nullable: true })
  companyAdmins: string;

  @Column({ name: 'company_avatar', nullable: true })
  companyAvatar: string;

  @Column({ name: 'address', nullable: true })
  address: string;

  @Column({ name: 'phone', nullable: true })
  phone: string;

  @Column({ name: 'tags', nullable: true })
  tags: string;

  @Column({ name: 'license', nullable: true })
  license: string;

  @Column({ name: 'company_email', nullable: true })
  companyEmail: string;

  @Column({ name: 'overview', nullable: true })
  overview: string;

  //TODO: update default = PENDING when stable
  @Column({ name: 'status', nullable: true, default: OrganizationStatusEnum.APPROVED, enum: OrganizationStatusEnum })
  status: OrganizationStatusEnum;

  @Column({ name: 'plan_type', nullable: true, default: PlanTypeEnum.BASIC, enum: PlanTypeEnum })
  planType?: PlanTypeEnum;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt: Date;

  @OneToMany(() => UserEntity, (user) => user.organization)
  @JoinColumn({ referencedColumnName: 'organizationId' })
  users: UserEntity[];

  @OneToMany(() => CrmIndustryEntity, (industry) => industry.organization, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
  @JoinColumn({ referencedColumnName: 'industry' })
  industry: CrmIndustryEntity[];

  @OneToMany(() => CrmTagEntity, (tag) => tag.organization, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
  @JoinColumn({ referencedColumnName: 'tag' })
  tag: CrmTagEntity[];

  @OneToMany(() => CrmSkillsEntity, (skill) => skill.organization, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
  @JoinColumn({ referencedColumnName: 'skills' })
  skills: CrmSkillsEntity[];

  @Column({ name: 'note', nullable: true })
  note: string;

  @Column({ name: 'total_original_credits', nullable: true })
  totalOriginalCredits: string;

  @Column({ name: 'subscription_id', nullable: true })
  subscriptionId: string;

  @Column({ name: 'created_by', nullable: true })
  createdBy: string;

  @Column({ name: 'documents', type: 'json', nullable: true })
  documents?: Array<{ fileId: string; note: string; createdBy: string; createdAt: Date }>;

  @Column({ name: 'admin_onboarding_completed', type: 'boolean', default: false })
  adminOnboardingCompleted: boolean;

  @Column({ name: 'stripe_customer_id', nullable: true })
  stripeCustomerId?: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP'})
  createdAt: Date;

}

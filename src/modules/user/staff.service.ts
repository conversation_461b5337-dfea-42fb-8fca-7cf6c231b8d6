import { I18nService } from "nestjs-i18n";
import {
  Injectable,
  Logger,
} from "@nestjs/common";
import { UserRepository } from "./repositories/user.repository";
import { BaseAbstractService } from "../../base/base.abstract.service";

@Injectable()
export class StaffService extends BaseAbstractService {
  private readonly logger = new Logger(StaffService.name);

  constructor(
    private readonly userRepository: UserRepository,
    readonly i18nService: I18nService,
  ) {
    super(i18nService);
  }

  async getMyStaff(userId: string) {
    const user = await this.userRepository.findOneBy({ id: userId });
    const organizationId = user.organizationId;

    const data = organizationId ? await this.userRepository.findBy({ organizationId: user.organizationId, isDeleted: false }) : [];

    return this.formatOutputData({ key: "GET_MY_STAFF" }, { data })
  }

}

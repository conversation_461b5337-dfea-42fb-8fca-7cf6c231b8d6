import {
  Body,
  Controller,
  Param,
  Post,
  Put,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  HttpException,
  HttpStatus,
  Get,
  Delete,
  Query,
  Req,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiConsumes,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';

import { BaseErrorResponseDto } from '../../common/dto/common.dto';
import { UserSignatureDto } from './dto/user-signature.dto';
import { ResponseMessage } from '../../common/constants/common.constant';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { UserSignatureServices } from './user-signature.service';
import { MAX_IMAGE_SIZE_IN_BYTES } from './constants/user-signature.constants';

const signatureFileFilter = (
  req: Request,
  file: Express.Multer.File,
  callback: (error: Error, acceptFile: boolean) => void
) => {
  if (!file.mimetype.match(/^image\/(jpg|jpeg|png|gif)$/i)) {
    callback(
      new HttpException(
        'Upload not allowed. Upload only files of type: image/jpg, image/jpeg, image/png, image/gif',
        HttpStatus.BAD_REQUEST
      ),
      false
    );
  }

  callback(null, true);
};

@ApiTags('User Signatures')
@Controller('user-signatures')
@SkipThrottle()
@ApiBadRequestResponse({
  description: ResponseMessage.Common.BAD_REQUEST,
  type: BaseErrorResponseDto,
})
@ApiNotFoundResponse({
  description: ResponseMessage.Common.NOT_FOUND,
  type: BaseErrorResponseDto,
})
@ApiUnauthorizedResponse({
  description: ResponseMessage.Common.UNAUTHORIZED,
  type: BaseErrorResponseDto,
})
@ApiForbiddenResponse({
  description: ResponseMessage.Common.FORBIDDEN,
  type: BaseErrorResponseDto,
})
@UseGuards(AuthenticationGuard, PermissionGuard)
export class UserSignatureController {
  constructor(private readonly userSignatureServices: UserSignatureServices) {}

  @Get('view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getSignaturesLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.userSignatureServices.getSignatures(userId);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getSignatures(@Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userSignatureServices.getSignatures(userId);
  }

  @Get('view-as/:userId/default')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getSignatureDefaultLegacy(@Param('userId') userId: string) {
    // TODO: Remove this after client migration is complete
    return this.userSignatureServices.getSignatureDefault(userId);
  }

  @Get('default')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Read)
  async getSignatureDefault(@Req() req: any) {
    const userId = req.viewAsUser.id;
    return this.userSignatureServices.getSignatureDefault(userId);
  }

  @Post('view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        signature: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        content: {
          type: 'string',
          description: 'The content associated with the signature uploads.',
        },
      },
      required: ['content'],
    },
  })
  @UseInterceptors(
    FilesInterceptor('signature', 100, {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: signatureFileFilter,
    })
  )
  async createSignatureLegacy(
    @Param('userId') userId: string,
    @Body() signatureDto: UserSignatureDto,
    @UploadedFiles() signatures: Array<Express.Multer.File>
  ) {
    // TODO: Remove this after client migration is complete
    return this.userSignatureServices.createSignature(userId, signatureDto, signatures);
  }

  @Post()
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        signature: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        content: {
          type: 'string',
          description: 'The content associated with the signature uploads.',
        },
      },
      required: ['content'],
    },
  })
  @UseInterceptors(
    FilesInterceptor('signature', 100, {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: signatureFileFilter,
    })
  )
  async createSignature(
    @Req() req: any,
    @Body() signatureDto: UserSignatureDto,
    @UploadedFiles() signatures: Array<Express.Multer.File>
  ) {
    const userId = req.viewAsUser.id;
    return this.userSignatureServices.createSignature(userId, signatureDto, signatures);
  }

  @Put('view-as/:userId/:signatureId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        signature: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        content: {
          type: 'string',
          description: 'The content associated with the signature uploads.',
        },
      },
      required: ['content'],
    },
  })
  @UseInterceptors(
    FilesInterceptor('signature', 100, {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: signatureFileFilter,
    })
  )
  async updateSignatureLegacy(
    @Param('userId') userId: string,
    @Param('signatureId') signatureId: string,
    @Body() signatureDto: UserSignatureDto,
    @UploadedFiles() signatures: Array<Express.Multer.File>
  ) {
    // TODO: Remove this after client migration is complete
    return this.userSignatureServices.updateSignature(userId, signatureId, signatureDto, signatures);
  }

  @Put(':signatureId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        signature: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        content: {
          type: 'string',
          description: 'The content associated with the signature uploads.',
        },
      },
      required: ['content'],
    },
  })
  @UseInterceptors(
    FilesInterceptor('signature', 100, {
      limits: { fileSize: MAX_IMAGE_SIZE_IN_BYTES },
      fileFilter: signatureFileFilter,
    })
  )
  async updateSignature(
    @Req() req: any,
    @Param('signatureId') signatureId: string,
    @Body() signatureDto: UserSignatureDto,
    @UploadedFiles() signatures: Array<Express.Multer.File>
  ) {
    const userId = req.viewAsUser.id;
    return this.userSignatureServices.updateSignature(userId, signatureId, signatureDto, signatures);
  }

  @Delete('/:signatureId/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async deleteSignatureLegacy(@Param('userId') userId: string, @Param('signatureId') signatureId: string) {
    // TODO: Remove this after client migration is complete
    return this.userSignatureServices.deleteSignature(userId, signatureId);
  }

  @Delete('/:signatureId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async deleteSignature(@Req() req: any, @Param('signatureId') signatureId: string) {
    const userId = req.viewAsUser.id;
    return this.userSignatureServices.deleteSignature(userId, signatureId);
  }

  @Put('set-default-signature/:signatureId/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async setDefaultSignatureLegacy(@Param('userId') userId: string, @Param('signatureId') signatureId: string) {
    // TODO: Remove this after client migration is complete
    return this.userSignatureServices.setDefaultSignature(userId, signatureId);
  }

  @Put('set-default-signature/:signatureId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async setDefaultSignature(@Req() req: any, @Param('signatureId') signatureId: string) {
    const userId = req.viewAsUser.id;
    return this.userSignatureServices.setDefaultSignature(userId, signatureId);
  }

  @Put('remove-default-signature/:signatureId/view-as/:userId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async removeDefaultSignatureLegacy(@Param('userId') userId: string, @Param('signatureId') signatureId: string) {
    // TODO: Remove this after client migration is complete
    return this.userSignatureServices.removeDefaultSignature(userId, signatureId);
  }

  @Put('remove-default-signature/:signatureId')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async removeDefaultSignature(@Req() req: any, @Param('signatureId') signatureId: string) {
    const userId = req.viewAsUser.id;
    return this.userSignatureServices.removeDefaultSignature(userId, signatureId);
  }

  @Post('app/url-to-base64')
  @Permission(PermissionResource[ResourceEnum.USER_MANAGEMENT].Write)
  async urlToBase64(@Body() { url, isUploadToS3 }: { url: string; isUploadToS3: boolean }) {
    return this.userSignatureServices.urlToBase64(url, isUploadToS3);
  }
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CompleteOnboardingDto {
  @ApiProperty({ description: 'The initial password provided during user creation' })
  @IsString()
  @IsNotEmpty()
  initialPassword: string;

  @ApiProperty({ description: 'The new password to set' })
  @IsString()
  @IsNotEmpty()
  newPassword: string;

  @ApiPropertyOptional({ description: 'Additional user profile information (optional)' })
  @IsOptional()
  profileInfo?: {
    fullName?: string;
    jobTitle?: string;
    timezone?: string;
    linkedinUrl?: string;
  };
}

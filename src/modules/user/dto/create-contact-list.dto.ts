import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsBoolean, IsEmail, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';

export class CreateContactListDto {
  @ApiProperty({ example: 'List name' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ example: 'descriptions' })
  @IsOptional()
  contactListDescription?: string;

  @ApiPropertyOptional({ example: 'location' })
  @IsOptional()
  location?: string;
}

export class UpdateContactInListDto {
  @ApiProperty({ example: 'contact User name' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ example: '*********0' })
  @IsOptional()
  phone: string;

  @ApiPropertyOptional({ example: 'title' })
  @IsOptional()
  @IsString()
  contactTitle?: string;

  @ApiPropertyOptional({ example: 'location' })
  @IsOptional()
  @IsString()
  contactLocation?: string;

  @ApiPropertyOptional({ example: 'location' })
  @IsOptional()
  @IsString()
  linkedInProfileUrl?: string;

  @ApiPropertyOptional({ example: 'companyName' })
  @IsOptional()
  @IsString()
  companyName?: string;
}


export class ToggleUpdateSubscribedDto {
  @ApiProperty({ example: 'email' })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ example: "true"})
  @IsNotEmpty()
  @IsBoolean()
  unsubscribed: boolean;
}

export class EditContactListDto {
  @ApiProperty({ example: 'List name' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ example: 'descriptions' })
  @IsOptional()
  contactListDescription?: string;

  @ApiPropertyOptional({ example: 'location' })
  @IsOptional()
  location?: string;
}

export class ContactInfo {
  @IsNotEmpty()
  email: string;

  @IsNotEmpty()
  name: string;
}

export class AddContactToContactListDto {
  @ApiProperty({ example: 'contact User name' })
  @IsOptional()
  @IsString()
  name: string;


  @ApiProperty({ example: 'firstName' })
  @IsOptional()
  @IsString()
  firstName: string;

  @ApiProperty({ example: 'lastName' })
  @IsOptional()
  @IsString()
  lastName: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ example: '*********0' })
  @IsOptional()
  phone: string;

  @ApiPropertyOptional({ example: '*********' })
  @IsOptional()
  @IsString()
  contactApolloId?: string;

  @ApiProperty()
  @IsArray()
  @ArrayMinSize(1)
  @IsNotEmpty()
  listIds: string[];

  @ApiPropertyOptional({ example: 'title' })
  @IsOptional()
  @IsString()
  contactTitle?: string;

  @ApiPropertyOptional({ example: 'location' })
  @IsOptional()
  @IsString()
  contactLocation?: string;

  @ApiPropertyOptional({ example: 'linkedinUrl' })
  @IsOptional()
  @IsString()
  linkedinUrl?: string;

  @ApiPropertyOptional({ example: 'companyName' })
  @IsOptional()
  @IsString()
  companyName?: string;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  unsubscribed?: boolean;
}

export class CsvRowDto {
  @IsNotEmpty({ message: 'firstName is required' })
  firstName: string;

  @IsNotEmpty({ message: 'lastName is required' })
  lastName: string;

  @IsNotEmpty({ message: 'LinkedInProfileUrl is required' })
  linkedInProfileUrl: string;

  @IsNotEmpty({ message: 'PhoneNumber is required' })
  phoneNumber: string;

  @IsNotEmpty({ message: 'JobTitle is required' })
  jobTitle: string;

  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Invalid email' })
  email: string;
}

export class GenerateCSVDto {
  @IsOptional()
  data: any;
}

export class InsertContactToListDto {
  @ApiProperty({ example: 'id' })
  @IsNotEmpty()
  @IsString()
  id: string;
}

class BulkAddContactDto extends OmitType(AddContactToContactListDto, ['listIds']) { };

export class BulkAddContactToListDto {
  @ApiProperty()
  @IsArray()
  @ArrayMinSize(1)
  listIds: string[];

  @ApiProperty()
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => BulkAddContactDto)
  contacts: BulkAddContactDto[];

  @ApiPropertyOptional({ example: 'requestId' })
  @IsOptional()
  @IsString()
  requestId?: string;
}

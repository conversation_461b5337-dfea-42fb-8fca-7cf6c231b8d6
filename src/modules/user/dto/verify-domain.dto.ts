import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class DomainAuthDto {
  @ApiProperty({ example: 'example.com', description: 'Domain to authenticate' })
  @IsNotEmpty()
  @IsString()
  domain: string;

  @ApiPropertyOptional({ description: 'Custom DKIM selector' })
  @IsOptional()
  @IsString()
  customDkimSelector?: string;

  @ApiPropertyOptional({ enum: ['eu', 'global'], default: 'global', description: 'Region for the domain' })
  @IsOptional()
  @IsEnum(['eu', 'global'])
  region?: 'eu' | 'global' = 'global';

  @ApiPropertyOptional({ example: 'mail', description: 'Subdomain for the domain' })
  @IsOptional()
  @IsString()
  subdomain?: string;
}

export class CheckDomainValidationDto {
  @ApiProperty({ description: 'Organization ID' })
  @IsNotEmpty()
  @IsUUID()
  organizationId: string;
}

export class BrandLinkDto {
  @ApiProperty({ example: 'example.com', description: 'Domain for the brand link' })
  @IsNotEmpty()
  @IsString()
  domain: string;

  @ApiPropertyOptional({ example: 'mail', description: 'Subdomain for the brand link' })
  @IsOptional()
  @IsString()
  subdomain?: string;

  @ApiPropertyOptional({ enum: ['eu', 'global'], default: 'global', description: 'Region for the brand link' })
  @IsOptional()
  @IsEnum(['eu', 'global'])
  region?: 'eu' | 'global' = 'global';
}

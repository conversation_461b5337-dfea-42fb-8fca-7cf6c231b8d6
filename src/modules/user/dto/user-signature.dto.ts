import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';
export class UserSignatureImageDto {
  imgId?: string;
  name?: string;
}

export class UserSignatureDto {
  @ApiProperty({ example: '<p>Best regards,</p><p><PERSON></p>' })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({ example: 'My Signature' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  createdByEmail?: boolean;
}
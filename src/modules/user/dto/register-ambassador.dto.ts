import { ApiProperty } from '@nestjs/swagger';
import { IsString, Matches, MaxLength, IsNotEmpty, MinLength } from 'class-validator';
import { BaseResponseDto } from 'src/common/dto/common.dto';

export class RegisterAmbassadorDto {
  @IsNotEmpty()
  @ApiProperty()
  @IsString()
  @MinLength(4, {
    message: 'Code must be 4 characters (letters and digits)',
  })
  @MaxLength(4, {
    message: 'Code must be 4 characters (letters and digits)',
  })
  code: string;
}

export class RegisterAmbassadorResponseDto extends BaseResponseDto<object> {}

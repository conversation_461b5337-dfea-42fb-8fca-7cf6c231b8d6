import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, ValidateIf } from 'class-validator';

export class LinkedConnectionDto {
  @ApiProperty({ example: 'string' })
  @IsString()
  @ValidateIf((obj) => !obj.accessToken &&  !obj.premiumToken)
  @IsNotEmpty()
  username: string;

  @ApiProperty({ example: 'string' })
  @IsString()
  @ValidateIf((obj) => !obj.accessToken &&  !obj.premiumToken)
  @IsNotEmpty()
  password: string;

  @ApiPropertyOptional({ example: 'string' })
  @IsString()
  @ValidateIf((obj) => !obj.username && !obj.password &&  !obj.premiumToken)
  @IsNotEmpty()
  accessToken: string;

  @ApiPropertyOptional({ example: 'string' })
  @ValidateIf((obj) => !obj.username &&  !obj.password &&  !obj.accessToken)
  @IsOptional()
  premiumToken: string;

  @ApiPropertyOptional({ example: 'string' })
  @IsOptional()
  userAgent: string;

  @ApiPropertyOptional({ example: 'string' })
  @IsOptional()
  country: string;

  @ApiPropertyOptional({ example: 'string' })
  @IsOptional()
  ip: string;
}

export class UnipileSolveCheckpointDto {
  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  @IsString()
  provider: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  @IsString()
  account_id: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  @IsString()
  code: string;
}

export class UnipileResendCheckpointDto {
  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  @IsString()
  provider: string;

  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  @IsString()
  account_id: string;
}

export class UnipileUpdateAccountInApp {
  @ApiProperty({ example: 'string' })
  @IsNotEmpty()
  @IsString()
  account_id: string;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';
import { TopupPackageEnum } from '../constants/topup-packages.constant';

export class TopupCreditsDto {
  @ApiProperty({
    description: 'Predefined topup package (required)',
    enum: TopupPackageEnum,
    example: TopupPackageEnum.STANDARD,
  })
  @IsNotEmpty()
  @IsEnum(TopupPackageEnum)
  packageId: TopupPackageEnum;

  @ApiPropertyOptional({
    description: 'Payment method ID from Stripe',
    example: 'pm_1234567890',
  })
  @IsOptional()
  @IsString()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'Whether to save the payment method for future use',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  savePaymentMethod?: boolean;
}

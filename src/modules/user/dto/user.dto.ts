import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { RoleEnum } from '../entities/role.entity';

export class UpdateBullhornConfigDto {
  @ApiPropertyOptional()
  @IsNotEmpty()
  clientId?: string;

  @ApiPropertyOptional()
  @IsNotEmpty()
  clientSecret?: string;

  @ApiPropertyOptional()
  @IsNotEmpty()
  username?: string;

  @ApiPropertyOptional()
  @IsNotEmpty()
  password?: string;
}

type DaysOfWeek = 'MON' | 'TUE' | 'WED' | 'THU' | 'FRI' | 'SAT' | 'SUN';

export class UpsertWorkingTimeDto {
  @IsNotEmpty()
  @IsObject()
  workingHours: Record<DaysOfWeek, { startTime: string; endTime: string }>;

  @IsNotEmpty()
  @IsString()
  timezone: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  nonWorkingDays: Record<string, { start: string; end: string, name?: string }>[];
}

export class QuickCreateUserDto {
  @ApiPropertyOptional()
  @IsOptional()
  firstName?: string;

  @ApiPropertyOptional()
  @IsNotEmpty()
  @IsOptional()
  lastName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  username?: string;

  @ApiPropertyOptional()
  @IsNotEmpty()
  password: string;

  @ApiPropertyOptional()
  @IsOptional()
  roleId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  roleName?: RoleEnum;

  @ApiPropertyOptional()
  @IsOptional()
  companyId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  companyName?: string;
}
export class GetDashboardMetricsQuery {
  @ApiPropertyOptional()
  @IsOptional()
  fromDate?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  toDate?: Date;
}
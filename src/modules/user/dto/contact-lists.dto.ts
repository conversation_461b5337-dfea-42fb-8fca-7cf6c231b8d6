import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export enum QUERY_CONTACTLIST_TYPE {
  MY_CONTACTLIST = 'MY_CONTACTLIST',
  COMPANY_CONTACTLIST = 'COMPANY_CONTACTLIST',
}

export class ContactListsQueryDto {
  //   @ApiPropertyOptional()
  //   @IsOptional()
  //   page?: number;

  //   @ApiPropertyOptional()
  //   @IsOptional()
  //   limit?: number;

  @ApiPropertyOptional({ default: QUERY_CONTACTLIST_TYPE.MY_CONTACTLIST })
  @IsEnum(QUERY_CONTACTLIST_TYPE)
  @IsOptional()
  type?: QUERY_CONTACTLIST_TYPE;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsNumber,
  IsArray,
  ValidateNested,
  IsUUID,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class UpsertCreditManagementDto {
  @ApiPropertyOptional()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  totalCredit?: number;
}

export class UserCreditAssignmentDto {
  @ApiProperty({
    description: 'User ID to assign credits to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Amount of credits to assign to the user',
    example: 100,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  credits: number;
}

export class AssignCreditQuotaDto {
  @ApiProperty({
    description: 'List of users and their credit assignments',
    type: [UserCreditAssignmentDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UserCreditAssignmentDto)
  assignments: UserCreditAssignmentDto[];

  @ApiPropertyOptional({
    description: 'Optional note for the credit assignment',
    example: 'Monthly credit allocation',
  })
  @IsOptional()
  @IsString()
  note?: string;
}

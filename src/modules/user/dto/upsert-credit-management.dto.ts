import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsN<PERSON>ber, IsUUID, Min } from 'class-validator';

export class UpsertCreditManagementDto {
  @ApiPropertyOptional()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  totalCredit?: number;
}

export class AssignCreditQuotaDto {
  @ApiProperty({
    description: 'User ID to assign credits to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Amount of credits to assign to the user',
    example: 100,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  credits: number;

  @ApiPropertyOptional({
    description: 'Optional note for the credit assignment',
    example: 'Monthly credit allocation',
  })
  @IsOptional()
  @IsString()
  note?: string;
}

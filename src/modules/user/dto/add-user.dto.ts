import { IsEmail, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>al, IsString, IsU<PERSON><PERSON> } from 'class-validator';
import { RoleEnum } from '../entities/role.entity';

export class AddUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsOptional()
  firstName?: string;

  @IsString()
  @IsOptional()
  lastName?: string;

  @IsString()
  @IsOptional()
  username?: string;

  @IsUUID()
  @IsOptional()
  organizationId?: string; // Optional - if not provided, use admin's organization

  @IsString()
  @IsOptional()
  role?: RoleEnum;
}

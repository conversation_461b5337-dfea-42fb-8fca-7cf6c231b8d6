import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  Min,
  ValidateNested,
} from 'class-validator';
import { RoleEnum } from '../entities/role.entity';
import { LicenseType } from '../entities/user.entity';
import { PlanTypeEnum } from '../enums/organization.enum';
import { USERNAME_REGEX } from '../../../common/constants/common.constant';
import { RenewInterval } from '../../subscription/entities/subscription.entity';

export class CreateOrganizationDto {
  @ApiProperty({ example: 'Organization name' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  bhClientId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  bhClientSecret?: string;

  @ApiPropertyOptional()
  @IsOptional()
  bhUsername?: string;

  @ApiPropertyOptional()
  @IsOptional()
  bhPassword?: string;

  // @ApiPropertyOptional()
  // @IsOptional()
  // bhToken?: string;

  @ApiPropertyOptional()
  @IsOptional()
  companySize?: string;

  @ApiPropertyOptional()
  @IsOptional()
  companyAvatar?: string;

  @ApiPropertyOptional()
  @IsOptional()
  companyWebsite?: string;

  @ApiPropertyOptional()
  @IsOptional()
  companyOwner?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => String)
  companyIndustries?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => String)
  companyTypes?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => String)
  companyAdmins?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => String)
  tags?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  address?: string;

  @ApiPropertyOptional()
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional()
  @IsOptional()
  license?: string;

  @ApiPropertyOptional()
  @IsOptional()
  companyEmail?: string;

  @ApiPropertyOptional()
  @IsOptional()
  overview?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(PlanTypeEnum)
  planType?: PlanTypeEnum;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(LicenseType)
  licenseType: LicenseType;

  @ApiPropertyOptional()
  @IsOptional()
  note?: string;

  @ApiPropertyOptional()
  @IsOptional()
  totalOriginalCredits?: string;
}

export class OnboardingUserDto {
  @ApiProperty()
  @IsNotEmpty()
  @Transform((data) => data.value?.trim().toLowerCase())
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  role: RoleEnum;

  @ApiProperty()
  @IsNotEmpty()
  @Matches(USERNAME_REGEX, {
    message: 'Username can only contain letters, numbers, underscores, and periods',
  })
  username: string;

  @ApiProperty()
  @IsNotEmpty()
  password: string;

  @ApiProperty()
  @IsNotEmpty()
  originalCredits: number;
}

export class SingleOnboardingUserDto extends OnboardingUserDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  organizationId: string;
}

export class SubscriptionOptionsDto {
  @ApiProperty({ description: 'Plan ID (e.g., basic, pro, pro_plus, enterprise)' })
  @IsNotEmpty()
  @IsString()
  planId: string;

  @ApiProperty({ description: 'Number of licenses to purchase', minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  licenseCount: number;

  @ApiProperty({
    description: 'Billing cycle',
    enum: RenewInterval,
    example: RenewInterval.MONTHLY
  })
  @IsNotEmpty()
  @IsEnum(RenewInterval)
  billingCycle: RenewInterval;

  @ApiProperty({ description: 'Whether this is a trial subscription', default: false })
  @IsOptional()
  @IsBoolean()
  isTrial?: boolean;
}

export class OnboardingCompanyDto {
  @ApiProperty()
  @IsNotEmpty()
  @Type(() => CreateOrganizationDto)
  company: CreateOrganizationDto;

  @ApiProperty()
  @IsOptional()
  @Type(() => OnboardingUserDto)
  @ValidateNested({ each: true })
  employees: OnboardingUserDto[];

  @ApiProperty({ description: 'Subscription configuration' })
  @IsNotEmpty()
  @Type(() => SubscriptionOptionsDto)
  @ValidateNested()
  subscription: SubscriptionOptionsDto;
}

export class CreateDocumentFileDto {
  @ApiPropertyOptional()
  @IsOptional()
  note: string;

  @ApiProperty()
  @IsNotEmpty()
  fileId: string;
}

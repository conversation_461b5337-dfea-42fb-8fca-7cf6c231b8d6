import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, Max, Min, ValidateNested } from 'class-validator';
import { BaseUpdateDto } from 'src/common/dto/update.dto';

export class UpdateUserDto extends BaseUpdateDto {
  @IsOptional()
  roleId?: string;

  @IsOptional()
  @Type(() => UpdateUserPermissionDto)
  @ValidateNested({ each: true })
  permissions?: UpdateUserPermissionDto[];

  @IsOptional()
  email: string;

  @IsOptional()
  fullName: string;

  @IsOptional()
  username: string;

  @IsOptional()
  organizationId: string;

  @IsOptional()
  organization?: string;

  @IsOptional()
  avatarId?: string;

  @IsOptional()
  jobTitle: string;

  @IsOptional()
  linkedinUrl: string;

  @IsOptional()
  timezone: string;

  @IsOptional()
  disableAccount: boolean;
}

export class UpdateUserPermissionDto {
  @IsOptional()
  id?: string;

  @IsNotEmpty()
  permissionId: string;

  @IsNotEmpty()
  allowRead: boolean;

  @IsNotEmpty()
  allowWrite: boolean;
}

export class UpdateMeRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  @Min(0)
  @Max(100)
  potentialLeadValue?: number;
}

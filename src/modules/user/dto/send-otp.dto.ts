import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

export class SendOtpDto {
  @ApiProperty({ example: 'AE' })
  @IsString()
  countryCode: string;

  @ApiProperty({ example: '041234567' })
  @IsString()
  @MaxLength(15)
  phoneNumber: string;
}

export class SendOtpEncryptDto {
  @ApiProperty({ example: '.....' })
  @IsString()
  encryptedMsg: string;
}

export class VerifyUpdatePhoneOtpDto extends SendOtpDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  otpCode: string;

  @ApiProperty()
  //TODO
  // @IsNotEmpty()
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty()
  //TODO
  // @IsNotEmpty()
  @IsOptional()
  @IsString()
  fcmToken?: string;
}

export enum TopupPackageEnum {
  BASIC = 'basic',
  STANDARD = 'standard', 
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
}

export interface TopupPackage {
  id: TopupPackageEnum;
  name: string;
  credits: number;
  price: number;
  currency: string;
  description: string;
  popular?: boolean;
  savings?: number; // Percentage savings compared to $1 per credit
  features?: string[];
}

export const TOPUP_PACKAGES: Record<TopupPackageEnum, TopupPackage> = {
  [TopupPackageEnum.BASIC]: {
    id: TopupPackageEnum.BASIC,
    name: 'Basic Package',
    credits: 5000,
    price: 350,
    currency: 'USD',
    description: 'Perfect for small teams getting started',
    savings: 30, // 30% savings vs $1 per credit
    features: [
      '5,000 credits',
      'Standard support',
      'Basic analytics',
    ],
  },
  [TopupPackageEnum.STANDARD]: {
    id: TopupPackageEnum.STANDARD,
    name: 'Standard Package',
    credits: 10000,
    price: 650,
    currency: 'USD',
    description: 'Most popular choice for growing teams',
    popular: true,
    savings: 35, // 35% savings vs $1 per credit
    features: [
      '10,000 credits',
      'Priority support',
      'Advanced analytics',
      'Team collaboration tools',
    ],
  },
  [TopupPackageEnum.PREMIUM]: {
    id: TopupPackageEnum.PREMIUM,
    name: 'Premium Package',
    credits: 20000,
    price: 1200,
    currency: 'USD',
    description: 'Best value for large teams and enterprises',
    savings: 40, // 40% savings vs $1 per credit
    features: [
      '20,000 credits',
      'Premium support',
      'Full analytics suite',
      'Advanced integrations',
      'Custom reporting',
    ],
  },
  [TopupPackageEnum.ENTERPRISE]: {
    id: TopupPackageEnum.ENTERPRISE,
    name: 'Enterprise Package',
    credits: 0, // Custom amount
    price: 0, // Contact for pricing
    currency: 'USD',
    description: 'Custom solutions for enterprise needs',
    features: [
      'Custom credit allocation',
      'Dedicated account manager',
      'Custom integrations',
      'SLA guarantees',
      'On-premise deployment options',
    ],
  },
};

export const getTopupPackage = (packageId: TopupPackageEnum): TopupPackage => {
  return TOPUP_PACKAGES[packageId];
};

export const getAllTopupPackages = (): TopupPackage[] => {
  return Object.values(TOPUP_PACKAGES);
};

export const calculateSavings = (packageId: TopupPackageEnum): number => {
  const pkg = getTopupPackage(packageId);
  if (pkg.id === TopupPackageEnum.ENTERPRISE || pkg.credits === 0) {
    return 0;
  }
  
  const regularPrice = pkg.credits * 1; // $1 per credit
  const packagePrice = pkg.price;
  const savings = ((regularPrice - packagePrice) / regularPrice) * 100;
  
  return Math.round(savings);
};

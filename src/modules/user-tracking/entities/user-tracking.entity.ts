import { JobSearchEntity } from "../../../modules/jobs/entities/job-search.entity";
import { UserEntity } from "../../../modules/user/entities/user.entity";
import { BaseEntity, Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from "typeorm";

@Entity('user_trackings')
export class UserTrackingEntity extends BaseEntity {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column({ name: 'user_id' })
    userId: string;

    @Column({ name: 'job_search_id' })
    jobSearchId: string;

    @Column({ name: 'last_visited_at', type: 'timestamptz', nullable: true })
    lastVisitedAt: Date;

    @ManyToOne(() => UserEntity, (user) => user.userTrackings, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'user_id' })
    user: UserEntity;

    @ManyToOne(() => JobSearchEntity, (js) => js.userTrackings)
    @JoinColumn({name: 'job_search_id'})
    jobSearch: JobSearchEntity;
}
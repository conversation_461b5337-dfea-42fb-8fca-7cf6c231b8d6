import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { SubscriptionRepository } from '../repositories/subscription.repository';
import {
  SubscriptionEntity,
  SubscriptionStatus,
  RenewInterval,
} from '../entities/subscription.entity';
import { QuotaService } from './quota.service';
import { LicenseService } from './license.service';
import { PLAN_FEATURES } from '../../../configs/plan-features.config';
import { PLANS } from '../../../configs/plans.config';
import { FEATURES } from '../../../configs/features.config';
import {
  SUBSCRIPTION_SYSTEM_CONFIG,
  CreditAllocationMode,
} from '../../../configs/subscription-system.config';
import { DataSource, Not } from 'typeorm';
import { OrganizationEntity } from '../../user/entities/organization.entity';
import { LicenseEntity } from '../entities/license.entity';
import {
  OrganizationQuotaEntity,
  QuotaResetInterval,
  QuotaSource,
} from '../entities/organization-quota.entity';
import {
  SubscriptionChangeEntity,
  SubscriptionChangeType,
} from '../entities/subscription-change.entity';
import { PerformedByType } from '../entities/quota-usage-log.entity';
import { RoleEnum } from '../../user/entities/role.entity';

// Feature IDs
const FEATURE_CREDITS = 'credits';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly quotaService: QuotaService,
    private readonly licenseService: LicenseService,
    private readonly dataSource: DataSource,
  ) {}

  async createSubscription(
    organizationId: string,
    planId: string = 'basic',
    licenseCount: number = 1,
    billingCycle: RenewInterval = RenewInterval.MONTHLY,
    isTrial: boolean = false,
    requestUserId?: string,
    entityManager?: any,
  ): Promise<SubscriptionEntity> {
    try {
      const repository = entityManager
        ? entityManager.getRepository(OrganizationEntity)
        : this.dataSource.getRepository(OrganizationEntity);
      const organization = await repository.findOne({ where: { id: organizationId } });

      if (!organization) {
        throw new NotFoundException(`Organization with ID ${organizationId} not found`);
      }

      if (!PLANS[planId]) {
        this.logger.warn(`Plan ${planId} not found, using basic plan instead`);
        throw new NotFoundException(`Plan ${planId} not found`);
      }

      const now = new Date();
      const nextBillingDate = this.calculateNextBillingDate(now, billingCycle);
      const trialEndDate = isTrial ? this.calculateTrialEndDate(now) : null;

      const subscription = this.subscriptionRepository.create({
        organizationId,
        planId,
        startDate: now,
        nextBillingDate,
        renewInterval: billingCycle,
        status: SubscriptionStatus.PENDING,
        autoRenew: true,
        licenseCount,
        isTrial,
        trialEndDate,
      });

      const subscriptionRepo = entityManager
        ? entityManager.getRepository(SubscriptionEntity)
        : this.subscriptionRepository;
      const savedSubscription = await subscriptionRepo.save(subscription);

      // Create licenses for the subscription
      await this.licenseService.createLicensesForSubscription(
        savedSubscription.id,
        licenseCount,
        entityManager,
      );

      // Assign quotas for the plan (organization-level quotas)
      await this.assignQuotasForPlan(
        organizationId,
        planId,
        savedSubscription.id,
        requestUserId,
        entityManager,
      );

      // Create subscription change record
      await this.createSubscriptionChangeRecord(
        savedSubscription.id,
        SubscriptionChangeType.PLAN_CHANGE,
        null,
        planId,
        null,
        licenseCount,
        now,
        'Initial subscription creation',
        requestUserId,
        entityManager,
      );

      const orgRepo = entityManager
        ? entityManager.getRepository(OrganizationEntity)
        : this.dataSource.getRepository(OrganizationEntity);
      await orgRepo.update(organizationId, { subscriptionId: savedSubscription.id });

      return savedSubscription;
    } catch (error) {
      this.logger.error(`Error creating subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get active subscription for an organization
   * @param organizationId Organization ID
   * @returns Active subscription or null if not found
   */
  async getActiveSubscriptionForOrganization(
    organizationId: string,
  ): Promise<SubscriptionEntity | null> {
    try {
      return await this.subscriptionRepository.findOne({
        where: {
          organizationId,
          status: SubscriptionStatus.ACTIVE,
        },
      });
    } catch (error) {
      this.logger.error(`Error getting active subscription: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get subscription for an organization
   * @param organizationId Organization ID
   * @returns subscription
   */
  async getSubscriptionForOrganization(organizationId: string): Promise<SubscriptionEntity | null> {
    try {
      return await this.subscriptionRepository.findOne({
        where: {
          organizationId,
        },
      });
    } catch (error) {
      this.logger.error(`Error getting subscription: ${error.message}`, error.stack);
      return null;
    }
  }

  private async assignQuotasForPlan(
    organizationId: string,
    planId: string,
    subscriptionId: string,
    requestUserId?: string,
    entityManager?: any,
  ): Promise<void> {
    try {
      await this.quotaService.allocateMultipleFeaturesToOrganization(
        organizationId,
        planId,
        subscriptionId,
        requestUserId,
        entityManager,
      );

      this.logger.log(
        `Assigned all features from plan ${planId} to organization ${organizationId}`,
      );
    } catch (error) {
      this.logger.error(`Error assigning quotas for plan: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Allocate credits to a specific user
   * @param subscriptionId Subscription ID
   * @param userId User ID
   * @param credits Amount of credits to allocate
   * @param entityManager Optional entity manager for transaction
   * @returns Promise<void>
   */
  async allocateCreditsToUser(
    subscriptionId: string,
    userId: string,
    credits: number,
    entityManager?: any,
  ): Promise<void> {
    try {
      const subscriptionRepo = entityManager
        ? entityManager.getRepository(SubscriptionEntity)
        : this.subscriptionRepository;
      const subscription = await subscriptionRepo.findOne({
        where: { id: subscriptionId },
      });

      if (!subscription) {
        throw new NotFoundException(`Subscription with ID ${subscriptionId} not found`);
      }

      const organizationId = subscription.organizationId;
      const planId = subscription.planId;
      const planConfig = PLANS[planId] || {};

      if (
        !planConfig ||
        !(FEATURE_CREDITS in planConfig) ||
        !(planConfig[FEATURE_CREDITS] as any).amount
      ) {
        throw new NotFoundException(`Plan ${planId} does not have credits feature`);
      }

      const userRepository = entityManager
        ? entityManager.getRepository('users')
        : this.dataSource.getRepository('users');

      const user = await userRepository.findOne({
        where: { id: userId, organizationId },
      });

      if (!user) {
        throw new NotFoundException(
          `User with ID ${userId} not found in organization ${organizationId}`,
        );
      }

      const totalCredits = (planConfig[FEATURE_CREDITS] as any).amount;
      const allocatedCredits = Math.min(credits, totalCredits);

      const orgQuotaRepo = entityManager
        ? entityManager.getRepository(OrganizationQuotaEntity)
        : this.dataSource.getRepository(OrganizationQuotaEntity);
      const orgQuota = await orgQuotaRepo.findOne({
        where: {
          organizationId,
          featureId: FEATURE_CREDITS,
          source: QuotaSource.SUBSCRIPTION,
          sourceId: subscriptionId,
        },
      });

      if (!orgQuota) {
        throw new NotFoundException(`Organization quota for feature ${FEATURE_CREDITS} not found`);
      }

      if (!orgQuota.unlimited) {
        orgQuota.remaining = Math.max(0, orgQuota.remaining - allocatedCredits);
        await orgQuotaRepo.save(orgQuota);
      }

      await this.quotaService.allocateQuotaToUser(
        userId,
        organizationId,
        FEATURE_CREDITS,
        allocatedCredits,
        'credit',
        QuotaResetInterval.SUBSCRIPTION,
        QuotaSource.SUBSCRIPTION,
        subscriptionId,
        entityManager,
      );

      this.logger.log(
        `Allocated ${allocatedCredits} credits to user ${userId} from subscription ${subscriptionId}`,
      );
    } catch (error) {
      this.logger.error(`Error allocating credits to user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Allocate credits to multiple users
   * @param subscriptionId Subscription ID
   * @param userCreditsMap Map of userId to credits amount
   * @param entityManager Optional entity manager for transaction
   * @returns Promise<void>
   */
  async allocateCreditsToMultipleUsers(
    subscriptionId: string,
    userCreditsMap: Record<string, number>,
    requestUserId?: string,
    entityManager?: any,
  ): Promise<void> {
    try {
      const subscriptionRepo = entityManager
        ? entityManager.getRepository(SubscriptionEntity)
        : this.subscriptionRepository;
      const subscription = await subscriptionRepo.findOne({
        where: { id: subscriptionId },
      });

      if (!subscription) {
        throw new NotFoundException(`Subscription with ID ${subscriptionId} not found`);
      }

      const organizationId = subscription.organizationId;
      const planId = subscription.planId;
      const planConfig = PLANS[planId] || {};

      if (
        !planConfig ||
        !(FEATURE_CREDITS in planConfig) ||
        !(planConfig[FEATURE_CREDITS] as any).amount
      ) {
        throw new NotFoundException(`Plan ${planId} does not have credits feature`);
      }

      const totalPlanCredits = (planConfig[FEATURE_CREDITS] as any).amount;
      const totalUserCredits = Object.values(userCreditsMap).reduce(
        (sum: number, credits: number) => sum + credits,
        0,
      );

      if (totalUserCredits > totalPlanCredits) {
        this.logger.warn(
          `Total credits allocated to users (${totalUserCredits}) exceeds the plan limit (${totalPlanCredits}). Adjusting credits.`,
        );

        const ratio = totalPlanCredits / totalUserCredits;
        for (const userId in userCreditsMap) {
          userCreditsMap[userId] = Math.floor(userCreditsMap[userId] * ratio);
        }
      }

      const orgQuotaRepo = entityManager
        ? entityManager.getRepository(OrganizationQuotaEntity)
        : this.dataSource.getRepository(OrganizationQuotaEntity);
      const orgQuota = await orgQuotaRepo.findOne({
        where: {
          organizationId,
          featureId: FEATURE_CREDITS,
          source: QuotaSource.SUBSCRIPTION,
          sourceId: subscriptionId,
        },
      });

      if (!orgQuota) {
        throw new NotFoundException(`Organization quota for feature ${FEATURE_CREDITS} not found`);
      }

      if (!orgQuota.unlimited) {
        orgQuota.remaining = Math.max(0, orgQuota.remaining - totalUserCredits);
        await orgQuotaRepo.save(orgQuota);
      }

      await this.quotaService.allocateQuotaToMultipleUsers(
        userCreditsMap,
        organizationId,
        FEATURE_CREDITS,
        'credit',
        QuotaResetInterval.SUBSCRIPTION,
        QuotaSource.SUBSCRIPTION,
        subscriptionId,
        requestUserId,
        requestUserId ? PerformedByType.USER : PerformedByType.SYSTEM,
        entityManager,
      );

      this.logger.log(
        `Allocated credits to ${Object.keys(userCreditsMap).length} users from organization ${organizationId} (subscription ${subscriptionId})`,
      );
    } catch (error) {
      this.logger.error(
        `Error allocating credits to multiple users: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Calculate next billing date based on billing cycle
   */
  private calculateNextBillingDate(startDate: Date, billingCycle: RenewInterval): Date {
    const nextBillingDate = new Date(startDate);

    switch (billingCycle) {
      case RenewInterval.MONTHLY:
        nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
        break;
      case RenewInterval.QUARTERLY:
        nextBillingDate.setMonth(nextBillingDate.getMonth() + 3);
        break;
      case RenewInterval.ANNUALLY:
        nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
        break;
      case RenewInterval.BIANNUALLY:
        nextBillingDate.setMonth(nextBillingDate.getMonth() + 6);
        break;
      default:
        nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
    }

    return nextBillingDate;
  }

  /**
   * Calculate trial end date
   */
  private calculateTrialEndDate(startDate: Date): Date {
    const trialEndDate = new Date(startDate);
    trialEndDate.setDate(trialEndDate.getDate() + SUBSCRIPTION_SYSTEM_CONFIG.TRIAL_DURATION_DAYS);
    return trialEndDate;
  }

  /**
   * Create subscription change record for audit trail
   */
  private async createSubscriptionChangeRecord(
    subscriptionId: string,
    changeType: SubscriptionChangeType,
    oldPlanId: string | null,
    newPlanId: string | null,
    oldLicenseCount: number | null,
    newLicenseCount: number | null,
    effectiveDate: Date,
    reason: string,
    performedBy?: string,
    entityManager?: any,
  ): Promise<SubscriptionChangeEntity> {
    const repository = entityManager
      ? entityManager.getRepository(SubscriptionChangeEntity)
      : this.dataSource.getRepository(SubscriptionChangeEntity);

    const change = repository.create({
      subscriptionId,
      changeType,
      oldPlanId,
      newPlanId,
      oldLicenseCount,
      newLicenseCount,
      effectiveDate,
      reason,
      performedBy,
    });

    return repository.save(change);
  }

  /**
   * Calculate subscription price based on plan, license count, and billing cycle
   */
  calculateSubscriptionPrice(
    planId: string,
    licenseCount: number,
    billingCycle: RenewInterval,
  ): number {
    const plan = PLANS[planId];
    if (!plan) {
      throw new NotFoundException(`Plan ${planId} not found`);
    }

    const basePrice = plan.price;
    const totalPrice = basePrice * licenseCount;

    // Apply billing cycle discounts if any
    switch (billingCycle) {
      case RenewInterval.ANNUALLY:
        return totalPrice * 12;
      case RenewInterval.QUARTERLY:
        return totalPrice * 3;
      default:
        return totalPrice;
    }
  }

  /**
   * Get used license count for a subscription
   */
  async getUsedLicenseCount(subscriptionId: string): Promise<number> {
    const licenseRepo = this.dataSource.getRepository(LicenseEntity);
    const count = await licenseRepo
      .createQueryBuilder('license')
      .where('license.subscriptionId = :subscriptionId', { subscriptionId })
      .andWhere('license.userId IS NOT NULL')
      .getCount();

    return count;
  }
}

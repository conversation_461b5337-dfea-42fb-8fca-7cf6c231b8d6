import { Injectable, Logger } from '@nestjs/common';
import { FEATURES } from '../../../configs/features.config';
import { PLAN_FEATURES } from '../../../configs/plan-features.config';
import { UserEntity } from '../../user/entities/user.entity';
import { DataSource } from 'typeorm';
import { SubscriptionEntity, SubscriptionStatus } from '../entities/subscription.entity';

@Injectable()
export class FeatureCostService {
  private readonly logger = new Logger(FeatureCostService.name);

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Get the DataSource instance
   * @returns DataSource instance
   */
  getDataSource(): DataSource {
    return this.dataSource;
  }

  /**
   * Get the cost of a feature for a user
   * @param userId User ID
   * @param featureId Feature ID
   * @returns Cost of the feature
   */
  async getFeatureCost(userId: string, featureId: string): Promise<number> {
    try {
      // Get user with organization
      const user = await this.dataSource.getRepository(UserEntity).findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      if (!user || !user.organizationId) {
        this.logger.warn(`User ${userId} not found or has no organization`);
        return this.getDefaultCost(featureId);
      }

      // Get subscription for the organization
      const subscription = await this.dataSource.getRepository(SubscriptionEntity).findOne({
        where: {
          organizationId: user.organizationId,
          status: SubscriptionStatus.ACTIVE,
        },
        // Use a more compatible way to specify order
        order: {
          id: 'DESC', // Order by ID instead of createdAt
        },
      });

      if (!subscription) {
        this.logger.warn(`No active subscription found for organization ${user.organizationId}`);
        return this.getDefaultCost(featureId);
      }

      // Get plan ID from subscription
      const planId = subscription.planId;

      // Get cost from plan features
      return this.getCostFromPlan(planId, featureId);
    } catch (error) {
      this.logger.error(`Error getting feature cost: ${error.message}`, error.stack);
      return this.getDefaultCost(featureId);
    }
  }

  /**
   * Get the cost of a feature from a plan
   * @param planId Plan ID
   * @param featureId Feature ID
   * @returns Cost of the feature
   */
  getCostFromPlan(planId: string, featureId: string): number {
    try {
      // Get plan features
      const planFeatures = PLAN_FEATURES[planId] || {};

      // Get feature from plan
      const feature = planFeatures[featureId];

      if (!feature) {
        this.logger.warn(`Feature ${featureId} not found in plan ${planId}`);
        return this.getDefaultCost(featureId);
      }

      // If feature is unlimited, cost is 0
      if (feature.unlimited) {
        return 0;
      }

      // Get feature cost from feature configuration
      const featureConfig = FEATURES[featureId];

      if (!featureConfig) {
        this.logger.warn(`Feature ${featureId} not found in features configuration`);
        return 1; // Default cost
      }

      // Return cost per use if available, otherwise default to 1
      return featureConfig.costPerUse || 1;
    } catch (error) {
      this.logger.error(`Error getting cost from plan: ${error.message}`, error.stack);
      return this.getDefaultCost(featureId);
    }
  }

  /**
   * Get the default cost of a feature
   * @param featureId Feature ID
   * @returns Default cost of the feature
   */
  getDefaultCost(featureId: string): number {
    try {
      // Get feature configuration
      const featureConfig = FEATURES[featureId];

      if (!featureConfig) {
        this.logger.warn(`Feature ${featureId} not found in features configuration`);
        return 1; // Default cost
      }

      // Return cost per use if available, otherwise default to 1
      return featureConfig.costPerUse || 1;
    } catch (error) {
      this.logger.error(`Error getting default cost: ${error.message}`, error.stack);
      return 1; // Default cost
    }
  }
}

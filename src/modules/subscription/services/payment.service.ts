import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ModuleRef } from '@nestjs/core';
import Stripe from 'stripe';
import {
  SubscriptionEntity,
  SubscriptionStatus,
  RenewInterval,
} from '../entities/subscription.entity';
import { LicenseEntity } from '../entities/license.entity';
import { OrganizationEntity } from '../../user/entities/organization.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { SubscriptionService } from './subscription.service';
import { LicenseService } from './license.service';
import { STRIPE_CONFIG } from '../../../configs/configs.constants';
import { PLANS } from '../../../configs/plans.config';
import { PLAN_FEATURES } from '../../../configs/plan-features.config';
import { SUBSCRIPTION_SYSTEM_CONFIG } from '../../../configs/subscription-system.config';
import { QuotaSource } from '../entities/organization-quota.entity';

export interface CreatePaymentIntentForSubscriptionDto {
  subscriptionId: string;
  organizationId: string;
  paymentMethodId?: string;
  savePaymentMethod?: boolean;
  returnUrl?: string;
}

export interface PaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
  status: string;
}

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  private stripe: Stripe;

  constructor(
    @InjectRepository(SubscriptionEntity)
    private subscriptionRepository: Repository<SubscriptionEntity>,
    @InjectRepository(OrganizationEntity)
    private organizationRepository: Repository<OrganizationEntity>,
    private dataSource: DataSource,
    private subscriptionService: SubscriptionService,
    private licenseService: LicenseService,
    private moduleRef: ModuleRef,
  ) {
    // Validate Stripe secret key
    if (!STRIPE_CONFIG.secretKey || STRIPE_CONFIG.secretKey === 'sk_test_...') {
      throw new Error('STRIPE_SECRET_KEY environment variable is not set or invalid');
    }

    this.stripe = new Stripe(STRIPE_CONFIG.secretKey);
  }

  /**
   * Create payment intent for existing subscription (used after onboarding)
   */
  async createPaymentIntentForSubscription(
    dto: CreatePaymentIntentForSubscriptionDto,
  ): Promise<PaymentIntentResponse> {
    try {
      const {
        subscriptionId,
        organizationId,
        paymentMethodId,
        savePaymentMethod = false,
        returnUrl,
      } = dto;

      // Get subscription and validate it belongs to organization
      const subscription = await this.subscriptionRepository.findOne({
        where: { id: subscriptionId, organizationId },
        relations: ['organization'],
      });

      if (!subscription) {
        throw new NotFoundException(
          `Subscription ${subscriptionId} not found for organization ${organizationId}`,
        );
      }

      if (subscription.status === SubscriptionStatus.ACTIVE) {
        throw new BadRequestException('Subscription is already active');
      }

      // Get the most up-to-date license count from database
      // This ensures we use the correct count even if licenses were added recently
      const licenseRepo = this.dataSource.getRepository(LicenseEntity);
      const actualLicenseCount = await licenseRepo.count({
        where: { subscriptionId: subscription.id },
      });

      // Use the actual license count from database, not from subscription entity
      // This handles cases where licenses were added but subscription entity wasn't refreshed
      const effectiveLicenseCount = Math.max(subscription.licenseCount, actualLicenseCount);

      this.logger.log(
        `Creating PaymentIntent for subscription ${subscriptionId}: subscription.licenseCount=${subscription.licenseCount}, actualLicenseCount=${actualLicenseCount}, using=${effectiveLicenseCount}`,
      );

      // Get plan information
      const plan = PLANS[subscription.planId];
      if (!plan) {
        throw new BadRequestException(`Plan ${subscription.planId} not found`);
      }

      // Calculate amount using effective license count
      const amount = await this.calculateFullSubscriptionAmount(
        effectiveLicenseCount,
        subscription.planId,
        subscription.renewInterval,
      );

      // Get or create Stripe customer
      const stripeCustomerId = await this.getOrCreateStripeCustomer(subscription.organization);

      // Create payment intent with flexible payment method support
      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: Math.round(amount * 100), // Convert to cents
        currency: 'usd',
        customer: stripeCustomerId,
        metadata: {
          subscriptionId: subscription.id,
          organizationId: subscription.organizationId,
          planId: subscription.planId,
          licenseCount: effectiveLicenseCount.toString(), // Use effective license count
        },
      };

      // Configure payment methods based on return URL
      if (returnUrl) {
        // Allow all payment methods including redirects when return URL is provided
        paymentIntentParams.automatic_payment_methods = {
          enabled: true,
        };
        paymentIntentParams.return_url = returnUrl;
      } else {
        // Only allow card payments when no return URL
        paymentIntentParams.automatic_payment_methods = {
          enabled: true,
          allow_redirects: 'never' as any,
        };
      }

      // If payment method is provided, attach it
      if (paymentMethodId) {
        paymentIntentParams.payment_method = paymentMethodId;
      }

      // If saving payment method for future use
      if (savePaymentMethod) {
        paymentIntentParams.setup_future_usage = 'off_session';
      }

      const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams);

      // Create a draft invoice for record keeping (but don't auto-pay it)
      let invoiceId = null;
      try {
        const invoice = await this.stripe.invoices.create({
          customer: stripeCustomerId,
          currency: 'usd',
          description: `Subscription Payment - ${plan.name} (${effectiveLicenseCount} licenses)`,
          metadata: {
            organizationId: subscription.organizationId,
            subscriptionId: subscription.id,
            planId: subscription.planId,
            licenseCount: effectiveLicenseCount.toString(),
            type: 'subscription',
            paymentIntentId: paymentIntent.id, // Link to PaymentIntent
          },
          auto_advance: false, // Don't auto-pay, we'll handle via PaymentIntent
          collection_method: 'charge_automatically',
        });

        // Add invoice item for subscription
        await this.stripe.invoiceItems.create({
          customer: stripeCustomerId,
          invoice: invoice.id,
          amount: Math.round(amount * 100), // Convert to cents
          currency: 'usd',
          description: `${plan.name} - ${effectiveLicenseCount} licenses`,
          metadata: {
            organizationId: subscription.organizationId,
            subscriptionId: subscription.id,
            planId: subscription.planId,
            licenseCount: effectiveLicenseCount.toString(),
            type: 'subscription',
            paymentIntentId: paymentIntent.id,
          },
        });

        // Finalize the invoice (but don't pay it yet)
        await this.stripe.invoices.finalizeInvoice(invoice.id);
        invoiceId = invoice.id;

        this.logger.log(
          `Created draft invoice ${invoice.id} for subscription ${subscription.id} linked to PaymentIntent ${paymentIntent.id}`,
        );
      } catch (invoiceError) {
        this.logger.warn(
          `Failed to create invoice for subscription ${subscription.id}: ${invoiceError.message}. PaymentIntent will still work.`,
        );
      }

      // Update subscription with Stripe IDs
      await this.subscriptionRepository.update(subscription.id, {
        stripeCustomerId,
        stripeSubscriptionId: paymentIntent.id,
      });

      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount,
        currency: 'usd',
        status: paymentIntent.status,
        invoiceId: invoiceId, // Include invoice ID in response for reference
      } as PaymentIntentResponse & { invoiceId?: string };
    } catch (error) {
      this.logger.error(
        `Error creating payment intent for subscription: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Execute transaction with custom timeout
   */
  private async executeTransactionWithTimeout<T>(
    operation: (entityManager: any) => Promise<T>,
    timeoutSeconds: number = SUBSCRIPTION_SYSTEM_CONFIG.PAYMENT_CONFIRMATION_TIMEOUT_SECONDS,
  ): Promise<T> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Set statement timeout for this transaction
      await queryRunner.query(`SET SESSION idle_in_transaction_session_timeout = '3min'`);
      await queryRunner.query(`SET SESSION statement_timeout = '${timeoutSeconds}s'`);

      const result = await operation(queryRunner.manager);
      await queryRunner.commitTransaction();
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Process successful payment and activate subscription
   */
  async processSuccessfulPayment(paymentIntentId: string): Promise<SubscriptionEntity> {
    try {
      // Get payment intent from Stripe
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      if (paymentIntent.status !== 'succeeded') {
        throw new BadRequestException(`Payment intent ${paymentIntentId} has not succeeded`);
      }

      const { subscriptionId, type, additionalLicenseCount } = paymentIntent.metadata;
      if (!subscriptionId) {
        throw new BadRequestException('Subscription ID not found in payment intent metadata');
      }

      // Use extended timeout for payment confirmation transaction
      return await this.executeTransactionWithTimeout(async (entityManager) => {
        // Get subscription
        const subscription = await entityManager.findOne(SubscriptionEntity, {
          where: { id: subscriptionId },
          relations: ['organization'],
        });

        if (!subscription) {
          throw new NotFoundException(`Subscription ${subscriptionId} not found`);
        }

        // Handle different payment types
        if (type === 'additional_licenses') {
          // This is a payment for additional licenses
          return await this.processAdditionalLicensesPayment(
            subscription,
            parseInt(additionalLicenseCount),
            paymentIntentId,
            entityManager,
          );
        } else {
          // This is a regular subscription activation payment
          return await this.processSubscriptionActivationPayment(
            subscription,
            paymentIntentId,
            entityManager,
          );
        }
      }, SUBSCRIPTION_SYSTEM_CONFIG.PAYMENT_CONFIRMATION_TIMEOUT_SECONDS);
    } catch (error) {
      this.logger.error(`Error processing successful payment: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process payment for subscription activation
   */
  private async processSubscriptionActivationPayment(
    subscription: SubscriptionEntity,
    paymentIntentId: string,
    entityManager: any,
  ): Promise<SubscriptionEntity> {
    // Check if subscription is already active
    if (subscription.status === SubscriptionStatus.ACTIVE) {
      this.logger.log(
        `Subscription ${subscription.id} is already active for payment ${paymentIntentId}. Skipping activation.`,
      );
      return subscription;
    }

    // Get payment intent from Stripe to verify amount
    const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
    const paidAmountInCents = paymentIntent.amount;
    const paidAmount = paidAmountInCents / 100; // Convert from cents to dollars

    // Get actual license count from database for verification
    const licenseRepo = entityManager.getRepository(LicenseEntity);
    const actualLicenseCount = await licenseRepo.count({
      where: { subscriptionId: subscription.id },
    });

    // Use the effective license count (max of subscription count and actual count)
    const effectiveLicenseCount = Math.max(subscription.licenseCount, actualLicenseCount);

    // Calculate expected amount using effective license count
    const expectedAmount = await this.calculateFullSubscriptionAmount(
      effectiveLicenseCount,
      subscription.planId,
      subscription.renewInterval,
    );

    // Verify payment amount matches expected amount (with small tolerance for rounding)
    const tolerance = 1; // $1 tolerance for rounding differences
    if (Math.abs(paidAmount - expectedAmount) > tolerance) {
      this.logger.error(
        `Payment amount mismatch for subscription activation ${paymentIntentId}: paid $${paidAmount}, expected $${expectedAmount} for ${effectiveLicenseCount} licenses (subscription: ${subscription.licenseCount}, actual: ${actualLicenseCount})`,
      );
      throw new BadRequestException(
        `Payment amount verification failed. Paid: $${paidAmount}, Expected: $${expectedAmount}`,
      );
    }

    // Verify license count from metadata matches effective license count
    const metadataLicenseCount = parseInt(paymentIntent.metadata.licenseCount);
    if (metadataLicenseCount !== effectiveLicenseCount) {
      this.logger.error(
        `License count mismatch for subscription activation ${paymentIntentId}: metadata has ${metadataLicenseCount}, effective count is ${effectiveLicenseCount} (subscription: ${subscription.licenseCount}, actual: ${actualLicenseCount})`,
      );
      throw new BadRequestException(
        `License count verification failed. Metadata: ${metadataLicenseCount}, Effective: ${effectiveLicenseCount}`,
      );
    }

    this.logger.log(
      `Payment verification successful for subscription activation ${paymentIntentId}: $${paidAmount} for ${subscription.licenseCount} licenses`,
    );

    // Activate subscription
    subscription.status = SubscriptionStatus.ACTIVE;
    subscription.stripeSubscriptionId = paymentIntentId;

    const activatedSubscription = await entityManager.save(SubscriptionEntity, subscription);

    // Assign licenses to existing users if any
    await this.assignLicensesToExistingUsers(subscription.organizationId, entityManager);

    this.logger.log(
      `Successfully activated subscription ${subscription.id} after payment ${paymentIntentId}`,
    );
    return activatedSubscription;
  }

  /**
   * Process payment for additional licenses
   */
  private async processAdditionalLicensesPayment(
    subscription: SubscriptionEntity,
    additionalLicenseCount: number,
    paymentIntentId: string,
    entityManager: any,
  ): Promise<SubscriptionEntity> {
    console.log('🚀 ~ PaymentService ~ subscription:', subscription);
    if (subscription.status !== SubscriptionStatus.ACTIVE) {
      throw new BadRequestException(
        `Cannot process additional licenses payment for subscription with status: ${subscription.status}`,
      );
    }

    this.logger.log(
      `Starting to process additional licenses payment ${paymentIntentId} for ${additionalLicenseCount} licenses`,
    );
    this.logger.log(
      `Initial subscription state - ID: ${subscription.id}, licenseCount: ${subscription.licenseCount}`,
    );

    // CRITICAL: Use database row-level locking to prevent race conditions
    // Get fresh subscription data with FOR UPDATE lock
    const lockedSubscription = await entityManager.query(
      `SELECT * FROM subscriptions WHERE id = $1 FOR UPDATE`,
      [subscription.id],
    );

    if (!lockedSubscription || lockedSubscription.length === 0) {
      throw new NotFoundException(`Subscription ${subscription.id} not found`);
    }

    const currentSubscription = lockedSubscription[0];
    this.logger.log(
      `Locked subscription state - ID: ${currentSubscription.id}, license_count: ${currentSubscription.license_count}`,
    );

    // Idempotency check: Check if this payment intent has already been processed
    // We'll use a more robust check by looking at the payment intent metadata and current state
    const paymentIntentData = await this.stripe.paymentIntents.retrieve(paymentIntentId);

    // Check if payment intent has a processed flag in metadata
    if (paymentIntentData.metadata && paymentIntentData.metadata.processed === 'true') {
      this.logger.log(
        `Payment ${paymentIntentId} already marked as processed in Stripe metadata. Skipping duplicate processing.`,
      );
      // Return the current subscription state
      const currentSubscription = await this.subscriptionRepository.findOne({
        where: { id: subscription.id },
      });
      return currentSubscription || subscription;
    }

    // Also check license count as secondary verification using LOCKED subscription data
    const currentLicenseCount = await this.licenseService.getLicenseCountForSubscription(
      subscription.id,
    );
    const expectedTotalCount = currentSubscription.license_count + additionalLicenseCount;
    this.logger.log(
      `License count check - Current: ${currentLicenseCount}, Expected: ${expectedTotalCount} (based on locked subscription license_count: ${currentSubscription.license_count})`,
    );
    console.log('🚀 ~ PaymentService ~ currentLicenseCount:', currentLicenseCount);
    console.log('🚀 ~ PaymentService ~ expectedTotalCount:', expectedTotalCount);
    if (currentLicenseCount >= expectedTotalCount) {
      this.logger.log(
        `Payment ${paymentIntentId} appears to be already processed based on license count. Current licenses: ${currentLicenseCount}, Expected: ${expectedTotalCount}. Skipping duplicate processing.`,
      );

      // Mark payment intent as processed in Stripe metadata
      try {
        await this.stripe.paymentIntents.update(paymentIntentId, {
          metadata: {
            ...paymentIntentData.metadata,
            processed: 'true',
            processedAt: new Date().toISOString(),
          },
        });
      } catch (metadataError) {
        this.logger.warn(`Failed to update payment intent metadata: ${metadataError.message}`);
      }

      // Return the current subscription state
      const currentSubscription = await this.subscriptionRepository.findOne({
        where: { id: subscription.id },
      });
      return currentSubscription || subscription;
    }

    // Use the already retrieved payment intent data for verification
    const paidAmountInCents = paymentIntentData.amount;
    const paidAmount = paidAmountInCents / 100; // Convert from cents to dollars

    // Calculate expected amount for the additional licenses
    const expectedProRataCalculation = await this.calculateProRataAmountForAdditionalLicenses(
      subscription.id,
      additionalLicenseCount,
    );
    console.log('🚀 ~ PaymentService ~ additionalLicenseCount:', additionalLicenseCount);

    // Verify payment amount matches expected amount (with small tolerance for rounding)
    const tolerance = 1; // $1 tolerance for rounding differences
    if (Math.abs(paidAmount - expectedProRataCalculation.amount) > tolerance) {
      this.logger.error(
        `Payment amount mismatch for ${paymentIntentId}: paid $${paidAmount}, expected $${expectedProRataCalculation.amount} for ${additionalLicenseCount} licenses`,
      );
      throw new BadRequestException(
        `Payment amount verification failed. Paid: $${paidAmount}, Expected: $${expectedProRataCalculation.amount}`,
      );
    }

    // Verify license count from metadata matches calculation
    const metadataLicenseCount = parseInt(paymentIntentData.metadata.additionalLicenseCount);
    if (metadataLicenseCount !== additionalLicenseCount) {
      this.logger.error(
        `License count mismatch for ${paymentIntentId}: metadata has ${metadataLicenseCount}, processing ${additionalLicenseCount}`,
      );
      throw new BadRequestException(
        `License count verification failed. Metadata: ${metadataLicenseCount}, Processing: ${additionalLicenseCount}`,
      );
    }

    this.logger.log(
      `Payment verification successful for ${paymentIntentId}: $${paidAmount} for ${additionalLicenseCount} licenses`,
    );

    // Create a proper SubscriptionEntity object from locked data
    const lockedSubscriptionEntity = new SubscriptionEntity();
    Object.assign(lockedSubscriptionEntity, {
      id: currentSubscription.id,
      organizationId: currentSubscription.organization_id,
      planId: currentSubscription.plan_id,
      licenseCount: currentSubscription.license_count,
      status: currentSubscription.status,
      // Copy other necessary fields
      startDate: currentSubscription.start_date,
      nextBillingDate: currentSubscription.next_billing_date,
      renewInterval: currentSubscription.renew_interval,
      autoRenew: currentSubscription.auto_renew,
    });

    console.log('🚀 ~ PaymentService ~ lockedSubscriptionEntity:', lockedSubscriptionEntity);
    // Add licenses to subscription using LOCKED subscription data
    const result = await this.licenseService.addLicensesToSubscription(
      lockedSubscriptionEntity.id,
      additionalLicenseCount,
      lockedSubscriptionEntity.organizationId,
      'payment_system',
      entityManager,
    );

    this.logger.log(
      `Successfully processed payment ${paymentIntentId} for ${additionalLicenseCount} additional licenses. Total licenses: ${result.totalLicenseCount}`,
    );

    // Mark payment intent as processed in Stripe metadata to prevent duplicate processing
    try {
      await this.stripe.paymentIntents.update(paymentIntentId, {
        metadata: {
          ...paymentIntentData.metadata,
          processed: 'true',
          processedAt: new Date().toISOString(),
          finalLicenseCount: result.totalLicenseCount.toString(),
        },
      });
      this.logger.log(`Marked payment intent ${paymentIntentId} as processed in Stripe metadata`);
    } catch (metadataError) {
      this.logger.warn(`Failed to update payment intent metadata: ${metadataError.message}`);
      // Don't throw here as the main operation succeeded
    }

    return result.subscription;
  }

  /**
   * Handle Stripe webhook events
   */
  async handleWebhookEvent(event: Stripe.Event): Promise<void> {
    try {
      this.logger.log(`Processing webhook event: ${event.type} (${event.id})`);

      switch (event.type) {
        // Payment Intent Events
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
          break;

        // Invoice Events
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;
        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
          break;

        default:
          this.logger.log(`Unhandled webhook event type: ${event.type}`);
      }

      this.logger.log(`Successfully processed webhook event: ${event.type} (${event.id})`);
    } catch (error) {
      this.logger.error(
        `Error handling webhook event ${event.type} (${event.id}): ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Calculate full subscription amount for given parameters (public method for preview)
   */
  async calculateFullSubscriptionAmount(
    licenseCount: number,
    planId: string,
    renewInterval: string,
  ): Promise<number> {
    const plan = PLANS[planId];
    if (!plan) {
      throw new BadRequestException(`Plan ${planId} not found`);
    }

    const baseAmount = plan.price * licenseCount;

    // Apply billing cycle multiplier
    let multiplier = 1;
    switch (renewInterval) {
      case RenewInterval.ANNUALLY:
        multiplier = 12;
        break;
      case RenewInterval.QUARTERLY:
        multiplier = 3;
        break;
      default:
        multiplier = 1;
    }

    const finalAmount = baseAmount * multiplier;
    return finalAmount;
  }

  /**
   * Create PaymentIntent for credit topup (supports new cards without saving)
   */
  async createPaymentIntentForCreditTopup(params: {
    organizationId: string;
    amount: number;
    credits: number;
    paymentMethodId?: string;
    savePaymentMethod?: boolean;
    description?: string;
    packageId?: string;
  }): Promise<{
    clientSecret: string;
    paymentIntentId: string;
    amount: number;
    currency: string;
    status: string;
    invoiceId?: string;
  }> {
    try {
      const {
        organizationId,
        amount,
        credits,
        paymentMethodId,
        savePaymentMethod = false,
        description,
        packageId,
      } = params;

      // Get organization
      const organization = await this.organizationRepository.findOne({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new NotFoundException(`Organization ${organizationId} not found`);
      }

      // Get or create Stripe customer
      const stripeCustomerId = await this.getOrCreateStripeCustomer(organization);

      // Create payment intent with flexible payment method support
      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: Math.round(amount * 100), // Convert to cents
        currency: 'usd',
        customer: stripeCustomerId,
        description: description || `Credit Topup - ${credits} credits`,
        metadata: {
          organizationId,
          credits: credits.toString(),
          type: 'credit_topup',
          packageId: packageId || 'custom',
          amount: amount.toString(),
        },
        automatic_payment_methods: {
          enabled: true,
          allow_redirects: 'never', // Disable redirect-based payment methods
        },
      };

      // If payment method is provided, attach it
      if (paymentMethodId) {
        paymentIntentParams.payment_method = paymentMethodId;
      }

      // If saving payment method for future use
      if (savePaymentMethod) {
        paymentIntentParams.setup_future_usage = 'off_session';
      }

      console.log('🚀 ~ PaymentService ~ paymentIntentParams:', paymentIntentParams);
      let paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams);

      // If paymentMethodId is provided, confirm the payment automatically
      if (paymentMethodId) {
        try {
          this.logger.log(
            `Auto-confirming PaymentIntent ${paymentIntent.id} with payment method ${paymentMethodId}`,
          );
          paymentIntent = await this.stripe.paymentIntents.confirm(paymentIntent.id, {
            payment_method: paymentMethodId,
          });
          this.logger.log(
            `PaymentIntent ${paymentIntent.id} confirmed with status: ${paymentIntent.status}`,
          );
        } catch (confirmError) {
          this.logger.error(
            `Failed to confirm PaymentIntent ${paymentIntent.id}: ${confirmError.message}`,
            confirmError.stack,
          );
          // Don't throw here, let the frontend handle confirmation if auto-confirm fails
        }
      }

      // Create a draft invoice for record keeping (but don't auto-pay it)
      let invoiceId = null;
      try {
        const invoice = await this.stripe.invoices.create({
          customer: stripeCustomerId,
          currency: 'usd',
          description: description || `Credit Topup Invoice - ${credits} credits`,
          metadata: {
            organizationId,
            credits: credits.toString(),
            type: 'credit_topup',
            packageId: packageId || 'custom',
            amount: amount.toString(),
            paymentIntentId: paymentIntent.id, // Link to PaymentIntent
          },
          auto_advance: false, // Don't auto-pay, we'll handle via PaymentIntent
          collection_method: 'charge_automatically',
        });

        // Add invoice item for credits
        await this.stripe.invoiceItems.create({
          customer: stripeCustomerId,
          invoice: invoice.id,
          amount: Math.round(amount * 100), // Convert to cents
          currency: 'usd',
          description: `Credit Topup - ${credits} credits`,
          metadata: {
            organizationId,
            credits: credits.toString(),
            type: 'credit_topup',
            paymentIntentId: paymentIntent.id,
          },
        });

        // Finalize the invoice (but don't pay it yet)
        await this.stripe.invoices.finalizeInvoice(invoice.id);
        invoiceId = invoice.id;

        this.logger.log(
          `Created draft invoice ${invoice.id} for credit topup linked to PaymentIntent ${paymentIntent.id}`,
        );
      } catch (invoiceError) {
        this.logger.warn(
          `Failed to create invoice for credit topup: ${invoiceError.message}. PaymentIntent will still work.`,
        );
      }

      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount,
        currency: 'usd',
        status: paymentIntent.status,
        invoiceId,
      };
    } catch (error) {
      this.logger.error(
        `Error creating payment intent for credit topup: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create invoice for credit topup (legacy method - auto-pays with saved payment method)
   */
  async createInvoiceForCredits(params: {
    organizationId: string;
    amount: number;
    credits: number;
    paymentMethodId?: string;
    description?: string;
    packageId?: string;
  }): Promise<Stripe.Invoice> {
    try {
      const { organizationId, amount, credits, paymentMethodId, description, packageId } = params;

      // Get organization
      const organization = await this.organizationRepository.findOne({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new NotFoundException(`Organization ${organizationId} not found`);
      }

      // Get or create Stripe customer
      const stripeCustomerId = await this.getOrCreateStripeCustomer(organization);

      // Set default payment method if provided
      if (paymentMethodId) {
        await this.stripe.customers.update(stripeCustomerId, {
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        });
      }

      // Create invoice
      const invoice = await this.stripe.invoices.create({
        customer: stripeCustomerId,
        currency: 'usd',
        description: description || `Credit Topup Invoice - ${credits} credits`,
        metadata: {
          organizationId,
          credits: credits.toString(),
          type: 'credit_topup',
          packageId: packageId || 'custom',
          amount: amount.toString(),
        },
        auto_advance: true, // Automatically finalize and attempt payment
        collection_method: 'charge_automatically',
      });

      // Add invoice item for credits
      await this.stripe.invoiceItems.create({
        customer: stripeCustomerId,
        invoice: invoice.id,
        amount: Math.round(amount * 100), // Convert to cents
        currency: 'usd',
        description: `Credit Topup - ${credits} credits`,
        metadata: {
          organizationId,
          credits: credits.toString(),
          type: 'credit_topup',
        },
      });

      // Finalize the invoice
      const finalizedInvoice = await this.stripe.invoices.finalizeInvoice(invoice.id);

      // Attempt to pay the invoice immediately
      const paidInvoice = await this.stripe.invoices.pay(finalizedInvoice.id);

      this.logger.log(
        `Created and paid invoice ${paidInvoice.id} for ${credits} credits ($${amount}) for organization ${organizationId}`,
      );

      return paidInvoice;
    } catch (error) {
      this.logger.error(`Error creating invoice for credits: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Legacy method name for backward compatibility
   * @deprecated Use createInvoiceForCredits instead
   */
  async createPaymentIntentForCredits(params: {
    organizationId: string;
    amount: number;
    credits: number;
    paymentMethodId?: string;
    description?: string;
  }): Promise<Stripe.Invoice> {
    return this.createInvoiceForCredits(params);
  }

  /**
   * Create invoice for subscription payment
   */
  async createInvoiceForSubscription(params: {
    organizationId: string;
    subscriptionId: string;
    amount: number;
    description: string;
    planId: string;
    licenseCount: number;
    paymentMethodId?: string;
  }): Promise<Stripe.Invoice> {
    try {
      const {
        organizationId,
        subscriptionId,
        amount,
        description,
        planId,
        licenseCount,
        paymentMethodId,
      } = params;

      // Get organization
      const organization = await this.organizationRepository.findOne({
        where: { id: organizationId },
      });

      if (!organization) {
        throw new NotFoundException(`Organization ${organizationId} not found`);
      }

      // Get or create Stripe customer
      const stripeCustomerId = await this.getOrCreateStripeCustomer(organization);

      // Set default payment method if provided
      if (paymentMethodId) {
        await this.stripe.customers.update(stripeCustomerId, {
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        });
      }

      // Create invoice
      const invoice = await this.stripe.invoices.create({
        customer: stripeCustomerId,
        currency: 'usd',
        description: description,
        metadata: {
          organizationId,
          subscriptionId,
          planId,
          licenseCount: licenseCount.toString(),
          type: 'subscription',
        },
        auto_advance: true, // Automatically finalize and attempt payment
        collection_method: 'charge_automatically',
      });

      // Add invoice item for subscription
      await this.stripe.invoiceItems.create({
        customer: stripeCustomerId,
        invoice: invoice.id,
        amount: Math.round(amount * 100), // Convert to cents
        currency: 'usd',
        description: description,
        metadata: {
          organizationId,
          subscriptionId,
          planId,
          licenseCount: licenseCount.toString(),
          type: 'subscription',
        },
      });

      // Finalize the invoice
      const finalizedInvoice = await this.stripe.invoices.finalizeInvoice(invoice.id);

      // Attempt to pay the invoice immediately
      const paidInvoice = await this.stripe.invoices.pay(finalizedInvoice.id);

      this.logger.log(
        `Created and paid subscription invoice ${paidInvoice.id} for $${amount} for organization ${organizationId}`,
      );

      return paidInvoice;
    } catch (error) {
      this.logger.error(`Error creating invoice for subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate pro-rata amount for additional licenses added mid-cycle
   */
  async calculateProRataAmountForAdditionalLicenses(
    subscriptionId: string,
    additionalLicenseCount: number,
  ): Promise<{
    amount: number;
    description: string;
    remainingDays: number;
    totalDaysInCycle: number;
  }> {
    try {
      // Get subscription with billing details
      const subscription = await this.subscriptionRepository.findOne({
        where: { id: subscriptionId },
      });

      if (!subscription) {
        throw new NotFoundException(`Subscription ${subscriptionId} not found`);
      }

      const plan = PLANS[subscription.planId];
      if (!plan) {
        throw new BadRequestException(`Plan ${subscription.planId} not found`);
      }

      // Calculate billing cycle details
      const now = new Date();
      const cycleStart = subscription.startDate;
      const cycleEnd = this.calculateCycleEnd(cycleStart, subscription.renewInterval);

      // Calculate remaining days in current cycle
      const totalDaysInCycle = Math.ceil(
        (cycleEnd.getTime() - cycleStart.getTime()) / (1000 * 60 * 60 * 24),
      );
      const remainingDays = Math.ceil((cycleEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      // Ensure remaining days is not negative
      const actualRemainingDays = Math.max(0, remainingDays);

      // Calculate pro-rata amount
      const fullCycleAmountPerLicense =
        plan.price * this.getBillingMultiplier(subscription.renewInterval);
      const proRataAmountPerLicense =
        (fullCycleAmountPerLicense * actualRemainingDays) / totalDaysInCycle;
      const totalProRataAmount = Math.round(proRataAmountPerLicense * additionalLicenseCount);

      const description = `Pro-rata charge for ${additionalLicenseCount} additional license(s) - ${actualRemainingDays} days remaining in cycle`;

      return {
        amount: totalProRataAmount,
        description,
        remainingDays: actualRemainingDays,
        totalDaysInCycle,
      };
    } catch (error) {
      this.logger.error(`Error calculating pro-rata amount: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate cycle end date based on start date and renewal interval
   */
  private calculateCycleEnd(cycleStart: Date, renewInterval: RenewInterval): Date {
    const cycleEnd = new Date(cycleStart);

    switch (renewInterval) {
      case RenewInterval.MONTHLY:
        cycleEnd.setMonth(cycleEnd.getMonth() + 1);
        break;
      case RenewInterval.QUARTERLY:
        cycleEnd.setMonth(cycleEnd.getMonth() + 3);
        break;
      case RenewInterval.ANNUALLY:
        cycleEnd.setFullYear(cycleEnd.getFullYear() + 1);
        break;
      default:
        cycleEnd.setMonth(cycleEnd.getMonth() + 1);
    }

    return cycleEnd;
  }

  /**
   * Get billing multiplier for renewal interval
   */
  private getBillingMultiplier(renewInterval: RenewInterval): number {
    switch (renewInterval) {
      case RenewInterval.ANNUALLY:
        return 12;
      case RenewInterval.QUARTERLY:
        return 3;
      default:
        return 1;
    }
  }

  /**
   * Calculate full cycle price for additional licenses
   */
  private calculateFullCyclePrice(subscription: any, additionalLicenseCount: number): number {
    const plan = PLANS[subscription.planId];
    if (!plan) {
      return 0;
    }

    const basePrice = plan.price;
    const billingMultiplier = this.getBillingMultiplier(subscription.renewInterval);
    return basePrice * additionalLicenseCount * billingMultiplier;
  }

  /**
   * Calculate full cycle price for additional licenses (public method for preview)
   */
  async calculateFullCyclePriceForPreview(
    subscription: any,
    additionalLicenseCount: number,
  ): Promise<number> {
    return this.calculateFullCyclePrice(subscription, additionalLicenseCount);
  }

  /**
   * Calculate credit allocation for additional licenses
   */
  private async calculateCreditAllocation(
    subscription: any,
    additionalLicenseCount: number,
    remainingDays: number,
    totalDaysInCycle: number,
  ): Promise<{
    creditsPerLicense: number;
    totalCreditsToAllocate: number;
    proRataCredits: number;
  } | null> {
    try {
      const planFeatures = PLAN_FEATURES[subscription.planId];
      if (!planFeatures || !planFeatures.credits) {
        return null;
      }

      const creditsFeature = planFeatures.credits;
      const creditsPerLicense = creditsFeature.amount || 0;

      if (creditsPerLicense === 0 || creditsFeature.unlimited) {
        return null;
      }

      const totalCreditsToAllocate = creditsPerLicense * additionalLicenseCount;

      // Calculate pro-rata credits based on remaining cycle time
      const proRataCredits = Math.round(
        (totalCreditsToAllocate * remainingDays) / totalDaysInCycle,
      );

      return {
        creditsPerLicense,
        totalCreditsToAllocate,
        proRataCredits,
      };
    } catch (error) {
      this.logger.error(`Error calculating credit allocation: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Calculate credit allocation for additional licenses (public method for preview)
   */
  async calculateCreditAllocationForPreview(
    subscription: any,
    additionalLicenseCount: number,
    remainingDays: number,
    totalDaysInCycle: number,
  ): Promise<{
    creditsPerLicense: number;
    totalCreditsToAllocate: number;
    proRataCredits: number;
  } | null> {
    return this.calculateCreditAllocation(
      subscription,
      additionalLicenseCount,
      remainingDays,
      totalDaysInCycle,
    );
  }

  /**
   * Calculate license addition preview - READ-ONLY method that doesn't modify any data
   * This method is safe for preview purposes and won't create customers, payment intents, or modify database
   */
  async calculateLicenseAdditionPreview(
    subscription: any,
    additionalLicenseCount: number,
  ): Promise<{
    proRataDetails: {
      description: string;
      remainingDays: number;
      totalDaysInCycle: number;
      fullCyclePrice: number;
      proRataPrice: number;
    };
    creditAllocation?: {
      creditsPerLicense: number;
      totalCreditsToAllocate: number;
      proRataCredits: number;
    };
  }> {
    try {
      // Get plan details (read-only)
      const plan = PLANS[subscription.planId];
      if (!plan) {
        throw new BadRequestException(`Plan ${subscription.planId} not found`);
      }

      // Calculate billing cycle details (read-only calculations)
      const now = new Date();
      const cycleStart = subscription.startDate;
      const cycleEnd = this.calculateCycleEnd(cycleStart, subscription.renewInterval);

      // Calculate remaining days in current cycle
      const totalDaysInCycle = Math.ceil(
        (cycleEnd.getTime() - cycleStart.getTime()) / (1000 * 60 * 60 * 24),
      );
      const remainingDays = Math.ceil((cycleEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      // Ensure remaining days is not negative
      const actualRemainingDays = Math.max(0, remainingDays);

      // Calculate pro-rata amount (read-only calculations)
      const fullCycleAmountPerLicense =
        plan.price * this.getBillingMultiplier(subscription.renewInterval);
      const proRataAmountPerLicense =
        (fullCycleAmountPerLicense * actualRemainingDays) / totalDaysInCycle;
      const totalProRataAmount = Math.round(proRataAmountPerLicense * additionalLicenseCount);

      const description = `Pro-rata charge for ${additionalLicenseCount} additional license(s) - ${actualRemainingDays} days remaining in cycle`;

      // Calculate full cycle price (read-only)
      const fullCyclePrice = this.calculateFullCyclePrice(subscription, additionalLicenseCount);

      // Calculate credit allocation (read-only)
      const creditAllocation = await this.calculateCreditAllocation(
        subscription,
        additionalLicenseCount,
        actualRemainingDays,
        totalDaysInCycle,
      );

      return {
        proRataDetails: {
          description,
          remainingDays: actualRemainingDays,
          totalDaysInCycle,
          fullCyclePrice,
          proRataPrice: totalProRataAmount,
        },
        creditAllocation,
      };
    } catch (error) {
      this.logger.error(
        `Error calculating license addition preview: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create payment intent for additional licenses (pro-rata billing)
   */
  async createPaymentIntentForAdditionalLicenses(
    subscriptionId: string,
    additionalLicenseCount: number,
    organizationId: string,
    autoConfirm: boolean = true,
    paymentMethodId?: string,
    savePaymentMethod: boolean = false,
  ): Promise<{
    clientSecret?: string;
    paymentIntentId?: string;
    amount: number;
    currency: string;
    status: string;
    proRataDetails: {
      description: string;
      remainingDays: number;
      totalDaysInCycle: number;
      fullCyclePrice: number;
      proRataPrice: number;
    };
    creditAllocation?: {
      creditsPerLicense: number;
      totalCreditsToAllocate: number;
      proRataCredits: number;
    };
    paymentSuccessful?: boolean;
    newLicenses?: any[];
    totalLicenseCount?: number;
    paymentError?: {
      code: string;
      message: string;
      requiresNewPaymentMethod: boolean;
    };
  }> {
    try {
      // Get subscription with organization
      const subscription = await this.subscriptionRepository.findOne({
        where: { id: subscriptionId, organizationId },
        relations: ['organization'],
      });

      if (!subscription) {
        throw new NotFoundException(`Subscription ${subscriptionId} not found or not accessible`);
      }

      if (subscription.status !== SubscriptionStatus.ACTIVE) {
        throw new BadRequestException(
          `Cannot add licenses to subscription with status: ${subscription.status}`,
        );
      }

      // Calculate pro-rata amount
      const proRataCalculation = await this.calculateProRataAmountForAdditionalLicenses(
        subscriptionId,
        additionalLicenseCount,
      );

      if (proRataCalculation.amount <= 0) {
        throw new BadRequestException(
          'No charge required - billing cycle is ending soon or already ended',
        );
      }

      // Get or create Stripe customer
      const stripeCustomerId = await this.getOrCreateStripeCustomer(subscription.organization);

      // Calculate credit allocation details
      const creditAllocation = await this.calculateCreditAllocation(
        subscription,
        additionalLicenseCount,
        proRataCalculation.remainingDays,
        proRataCalculation.totalDaysInCycle,
      );
      console.log('🚀 ~ PaymentService ~ creditAllocation:', creditAllocation);

      const proRataDetails = {
        description: proRataCalculation.description,
        remainingDays: proRataCalculation.remainingDays,
        totalDaysInCycle: proRataCalculation.totalDaysInCycle,
        fullCyclePrice: this.calculateFullCyclePrice(subscription, additionalLicenseCount),
        proRataPrice: proRataCalculation.amount,
      };

      // If autoConfirm is true, try to charge using specified or saved payment method
      if (autoConfirm) {
        try {
          // Use provided paymentMethodId or get customer's default payment method
          const targetPaymentMethodId =
            paymentMethodId || (await this.getCustomerDefaultPaymentMethod(stripeCustomerId));

          if (targetPaymentMethodId) {
            // Create and confirm payment intent immediately
            const paymentIntentParams: any = {
              amount: proRataCalculation.amount * 100, // Convert to cents
              currency: 'usd',
              customer: stripeCustomerId,
              payment_method: targetPaymentMethodId,
              description: proRataCalculation.description,
              confirm: true, // Auto-confirm the payment
              metadata: {
                subscriptionId: subscription.id,
                organizationId: subscription.organizationId,
                planId: subscription.planId,
                additionalLicenseCount: additionalLicenseCount.toString(),
                type: 'additional_licenses',
                remainingDays: proRataCalculation.remainingDays.toString(),
                totalDaysInCycle: proRataCalculation.totalDaysInCycle.toString(),
              },
              return_url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard/subscription`,
            };

            // If saving payment method for future use
            if (savePaymentMethod) {
              paymentIntentParams.setup_future_usage = 'off_session';
            }

            const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentParams);

            // If payment succeeded, process the licenses immediately
            if (paymentIntent.status === 'succeeded') {
              const result = await this.processAdditionalLicensesPayment(
                subscription,
                additionalLicenseCount,
                paymentIntent.id,
                null, // No entity manager, will create its own transaction
              );
              console.log('🚀 ~ PaymentService ~ result:', result);

              return {
                amount: proRataCalculation.amount,
                currency: 'usd',
                status: 'succeeded',
                proRataDetails,
                creditAllocation,
                paymentSuccessful: true,
                newLicenses: [], // Will be populated by the license service
                totalLicenseCount: result.licenseCount,
              };
            }
          }
        } catch (autoPaymentError) {
          this.logger.warn(
            `Auto-payment failed for subscription ${subscriptionId}: ${autoPaymentError.message}`,
          );

          // Check if it's a card declined error (insufficient funds, etc.)
          if (
            autoPaymentError.code === 'card_declined' ||
            autoPaymentError.decline_code === 'insufficient_funds' ||
            autoPaymentError.code === 'authentication_required'
          ) {
            // Return error info for frontend to handle
            return {
              amount: proRataCalculation.amount,
              currency: 'usd',
              status: 'requires_payment_method',
              proRataDetails,
              creditAllocation,
              paymentError: {
                code: autoPaymentError.code || 'payment_failed',
                message: autoPaymentError.message || 'Payment failed with saved payment method',
                requiresNewPaymentMethod: true,
              },
            };
          }

          // For other errors, fall through to manual payment intent creation
        }
      }

      // Create manual payment intent (when autoConfirm is false or auto-payment failed)
      const manualPaymentIntentParams: any = {
        amount: proRataCalculation.amount * 100, // Convert to cents
        currency: 'usd',
        customer: stripeCustomerId,
        description: proRataCalculation.description,
        metadata: {
          subscriptionId: subscription.id,
          organizationId: subscription.organizationId,
          planId: subscription.planId,
          additionalLicenseCount: additionalLicenseCount.toString(),
          type: 'additional_licenses',
          remainingDays: proRataCalculation.remainingDays.toString(),
          totalDaysInCycle: proRataCalculation.totalDaysInCycle.toString(),
        },
        automatic_payment_methods: {
          enabled: true,
          allow_redirects: 'never', // Disable redirect-based payment methods
        },
      };

      // If specific payment method is provided for manual payment
      if (paymentMethodId) {
        manualPaymentIntentParams.payment_method = paymentMethodId;
      }

      // If saving payment method for future use
      if (savePaymentMethod) {
        manualPaymentIntentParams.setup_future_usage = 'off_session';
      }

      const paymentIntent = await this.stripe.paymentIntents.create(manualPaymentIntentParams);

      // Create a draft invoice for additional licenses (for record keeping)
      let invoiceId = null;
      try {
        const invoice = await this.stripe.invoices.create({
          customer: stripeCustomerId,
          currency: 'usd',
          description: proRataCalculation.description,
          metadata: {
            subscriptionId: subscription.id,
            organizationId: subscription.organizationId,
            planId: subscription.planId,
            additionalLicenseCount: additionalLicenseCount.toString(),
            type: 'additional_licenses',
            paymentIntentId: paymentIntent.id, // Link to PaymentIntent
            remainingDays: proRataCalculation.remainingDays.toString(),
            totalDaysInCycle: proRataCalculation.totalDaysInCycle.toString(),
          },
          auto_advance: false, // Don't auto-pay, we'll handle via PaymentIntent
          collection_method: 'charge_automatically',
        });

        // Add invoice item for additional licenses
        await this.stripe.invoiceItems.create({
          customer: stripeCustomerId,
          invoice: invoice.id,
          amount: proRataCalculation.amount * 100, // Convert to cents
          currency: 'usd',
          description: proRataCalculation.description,
          metadata: {
            subscriptionId: subscription.id,
            organizationId: subscription.organizationId,
            planId: subscription.planId,
            additionalLicenseCount: additionalLicenseCount.toString(),
            type: 'additional_licenses',
            paymentIntentId: paymentIntent.id,
          },
        });

        // Finalize the invoice (but don't pay it yet)
        await this.stripe.invoices.finalizeInvoice(invoice.id);
        invoiceId = invoice.id;

        this.logger.log(
          `Created draft invoice ${invoice.id} for additional licenses linked to PaymentIntent ${paymentIntent.id}`,
        );
      } catch (invoiceError) {
        this.logger.warn(
          `Failed to create invoice for additional licenses: ${invoiceError.message}. PaymentIntent will still work.`,
        );
      }

      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount: proRataCalculation.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status,
        invoiceId: invoiceId, // Include invoice ID for reference
        proRataDetails,
        creditAllocation,
      } as any;
    } catch (error) {
      this.logger.error(
        `Error creating payment intent for additional licenses: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get or create Stripe customer for organization
   */
  private async getOrCreateStripeCustomer(organization: OrganizationEntity): Promise<string> {
    if (organization.stripeCustomerId) {
      return organization.stripeCustomerId;
    }

    const customer = await this.stripe.customers.create({
      name: organization.name,
      metadata: {
        organizationId: organization.id,
      },
    });

    // Update organization with Stripe customer ID
    await this.organizationRepository.update(organization.id, {
      stripeCustomerId: customer.id,
    });

    return customer.id;
  }

  /**
   * Get customer's default payment method
   */
  private async getCustomerDefaultPaymentMethod(stripeCustomerId: string): Promise<string | null> {
    try {
      // First try to get from customer's invoice settings
      const customer = await this.stripe.customers.retrieve(stripeCustomerId);
      const defaultPaymentMethodId = (customer as any).invoice_settings?.default_payment_method;

      if (defaultPaymentMethodId) {
        return defaultPaymentMethodId;
      }

      // If no default payment method, try to get the most recent payment method
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: stripeCustomerId,
        type: 'card',
        limit: 1,
      });

      if (paymentMethods.data.length > 0) {
        return paymentMethods.data[0].id;
      }

      return null;
    } catch (error) {
      this.logger.warn(
        `Failed to get default payment method for customer ${stripeCustomerId}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Assign licenses to existing users when subscription is activated
   */
  private async assignLicensesToExistingUsers(
    organizationId: string,
    entityManager: any,
  ): Promise<void> {
    try {
      // Get all users in the organization who don't have licenses
      const usersWithoutLicenses = await entityManager
        .createQueryBuilder(UserEntity, 'u')
        .leftJoin('licenses', 'l', 'l.user_id = u.id')
        .where('u.organizationId = :organizationId', { organizationId })
        .andWhere('u.isDeleted = false')
        .andWhere('l.id IS NULL') // Users without licenses
        .getMany();

      if (usersWithoutLicenses.length === 0) {
        this.logger.log(`No users without licenses found for organization ${organizationId}`);
        return;
      }

      // Get subscription
      const subscription = await entityManager.findOne(SubscriptionEntity, {
        where: { organizationId: organizationId, status: SubscriptionStatus.ACTIVE },
      });

      if (!subscription) {
        this.logger.warn(`No active subscription found for organization ${organizationId}`);
        return;
      }

      // Get available licenses
      const availableLicenses = await this.licenseService.getAvailableLicenses(subscription.id);

      if (availableLicenses.length === 0) {
        this.logger.warn(`No available licenses for organization ${organizationId}`);
        return;
      }

      // Separate admin and regular users
      const adminUsers = usersWithoutLicenses.filter(
        (user) => user.roleId && user.roleId.toString().includes('admin'),
      );
      const regularUsers = usersWithoutLicenses.filter(
        (user) => !user.roleId || !user.roleId.toString().includes('admin'),
      );

      let licenseIndex = 0;

      // Assign licenses to admin users first
      for (const adminUser of adminUsers) {
        if (licenseIndex < availableLicenses.length) {
          await this.licenseService.assignLicenseToUser(
            availableLicenses[licenseIndex].id,
            adminUser.id,
            'system', // performed by system
            entityManager,
          );
          licenseIndex++;
          this.logger.log(`Assigned license to admin user ${adminUser.id} after payment`);
        }
      }

      // Assign licenses to regular users
      for (const regularUser of regularUsers) {
        if (licenseIndex < availableLicenses.length) {
          await this.licenseService.assignLicenseToUser(
            availableLicenses[licenseIndex].id,
            regularUser.id,
            'system', // performed by system
            entityManager,
          );
          licenseIndex++;
          this.logger.log(`Assigned license to user ${regularUser.id} after payment`);
        }
      }

      this.logger.log(
        `Assigned ${licenseIndex} licenses to existing users for organization ${organizationId} after payment`,
      );
    } catch (error) {
      this.logger.error(
        `Error assigning licenses to existing users: ${error.message}`,
        error.stack,
      );
      // Don't throw error as this is a post-payment cleanup step
    }
  }

  /**
   * Handle successful payment intent
   */
  private async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    try {
      // Check if this is a credit topup payment
      if (paymentIntent.metadata && paymentIntent.metadata.type === 'credit_topup') {
        await this.processCreditTopupFromPaymentIntent(paymentIntent);
        return;
      }

      // Check if this is an additional licenses payment
      if (paymentIntent.metadata && paymentIntent.metadata.type === 'additional_licenses') {
        // For additional licenses, check if already processed to avoid double processing
        const subscriptionId = paymentIntent.metadata.subscriptionId;
        const additionalLicenseCount = parseInt(paymentIntent.metadata.additionalLicenseCount);

        if (subscriptionId && additionalLicenseCount) {
          this.logger.log(
            `Webhook received for additional licenses payment: ${paymentIntent.id} for ${additionalLicenseCount} licenses`,
          );

          // Check if this payment was already processed via Stripe metadata
          if (paymentIntent.metadata && paymentIntent.metadata.processed === 'true') {
            this.logger.log(
              `Payment ${paymentIntent.id} already marked as processed in metadata. Skipping webhook processing.`,
            );
          } else {
            // Check if licenses were already created (secondary check)
            const subscription = await this.subscriptionRepository.findOne({
              where: { id: subscriptionId },
            });

            if (subscription) {
              const currentLicenseCount = await this.licenseService.getLicenseCountForSubscription(
                subscription.id,
              );
              const expectedTotalCount = subscription.licenseCount + additionalLicenseCount;
              console.log(
                '🚀 ~ PaymentService ~ handlePaymentIntentSucceeded ~ additionalLicenseCount:',
                additionalLicenseCount,
              );

              if (currentLicenseCount >= expectedTotalCount) {
                this.logger.log(
                  `Payment ${paymentIntent.id} already processed based on license count. Current: ${currentLicenseCount}, Expected: ${expectedTotalCount}. Skipping webhook processing.`,
                );
              } else {
                // Process additional licenses payment only if not already processed
                this.logger.log(
                  `Processing additional licenses payment from webhook: ${paymentIntent.id}`,
                );
                await this.processSuccessfulPayment(paymentIntent.id);
              }
            }
          }
        }

        // Mark linked invoice as paid
        await this.markLinkedInvoiceAsPaid(paymentIntent.id);
        return;
      }

      // For regular subscription payments only
      await this.processSuccessfulPayment(paymentIntent.id);

      // If this PaymentIntent has a linked invoice, mark it as paid
      await this.markLinkedInvoiceAsPaid(paymentIntent.id);
    } catch (error) {
      // If subscription is already active, log and continue
      if (
        error.message?.includes('already active') ||
        error.message?.includes('already processed')
      ) {
        this.logger.log(`Payment intent ${paymentIntent.id} already processed. Skipping.`);
        return;
      }
      throw error;
    }
  }

  /**
   * Mark linked invoice as paid when PaymentIntent succeeds
   */
  private async markLinkedInvoiceAsPaid(paymentIntentId: string): Promise<void> {
    try {
      // Find invoices that are linked to this PaymentIntent
      const invoices = await this.stripe.invoices.list({
        limit: 10,
      });

      const linkedInvoice = invoices.data.find(
        (invoice) => invoice.metadata?.paymentIntentId === paymentIntentId,
      );

      if (linkedInvoice && linkedInvoice.status === 'open') {
        // Mark the invoice as paid
        await this.stripe.invoices.pay(linkedInvoice.id, {
          paid_out_of_band: true, // Mark as paid outside of Stripe
        });

        this.logger.log(
          `Marked invoice ${linkedInvoice.id} as paid for PaymentIntent ${paymentIntentId}`,
        );
      }
    } catch (error) {
      this.logger.warn(
        `Failed to mark linked invoice as paid for PaymentIntent ${paymentIntentId}: ${error.message}`,
      );
      // Don't throw error as this is not critical for the main flow
    }
  }

  /**
   * Process credit topup from PaymentIntent success
   */
  private async processCreditTopupFromPaymentIntent(
    paymentIntent: Stripe.PaymentIntent,
  ): Promise<void> {
    try {
      const { organizationId, credits } = paymentIntent.metadata;
      const creditAmount = parseInt(credits);

      if (!organizationId || !creditAmount) {
        throw new BadRequestException('Invalid credit topup metadata in PaymentIntent');
      }

      this.logger.log(
        `Processing credit topup from PaymentIntent: ${creditAmount} credits for organization ${organizationId}`,
      );

      // Add credits to organization quota
      const quotaService = this.moduleRef.get('QuotaService', { strict: false });
      if (!quotaService) {
        throw new BadRequestException('Quota service not available');
      }

      await quotaService.allocateQuotaToOrganization(
        organizationId,
        'credits',
        creditAmount,
        'credit',
        null,
        QuotaSource.TOPUP,
        paymentIntent.id,
      );

      this.logger.log(
        `Successfully added ${creditAmount} credits to organization ${organizationId} from PaymentIntent ${paymentIntent.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing credit topup from PaymentIntent ${paymentIntent.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process credit topup from invoice payment
   */
  private async processCreditTopupFromInvoice(invoice: Stripe.Invoice): Promise<void> {
    try {
      const { organizationId, credits } = invoice.metadata;
      const creditAmount = parseInt(credits);

      if (!organizationId || !creditAmount) {
        throw new BadRequestException('Invalid credit topup metadata');
      }

      this.logger.log(
        `Processing credit topup from invoice: ${creditAmount} credits for organization ${organizationId}`,
      );

      // Add credits to organization quota
      const quotaService = this.moduleRef.get('QuotaService', { strict: false });
      if (!quotaService) {
        throw new BadRequestException('Quota service not available');
      }

      await quotaService.allocateQuotaToOrganization(
        organizationId,
        'credits',
        creditAmount,
        'credit',
        null,
        QuotaSource.TOPUP,
        invoice.id,
      );

      this.logger.log(
        `Successfully added ${creditAmount} credits to organization ${organizationId} from invoice ${invoice.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing credit topup from invoice ${invoice.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process subscription invoice payment
   */
  private async processSubscriptionInvoicePayment(invoice: Stripe.Invoice): Promise<void> {
    try {
      const { subscriptionId, organizationId } = invoice.metadata;

      if (!subscriptionId || !organizationId) {
        throw new BadRequestException('Invalid subscription invoice metadata');
      }

      this.logger.log(
        `Processing subscription invoice payment: ${invoice.id} for subscription ${subscriptionId}`,
      );

      // Find the subscription
      const subscription = await this.subscriptionRepository.findOne({
        where: { id: subscriptionId, organizationId },
      });

      if (!subscription) {
        throw new NotFoundException(`Subscription ${subscriptionId} not found`);
      }

      // Activate subscription if it's pending
      if (subscription.status === SubscriptionStatus.PENDING) {
        await this.dataSource.transaction(async (entityManager) => {
          // Update subscription status
          subscription.status = SubscriptionStatus.ACTIVE;
          subscription.failedPaymentCount = 0;

          // Set next billing date
          if (subscription.renewInterval && subscription.autoRenew) {
            subscription.nextBillingDate = this.calculateNextBillingDate(
              new Date(),
              subscription.renewInterval,
            );
          }

          await entityManager.save(SubscriptionEntity, subscription);

          // Assign licenses to existing users
          await this.assignLicensesToExistingUsers(organizationId, entityManager);

          this.logger.log(
            `Activated subscription ${subscriptionId} after invoice payment ${invoice.id}`,
          );
        });
      } else {
        // For existing active subscriptions, just reset failed payment count and update billing date
        subscription.failedPaymentCount = 0;

        if (subscription.renewInterval && subscription.autoRenew) {
          subscription.nextBillingDate = this.calculateNextBillingDate(
            new Date(),
            subscription.renewInterval,
          );
        }

        await this.subscriptionRepository.save(subscription);
        this.logger.log(
          `Updated subscription ${subscriptionId} after invoice payment ${invoice.id}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error processing subscription invoice payment ${invoice.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle failed payment intent
   */
  private async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    const subscriptionId = paymentIntent.metadata.subscriptionId;
    if (!subscriptionId) {
      this.logger.warn(
        `Payment intent ${paymentIntent.id} failed but no subscription ID in metadata`,
      );
      return;
    }

    try {
      await this.dataSource.transaction(async (entityManager) => {
        // Find the subscription
        const subscription = await entityManager.findOne(SubscriptionEntity, {
          where: { id: subscriptionId },
        });

        if (!subscription) {
          this.logger.warn(
            `Subscription ${subscriptionId} not found for failed payment ${paymentIntent.id}`,
          );
          return;
        }

        // Increment failed payment count
        subscription.failedPaymentCount = (subscription.failedPaymentCount || 0) + 1;

        // If too many failed payments, cancel subscription with grace period
        if (subscription.failedPaymentCount >= 3) {
          subscription.status = SubscriptionStatus.CANCELED;
          subscription.gracePeriodEnd = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days grace period
          subscription.cancelledAt = new Date();
          subscription.cancellationReason = `Automatic cancellation due to ${subscription.failedPaymentCount} failed payments`;
          this.logger.warn(
            `Subscription ${subscriptionId} canceled due to ${subscription.failedPaymentCount} failed payments`,
          );
        }

        await entityManager.save(SubscriptionEntity, subscription);

        // Log the failed payment
        this.logger.error(
          `Payment failed for subscription ${subscriptionId}: ${paymentIntent.id}. Failed count: ${subscription.failedPaymentCount}`,
        );
      });
    } catch (error) {
      this.logger.error(
        `Error handling failed payment intent ${paymentIntent.id}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Handle successful invoice payment
   */
  private async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    this.logger.log(`Invoice payment succeeded: ${invoice.id}`);

    try {
      // Check if this is a credit topup invoice
      if (invoice.metadata && invoice.metadata.type === 'credit_topup') {
        await this.processCreditTopupFromInvoice(invoice);
        return;
      }

      // Check if this is a subscription invoice
      if (invoice.metadata && invoice.metadata.type === 'subscription') {
        await this.processSubscriptionInvoicePayment(invoice);
        return;
      }

      // Handle legacy subscription invoice payments (without metadata type)
      if (invoice.customer) {
        const subscription = await this.subscriptionRepository.findOne({
          where: { stripeCustomerId: invoice.customer as string },
        });

        if (subscription) {
          // Reset failed payment count on successful payment
          subscription.failedPaymentCount = 0;

          // Update next billing date if this was a recurring payment
          if (subscription.renewInterval && subscription.autoRenew) {
            subscription.nextBillingDate = this.calculateNextBillingDate(
              new Date(),
              subscription.renewInterval,
            );
          }

          await this.subscriptionRepository.save(subscription);
          this.logger.log(
            `Updated subscription ${subscription.id} after successful invoice payment`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error handling successful invoice payment: ${error.message}`, error.stack);
    }
  }

  /**
   * Handle failed invoice payment
   */
  private async handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    this.logger.error(`Invoice payment failed: ${invoice.id}`);

    try {
      // Check if this is a credit topup invoice
      if (invoice.metadata && invoice.metadata.type === 'credit_topup') {
        this.logger.error(
          `Credit topup invoice payment failed: ${invoice.id} for organization ${invoice.metadata.organizationId}`,
        );
        // For credit topup failures, we don't need to do anything special
        // The credits won't be added until payment succeeds
        return;
      }

      // Handle subscription invoice payment failures
      if (invoice.customer) {
        const subscription = await this.subscriptionRepository.findOne({
          where: { stripeCustomerId: invoice.customer as string },
        });

        if (subscription) {
          // Increment failed payment count
          subscription.failedPaymentCount = (subscription.failedPaymentCount || 0) + 1;

          // If too many failed payments, cancel subscription
          if (subscription.failedPaymentCount >= 3) {
            subscription.status = SubscriptionStatus.CANCELED;
            subscription.gracePeriodEnd = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
            subscription.cancelledAt = new Date();
            subscription.cancellationReason = `Automatic cancellation due to ${subscription.failedPaymentCount} failed invoice payments`;
          }

          await this.subscriptionRepository.save(subscription);
          this.logger.warn(
            `Updated subscription ${subscription.id} after failed invoice payment. Failed count: ${subscription.failedPaymentCount}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error handling failed invoice payment: ${error.message}`, error.stack);
    }
  }

  /**
   * Calculate next billing date based on renewal interval
   */
  private calculateNextBillingDate(currentDate: Date, renewInterval: RenewInterval): Date {
    const nextDate = new Date(currentDate);

    switch (renewInterval) {
      case RenewInterval.MONTHLY:
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case RenewInterval.QUARTERLY:
        nextDate.setMonth(nextDate.getMonth() + 3);
        break;
      case RenewInterval.BIANNUALLY:
        nextDate.setMonth(nextDate.getMonth() + 6);
        break;
      case RenewInterval.ANNUALLY:
        nextDate.setFullYear(nextDate.getFullYear() + 1);
        break;
      default:
        nextDate.setMonth(nextDate.getMonth() + 1);
    }

    return nextDate;
  }

  /**
   * Verify webhook signature and construct event
   */
  async verifyWebhookSignature(rawBody: Buffer, signature: string): Promise<Stripe.Event> {
    try {
      return this.stripe.webhooks.constructEvent(rawBody, signature, STRIPE_CONFIG.webhookSecret);
    } catch (error) {
      this.logger.error(`Webhook signature verification failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get recent webhook events for an organization
   */
  async getRecentWebhookEvents(organizationId: string): Promise<any[]> {
    try {
      // Get subscription to find Stripe customer ID
      const subscription = await this.subscriptionRepository.findOne({
        where: { organizationId, status: SubscriptionStatus.ACTIVE },
      });

      if (!subscription?.stripeCustomerId) {
        return [];
      }

      // Get recent events from Stripe
      const events = await this.stripe.events.list({
        limit: 20,
        type: '*', // Get all event types
        created: {
          gte: Math.floor((Date.now() - 7 * 24 * 60 * 60 * 1000) / 1000), // Last 7 days
        },
      });

      // Filter events related to this customer
      const customerEvents = events.data.filter((event) => {
        const data = event.data.object as any;
        return (
          data.customer === subscription.stripeCustomerId ||
          (data.metadata && data.metadata.subscriptionId === subscription.id)
        );
      });

      // Format events for response
      return customerEvents.map((event) => ({
        id: event.id,
        type: event.type,
        created: new Date(event.created * 1000),
        apiVersion: event.api_version,
        livemode: event.livemode,
        pendingWebhooks: event.pending_webhooks,
        data: {
          object: event.data.object,
        },
      }));
    } catch (error) {
      this.logger.error(`Error fetching webhook events: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create setup intent for saving payment method
   */
  async createSetupIntent(organizationId: string): Promise<Stripe.SetupIntent> {
    try {
      return await this.stripe.setupIntents.create({
        usage: 'off_session',
        metadata: {
          organizationId,
        },
      });
    } catch (error) {
      this.logger.error(`Error creating setup intent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cancel existing PaymentIntent if it exists and is cancelable
   */
  async cancelPaymentIntentIfExists(paymentIntentId: string): Promise<boolean> {
    try {
      if (!paymentIntentId) {
        return false;
      }

      // Get payment intent from Stripe
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      // Check if payment intent can be canceled
      if (
        paymentIntent.status === 'requires_payment_method' ||
        paymentIntent.status === 'requires_confirmation' ||
        paymentIntent.status === 'requires_action'
      ) {
        await this.stripe.paymentIntents.cancel(paymentIntentId);
        this.logger.log(`Canceled PaymentIntent ${paymentIntentId} due to subscription changes`);
        return true;
      } else {
        this.logger.log(
          `PaymentIntent ${paymentIntentId} cannot be canceled (status: ${paymentIntent.status})`,
        );
        return false;
      }
    } catch (error) {
      this.logger.error(`Error canceling PaymentIntent ${paymentIntentId}: ${error.message}`);
      // Don't throw error as this is cleanup operation
      return false;
    }
  }

  /**
   * Get payment methods for organization (raw Stripe data)
   */
  async getPaymentMethods(organizationId: string): Promise<Stripe.PaymentMethod[]> {
    try {
      // Get organization to find Stripe customer ID
      const organization = await this.organizationRepository.findOne({
        where: { id: organizationId },
      });

      if (!organization || !organization.stripeCustomerId) {
        return [];
      }

      // Get payment methods from Stripe
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: organization.stripeCustomerId,
        type: 'card',
      });

      return paymentMethods.data;
    } catch (error) {
      this.logger.error(`Error getting payment methods: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get payment methods with default payment method info
   */
  async getPaymentMethodsWithDefault(organizationId: string): Promise<any[]> {
    try {
      // Get organization to find Stripe customer ID
      const organization = await this.organizationRepository.findOne({
        where: { id: organizationId },
      });

      if (!organization || !organization.stripeCustomerId) {
        return [];
      }

      // Get customer to find default payment method
      const customer = await this.stripe.customers.retrieve(organization.stripeCustomerId);
      const defaultPaymentMethodId =
        customer && !customer.deleted
          ? (customer as any).invoice_settings?.default_payment_method
          : null;

      // Get payment methods from Stripe
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: organization.stripeCustomerId,
        type: 'card',
      });

      // Map payment methods with isDefault property
      return paymentMethods.data
        .filter((pm) => pm.card)
        .map((pm) => ({
          id: pm.id,
          type: pm.type,
          last4: pm.card!.last4,
          brand: pm.card!.brand,
          expiryMonth: pm.card!.exp_month,
          expiryYear: pm.card!.exp_year,
          isDefault: pm.id === defaultPaymentMethodId,
        }));
    } catch (error) {
      this.logger.error(
        `Error getting payment methods with default: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Detach payment method
   */
  async detachPaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod> {
    try {
      return await this.stripe.paymentMethods.detach(paymentMethodId);
    } catch (error) {
      this.logger.error(`Error detaching payment method: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get billing information for dashboard
   */
  async getBillingInfoForDashboard(
    organizationId: string,
    subscription: SubscriptionEntity,
  ): Promise<any> {
    let defaultPaymentMethod = null;
    let hasSavedPaymentMethods = false;
    let billingAddress = null;
    let paymentMethods = [];

    if (subscription.stripeCustomerId) {
      try {
        // Get customer from Stripe
        const customer = await this.stripe.customers.retrieve(subscription.stripeCustomerId);

        if (customer && !customer.deleted) {
          // Get default payment method
          if ((customer as any).invoice_settings?.default_payment_method) {
            const paymentMethod = await this.stripe.paymentMethods.retrieve(
              (customer as any).invoice_settings.default_payment_method as string,
            );

            if (paymentMethod.card) {
              defaultPaymentMethod = {
                id: paymentMethod.id,
                type: paymentMethod.type,
                last4: paymentMethod.card.last4,
                brand: paymentMethod.card.brand,
                expiryMonth: paymentMethod.card.exp_month,
                expiryYear: paymentMethod.card.exp_year,
              };
            }
          }

          // Get all payment methods
          const stripePaymentMethods = await this.stripe.paymentMethods.list({
            customer: subscription.stripeCustomerId,
            type: 'card',
          });

          hasSavedPaymentMethods = stripePaymentMethods.data.length > 0;

          paymentMethods = stripePaymentMethods.data
            .filter((pm) => pm.card)
            .map((pm) => ({
              id: pm.id,
              type: pm.type,
              last4: pm.card!.last4,
              brand: pm.card!.brand,
              expiryMonth: pm.card!.exp_month,
              expiryYear: pm.card!.exp_year,
              isDefault: pm.id === (customer as any).invoice_settings?.default_payment_method,
            }));

          // Get billing address
          if ((customer as any).address) {
            billingAddress = {
              line1: (customer as any).address.line1 || '',
              line2: (customer as any).address.line2 || undefined,
              city: (customer as any).address.city || '',
              state: (customer as any).address.state || '',
              postalCode: (customer as any).address.postal_code || '',
              country: (customer as any).address.country || '',
            };
          }
        }
      } catch (error) {
        this.logger.warn(`Failed to fetch Stripe customer data: ${error.message}`);
      }
    }

    // Calculate next billing amount
    const nextBillingAmount = await this.calculateFullSubscriptionAmount(
      subscription.licenseCount,
      subscription.planId,
      subscription.renewInterval,
    );

    // Get upcoming invoice preview
    let upcomingInvoice = null;
    if (subscription.nextBillingDate) {
      const plan = PLANS[subscription.planId];
      upcomingInvoice = {
        amount: nextBillingAmount,
        dueDate: subscription.nextBillingDate,
        description: `${plan?.name || subscription.planId} plan - ${subscription.licenseCount} license${subscription.licenseCount > 1 ? 's' : ''}`,
      };
    }

    return {
      stripeCustomerId: subscription.stripeCustomerId,
      hasSavedPaymentMethods,
      defaultPaymentMethod,
      nextBillingDate: subscription.nextBillingDate,
      nextBillingAmount,
      billingCycle: subscription.renewInterval,
      autoRenew: subscription.autoRenew,
      billingAddress,
      paymentMethods,
      upcomingInvoice,
    };
  }

  /**
   * Get payment history for dashboard
   */
  async getPaymentHistoryForDashboard(
    organizationId: string,
    query: any,
  ): Promise<{ payments: any[]; total: number; page: number; limit: number; totalPages: number }> {
    // Get subscription to get Stripe customer ID
    const subscription = await this.subscriptionRepository.findOne({
      where: { organizationId, status: SubscriptionStatus.ACTIVE },
    });

    if (!subscription?.stripeCustomerId) {
      return {
        payments: [],
        total: 0,
        page: query.page,
        limit: query.limit,
        totalPages: 0,
      };
    }

    try {
      // Get payment intents from Stripe
      const paymentIntents = await this.stripe.paymentIntents.list({
        customer: subscription.stripeCustomerId,
        limit: query.limit,
        // Note: Simplified pagination - in production, you'd implement proper cursor-based pagination
      });

      const payments = [];

      for (const pi of paymentIntents.data) {
        // Filter by status if provided
        if (query.status && pi.status !== query.status) {
          continue;
        }

        // Filter by date range if provided
        const createdDate = new Date(pi.created * 1000);
        if (query.startDate && createdDate < query.startDate) {
          continue;
        }
        if (query.endDate && createdDate > query.endDate) {
          continue;
        }

        // Get payment method details
        let paymentMethod = { type: 'unknown' };
        if (pi.payment_method) {
          try {
            const pm = await this.stripe.paymentMethods.retrieve(pi.payment_method as string);
            paymentMethod = {
              type: pm.type,
              ...(pm.card?.last4 && { last4: pm.card.last4 }),
              ...(pm.card?.brand && { brand: pm.card.brand }),
            };
          } catch (error) {
            this.logger.warn(
              `Failed to fetch payment method ${pi.payment_method}: ${error.message}`,
            );
          }
        }

        payments.push({
          id: pi.id,
          paymentIntentId: pi.id,
          amount: pi.amount / 100, // Convert from cents
          currency: pi.currency.toUpperCase(),
          status: pi.status,
          createdAt: createdDate,
          description: pi.description || 'Subscription payment',
          paymentMethod,
          invoiceUrl: null, // Could be enhanced to get actual invoice URLs
          receiptUrl: pi.receipt_email ? `https://dashboard.stripe.com/receipts/${pi.id}` : null,
        });
      }

      const totalPages = Math.ceil(payments.length / query.limit);

      return {
        payments,
        total: payments.length, // Simplified - Stripe doesn't provide total count easily
        page: query.page,
        limit: query.limit,
        totalPages,
      };
    } catch (error) {
      this.logger.warn(`Failed to fetch payment history: ${error.message}`);
      return {
        payments: [],
        total: 0,
        page: query.page,
        limit: query.limit,
        totalPages: 0,
      };
    }
  }

  /**
   * Get payment intent status from Stripe
   */
  async getPaymentIntentStatus(paymentIntentId: string): Promise<Stripe.PaymentIntent | null> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
      return paymentIntent;
    } catch (error) {
      this.logger.warn(`Could not fetch payment intent ${paymentIntentId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Confirm PaymentIntent with payment method
   */
  async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethodId?: string,
  ): Promise<Stripe.PaymentIntent> {
    try {
      const confirmParams: Stripe.PaymentIntentConfirmParams = {};

      if (paymentMethodId) {
        confirmParams.payment_method = paymentMethodId;
      }

      const paymentIntent = await this.stripe.paymentIntents.confirm(
        paymentIntentId,
        confirmParams,
      );

      this.logger.log(
        `PaymentIntent ${paymentIntentId} confirmed with status: ${paymentIntent.status}`,
      );

      return paymentIntent;
    } catch (error) {
      this.logger.error(
        `Error confirming PaymentIntent ${paymentIntentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get invoice details from Stripe
   */
  async getInvoiceDetails(invoiceId: string): Promise<any> {
    try {
      const invoice = await this.stripe.invoices.retrieve(invoiceId);
      return {
        invoiceNumber: invoice.number,
        invoicePdf: invoice.invoice_pdf,
        hostedInvoiceUrl: invoice.hosted_invoice_url,
        paymentStatus: invoice.status === 'paid' ? 'paid' : 'unpaid',
        amountPaid: invoice.amount_paid / 100, // Convert from cents
        currency: invoice.currency,
      };
    } catch (error) {
      this.logger.warn(`Could not fetch invoice details for ${invoiceId}: ${error.message}`);
      return null;
    }
  }
}

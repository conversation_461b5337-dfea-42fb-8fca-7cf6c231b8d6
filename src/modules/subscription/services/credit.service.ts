import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { QuotaService } from './quota.service';
import { QuotaSource } from '../entities/organization-quota.entity';
import { RoleEnum } from '../../user/entities/role.entity';

// Feature IDs
const FEATURE_CREDITS = 'credits';

@Injectable()
export class CreditService {
  private readonly logger = new Logger(CreditService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly quotaService: QuotaService,
  ) {}

  /**
   * Check if a user has enough credits
   * @param userId User ID
   * @param amount Amount of credits to check
   * @param featureId Feature ID (default: 'credits')
   * @returns Boolean indicating if user has enough credits
   */
  async hasEnoughCredits(
    userId: string,
    amount: number = 1,
    featureId: string = FEATURE_CREDITS,
  ): Promise<boolean> {
    try {
      // Check using the quota system
      const quotaCheck = await this.quotaService.checkQuota(userId, featureId);

      if (quotaCheck.unlimited) {
        return true;
      }

      if (quotaCheck.hasQuota && quotaCheck.remaining >= amount) {
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error checking credits: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Get user's credit balance
   * @param userId User ID
   * @param featureId Feature ID (default: 'credits')
   * @returns Object with credit information
   */
  async getCreditBalance(
    userId: string,
    featureId: string = FEATURE_CREDITS,
  ): Promise<{
    balance: number;
    unlimited: boolean;
    used?: number;
    originalAmount?: number;
  }> {
    try {
      // Get balance from the quota system
      const quotaCheck = await this.quotaService.checkQuota(userId, featureId);

      if (quotaCheck.unlimited) {
        return { balance: -1, unlimited: true };
      }

      if (quotaCheck.hasQuota) {
        return {
          balance: quotaCheck.remaining,
          unlimited: false,
          used: 0,
          originalAmount: quotaCheck.remaining,
        };
      }

      return { balance: 0, unlimited: false };
    } catch (error) {
      this.logger.error(`Error getting credit balance: ${error.message}`, error.stack);
      return { balance: 0, unlimited: false };
    }
  }

  /**
   * Use credits for a specific feature
   * @param userId User ID
   * @param amount Amount of credits to use
   * @param purpose Purpose of credit usage
   * @param featureId Feature ID (default: 'credits')
   * @returns Usage information if credits were successfully used, or null if failed
   */
  async useCredits(
    userId: string,
    amount: number = 1,
    purpose: string = null,
    featureId: string = FEATURE_CREDITS,
  ): Promise<any> {
    try {
      // Use credits in the quota system
      const result = await this.quotaService.useQuota(userId, featureId, amount, purpose);
      return result;
    } catch (error) {
      this.logger.error(`Error using credits: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Restore credits that were previously used
   * @param usageInfo Usage information from the original useCredits call, or basic parameters
   * @param purpose Purpose of restoration
   * @returns Boolean indicating if credits were successfully restored
   */
  async restoreCredits(
    usageInfo: { userId: string; amount: number; featureId?: string } | any,
    purpose: string = 'Restore after failed operation',
  ): Promise<boolean> {
    try {
      // If usageInfo is from useQuota, pass it directly
      if (usageInfo && 'usedSource' in usageInfo && 'sourceQuotaId' in usageInfo) {
        return await this.quotaService.restoreQuota(usageInfo, purpose);
      }

      // Otherwise, extract basic information
      const { userId, amount, featureId = FEATURE_CREDITS } = usageInfo;

      if (!userId || !amount) {
        this.logger.error(
          `Invalid usage info for credit restoration: ${JSON.stringify(usageInfo)}`,
        );
        return false;
      }

      // Restore credits in the quota system
      return await this.quotaService.restoreQuota({ userId, featureId, amount }, purpose);
    } catch (error) {
      this.logger.error(`Error restoring credits: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Add credits to a user
   * @param userId User ID
   * @param amount Amount of credits to add
   * @param featureId Feature ID (default: 'credits')
   * @returns Boolean indicating if credits were successfully added
   */
  async addCredits(
    userId: string,
    amount: number,
    featureId: string = FEATURE_CREDITS,
  ): Promise<boolean> {
    try {
      // Get user information to get organization ID and role
      const user = await this.dataSource.getRepository('users').findOne({
        where: { id: userId },
        relations: ['role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      const isAdmin =
        user.role &&
        (user.role.keyCode === RoleEnum.ADMIN || user.role.keyCode === RoleEnum.SUPER_ADMIN);

      // Get current quota
      const currentQuota = await this.quotaService.checkQuota(userId, featureId);
      const newAmount = currentQuota.unlimited ? amount : currentQuota.remaining + amount;

      // If user is Admin, add credits to the organization instead of the user
      if (isAdmin) {
        // Add credits to the organization
        await this.quotaService.allocateQuotaToOrganization(
          user.organizationId,
          featureId,
          newAmount,
          'credit',
          null,
          QuotaSource.MANUAL,
          null,
        );
      } else {
        // Add credits to the user
        await this.quotaService.allocateQuotaToUser(
          userId,
          user.organizationId,
          featureId,
          newAmount,
          'credit',
          null,
          QuotaSource.MANUAL,
          null,
        );
      }

      return true;
    } catch (error) {
      this.logger.error(`Error adding credits: ${error.message}`, error.stack);
      return false;
    }
  }
}

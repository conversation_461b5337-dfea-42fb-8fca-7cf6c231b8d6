import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { OrganizationQuotaRepository } from '../repositories/organization-quota.repository';
import { UserQuotaRepository } from '../repositories/user-quota.repository';
import { QuotaUsageLogRepository } from '../repositories/quota-usage-log.repository';
import {
  OrganizationQuotaEntity,
  QuotaResetInterval,
  QuotaSource,
} from '../entities/organization-quota.entity';
import { UserQuotaEntity } from '../entities/user-quota.entity';
import {
  QuotaUsageLogEntity,
  QuotaUsageSource,
  PerformedByType,
  QuotaLogType,
} from '../entities/quota-usage-log.entity';
import { DataSource, LessThan } from 'typeorm';
import { UserEntity } from '../../user/entities/user.entity';
import { FEATURES, FeatureUnitEnum } from '../../../configs/features.config';
import { PLAN_FEATURES } from '../../../configs/plan-features.config';
import { SUBSCRIPTION_SYSTEM_CONFIG } from '../../../configs/subscription-system.config';

import { SubscriptionEntity, SubscriptionStatus } from '../entities/subscription.entity';
import { RoleEnum } from '../../user/entities/role.entity';
import { QuotaUsageInfo } from '../interfaces/quota-usage-info.interface';

// Feature IDs
const FEATURE_CREDITS = 'credits';

@Injectable()
export class QuotaService {
  private readonly logger = new Logger(QuotaService.name);

  constructor(
    private readonly orgQuotaRepository: OrganizationQuotaRepository,
    private readonly userQuotaRepository: UserQuotaRepository,
    private readonly usageLogRepository: QuotaUsageLogRepository,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Execute transaction with custom timeout for quota operations
   */
  private async executeTransactionWithTimeout<T>(
    operation: (entityManager: any) => Promise<T>,
    timeoutSeconds: number = SUBSCRIPTION_SYSTEM_CONFIG.QUOTA_ALLOCATION_TIMEOUT_SECONDS,
  ): Promise<T> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Set statement timeout for this transaction
      await queryRunner.query(`SET SESSION idle_in_transaction_session_timeout = '3min'`);
      await queryRunner.query(`SET SESSION statement_timeout = '${timeoutSeconds}s'`);

      const result = await operation(queryRunner.manager);
      await queryRunner.commitTransaction();
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async allocateQuotaToOrganization(
    organizationId: string,
    featureId: string,
    amount: number,
    unit: string = FeatureUnitEnum.COUNT,
    resetInterval: QuotaResetInterval = QuotaResetInterval.NEVER,
    source: QuotaSource = QuotaSource.SUBSCRIPTION,
    sourceId: string = null,
    entityManager?: any,
  ): Promise<OrganizationQuotaEntity> {
    try {
      const repository = entityManager
        ? entityManager.getRepository(OrganizationQuotaEntity)
        : this.orgQuotaRepository;

      let quota = await repository.findOne({
        where: {
          organizationId,
          featureId,
          source,
          sourceId,
        },
      });

      if (quota) {
        quota.remaining = amount;
        quota.originalAmount = amount;
        quota.resetInterval = resetInterval;
        quota.unit = unit;
        quota.lastResetAt = new Date();
      } else {
        quota = this.orgQuotaRepository.create({
          organizationId,
          featureId,
          remaining: amount,
          originalAmount: amount,
          unit,
          lastResetAt: new Date(),
          resetInterval,
          source,
          sourceId,
          unlimited: false,
        });
      }

      const savedQuota = await repository.save(quota);

      // Log allocation
      try {
        await this.logQuotaUsage(
          null, // Organization-level allocation
          organizationId,
          featureId,
          unit,
          amount,
          `Allocated ${amount} ${unit} to organization`,
          QuotaUsageSource.ORGANIZATION_QUOTA,
          savedQuota.id,
          'system',
          PerformedByType.SYSTEM,
          entityManager,
          false,
          null,
          QuotaLogType.ALLOCATION,
        );
      } catch (logError) {
        this.logger.warn(`Failed to log quota allocation: ${logError.message}`);
      }

      return savedQuota;
    } catch (error) {
      this.logger.error(`Error allocating quota to organization: ${error.message}`, error.stack);
      throw error;
    }
  }

  async allocateUnlimitedQuotaToOrganization(
    organizationId: string,
    featureId: string,
    unit: string = FeatureUnitEnum.COUNT,
    source: QuotaSource = QuotaSource.SUBSCRIPTION,
    sourceId: string = null,
    entityManager?: any,
  ): Promise<OrganizationQuotaEntity> {
    try {
      const repository = entityManager
        ? entityManager.getRepository(OrganizationQuotaEntity)
        : this.orgQuotaRepository;

      let quota = await repository.findOne({
        where: {
          organizationId,
          featureId,
          source,
          sourceId,
        },
      });

      if (quota) {
        quota.unlimited = true;
        quota.unit = unit;
      } else {
        quota = this.orgQuotaRepository.create({
          organizationId,
          featureId,
          remaining: 0,
          originalAmount: 0,
          unit,
          lastResetAt: new Date(),
          resetInterval: QuotaResetInterval.NEVER,
          source,
          sourceId,
          unlimited: true,
        });
      }

      return await repository.save(quota);
    } catch (error) {
      this.logger.error(
        `Error allocating unlimited quota to organization: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async allocateQuotaToUser(
    userId: string,
    organizationId: string,
    featureId: string,
    amount: number,
    unit: string = FeatureUnitEnum.COUNT,
    resetInterval: QuotaResetInterval = QuotaResetInterval.NEVER,
    source: QuotaSource = QuotaSource.SUBSCRIPTION,
    sourceId: string = null,
    entityManager?: any,
  ): Promise<UserQuotaEntity> {
    try {
      const userRepository = entityManager
        ? entityManager.getRepository(UserEntity)
        : this.dataSource.getRepository(UserEntity);
      const user = await userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      const repository = entityManager
        ? entityManager.getRepository(UserQuotaEntity)
        : this.userQuotaRepository;

      let quota = await repository.findOne({
        where: {
          userId,
          organizationId,
          featureId,
          source,
          sourceId,
        },
      });

      if (quota) {
        quota.remaining = amount;
        quota.originalAmount = amount;
        quota.resetInterval = resetInterval;
        quota.unit = unit;
        quota.lastResetAt = new Date();
      } else {
        quota = this.userQuotaRepository.create({
          userId,
          organizationId,
          featureId,
          remaining: amount,
          originalAmount: amount,
          unit,
          lastResetAt: new Date(),
          resetInterval,
          source,
          sourceId,
          unlimited: false,
        });
      }

      const savedQuota = await repository.save(quota);

      // Log allocation
      try {
        await this.logQuotaUsage(
          userId,
          organizationId,
          featureId,
          unit,
          amount,
          `Allocated ${amount} ${unit} to user`,
          QuotaUsageSource.USER_QUOTA,
          savedQuota.id,
          'system',
          PerformedByType.SYSTEM,
          entityManager,
          false,
          null,
          QuotaLogType.ALLOCATION,
        );
      } catch (logError) {
        this.logger.warn(`Failed to log user quota allocation: ${logError.message}`);
      }

      return savedQuota;
    } catch (error) {
      this.logger.error(`Error allocating quota to user: ${error.message}`, error.stack);
      throw error;
    }
  }

  async allocateUnlimitedQuotaToUser(
    userId: string,
    organizationId: string,
    featureId: string,
    unit: string = FeatureUnitEnum.COUNT,
    source: QuotaSource = QuotaSource.SUBSCRIPTION,
    sourceId: string = null,
    entityManager?: any,
  ): Promise<UserQuotaEntity> {
    try {
      const userRepository = entityManager
        ? entityManager.getRepository(UserEntity)
        : this.dataSource.getRepository(UserEntity);
      const user = await userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      const repository = entityManager
        ? entityManager.getRepository(UserQuotaEntity)
        : this.userQuotaRepository;

      let quota = await repository.findOne({
        where: {
          userId,
          organizationId,
          featureId,
          source,
          sourceId,
        },
      });

      if (quota) {
        quota.unlimited = true;
        quota.unit = unit;
      } else {
        quota = this.userQuotaRepository.create({
          userId,
          organizationId,
          featureId,
          remaining: 0,
          originalAmount: 0,
          unit,
          lastResetAt: new Date(),
          resetInterval: QuotaResetInterval.NEVER,
          source,
          sourceId,
          unlimited: true,
        });
      }

      return await repository.save(quota);
    } catch (error) {
      this.logger.error(`Error allocating unlimited quota to user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Use quota for a specific feature
   * @param userId User ID
   * @param featureId Feature ID
   * @param amount Amount to use
   * @param purpose Purpose of usage
   * @param performedBy ID of the user who performed the action (optional)
   * @param performedByType Type of the performer (optional)
   * @returns Object with usage information or null if quota was not used
   */
  async useQuota(
    userId: string,
    featureId: string,
    amount: number = 1,
    purpose: string = null,
    performedBy?: string,
    performedByType?: PerformedByType,
  ): Promise<QuotaUsageInfo | null> {
    try {
      const user = await this.dataSource.getRepository(UserEntity).findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      const { organizationId } = user;

      // Skip subscription check for SUPER_ADMIN users
      if (user.role && user.role.keyCode !== RoleEnum.SUPER_ADMIN) {
        // Check if the organization has an active subscription
        const subscriptionRepository = this.dataSource.getRepository(SubscriptionEntity);
        const subscription = await subscriptionRepository.findOne({
          where: {
            organizationId,
            status: SubscriptionStatus.ACTIVE,
          },
        });

        if (!subscription) {
          this.logger.warn(
            `Organization ${organizationId} does not have an active subscription. Credit usage denied.`,
          );
          throw new BadRequestException('Cannot use credits until admin completes onboarding');
        }
      }

      const isAdmin =
        user.role &&
        (user.role.keyCode === RoleEnum.ADMIN || user.role.keyCode === RoleEnum.SUPER_ADMIN);

      const feature = FEATURES[featureId];
      if (!feature) {
        throw new NotFoundException(`Feature ${featureId} not found`);
      }

      const allowUserQuota = this.checkIfFeatureAllowsUserQuota(featureId);

      if (isAdmin || !allowUserQuota || (isAdmin && featureId === FEATURE_CREDITS)) {
      } else {
        const userQuota = await this.userQuotaRepository.findOne({
          where: {
            userId,
            featureId,
          },
        });

        if (userQuota) {
          if (userQuota.unlimited) {
            const usageLog = await this.logQuotaUsage(
              userId,
              organizationId,
              featureId,
              userQuota.unit,
              amount,
              purpose,
              QuotaUsageSource.USER_QUOTA,
              userQuota.id,
              performedBy || userId,
              performedByType || PerformedByType.USER,
              null,
            );

            return {
              userId,
              organizationId,
              featureId,
              amount,
              unit: userQuota.unit,
              usedSource: QuotaUsageSource.USER_QUOTA,
              sourceQuotaId: userQuota.id,
              usageLogId: usageLog?.id,
            };
          }

          if (userQuota.remaining >= amount) {
            userQuota.remaining = Math.max(0, userQuota.remaining - amount);
            await this.userQuotaRepository.save(userQuota);

            let usageLog = null;
            try {
              usageLog = await this.logQuotaUsage(
                userId,
                organizationId,
                featureId,
                userQuota.unit,
                amount,
                purpose,
                QuotaUsageSource.USER_QUOTA,
                userQuota.id,
                performedBy || userId,
                performedByType || PerformedByType.USER,
                null,
              );
            } catch (error) {
              this.logger.error(`Error logging quota usage: ${error.message}`, error.stack);
            }

            return {
              userId,
              organizationId,
              featureId,
              amount,
              unit: userQuota.unit,
              usedSource: QuotaUsageSource.USER_QUOTA,
              sourceQuotaId: userQuota.id,
              usageLogId: usageLog?.id,
            };
          }

          if ((allowUserQuota && !isAdmin) || featureId === FEATURE_CREDITS) {
            return null;
          }
        }
      }

      // For credits, use priority-based consumption (plan first, then topup)
      if (featureId === FEATURE_CREDITS) {
        return await this.useCreditsWithPriority(
          userId,
          organizationId,
          amount,
          purpose,
          performedBy,
          performedByType,
        );
      }

      // For other features, use the original logic
      const orgQuota = await this.orgQuotaRepository.findOne({
        where: {
          organizationId,
          featureId,
        },
      });

      if (orgQuota) {
        if (orgQuota.unlimited) {
          let usageLog = null;
          try {
            usageLog = await this.logQuotaUsage(
              userId,
              organizationId,
              featureId,
              orgQuota.unit,
              amount,
              purpose,
              QuotaUsageSource.ORGANIZATION_QUOTA,
              orgQuota.id,
              performedBy || userId,
              performedByType || PerformedByType.USER,
              null,
              false,
              null,
              QuotaLogType.USAGE,
            );
          } catch (error) {
            this.logger.error(`Error logging quota usage: ${error.message}`, error.stack);
          }

          return {
            userId,
            organizationId,
            featureId,
            amount,
            unit: orgQuota.unit,
            usedSource: QuotaUsageSource.ORGANIZATION_QUOTA,
            sourceQuotaId: orgQuota.id,
            usageLogId: usageLog?.id,
          };
        }

        if (orgQuota.remaining >= amount) {
          orgQuota.remaining = Math.max(0, orgQuota.remaining - amount);
          await this.orgQuotaRepository.save(orgQuota);

          let usageLog = null;
          try {
            usageLog = await this.logQuotaUsage(
              userId,
              organizationId,
              featureId,
              orgQuota.unit,
              amount,
              purpose,
              QuotaUsageSource.ORGANIZATION_QUOTA,
              orgQuota.id,
              performedBy || userId,
              performedByType || PerformedByType.USER,
              null,
            );
          } catch (error) {
            this.logger.error(`Error logging quota usage: ${error.message}`, error.stack);
          }

          return {
            userId,
            organizationId,
            featureId,
            amount,
            unit: orgQuota.unit,
            usedSource: QuotaUsageSource.ORGANIZATION_QUOTA,
            sourceQuotaId: orgQuota.id,
            usageLogId: usageLog?.id,
          };
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`Error using quota: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Use credits with priority: plan credits first, then topup credits
   * @param userId User ID
   * @param organizationId Organization ID
   * @param amount Amount of credits to use
   * @param purpose Purpose of usage
   * @param performedBy ID of the user who performed the action
   * @param performedByType Type of the performer
   * @returns QuotaUsageInfo or null if insufficient credits
   */
  private async useCreditsWithPriority(
    userId: string,
    organizationId: string,
    amount: number,
    purpose: string,
    performedBy?: string,
    performedByType?: PerformedByType,
  ): Promise<QuotaUsageInfo | null> {
    try {
      // Get all organization quotas for credits, ordered by priority
      // SUBSCRIPTION (plan) first, then TOPUP
      const orgQuotas = await this.orgQuotaRepository.find({
        where: {
          organizationId,
          featureId: FEATURE_CREDITS,
        },
        order: {
          source: 'ASC', // SUBSCRIPTION comes before TOPUP alphabetically
        },
      });

      if (!orgQuotas.length) {
        return null;
      }

      let remainingAmount = amount;
      const usageLogs: any[] = [];

      // Try to consume from each quota in priority order
      for (const quota of orgQuotas) {
        if (remainingAmount <= 0) break;

        // Skip if quota is unlimited (should be handled separately)
        if (quota.unlimited) {
          let usageLog = null;
          try {
            usageLog = await this.logQuotaUsage(
              userId,
              organizationId,
              FEATURE_CREDITS,
              quota.unit,
              amount, // Log the full amount for unlimited quota
              purpose,
              QuotaUsageSource.ORGANIZATION_QUOTA,
              quota.id,
              performedBy || userId,
              performedByType || PerformedByType.USER,
              null,
              false, // isRestoration
              null, // originalUsageLogId
              QuotaLogType.USAGE, // logType
              quota.source, // quotaSource - NEW!
            );
          } catch (error) {
            this.logger.error(`Error logging quota usage: ${error.message}`, error.stack);
          }

          return {
            userId,
            organizationId,
            featureId: FEATURE_CREDITS,
            amount,
            unit: quota.unit,
            usedSource: QuotaUsageSource.ORGANIZATION_QUOTA,
            sourceQuotaId: quota.id,
            usageLogId: usageLog?.id,
          };
        }

        // Calculate how much we can consume from this quota
        const canConsume = Math.min(remainingAmount, quota.remaining);

        if (canConsume > 0) {
          // Consume from this quota
          quota.remaining = Math.max(0, quota.remaining - canConsume);
          await this.orgQuotaRepository.save(quota);

          // Log the usage
          let usageLog = null;
          try {
            usageLog = await this.logQuotaUsage(
              userId,
              organizationId,
              FEATURE_CREDITS,
              quota.unit,
              canConsume,
              `${purpose} (from ${quota.source})`,
              QuotaUsageSource.ORGANIZATION_QUOTA,
              quota.id,
              performedBy || userId,
              performedByType || PerformedByType.USER,
              null,
              false, // isRestoration
              null, // originalUsageLogId
              QuotaLogType.USAGE, // logType
              quota.source, // quotaSource - NEW!
            );
          } catch (error) {
            this.logger.error(`Error logging quota usage: ${error.message}`, error.stack);
          }

          usageLogs.push({
            quotaId: quota.id,
            source: quota.source,
            amount: canConsume,
            usageLogId: usageLog?.id,
          });

          remainingAmount -= canConsume;

          this.logger.log(
            `Consumed ${canConsume} credits from ${quota.source} quota for user ${userId}. Remaining to consume: ${remainingAmount}`,
          );
        }
      }

      // Check if we consumed the full amount
      if (remainingAmount > 0) {
        // Insufficient credits - rollback all changes
        this.logger.warn(
          `Insufficient credits for user ${userId}. Needed: ${amount}, Available: ${amount - remainingAmount}`,
        );

        // Rollback all quota changes
        for (const log of usageLogs) {
          const quota = orgQuotas.find(q => q.id === log.quotaId);
          if (quota) {
            quota.remaining += log.amount;
            await this.orgQuotaRepository.save(quota);
          }
        }

        return null;
      }

      // Return info about the primary quota used (first one with consumption)
      const primaryLog = usageLogs[0];
      const primaryQuota = orgQuotas.find(q => q.id === primaryLog.quotaId);

      return {
        userId,
        organizationId,
        featureId: FEATURE_CREDITS,
        amount,
        unit: primaryQuota?.unit || 'credit',
        usedSource: QuotaUsageSource.ORGANIZATION_QUOTA,
        sourceQuotaId: primaryLog.quotaId,
        usageLogId: primaryLog.usageLogId,
        // Add metadata about multi-source consumption
        metadata: {
          multiSource: usageLogs.length > 1,
          sources: usageLogs.map(log => ({
            quotaId: log.quotaId,
            source: log.source,
            amount: log.amount,
            usageLogId: log.usageLogId,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`Error using credits with priority: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Restore quota that was previously used
   * @param usageInfo Usage information from the original useQuota call, or basic parameters if not available
   * @param purpose Purpose of restoration
   * @param performedBy ID of the user who performed the action (optional)
   * @param performedByType Type of the performer (optional)
   * @returns Boolean indicating if quota was successfully restored
   */
  async restoreQuota(
    usageInfo: QuotaUsageInfo | { userId: string; featureId: string; amount: number },
    purpose: string = 'Restore after failed operation',
    performedBy?: string,
    performedByType?: PerformedByType,
  ): Promise<boolean> {
    return await this.executeTransactionWithTimeout(async () => {
      try {
        // Extract information from usageInfo
        const userId = 'userId' in usageInfo ? usageInfo.userId : null;
        const featureId = 'featureId' in usageInfo ? usageInfo.featureId : null;
        const amount = 'amount' in usageInfo ? usageInfo.amount : 0;

        if (!userId || !featureId || amount <= 0) {
          this.logger.error(`Invalid usage info for restoration: ${JSON.stringify(usageInfo)}`);
          return false;
        }

        // If we have complete usage info with sourceQuotaId, we can restore directly
        if ('usedSource' in usageInfo && 'sourceQuotaId' in usageInfo) {
          // This is a complete QuotaUsageInfo object
          const { usedSource, sourceQuotaId, organizationId: orgId } = usageInfo;

          if (usedSource === QuotaUsageSource.USER_QUOTA) {
            const userQuota = await this.userQuotaRepository.findOne({
              where: { id: sourceQuotaId },
            });

            if (userQuota && !userQuota.unlimited) {
              userQuota.remaining = userQuota.remaining + amount;
              await this.userQuotaRepository.save(userQuota);

              try {
                // If we have the original usage log ID, use it
                const originalLogId = 'usageLogId' in usageInfo ? usageInfo.usageLogId : null;

                await this.logQuotaUsage(
                  userId,
                  userQuota.organizationId,
                  userQuota.featureId,
                  userQuota.unit,
                  amount,
                  purpose,
                  QuotaUsageSource.USER_QUOTA,
                  userQuota.id,
                  performedBy || userId,
                  performedByType || PerformedByType.USER,
                  null,
                  true, // This is a restoration
                  originalLogId,
                );

                // If we have the original usage log ID, update it to mark as restored
                if (originalLogId) {
                  const originalLog = await this.usageLogRepository.findOne({
                    where: { id: originalLogId },
                  });

                  if (originalLog) {
                    originalLog.restoredByLogId = originalLogId;
                    await this.usageLogRepository.save(originalLog);
                  }
                }
              } catch (error) {
                this.logger.error(`Error logging quota restoration: ${error.message}`, error.stack);
              }

              return true;
            }
          } else if (usedSource === QuotaUsageSource.ORGANIZATION_QUOTA) {
            const orgQuota = await this.orgQuotaRepository.findOne({
              where: { id: sourceQuotaId },
            });

            if (orgQuota && !orgQuota.unlimited) {
              orgQuota.remaining = orgQuota.remaining + amount;
              await this.orgQuotaRepository.save(orgQuota);

              try {
                // If we have the original usage log ID, use it
                const originalLogId = 'usageLogId' in usageInfo ? usageInfo.usageLogId : null;

                const restorationLog = await this.logQuotaUsage(
                  userId,
                  orgQuota.organizationId,
                  orgQuota.featureId,
                  orgQuota.unit,
                  amount,
                  purpose,
                  QuotaUsageSource.ORGANIZATION_QUOTA,
                  orgQuota.id,
                  performedBy || userId,
                  performedByType || PerformedByType.USER,
                  null,
                  true, // This is a restoration
                  originalLogId,
                );

                // If we have the original usage log ID, update it to mark as restored
                if (originalLogId && restorationLog) {
                  const originalLog = await this.usageLogRepository.findOne({
                    where: { id: originalLogId },
                  });

                  if (originalLog) {
                    originalLog.restoredByLogId = restorationLog.id;
                    await this.usageLogRepository.save(originalLog);
                  }
                }
              } catch (error) {
                this.logger.error(`Error logging quota restoration: ${error.message}`, error.stack);
              }

              return true;
            }
          }
        }

        // If we don't have complete usage info or direct restoration failed, try the regular way
        const user = await this.dataSource.getRepository(UserEntity).findOne({
          where: { id: userId },
          relations: ['organization', 'role'],
        });

        if (!user) {
          throw new NotFoundException(`User with ID ${userId} not found`);
        }

        const organizationId = user.organizationId;
        const isAdmin =
          user.role &&
          (user.role.keyCode === RoleEnum.ADMIN || user.role.keyCode === RoleEnum.SUPER_ADMIN);

        const feature = FEATURES[featureId];
        if (!feature) {
          throw new NotFoundException(`Feature ${featureId} not found`);
        }

        const allowUserQuota = this.checkIfFeatureAllowsUserQuota(featureId);

        // First try to restore to user quota if applicable
        if (!isAdmin && allowUserQuota && featureId !== FEATURE_CREDITS) {
          const userQuota = await this.userQuotaRepository.findOne({
            where: {
              userId,
              featureId,
            },
          });

          if (userQuota && !userQuota.unlimited) {
            userQuota.remaining = userQuota.remaining + amount;
            await this.userQuotaRepository.save(userQuota);

            try {
              // For fallback restoration, we don't have the original usage log ID
              await this.logQuotaUsage(
                userId,
                organizationId,
                featureId,
                userQuota.unit,
                amount,
                purpose,
                QuotaUsageSource.USER_QUOTA,
                userQuota.id,
                performedBy || userId,
                performedByType || PerformedByType.USER,
                null,
                true, // This is a restoration
              );
            } catch (error) {
              this.logger.error(`Error logging quota restoration: ${error.message}`, error.stack);
            }

            return true;
          }
        }

        // If no user quota or user is admin, restore to organization quota
        const orgQuota = await this.orgQuotaRepository.findOne({
          where: {
            organizationId,
            featureId,
          },
        });

        if (orgQuota && !orgQuota.unlimited) {
          orgQuota.remaining = orgQuota.remaining + amount;
          await this.orgQuotaRepository.save(orgQuota);

          try {
            // For fallback restoration, we don't have the original usage log ID
            await this.logQuotaUsage(
              userId,
              organizationId,
              featureId,
              orgQuota.unit,
              amount,
              purpose,
              QuotaUsageSource.ORGANIZATION_QUOTA,
              orgQuota.id,
              performedBy || userId,
              performedByType || PerformedByType.USER,
              null,
              true, // This is a restoration
            );
          } catch (error) {
            this.logger.error(`Error logging quota restoration: ${error.message}`, error.stack);
          }

          return true;
        }

        // If we get here, there was no quota to restore to
        return false;
      } catch (error) {
        this.logger.error(`Error restoring quota: ${error.message}`, error.stack);
        throw error;
      }
    }, SUBSCRIPTION_SYSTEM_CONFIG.QUOTA_ALLOCATION_TIMEOUT_SECONDS);
  }

  /**
   * Check quota for a specific organization
   * @param organizationId Organization ID
   * @param featureId Feature ID
   * @returns Object with quota information
   */
  async checkOrganizationQuota(
    organizationId: string,
    featureId: string,
  ): Promise<{ hasQuota: boolean; remaining: number; unlimited: boolean }> {
    try {
      const orgQuota = await this.orgQuotaRepository.findOne({
        where: {
          organizationId,
          featureId,
        },
      });

      if (orgQuota) {
        if (orgQuota.unlimited) {
          return { hasQuota: true, remaining: -1, unlimited: true };
        }
        if (orgQuota.remaining > 0) {
          return { hasQuota: true, remaining: orgQuota.remaining, unlimited: false };
        }
      }

      return { hasQuota: false, remaining: 0, unlimited: false };
    } catch (error) {
      this.logger.error(`Error checking organization quota: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get organization quota entity for a specific feature
   * @param organizationId Organization ID
   * @param featureId Feature ID
   * @returns OrganizationQuotaEntity or null if not found
   */
  async getOrganizationQuota(
    organizationId: string,
    featureId: string,
  ): Promise<OrganizationQuotaEntity | null> {
    try {
      return await this.orgQuotaRepository.findOne({
        where: {
          organizationId,
          featureId,
        },
      });
    } catch (error) {
      this.logger.error(`Error getting organization quota: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check quota for a specific feature
   * @param userId User ID
   * @param featureId Feature ID
   * @returns Object with quota information
   */
  async checkQuota(
    userId: string,
    featureId: string,
  ): Promise<{ hasQuota: boolean; remaining: number; unlimited: boolean }> {
    try {
      const user = await this.dataSource.getRepository(UserEntity).findOne({
        where: { id: userId },
        relations: ['role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      const { organizationId } = user;

      // Skip subscription check for SUPER_ADMIN users
      if (user.role && user.role.keyCode !== RoleEnum.SUPER_ADMIN) {
        // Check if the organization has an active subscription
        const subscriptionRepository = this.dataSource.getRepository(SubscriptionEntity);
        const subscription = await subscriptionRepository.findOne({
          where: {
            organizationId,
            status: SubscriptionStatus.ACTIVE,
          },
        });

        if (!subscription) {
          this.logger.warn(
            `Organization ${organizationId} does not have an active subscription. Credit check denied.`,
          );
          return { hasQuota: false, remaining: 0, unlimited: false };
        }
      }

      const isAdmin =
        user.role &&
        (user.role.keyCode === RoleEnum.ADMIN || user.role.keyCode === RoleEnum.SUPER_ADMIN);

      const featureConfig = FEATURES[featureId];
      if (!featureConfig) {
        this.logger.warn(`Feature ${featureId} not found in features config`);
      }

      const allowUserQuota = this.checkIfFeatureAllowsUserQuota(featureId);

      if (isAdmin || !allowUserQuota || (isAdmin && featureId === FEATURE_CREDITS)) {
      } else {
        const userQuota = await this.userQuotaRepository.findOne({
          where: {
            userId,
            featureId,
          },
        });

        if (userQuota) {
          if (userQuota.unlimited) {
            return { hasQuota: true, remaining: -1, unlimited: true };
          }
          if (userQuota.remaining > 0) {
            return { hasQuota: true, remaining: userQuota.remaining, unlimited: false };
          }

          if ((allowUserQuota && !isAdmin) || featureId === FEATURE_CREDITS) {
            return { hasQuota: false, remaining: 0, unlimited: false };
          }
        }
      }
      // Check organization quota
      return await this.checkOrganizationQuota(organizationId, featureId);
    } catch (error) {
      this.logger.error(`Error checking quota: ${error.message}`, error.stack);
      throw error;
    }
  }

  async removeAllQuotasForOrganization(organizationId: string): Promise<void> {
    try {
      await this.orgQuotaRepository.delete({ organizationId });

      await this.userQuotaRepository.delete({ organizationId });
    } catch (error) {
      this.logger.error(`Error removing quotas for organization: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Allocate multiple quotas to organization in bulk
   * @param organizationId Organization ID
   * @param quotas Array of quotas to allocate
   * @param entityManager Entity manager for transaction
   * @returns Promise<OrganizationQuotaEntity[]>
   */
  async allocateMultipleQuotasToOrganization(
    organizationId: string,
    quotas: {
      featureId: string;
      amount?: number;
      unit: string;
      resetInterval: QuotaResetInterval;
      source: QuotaSource;
      sourceId: string;
      unlimited: boolean;
    }[],
    entityManager?: any,
  ): Promise<OrganizationQuotaEntity[]> {
    try {
      const repository = entityManager
        ? entityManager.getRepository(OrganizationQuotaEntity)
        : this.orgQuotaRepository;
      const now = new Date();

      // Find existing quotas
      const existingQuotas = await repository.find({
        where: { organizationId },
      });

      const existingQuotasMap: Record<string, OrganizationQuotaEntity> = {};
      existingQuotas.forEach((quota: OrganizationQuotaEntity) => {
        const key = `${quota.featureId}-${quota.source}-${quota.sourceId}`;
        existingQuotasMap[key] = quota;
      });

      // Prepare quotas to save
      const quotasToSave = [];

      for (const quota of quotas) {
        const key = `${quota.featureId}-${quota.source}-${quota.sourceId}`;
        const existingQuota = existingQuotasMap[key];

        if (existingQuota) {
          // Update existing quota
          existingQuota.unlimited = quota.unlimited;
          existingQuota.unit = quota.unit;
          existingQuota.resetInterval = quota.resetInterval;

          if (!quota.unlimited && quota.amount !== undefined) {
            existingQuota.remaining = quota.amount;
            existingQuota.originalAmount = quota.amount;
          }

          existingQuota.lastResetAt = now;
          quotasToSave.push(existingQuota);
        } else {
          // Create new quota
          const newQuota = repository.create({
            organizationId,
            featureId: quota.featureId,
            remaining: quota.unlimited ? 0 : quota.amount || 0,
            originalAmount: quota.unlimited ? 0 : quota.amount || 0,
            unit: quota.unit,
            lastResetAt: now,
            resetInterval: quota.resetInterval,
            source: quota.source,
            sourceId: quota.sourceId,
            unlimited: quota.unlimited,
          });
          quotasToSave.push(newQuota);
        }
      }

      // Save all quotas in bulk
      return await repository.save(quotasToSave);
    } catch (error) {
      this.logger.error(
        `Error allocating multiple quotas to organization: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Allocate multiple features to organization
   * @param organizationId Organization ID
   * @param planId Plan ID
   * @param subscriptionId Subscription ID
   * @param requestUserId Optional user ID of the user making the request
   * @param entityManager Entity manager for transaction
   * @returns Promise<void>
   */
  async allocateMultipleFeaturesToOrganization(
    organizationId: string,
    planId: string,
    subscriptionId: string,
    requestUserId?: string,
    entityManager?: any,
  ): Promise<void> {
    try {
      const planFeatures = PLAN_FEATURES[planId] || {};
      const features = FEATURES || {};

      this.logger.log(`Allocating features from plan ${planId} to organization ${organizationId}`);

      const quotasToAllocate = [];
      const usageLogs = [];

      for (const featureId in planFeatures) {
        const feature = planFeatures[featureId];
        const featureConfig = features[featureId];

        if (!featureConfig) {
          this.logger.warn(`Feature ${featureId} not found in features config`);
          continue;
        }

        const resetInterval =
          feature.resetInterval === QuotaResetInterval.SUBSCRIPTION
            ? QuotaResetInterval.SUBSCRIPTION
            : feature.resetInterval || QuotaResetInterval.NEVER;

        quotasToAllocate.push({
          featureId,
          amount: feature.unlimited ? undefined : feature.amount,
          unit: featureConfig.unit,
          resetInterval,
          source: QuotaSource.SUBSCRIPTION,
          sourceId: subscriptionId,
          unlimited: feature.unlimited,
        });
      }

      if (quotasToAllocate.length > 0) {
        const savedQuotas = await this.allocateMultipleQuotasToOrganization(
          organizationId,
          quotasToAllocate,
          entityManager,
        );

        this.logger.log(
          `Allocated ${savedQuotas.length} features to organization ${organizationId} from plan ${planId}`,
        );

        for (const quota of savedQuotas) {
          if (!quota.unlimited) {
            usageLogs.push({
              userId: requestUserId || null,
              organizationId,
              featureId: quota.featureId,
              unit: quota.unit,
              amount: quota.originalAmount,
              purpose: 'Initial allocation from plan',
              usedSource: QuotaUsageSource.ORGANIZATION_QUOTA,
              sourceQuotaId: quota.id,
              performedBy: requestUserId || null,
              performedByType: requestUserId ? PerformedByType.USER : PerformedByType.SYSTEM,
            });
          }
        }

        if (usageLogs.length > 0) {
          try {
            const logResults = await this.logQuotaUsageBulk(usageLogs, entityManager);
            this.logger.log(
              `Logged ${logResults.length} quota allocations for organization ${organizationId}`,
            );
          } catch (error) {
            this.logger.error(
              `Error logging quota allocations for organization ${organizationId}: ${error.message}`,
              error.stack,
            );
          }
        }
      }
    } catch (error) {
      this.logger.error(
        `Error allocating multiple features to organization: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Allocate credits to multiple users
   * @param userCreditsMap Map of userId to credits amount
   * @param organizationId Organization ID
   * @param featureId Feature ID
   * @param unit Unit of measurement
   * @param resetInterval Reset interval
   * @param source Source of quota
   * @param sourceId Source ID
   * @param entityManager Entity manager for transaction
   * @returns Promise<void>
   */
  async allocateQuotaToMultipleUsers(
    userCreditsMap: Record<string, number>,
    organizationId: string,
    featureId: string,
    unit: string = FeatureUnitEnum.CREDIT,
    resetInterval: QuotaResetInterval = QuotaResetInterval.SUBSCRIPTION,
    source: QuotaSource = QuotaSource.SUBSCRIPTION,
    sourceId: string = null,
    performedBy?: string,
    performedByType?: PerformedByType,
    entityManager?: any,
  ): Promise<void> {
    try {
      const totalUserCredits = Object.values(userCreditsMap).reduce(
        (sum, credits) => sum + credits,
        0,
      );

      this.logger.log(
        `Allocating a total of ${totalUserCredits} credits to ${Object.keys(userCreditsMap).length} users`,
      );

      const orgQuotaRepo = entityManager
        ? entityManager.getRepository(OrganizationQuotaEntity)
        : this.orgQuotaRepository;
      const orgQuota = await orgQuotaRepo.findOne({
        where: {
          organizationId,
          featureId,
          source,
          sourceId,
        },
      });

      if (orgQuota && !orgQuota.unlimited) {
        orgQuota.remaining = Math.max(0, orgQuota.remaining - totalUserCredits);
        await orgQuotaRepo.save(orgQuota);
      }

      const usersToAllocate = [];
      const usageLogs = [];

      for (const [userId, credits] of Object.entries(userCreditsMap)) {
        if (credits <= 0) continue; // Skip users with no credits
        usersToAllocate.push({ userId, credits });
      }

      for (const { userId, credits } of usersToAllocate) {
        const userQuota = await this.allocateQuotaToUser(
          userId,
          organizationId,
          featureId,
          credits,
          unit,
          resetInterval,
          source,
          sourceId,
          entityManager,
        );

        usageLogs.push({
          userId,
          organizationId,
          featureId,
          unit,
          amount: credits,
          purpose: 'Allocated quota to user',
          usedSource: QuotaUsageSource.USER_QUOTA,
          sourceQuotaId: userQuota.id,
          performedBy: performedBy || null,
          performedByType: performedByType || PerformedByType.SYSTEM,
        });

        this.logger.log(
          `Allocated ${credits} ${featureId} to user ${userId} from organization ${organizationId}`,
        );
      }

      if (usageLogs.length > 0) {
        try {
          const logResults = await this.logQuotaUsageBulk(usageLogs, entityManager);
          this.logger.log(
            `Logged ${logResults.length} quota allocations for users in organization ${organizationId}`,
          );
        } catch (error) {
          this.logger.error(
            `Error logging quota allocations for users in organization ${organizationId}: ${error.message}`,
            error.stack,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error allocating quota to multiple users: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a feature allows allocating quota at the user level
   * @param featureId Feature ID
   * @returns Boolean indicating if the feature allows allocating quota at the user level
   */
  private checkIfFeatureAllowsUserQuota(featureId: string): boolean {
    if (featureId === FEATURE_CREDITS) {
      return true;
    }

    for (const planId in PLAN_FEATURES) {
      const plan = PLAN_FEATURES[planId];
      if (featureId in plan && plan[featureId].allowUserQuota) {
        return true;
      }
    }
    return false;
  }

  /**
   * Log quota usage
   * @param userId User ID or null for organization-level operations
   * @param organizationId Organization ID
   * @param featureId Feature ID
   * @param unit Unit of measurement
   * @param amount Amount used
   * @param purpose Purpose of usage
   * @param usedSource Source of usage
   * @param sourceQuotaId Source quota ID
   * @param performedBy ID of the user who performed the action (optional)
   * @param performedByType Type of the performer (optional)
   * @param entityManager Entity manager for transaction (optional)
   * @param isRestoration Whether this is a restoration log (optional)
   * @param originalUsageLogId ID of the original usage log being restored (optional)
   * @param logType Type of log (usage, allocation, restoration) (optional)
   * @param quotaSource Source type of the quota (subscription, topup, etc.) (optional)
   * @returns Promise<QuotaUsageLogEntity>
   */
  async logQuotaUsage(
    userId: string | null,
    organizationId: string,
    featureId: string,
    unit: string,
    amount: number,
    purpose: string,
    usedSource: QuotaUsageSource,
    sourceQuotaId: string,
    performedBy?: string,
    performedByType?: PerformedByType,
    entityManager?: any,
    isRestoration: boolean = false,
    originalUsageLogId?: string,
    logType?: QuotaLogType,
    quotaSource?: QuotaSource,
  ): Promise<QuotaUsageLogEntity | null> {
    try {
      const repository = entityManager
        ? entityManager.getRepository(QuotaUsageLogEntity)
        : this.usageLogRepository;

      // Determine log type if not provided
      let finalLogType = logType;
      if (!finalLogType) {
        if (isRestoration) {
          finalLogType = QuotaLogType.RESTORATION;
        } else if (purpose && (
          purpose.toLowerCase().includes('allocat') ||
          purpose.toLowerCase().includes('topup') ||
          purpose.toLowerCase().includes('add') ||
          purpose.toLowerCase().includes('credit')
        )) {
          finalLogType = QuotaLogType.ALLOCATION;
        } else {
          finalLogType = QuotaLogType.USAGE;
        }
      }

      const log = repository.create({
        userId,
        organizationId,
        featureId,
        unit,
        amount,
        purpose,
        usedSource,
        sourceQuotaId,
        quotaSource: quotaSource || null,
        performedBy: performedBy || null,
        performedByType: performedByType || PerformedByType.SYSTEM,
        logType: finalLogType,
        isRestoration,
        originalUsageLogId: originalUsageLogId || null,
      });

      return await repository.save(log);
    } catch (error) {
      if (error.code === '23503') {
        // Foreign key violation
        this.logger.warn(`Skipping quota usage log due to foreign key violation: ${error.detail}`);
        return null;
      }
      this.logger.error(`Error logging quota usage: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Log quota usage in bulk
   * @param logs Array of quota usage logs to create
   * @param entityManager Entity manager for transaction
   * @returns Promise<QuotaUsageLogEntity[]>
   */
  async logQuotaUsageBulk(
    logs: {
      userId: string | null;
      organizationId: string;
      featureId: string;
      unit: string;
      amount: number;
      purpose: string;
      usedSource: QuotaUsageSource;
      sourceQuotaId: string;
      performedBy?: string;
      performedByType?: PerformedByType;
      isRestoration?: boolean;
      originalUsageLogId?: string;
      logType?: QuotaLogType;
    }[],
    entityManager?: any,
  ): Promise<QuotaUsageLogEntity[]> {
    try {
      const repository = entityManager
        ? entityManager.getRepository(QuotaUsageLogEntity)
        : this.usageLogRepository;

      const entities = logs.map((log) => {
        // Determine log type if not provided
        let finalLogType = log.logType;
        if (!finalLogType) {
          if (log.isRestoration) {
            finalLogType = QuotaLogType.RESTORATION;
          } else if (log.purpose && (
            log.purpose.toLowerCase().includes('allocat') ||
            log.purpose.toLowerCase().includes('topup') ||
            log.purpose.toLowerCase().includes('add') ||
            log.purpose.toLowerCase().includes('credit')
          )) {
            finalLogType = QuotaLogType.ALLOCATION;
          } else {
            finalLogType = QuotaLogType.USAGE;
          }
        }

        return repository.create({
          userId: log.userId,
          organizationId: log.organizationId,
          featureId: log.featureId,
          unit: log.unit,
          amount: log.amount,
          purpose: log.purpose,
          usedSource: log.usedSource,
          sourceQuotaId: log.sourceQuotaId,
          performedBy: log.performedBy || null,
          performedByType: log.performedByType || PerformedByType.SYSTEM,
          logType: finalLogType,
          isRestoration: log.isRestoration || false,
          originalUsageLogId: log.originalUsageLogId || null,
        });
      });

      return await repository.save(entities);
    } catch (error) {
      this.logger.error(`Error logging quota usage in bulk: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Get detailed credits breakdown by source for an organization
   * @param organizationId Organization ID
   * @returns Object with credits breakdown by source
   */
  async getOrganizationCreditsBreakdown(organizationId: string): Promise<{
    total: { remaining: number; originalAmount: number };
    plan: { remaining: number; originalAmount: number } | null;
    topup: { remaining: number; originalAmount: number } | null;
    sources: Array<{
      id: string;
      source: QuotaSource;
      remaining: number;
      originalAmount: number;
      sourceId: string | null;
    }>;
  }> {
    try {
      const quotas = await this.orgQuotaRepository.find({
        where: {
          organizationId,
          featureId: FEATURE_CREDITS,
        },
        order: {
          source: 'ASC', // SUBSCRIPTION first, then TOPUP
        },
      });

      const breakdown = {
        total: { remaining: 0, originalAmount: 0 },
        plan: null as { remaining: number; originalAmount: number } | null,
        topup: null as { remaining: number; originalAmount: number } | null,
        sources: [] as Array<{
          id: string;
          source: QuotaSource;
          remaining: number;
          originalAmount: number;
          sourceId: string | null;
        }>,
      };

      for (const quota of quotas) {
        // Add to total
        breakdown.total.remaining += quota.remaining;
        breakdown.total.originalAmount += quota.originalAmount;

        // Add to sources array
        breakdown.sources.push({
          id: quota.id,
          source: quota.source,
          remaining: quota.remaining,
          originalAmount: quota.originalAmount,
          sourceId: quota.sourceId,
        });

        // Categorize by source
        if (quota.source === QuotaSource.SUBSCRIPTION) {
          if (!breakdown.plan) {
            breakdown.plan = { remaining: 0, originalAmount: 0 };
          }
          breakdown.plan.remaining += quota.remaining;
          breakdown.plan.originalAmount += quota.originalAmount;
        } else if (quota.source === QuotaSource.TOPUP) {
          if (!breakdown.topup) {
            breakdown.topup = { remaining: 0, originalAmount: 0 };
          }
          breakdown.topup.remaining += quota.remaining;
          breakdown.topup.originalAmount += quota.originalAmount;
        }
      }

      return breakdown;
    } catch (error) {
      this.logger.error(`Error getting credits breakdown: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get detailed credits breakdown by source for a user
   * @param userId User ID
   * @returns Object with user credits breakdown by source
   */
  async getUserCreditsBreakdown(userId: string): Promise<{
    total: { remaining: number; originalAmount: number };
    plan: { remaining: number; originalAmount: number } | null;
    topup: { remaining: number; originalAmount: number } | null;
    sources: Array<{
      id: string;
      source: QuotaSource;
      remaining: number;
      originalAmount: number;
      sourceId: string | null;
    }>;
  }> {
    try {
      const quotas = await this.userQuotaRepository.find({
        where: {
          userId,
          featureId: FEATURE_CREDITS,
        },
        order: {
          source: 'ASC', // SUBSCRIPTION first, then TOPUP
        },
      });

      const breakdown = {
        total: { remaining: 0, originalAmount: 0 },
        plan: null as { remaining: number; originalAmount: number } | null,
        topup: null as { remaining: number; originalAmount: number } | null,
        sources: [] as Array<{
          id: string;
          source: QuotaSource;
          remaining: number;
          originalAmount: number;
          sourceId: string | null;
        }>,
      };

      for (const quota of quotas) {
        // Add to total
        breakdown.total.remaining += quota.remaining;
        breakdown.total.originalAmount += quota.originalAmount;

        // Add to sources array
        breakdown.sources.push({
          id: quota.id,
          source: quota.source,
          remaining: quota.remaining,
          originalAmount: quota.originalAmount,
          sourceId: quota.sourceId,
        });

        // Categorize by source
        if (quota.source === QuotaSource.SUBSCRIPTION) {
          if (!breakdown.plan) {
            breakdown.plan = { remaining: 0, originalAmount: 0 };
          }
          breakdown.plan.remaining += quota.remaining;
          breakdown.plan.originalAmount += quota.originalAmount;
        } else if (quota.source === QuotaSource.TOPUP) {
          if (!breakdown.topup) {
            breakdown.topup = { remaining: 0, originalAmount: 0 };
          }
          breakdown.topup.remaining += quota.remaining;
          breakdown.topup.originalAmount += quota.originalAmount;
        }
      }

      return breakdown;
    } catch (error) {
      this.logger.error(`Error getting user credits breakdown: ${error.message}`, error.stack);
      throw error;
    }
  }
}

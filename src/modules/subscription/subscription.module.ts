import { Module } from '@nestjs/common';
import { SubscriptionService } from './services/subscription.service';
import { QuotaService } from './services/quota.service';
import { CreditService } from './services/credit.service';
import { LicenseService } from './services/license.service';
import { SubscriptionRepository } from './repositories/subscription.repository';
import { OrganizationQuotaRepository } from './repositories/organization-quota.repository';
import { UserQuotaRepository } from './repositories/user-quota.repository';
import { QuotaUsageLogRepository } from './repositories/quota-usage-log.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionEntity } from './entities/subscription.entity';
import { OrganizationQuotaEntity } from './entities/organization-quota.entity';
import { UserQuotaEntity } from './entities/user-quota.entity';
import { QuotaUsageLogEntity } from './entities/quota-usage-log.entity';
import { LicenseEntity } from './entities/license.entity';
import { SubscriptionChangeEntity } from './entities/subscription-change.entity';

import { LicenseTransferEntity } from './entities/license-transfer.entity';
import { SubscriptionNotificationEntity } from './entities/subscription-notification.entity';
import { OrganizationEntity } from '../user/entities/organization.entity';
import { UserEntity } from '../user/entities/user.entity';
import { SubscriptionController } from './controllers/subscription.controller';
import { LicenseController } from './controllers/license.controller';
import { PaymentController } from './controllers/payment.controller';
import { FeatureCostService } from './services/feature-cost.service';
import { PaymentService } from './services/payment.service';
import { FeatureCreditsInterceptor } from './interceptors/feature-credits.interceptor';
import { APP_INTERCEPTOR } from '@nestjs/core';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SubscriptionEntity,
      OrganizationQuotaEntity,
      UserQuotaEntity,
      QuotaUsageLogEntity,
      LicenseEntity,
      SubscriptionChangeEntity,
      LicenseTransferEntity,
      SubscriptionNotificationEntity,
      OrganizationEntity,
      UserEntity,
    ]),
  ],
  providers: [
    SubscriptionService,
    QuotaService,
    CreditService,
    LicenseService,
    PaymentService,
    SubscriptionRepository,
    OrganizationQuotaRepository,
    UserQuotaRepository,
    QuotaUsageLogRepository,
    FeatureCostService,
    {
      provide: APP_INTERCEPTOR,
      useClass: FeatureCreditsInterceptor,
    },
  ],
  controllers: [SubscriptionController, LicenseController, PaymentController],
  exports: [SubscriptionService, QuotaService, CreditService, LicenseService, PaymentService],
})
export class SubscriptionModule {}

import { BaseEntity, Column, <PERSON>tity, Join<PERSON><PERSON>um<PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { SubscriptionEntity } from './subscription.entity';
import { UserEntity } from '../../user/entities/user.entity';

export enum SubscriptionNotificationType {
  LOW_CREDITS = 'low_credits',
  EXPIRY_WARNING = 'expiry_warning',
  PAYMENT_FAILED = 'payment_failed',
  TRIAL_ENDING = 'trial_ending',
  SUBSCRIPTION_CANCELLED = 'subscription_cancelled',
  SUBSCRIPTION_RENEWED = 'subscription_renewed',
  PLAN_CHANGED = 'plan_changed',
  LICENSE_ASSIGNED = 'license_assigned',
  LICENSE_REMOVED = 'license_removed',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

@Entity('subscription_notifications')
export class SubscriptionNotificationEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'subscription_id' })
  subscriptionId: string;

  @Column({ name: 'notification_type', enum: SubscriptionNotificationType })
  notificationType: SubscriptionNotificationType;

  @Column({ name: 'recipient_user_id', nullable: true })
  recipientUserId: string | null;

  @Column({ name: 'message', nullable: true })
  message: string | null;

  @Column({ name: 'sent_at', type: 'timestamptz', nullable: true })
  sentAt: Date | null;

  @Column({ name: 'status', enum: NotificationStatus, default: NotificationStatus.PENDING })
  status: NotificationStatus;

  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: any;

  @Column({ name: 'created_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  // Relations
  @ManyToOne(() => SubscriptionEntity)
  @JoinColumn({ name: 'subscription_id' })
  subscription: SubscriptionEntity;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'recipient_user_id' })
  recipient: UserEntity | null;
}

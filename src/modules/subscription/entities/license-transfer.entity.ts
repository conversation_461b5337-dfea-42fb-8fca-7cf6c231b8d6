import { <PERSON><PERSON>ntity, Column, En<PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { LicenseEntity } from './license.entity';
import { UserEntity } from '../../user/entities/user.entity';

@Entity('license_transfers')
export class LicenseTransferEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'license_id' })
  licenseId: string;

  @Column({ name: 'from_user_id', nullable: true })
  fromUserId: string | null;

  @Column({ name: 'to_user_id', nullable: true })
  toUserId: string | null;

  @Column({ name: 'transfer_reason', nullable: true })
  transferReason: string | null;

  @Column({ name: 'credits_transferred', type: 'jsonb', nullable: true })
  creditsTransferred: any;

  @Column({ name: 'performed_by', nullable: true })
  performedBy: string | null;

  @Column({ name: 'created_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  // Relations
  @ManyToOne(() => LicenseEntity)
  @JoinColumn({ name: 'license_id' })
  license: LicenseEntity;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'from_user_id' })
  fromUser: UserEntity | null;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'to_user_id' })
  toUser: UserEntity | null;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'performed_by' })
  performer: UserEntity | null;
}

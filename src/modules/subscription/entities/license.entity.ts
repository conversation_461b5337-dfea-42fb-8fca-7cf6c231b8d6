import {
  BaseEntity,
  Column,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { SubscriptionEntity } from './subscription.entity';
import { UserEntity } from '../../user/entities/user.entity';

import { LicenseTransferEntity } from './license-transfer.entity';
import { UserQuotaEntity } from './user-quota.entity';

export enum LicenseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  TRANSFERRED = 'transferred',
}

@Entity('licenses')
export class LicenseEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'subscription_id' })
  subscriptionId: string;

  @Column({ name: 'user_id', nullable: true })
  userId: string | null;

  @Column({ name: 'status', enum: LicenseStatus, default: LicenseStatus.ACTIVE })
  status: LicenseStatus;

  @Column({ name: 'assigned_at', type: 'timestamptz', nullable: true })
  assignedAt: Date | null;

  @Column({ name: 'created_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ name: 'updated_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => SubscriptionEntity, (subscription) => subscription.licenses)
  @JoinColumn({ name: 'subscription_id' })
  subscription: SubscriptionEntity;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity | null;

  @OneToMany(() => LicenseTransferEntity, (transfer) => transfer.license)
  transfers: LicenseTransferEntity[];

  @OneToMany(() => UserQuotaEntity, (quota) => quota.license)
  quotas: UserQuotaEntity[];
}

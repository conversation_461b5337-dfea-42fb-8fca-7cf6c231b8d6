import { BaseEntity, Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { SubscriptionEntity } from './subscription.entity';
import { UserEntity } from '../../user/entities/user.entity';

export enum SubscriptionChangeType {
  UPGRADE = 'upgrade',
  DOWNGRADE = 'downgrade',
  LICENSE_ADD = 'license_add',
  LICENSE_REMOVE = 'license_remove',
  PLAN_CHANGE = 'plan_change',
  BILLING_CYCLE_CHANGE = 'billing_cycle_change',
  CANCELLATION = 'cancellation',
  REACTIVATION = 'reactivation',
}

@Entity('subscription_changes')
export class SubscriptionChangeEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'subscription_id' })
  subscriptionId: string;

  @Column({ name: 'change_type', enum: SubscriptionChangeType })
  changeType: SubscriptionChangeType;

  @Column({ name: 'old_plan_id', nullable: true })
  oldPlanId: string | null;

  @Column({ name: 'new_plan_id', nullable: true })
  newPlanId: string | null;

  @Column({ name: 'old_license_count', nullable: true })
  oldLicenseCount: number | null;

  @Column({ name: 'new_license_count', nullable: true })
  newLicenseCount: number | null;

  @Column({ name: 'effective_date', type: 'timestamptz' })
  effectiveDate: Date;

  @Column({ name: 'prorated_amount', type: 'decimal', precision: 10, scale: 2, nullable: true })
  proratedAmount: number | null;

  @Column({ name: 'reason', nullable: true })
  reason: string | null;

  @Column({ name: 'performed_by', nullable: true })
  performedBy: string | null;

  @Column({ name: 'created_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  // Relations
  @ManyToOne(() => SubscriptionEntity)
  @JoinColumn({ name: 'subscription_id' })
  subscription: SubscriptionEntity;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'performed_by' })
  performer: UserEntity | null;
}

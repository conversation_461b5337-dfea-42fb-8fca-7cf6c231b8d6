import { <PERSON><PERSON>ntity, Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { UserEntity } from '../../user/entities/user.entity';
import { OrganizationEntity } from '../../user/entities/organization.entity';
import { QuotaResetInterval, QuotaSource } from './organization-quota.entity';
import { LicenseEntity } from './license.entity';

@Entity('user_quotas')
export class UserQuotaEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', nullable: true })
  userId: string | null;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({ name: 'feature_id' })
  featureId: string;

  @Column({ name: 'unit' })
  unit: string;

  @Column({ name: 'remaining', type: 'float' })
  remaining: number;

  @Column({ name: 'original_amount', type: 'float' })
  originalAmount: number;

  @Column({ name: 'last_reset_at', type: 'timestamptz', nullable: true })
  lastResetAt: Date;

  @Column({
    name: 'reset_interval',
    nullable: true,
    enum: QuotaResetInterval,
    default: QuotaResetInterval.NEVER,
  })
  resetInterval: QuotaResetInterval;

  @Column({ name: 'source', nullable: true, enum: QuotaSource, default: QuotaSource.SUBSCRIPTION })
  source: QuotaSource;

  @Column({ name: 'source_id', nullable: true })
  sourceId: string;

  @Column({ name: 'unlimited', default: false })
  unlimited: boolean;

  @Column({ name: 'license_id', nullable: true })
  licenseId: string | null;

  @ManyToOne(() => UserEntity, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity | null;

  @ManyToOne(() => OrganizationEntity)
  @JoinColumn({ name: 'organization_id' })
  organization: OrganizationEntity;

  @ManyToOne(() => LicenseEntity, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'license_id' })
  license: LicenseEntity | null;
}

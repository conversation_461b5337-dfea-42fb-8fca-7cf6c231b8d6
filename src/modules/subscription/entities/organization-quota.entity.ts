import { BaseEntity, Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { OrganizationEntity } from '../../user/entities/organization.entity';

export enum QuotaResetInterval {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
  NEVER = 'never',
  SUBSCRIPTION = 'subscription',
}

export enum QuotaSource {
  SUBSCRIPTION = 'subscription',
  TOPUP = 'topup',
  MANUAL = 'manual',
}

@Entity('organization_quotas')
export class OrganizationQuotaEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({ name: 'feature_id' })
  featureId: string;

  @Column({ name: 'unit' })
  unit: string; // 'credit', 'count', etc.

  @Column({ name: 'remaining', type: 'float' })
  remaining: number;

  @Column({ name: 'original_amount', type: 'float' })
  originalAmount: number;

  @Column({ name: 'last_reset_at', type: 'timestamptz', nullable: true })
  lastResetAt: Date;

  @Column({
    name: 'reset_interval',
    nullable: true,
    enum: QuotaResetInterval,
    default: QuotaResetInterval.NEVER,
  })
  resetInterval: QuotaResetInterval;

  @Column({ name: 'source', nullable: true, enum: QuotaSource, default: QuotaSource.SUBSCRIPTION })
  source: QuotaSource;

  @Column({ name: 'source_id', nullable: true })
  sourceId: string;

  @Column({ name: 'unlimited', default: false })
  unlimited: boolean;

  @ManyToOne(() => OrganizationEntity)
  @JoinColumn({ name: 'organization_id' })
  organization: OrganizationEntity;
}

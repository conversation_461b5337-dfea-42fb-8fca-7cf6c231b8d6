import { BaseEntity, Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { UserEntity } from '../../user/entities/user.entity';
import { OrganizationEntity } from '../../user/entities/organization.entity';
import { QuotaSource } from './organization-quota.entity';

export enum QuotaUsageSource {
  USER_QUOTA = 'user_quota',
  ORGANIZATION_QUOTA = 'organization_quota',
}

export enum PerformedByType {
  SYSTEM = 'system',
  USER = 'user',
}

export enum QuotaLogType {
  USAGE = 'usage',           // Sử dụng quota (trừ đi)
  ALLOCATION = 'allocation', // Thêm quota (cộng vào)
  RESTORATION = 'restoration', // Khôi phục quota (cộng lại)
}

@Entity('quota_usage_logs')
export class QuotaUsageLogEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', nullable: true })
  userId: string | null;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({ name: 'feature_id' })
  featureId: string;

  @Column({ name: 'unit' })
  unit: string;

  @Column({ name: 'amount', type: 'float' })
  amount: number;

  @Column({ name: 'purpose', nullable: true })
  purpose: string;

  @Column({ name: 'used_source', enum: QuotaUsageSource })
  usedSource: QuotaUsageSource;

  @Column({ name: 'source_quota_id' })
  sourceQuotaId: string;

  // Track the source type of the quota (subscription, topup, etc.)
  @Column({ name: 'quota_source', enum: QuotaSource, nullable: true })
  quotaSource: QuotaSource | null;

  // Who did this action
  @Column({ name: 'performed_by', nullable: true })
  performedBy: string;

  @Column({ name: 'performed_by_type', enum: PerformedByType, default: PerformedByType.SYSTEM })
  performedByType: PerformedByType;

  @Column({ name: 'log_type', enum: QuotaLogType, default: QuotaLogType.USAGE })
  logType: QuotaLogType;

  @Column({ name: 'is_restoration', default: false })
  isRestoration: boolean;

  @Column({ name: 'restored_by_log_id', nullable: true })
  restoredByLogId: string | null;

  @Column({ name: 'original_usage_log_id', nullable: true })
  originalUsageLogId: string | null;

  @Column({ name: 'created_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @ManyToOne(() => UserEntity, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity | null;

  @ManyToOne(() => OrganizationEntity)
  @JoinColumn({ name: 'organization_id' })
  organization: OrganizationEntity;

  @ManyToOne(() => QuotaUsageLogEntity, { nullable: true })
  @JoinColumn({ name: 'original_usage_log_id' })
  originalUsageLog: QuotaUsageLogEntity;

  @ManyToOne(() => QuotaUsageLogEntity, { nullable: true })
  @JoinColumn({ name: 'restored_by_log_id' })
  restoredByLog: QuotaUsageLogEntity;
}

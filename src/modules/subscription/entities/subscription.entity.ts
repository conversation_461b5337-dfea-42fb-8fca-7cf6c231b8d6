import {
  BaseEntity,
  Column,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { OrganizationEntity } from '../../user/entities/organization.entity';
import { LicenseEntity } from './license.entity';
import { SubscriptionChangeEntity } from './subscription-change.entity';
import { SubscriptionNotificationEntity } from './subscription-notification.entity';

export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELED = 'canceled',
  EXPIRED = 'expired',
  PENDING = 'pending',
}

export enum RenewInterval {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  BIANNUALLY = 'biannually',
  ANNUALLY = 'annually',
  ONETIME = 'onetime',
}

@Entity('subscriptions')
export class SubscriptionEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({ name: 'plan_id' })
  planId: string;

  @Column({ name: 'start_date', type: 'timestamptz' })
  startDate: Date;

  @Column({ name: 'next_billing_date', type: 'timestamptz', nullable: true })
  nextBillingDate: Date;

  @Column({
    name: 'renew_interval',
    nullable: true,
    enum: RenewInterval,
    default: RenewInterval.MONTHLY,
  })
  renewInterval: RenewInterval;

  @Column({ name: 'status', enum: SubscriptionStatus, default: SubscriptionStatus.ACTIVE })
  status: SubscriptionStatus;

  @Column({ name: 'auto_renew', default: true })
  autoRenew: boolean;

  @Column({ name: 'license_count', default: 1 })
  licenseCount: number;

  @Column({ name: 'end_date', type: 'timestamptz', nullable: true })
  endDate: Date | null;

  @Column({ name: 'grace_period_end', type: 'timestamptz', nullable: true })
  gracePeriodEnd: Date | null;

  @Column({ name: 'failed_payment_count', default: 0 })
  failedPaymentCount: number;

  @Column({ name: 'stripe_subscription_id', nullable: true })
  stripeSubscriptionId: string | null;

  @Column({ name: 'stripe_customer_id', nullable: true })
  stripeCustomerId: string | null;

  @Column({ name: 'trial_end_date', type: 'timestamptz', nullable: true })
  trialEndDate: Date | null;

  @Column({ name: 'is_trial', default: false })
  isTrial: boolean;

  @Column({ name: 'cancellation_reason', nullable: true })
  cancellationReason: string | null;

  @Column({ name: 'cancelled_at', type: 'timestamptz', nullable: true })
  cancelledAt: Date | null;

  // Relations
  @ManyToOne(() => OrganizationEntity)
  @JoinColumn({ name: 'organization_id' })
  organization: OrganizationEntity;

  @OneToMany(() => LicenseEntity, (license) => license.subscription)
  licenses: LicenseEntity[];

  @OneToMany(() => SubscriptionChangeEntity, (change) => change.subscription)
  changes: SubscriptionChangeEntity[];

  @OneToMany(() => SubscriptionNotificationEntity, (notification) => notification.subscription)
  notifications: SubscriptionNotificationEntity[];
}

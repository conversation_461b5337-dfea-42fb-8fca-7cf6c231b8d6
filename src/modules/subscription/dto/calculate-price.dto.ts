import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNotEmpty, IsString, Min } from 'class-validator';
import { RenewInterval } from '../entities/subscription.entity';

export class CalculatePriceDto {
  @ApiProperty({ description: 'Plan ID (e.g., basic, pro, pro_plus, enterprise)' })
  @IsNotEmpty()
  @IsString()
  planId: string;

  @ApiProperty({ description: 'Number of licenses', minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  licenseCount: number;

  @ApiProperty({
    description: 'Billing cycle',
    enum: RenewInterval,
    example: RenewInterval.MONTHLY,
  })
  @IsNotEmpty()
  @IsEnum(RenewInterval)
  billingCycle: RenewInterval;
}

export class SubscriptionPriceResponseDto {
  @ApiProperty({ description: 'Plan information' })
  plan: {
    id: string;
    name: string;
    basePrice: number;
    currency: string;
  };

  @ApiProperty({ description: 'Number of licenses' })
  licenseCount: number;

  @ApiProperty({ description: 'Billing cycle' })
  billingCycle: RenewInterval;

  @ApiProperty({ description: 'Base total price (before discounts)' })
  baseTotal: number;

  @ApiProperty({ description: 'Discount percentage applied' })
  discountPercentage: number;

  @ApiProperty({ description: 'Discount amount' })
  discountAmount: number;

  @ApiProperty({ description: 'Final total price after discounts' })
  finalTotal: number;

  @ApiProperty({ description: 'Price breakdown' })
  breakdown: {
    pricePerLicense: number;
    totalLicenses: number;
    subtotal: number;
    billingCycleMultiplier: number;
    discount: number;
    total: number;
  };
}

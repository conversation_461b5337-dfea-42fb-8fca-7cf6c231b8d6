import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsUUID,
  IsEnum,
  IsNumber,
  Min,
  Max,
} from 'class-validator';
import { RenewInterval } from '../entities/subscription.entity';

export class PayForSubscriptionDto {
  @ApiProperty({
    description: 'Subscription ID to pay for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  subscriptionId: string;

  @ApiPropertyOptional({
    description: 'Payment method ID from Stripe',
    example: 'pm_1234567890',
  })
  @IsOptional()
  @IsString()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'Whether to save payment method for future use',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  savePaymentMethod?: boolean;

  @ApiPropertyOptional({
    description: 'Return URL for redirect-based payment methods',
    example: 'https://your-app.com/payment/return',
  })
  @IsOptional()
  @IsString()
  returnUrl?: string;
}

export class ConfirmPaymentDto {
  @ApiProperty({
    description: 'Payment intent ID from Stripe',
    example: 'pi_1234567890',
  })
  @IsString()
  paymentIntentId: string;
}

export class RetryPaymentDto {
  @ApiProperty({
    description: 'Subscription ID to retry payment for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  subscriptionId: string;

  @ApiPropertyOptional({
    description: 'New payment method ID from Stripe',
    example: 'pm_1234567890',
  })
  @IsOptional()
  @IsString()
  paymentMethodId?: string;
}

export class SetupPaymentMethodDto {
  @ApiProperty({
    description: 'Organization ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  organizationId: string;
}

export class GetPaymentMethodsDto {
  @ApiProperty({
    description: 'Organization ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  organizationId: string;
}

export class DetachPaymentMethodDto {
  @ApiProperty({
    description: 'Payment method ID to remove',
    example: 'pm_1234567890',
  })
  @IsString()
  paymentMethodId: string;
}

export class PaymentIntentResponseDto {
  @ApiProperty({
    description: 'Client secret for Stripe payment',
    example: 'pi_1234567890_secret_abcdef',
  })
  clientSecret: string;

  @ApiProperty({
    description: 'Payment intent ID',
    example: 'pi_1234567890',
  })
  paymentIntentId: string;

  @ApiProperty({
    description: 'Payment amount in dollars',
    example: 135.0,
  })
  amount: number;

  @ApiProperty({
    description: 'Payment currency',
    example: 'usd',
  })
  currency: string;

  @ApiProperty({
    description: 'Payment intent status',
    example: 'requires_payment_method',
  })
  status: string;
}

export class SetupIntentResponseDto {
  @ApiProperty({
    description: 'Client secret for Stripe setup intent',
    example: 'seti_1234567890_secret_abcdef',
  })
  clientSecret: string;

  @ApiProperty({
    description: 'Setup intent ID',
    example: 'seti_1234567890',
  })
  setupIntentId: string;
}

export class PaymentMethodDto {
  @ApiProperty({
    description: 'Payment method ID',
    example: 'pm_1234567890',
  })
  id: string;

  @ApiProperty({
    description: 'Payment method type',
    example: 'card',
  })
  type: string;

  @ApiProperty({
    description: 'Card details',
  })
  card: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };

  @ApiProperty({
    description: 'Creation timestamp',
  })
  created: number;
}

export class PaymentMethodsResponseDto {
  @ApiProperty({
    description: 'List of saved payment methods',
    type: [PaymentMethodDto],
  })
  paymentMethods: PaymentMethodDto[];
}

export class WebhookEventDto {
  @ApiProperty({
    description: 'Webhook event type',
    example: 'payment_intent.succeeded',
  })
  eventType: string;

  @ApiProperty({
    description: 'Webhook event ID',
    example: 'evt_1234567890',
  })
  eventId: string;
}

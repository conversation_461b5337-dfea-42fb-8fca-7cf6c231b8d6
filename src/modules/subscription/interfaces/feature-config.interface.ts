/**
 * Interface for feature configuration
 */
export interface FeatureConfig {
  /** Feature ID */
  featureId: string;

  /** Purpose of credit usage */
  purpose?: string;

  /** Whether to skip credit check in development environment */
  skipInDev?: boolean;

  /**
   * Optional override for the amount of credits to use
   * This should only be used in special cases where you need to override the plan-based cost
   * In most cases, leave this undefined to use the cost from the user's plan
   */
  overrideAmount?: number;
}

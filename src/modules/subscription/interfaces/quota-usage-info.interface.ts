import { QuotaUsageSource } from '../entities/quota-usage-log.entity';

/**
 * Interface for quota usage information
 */
export interface QuotaUsageInfo {
  /** User ID */
  userId: string;
  /** Organization ID */
  organizationId: string;
  /** Feature ID */
  featureId: string;
  /** Amount used */
  amount: number;
  /** Unit of measurement */
  unit: string;
  /** Source of quota (user or organization) */
  usedSource: QuotaUsageSource;
  /** ID of the quota that was used */
  sourceQuotaId: string;
  /** ID of the usage log */
  usageLogId?: string;
  /** Optional metadata for complex usage scenarios */
  metadata?: {
    /** Whether multiple sources were used */
    multiSource?: boolean;
    /** Details of all sources used */
    sources?: Array<{
      quotaId: string;
      source: string;
      amount: number;
      usageLogId?: string;
    }>;
  };
}

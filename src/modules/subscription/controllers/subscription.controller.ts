import { Body, Controller, Get, Post, UseGuards, Req, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { SubscriptionService } from '../services/subscription.service';
import { PaymentService } from '../services/payment.service';
import { QuotaService } from '../services/quota.service';
import { AuthenticationGuard } from '../../auth/guards/auth.guard';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import { PLANS } from 'src/configs/plans.config';
import { FEATURES } from 'src/configs/features.config';
import { PLAN_FEATURES } from 'src/configs/plan-features.config';
import { CalculatePriceDto, SubscriptionPriceResponseDto } from '../dto/calculate-price.dto';
import { RenewInterval } from '../entities/subscription.entity';
import { RoleEnum } from 'src/modules/user/entities/role.entity';

@ApiTags('Subscriptions')
@Controller('subscriptions')
export class SubscriptionController extends BaseAbstractService {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly paymentService: PaymentService,
    private readonly quotaService: QuotaService,
    readonly i18nService: I18nService,
  ) {
    super(i18nService);
  }

  @Get('plans')
  @ApiOperation({ summary: 'Get all available plans with their features' })
  async getPlans() {
    try {
      const plans = Object.values(PLANS);

      const plansWithFeatures = plans.map((plan) => {
        const planFeatures = PLAN_FEATURES[plan.id] || {};
        const featuresDetails = {};

        Object.keys(planFeatures).forEach((featureId) => {
          const featureConfig = FEATURES[featureId];
          if (featureConfig) {
            featuresDetails[featureId] = {
              ...featureConfig,
              ...planFeatures[featureId],
            };
          }
        });

        return {
          ...plan,
          features: featuresDetails,
        };
      });

      return this.formatOutputData({ key: 'GET_PLANS_SUCCESS' }, { data: plansWithFeatures });
    } catch (error) {
      return this.throwCommonMessage('GET_PLANS_ERROR', error);
    }
  }

  @Get('my-subscription')
  @UseGuards(AuthenticationGuard)
  @ApiOperation({ summary: 'Get current user subscription' })
  async getMySubscription(@Req() req) {
    try {
      // TODO: Need view-as?
      const { organizationId } = req.viewAsUser;

      const subscription =
        await this.subscriptionService.getActiveSubscriptionForOrganization(organizationId);

      if (!subscription) {
        return this.formatOutputData({ key: 'GET_SUBSCRIPTION_SUCCESS' }, { data: null });
      }

      const plan = PLANS[subscription.planId];
      const planFeatures = PLAN_FEATURES[subscription.planId] || {};
      const featuresDetails = {};

      Object.keys(planFeatures).forEach((featureId) => {
        const featureConfig = FEATURES[featureId];
        if (featureConfig) {
          featuresDetails[featureId] = {
            ...featureConfig,
            ...planFeatures[featureId],
          };
        }
      });

      const result = {
        subscription,
        plan,
        features: featuresDetails,
      };

      return this.formatOutputData({ key: 'GET_SUBSCRIPTION_SUCCESS' }, { data: result });
    } catch (error) {
      return this.throwCommonMessage('GET_SUBSCRIPTION_ERROR', error);
    }
  }

  @Post('calculate-price')
  @ApiOperation({
    summary: 'Calculate subscription price based on plan, licenses, and billing cycle',
  })
  @ApiResponse({
    status: 200,
    description: 'Price calculated successfully',
    type: SubscriptionPriceResponseDto,
  })
  async calculatePrice(@Body() calculatePriceDto: CalculatePriceDto) {
    try {
      const { planId, licenseCount, billingCycle } = calculatePriceDto;

      const plan = PLANS[planId];
      if (!plan) {
        return this.throwCommonMessage('CALCULATE_PRICE_ERROR', 'Plan not found');
      }

      const basePrice = plan.price;
      const baseTotal = basePrice * licenseCount;

      // Calculate billing cycle multiplier and discount
      let billingCycleMultiplier = 1;
      let discountPercentage = 0;

      switch (billingCycle) {
        case RenewInterval.ANNUALLY:
          billingCycleMultiplier = 12;
          break;
        case RenewInterval.QUARTERLY:
          billingCycleMultiplier = 3;
          break;
        case RenewInterval.BIANNUALLY:
          billingCycleMultiplier = 6;
          break;
        default:
          billingCycleMultiplier = 1;
          discountPercentage = 0;
      }

      const subtotal = baseTotal * billingCycleMultiplier;
      const discountAmount = (subtotal * discountPercentage) / 100;
      const finalTotal = subtotal - discountAmount;

      const response: SubscriptionPriceResponseDto = {
        plan: {
          id: plan.id,
          name: plan.name,
          basePrice,
          currency: plan.currency,
        },
        licenseCount,
        billingCycle,
        baseTotal,
        discountPercentage,
        discountAmount,
        finalTotal,
        breakdown: {
          pricePerLicense: basePrice,
          totalLicenses: licenseCount,
          subtotal,
          billingCycleMultiplier,
          discount: discountAmount,
          total: finalTotal,
        },
      };

      return this.formatOutputData({ key: 'CALCULATE_PRICE_SUCCESS' }, { data: response });
    } catch (error) {
      return this.throwCommonMessage('CALCULATE_PRICE_ERROR', error);
    }
  }

  // Dashboard endpoints
  @Get('dashboard/subscription-info')
  @UseGuards(AuthenticationGuard)
  @ApiOperation({ summary: 'Get detailed subscription information for dashboard' })
  @ApiResponse({
    status: 200,
    description: 'Subscription information retrieved successfully',
  })
  async getDashboardSubscriptionInfo(@Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const subscriptionInfo = await this.getDashboardSubscriptionInfoData(organizationId);

      return this.formatOutputData(
        { key: 'GET_SUBSCRIPTION_INFO_SUCCESS' },
        { data: subscriptionInfo },
      );
    } catch (error) {
      return this.throwCommonMessage('GET_SUBSCRIPTION_INFO_ERROR', error);
    }
  }

  @Get('dashboard/billing-info')
  @UseGuards(AuthenticationGuard)
  @ApiOperation({ summary: 'Get billing information for dashboard' })
  @ApiResponse({
    status: 200,
    description: 'Billing information retrieved successfully',
  })
  async getDashboardBillingInfo(@Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const billingInfo = await this.getDashboardBillingInfoData(organizationId);

      return this.formatOutputData({ key: 'GET_BILLING_INFO_SUCCESS' }, { data: billingInfo });
    } catch (error) {
      return this.throwCommonMessage('GET_BILLING_INFO_ERROR', error);
    }
  }

  @Get('dashboard/payment-history')
  @UseGuards(AuthenticationGuard)
  @ApiOperation({ summary: 'Get payment history for dashboard' })
  @ApiResponse({
    status: 200,
    description: 'Payment history retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by payment status',
  })
  @ApiQuery({ name: 'startDate', required: false, type: Date, description: 'Start date filter' })
  @ApiQuery({ name: 'endDate', required: false, type: Date, description: 'End date filter' })
  async getDashboardPaymentHistory(@Query() query: any, @Req() req) {
    try {
      const { organizationId } = req.viewAsUser;

      // Set defaults for query parameters
      const queryWithDefaults = {
        page: query.page || 1,
        limit: query.limit || 10,
        status: query.status,
        startDate: query.startDate,
        endDate: query.endDate,
      };

      const paymentHistory = await this.paymentService.getPaymentHistoryForDashboard(
        organizationId,
        queryWithDefaults,
      );

      return this.formatOutputData(
        { key: 'GET_PAYMENT_HISTORY_SUCCESS' },
        { data: paymentHistory },
      );
    } catch (error) {
      return this.throwCommonMessage('GET_PAYMENT_HISTORY_ERROR', error);
    }
  }

  @Get('dashboard/plan-features')
  @UseGuards(AuthenticationGuard)
  @ApiOperation({ summary: 'Get plan features with usage information' })
  @ApiResponse({ status: 200, description: 'Plan features retrieved successfully' })
  async getDashboardPlanFeatures(@Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const features = await this.getDashboardPlanFeaturesData(organizationId);

      return this.formatOutputData({ key: 'GET_PLAN_FEATURES_SUCCESS' }, { data: features });
    } catch (error) {
      return this.throwCommonMessage('GET_PLAN_FEATURES_ERROR', error);
    }
  }

  @Get('dashboard/organization-info')
  @UseGuards(AuthenticationGuard)
  @ApiOperation({ summary: 'Get organization information for dashboard' })
  @ApiResponse({ status: 200, description: 'Organization information retrieved successfully' })
  async getDashboardOrganizationInfo(@Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const organizationInfo = await this.getDashboardOrganizationInfoData(organizationId);

      if (!organizationInfo) {
        return this.throwCommonMessage('GET_ORGANIZATION_INFO_ERROR', 'Organization not found');
      }

      return this.formatOutputData(
        { key: 'GET_ORGANIZATION_INFO_SUCCESS' },
        { data: organizationInfo },
      );
    } catch (error) {
      return this.throwCommonMessage('GET_ORGANIZATION_INFO_ERROR', error);
    }
  }

  @Get('dashboard/payment-methods')
  @UseGuards(AuthenticationGuard)
  @ApiOperation({ summary: 'Get saved payment methods (cards) for dashboard' })
  @ApiResponse({ status: 200, description: 'Payment methods retrieved successfully' })
  async getDashboardPaymentMethods(@Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const paymentMethods = await this.getDashboardPaymentMethodsData(organizationId);

      return this.formatOutputData(
        { key: 'GET_PAYMENT_METHODS_SUCCESS' },
        { data: paymentMethods },
      );
    } catch (error) {
      return this.throwCommonMessage('GET_PAYMENT_METHODS_ERROR', error);
    }
  }

  @Get('dashboard')
  @UseGuards(AuthenticationGuard)
  @ApiOperation({ summary: 'Get complete dashboard data for all tabs' })
  @ApiResponse({ status: 200, description: 'Complete dashboard data retrieved successfully' })
  async getCompleteDashboard(@Req() req, @Query() { orgId }: { orgId?: string }) {
    try {
      const userRole = req.viewAsUser.role?.keyCode || req.viewAsUser.role;
      const isSuperAdmin = userRole === RoleEnum.SUPER_ADMIN || userRole === RoleEnum.SALES;

      const organizationId = isSuperAdmin && orgId ? orgId : req.viewAsUser.organizationId;

      // Get all dashboard data in parallel
      const [subscriptionInfo, billingInfo, planFeatures, organizationInfo, paymentMethods] =
        await Promise.all([
          this.getDashboardSubscriptionInfoData(organizationId),
          this.getDashboardBillingInfoData(organizationId),
          this.getDashboardPlanFeaturesData(organizationId),
          this.getDashboardOrganizationInfoData(organizationId),
          this.getDashboardPaymentMethodsData(organizationId),
        ]);

      const dashboardData = {
        subscription: subscriptionInfo,
        billing: billingInfo,
        features: planFeatures,
        organization: organizationInfo,
        paymentMethods,
        notifications: [], // Placeholder for notifications
      };

      return this.formatOutputData(
        { key: 'GET_COMPLETE_DASHBOARD_SUCCESS' },
        { data: dashboardData },
      );
    } catch (error) {
      return this.throwCommonMessage('GET_COMPLETE_DASHBOARD_ERROR', error);
    }
  }

  // Helper methods for data fetching
  private async getDashboardSubscriptionInfoData(organizationId: string) {
    const subscription =
      await this.subscriptionService.getActiveSubscriptionForOrganization(organizationId);

    if (!subscription) return null;

    const plan = PLANS[subscription.planId];
    if (!plan) return null;

    const usedLicenses = await this.subscriptionService.getUsedLicenseCount(subscription.id);
    const pricePerLicense = plan.price;
    const totalMonthlyCost = pricePerLicense * subscription.licenseCount;

    let billingMultiplier = 1;
    switch (subscription.renewInterval) {
      case 'annually':
        billingMultiplier = 12;
        break;
      case 'quarterly':
        billingMultiplier = 3;
        break;
      case 'biannually':
        billingMultiplier = 6;
        break;
    }

    const currentBillingCycleCost = totalMonthlyCost * billingMultiplier;

    return {
      id: subscription.id,
      planId: subscription.planId,
      planName: plan.name,
      planDescription: plan.description,
      status: subscription.status,
      startDate: subscription.startDate,
      nextBillingDate: subscription.nextBillingDate,
      endDate: subscription.endDate,
      renewInterval: subscription.renewInterval,
      autoRenew: subscription.autoRenew,
      licenseCount: subscription.licenseCount,
      usedLicenseCount: usedLicenses,
      availableLicenseCount: subscription.licenseCount - usedLicenses,
      isTrial: subscription.isTrial,
      trialEndDate: subscription.trialEndDate,
      gracePeriodEnd: subscription.gracePeriodEnd,
      failedPaymentCount: subscription.failedPaymentCount,
      pricePerLicense,
      totalMonthlyCost,
      currentBillingCycleCost,
    };
  }

  private async getDashboardBillingInfoData(organizationId: string) {
    const subscription =
      await this.subscriptionService.getActiveSubscriptionForOrganization(organizationId);

    if (!subscription) return null;

    return await this.paymentService.getBillingInfoForDashboard(organizationId, subscription);
  }

  private async getDashboardPlanFeaturesData(organizationId: string) {
    const subscription =
      await this.subscriptionService.getActiveSubscriptionForOrganization(organizationId);

    if (!subscription) return [];

    const planFeatures = PLAN_FEATURES[subscription.planId] || {};
    const features = [];

    for (const [featureId, featureConfig] of Object.entries(planFeatures)) {
      const featureInfo = FEATURES[featureId];
      if (!featureInfo) continue;

      const unlimited = featureConfig.unlimited || false;
      const limit = unlimited ? -1 : featureConfig.amount || 0;

      // Get current usage from quota service
      let currentUsage = 0;
      try {
        const quota = await this.quotaService.getOrganizationQuota(organizationId, featureId);
        if (quota) {
          currentUsage = quota.originalAmount - quota.remaining;
        }
      } catch (error) {
        console.warn(`Failed to get quota for feature ${featureId}:`, error.message);
      }

      const usagePercentage =
        unlimited || limit <= 0 ? 0 : Math.round((currentUsage / limit) * 100);

      features.push({
        id: featureId,
        name: featureInfo.name,
        description: featureInfo.description,
        unit: featureInfo.unit,
        limit,
        unlimited,
        currentUsage,
        usagePercentage,
      });
    }

    return features;
  }

  private async getDashboardOrganizationInfoData(organizationId: string) {
    const orgRepo = this.subscriptionService['dataSource'].getRepository('organizations');
    const organization = await orgRepo.findOne({ where: { id: organizationId } });

    if (!organization) return null;

    const userRepo = this.subscriptionService['dataSource'].getRepository('users');
    const [totalUsers, activeUsers] = await Promise.all([
      userRepo.count({ where: { organizationId } }),
      userRepo.count({ where: { organizationId, status: 'ACTIVE' } }),
    ]);

    return {
      id: organization.id,
      name: organization.name,
      email: organization.email,
      totalUsers,
      activeUsers,
      createdAt: organization.createdAt,
    };
  }

  private async getDashboardPaymentMethodsData(organizationId: string) {
    try {
      // Get payment methods from PaymentService
      const paymentMethods = await this.paymentService.getPaymentMethods(organizationId);

      return paymentMethods || [];
    } catch (error) {
      console.warn(
        `Failed to get payment methods for organization ${organizationId}:`,
        error.message,
      );
      return [];
    }
  }
}

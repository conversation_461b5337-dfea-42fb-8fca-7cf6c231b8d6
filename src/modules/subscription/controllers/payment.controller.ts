import {
  Body,
  Controller,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Post,
  RawBody,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiHeader } from '@nestjs/swagger';
import { PaymentService } from '../services/payment.service';
import { PayForSubscriptionDto } from '../dto/payment.dto';
import { AuthenticationGuard } from '../../auth/guards/auth.guard';
import { PermissionGuard } from '../../../guards/permission.guard';
import { Permission } from '../../../common/decorators/permissions.decorator';
import { PermissionResource } from '../../../common/constants/permission.constant';
import { ResourceEnum } from '../../user/entities/permission.entity';
import { BaseAbstractService } from '../../../base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';

@ApiTags('Payments')
@Controller('payments')
export class PaymentController extends BaseAbstractService {
  private readonly logger = new Logger(PaymentController.name);

  constructor(
    private readonly paymentService: PaymentService,
    readonly i18nService: I18nService,
  ) {
    super(i18nService);
  }

  @Post('pay-for-subscription')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  // @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Create payment intent for existing subscription' })
  @ApiResponse({ status: 200, description: 'Payment intent created for subscription' })
  async payForSubscription(@Body() payForSubscriptionDto: PayForSubscriptionDto, @Req() req) {
    try {
      const { subscriptionId, paymentMethodId, savePaymentMethod, returnUrl } =
        payForSubscriptionDto;

      // Validate that subscription belongs to user's organization
      const userOrganizationId = req.viewAsUser.organizationId;

      const paymentIntent = await this.paymentService.createPaymentIntentForSubscription({
        subscriptionId,
        organizationId: userOrganizationId,
        paymentMethodId,
        savePaymentMethod,
        returnUrl,
      });

      return this.formatOutputData(
        { key: 'CREATE_PAYMENT_FOR_SUBSCRIPTION_SUCCESS' },
        { data: paymentIntent },
      );
    } catch (error) {
      return this.throwCommonMessage('CREATE_PAYMENT_FOR_SUBSCRIPTION_ERROR', error);
    }
  }

  @Post('confirm-payment')
  // @UseGuards(AuthenticationGuard, PermissionGuard)
  // @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Confirm payment and activate subscription' })
  @ApiResponse({ status: 200, description: 'Payment confirmed and subscription activated' })
  async confirmPayment(@Body() body: { paymentIntentId: string }, @Req() req) {
    try {
      const { paymentIntentId } = body;

      const subscription = await this.paymentService.processSuccessfulPayment(paymentIntentId);

      return this.formatOutputData(
        { key: 'CONFIRM_PAYMENT_SUCCESS' },
        {
          data: {
            subscription,
            message: 'Payment confirmed and subscription activated successfully',
          },
        },
      );
    } catch (error) {
      return this.throwCommonMessage('CONFIRM_PAYMENT_ERROR', error);
    }
  }

  @Post('webhook')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle Stripe webhook events' })
  @ApiHeader({ name: 'stripe-signature', description: 'Stripe webhook signature' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid webhook signature or payload' })
  @ApiResponse({ status: 500, description: 'Webhook processing error' })
  async handleWebhook(@Headers('stripe-signature') signature: string, @RawBody() rawBody: Buffer) {
    const startTime = Date.now();
    let event: any = null;

    try {
      // Validate required headers and body
      if (!signature) {
        this.logger.error('Webhook received without stripe-signature header');
        return this.throwCommonMessage('WEBHOOK_ERROR', 'Missing stripe-signature header');
      }

      if (!rawBody || rawBody.length === 0) {
        this.logger.error('Webhook received with empty body');
        return this.throwCommonMessage('WEBHOOK_ERROR', 'Empty webhook body');
      }

      this.logger.log(`Received webhook with signature: ${signature.substring(0, 20)}...`);

      // Verify webhook signature and get event
      try {
        event = await this.paymentService.verifyWebhookSignature(rawBody, signature);
        this.logger.log(
          `Webhook signature verified successfully for event: ${event.type} (${event.id})`,
        );
      } catch (err) {
        this.logger.error(`Webhook signature verification failed: ${err.message}`);
        return this.throwCommonMessage(
          'WEBHOOK_SIGNATURE_ERROR',
          `Webhook signature verification failed: ${err.message}`,
        );
      }

      // Log webhook event details
      this.logger.log(
        `Processing webhook event: ${event.type} (${event.id}) - API Version: ${event.api_version}`,
      );

      // Process the event
      await this.paymentService.handleWebhookEvent(event);

      const processingTime = Date.now() - startTime;
      this.logger.log(
        `Webhook event ${event.type} (${event.id}) processed successfully in ${processingTime}ms`,
      );

      return this.formatOutputData(
        { key: 'WEBHOOK_PROCESSED_SUCCESS' },
        {
          data: {
            eventType: event.type,
            eventId: event.id,
            processingTimeMs: processingTime,
            apiVersion: event.api_version,
          },
        },
      );
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const eventInfo = event ? `${event.type} (${event.id})` : 'unknown';

      this.logger.error(
        `Webhook processing error for event ${eventInfo} after ${processingTime}ms: ${error.message}`,
        error.stack,
      );

      // For webhook endpoints, we should return 200 even on processing errors
      // to prevent Stripe from retrying the webhook unnecessarily
      // Only return error status for signature verification failures
      if (error.message?.includes('signature verification')) {
        return this.throwCommonMessage('WEBHOOK_PROCESSING_ERROR', error);
      }

      // Log the error but return success to prevent retries for business logic errors
      return this.formatOutputData(
        { key: 'WEBHOOK_PROCESSED_WITH_ERROR' },
        {
          data: {
            eventType: event?.type || 'unknown',
            eventId: event?.id || 'unknown',
            error: error.message,
            processingTimeMs: processingTime,
          },
        },
      );
    }
  }

  @Post('create-payment-intent-for-additional-licenses')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Create payment intent for additional licenses with pro-rata billing' })
  @ApiResponse({ status: 200, description: 'Payment intent created successfully' })
  async createPaymentIntentForAdditionalLicenses(
    @Body() body: { subscriptionId: string; additionalLicenseCount: number },
    @Req() req,
  ) {
    try {
      const { organizationId } = req.viewAsUser;
      const { subscriptionId, additionalLicenseCount } = body;

      if (!subscriptionId || !additionalLicenseCount || additionalLicenseCount <= 0) {
        return this.throwCommonMessage(
          'CREATE_PAYMENT_INTENT_ERROR',
          'Invalid subscription ID or license count',
        );
      }

      const paymentIntent = await this.paymentService.createPaymentIntentForAdditionalLicenses(
        subscriptionId,
        additionalLicenseCount,
        organizationId,
      );

      return this.formatOutputData(
        { key: 'CREATE_PAYMENT_INTENT_SUCCESS' },
        {
          data: {
            paymentIntent,
            message: 'Payment intent created successfully for additional licenses',
          },
        },
      );
    } catch (error) {
      return this.throwCommonMessage('CREATE_PAYMENT_INTENT_ERROR', error);
    }
  }

  @Get('calculate-pro-rata/:subscriptionId/:additionalLicenseCount')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({ summary: 'Calculate pro-rata amount for additional licenses' })
  @ApiResponse({ status: 200, description: 'Pro-rata calculation completed' })
  async calculateProRataAmount(
    @Param('subscriptionId') subscriptionId: string,
    @Param('additionalLicenseCount') additionalLicenseCount: string,
  ) {
    try {
      const licenseCount = parseInt(additionalLicenseCount);
      if (isNaN(licenseCount) || licenseCount <= 0) {
        return this.throwCommonMessage(
          'CALCULATE_PRO_RATA_ERROR',
          'Invalid additional license count',
        );
      }

      const calculation = await this.paymentService.calculateProRataAmountForAdditionalLicenses(
        subscriptionId,
        licenseCount,
      );

      return this.formatOutputData(
        { key: 'CALCULATE_PRO_RATA_SUCCESS' },
        {
          data: {
            calculation,
            message: 'Pro-rata calculation completed successfully',
          },
        },
      );
    } catch (error) {
      return this.throwCommonMessage('CALCULATE_PRO_RATA_ERROR', error);
    }
  }

  @Post('retry-payment')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Retry failed payment for subscription' })
  @ApiResponse({ status: 200, description: 'Payment retry initiated successfully' })
  async retryPayment(
    @Body() body: { subscriptionId: string; paymentMethodId?: string },
    @Req() req,
  ) {
    try {
      const { subscriptionId, paymentMethodId } = body;

      // Validate that subscription belongs to user's organization
      const userOrganizationId = req.viewAsUser.organizationId;

      const paymentIntent = await this.paymentService.createPaymentIntentForSubscription({
        subscriptionId,
        organizationId: userOrganizationId,
        paymentMethodId,
        savePaymentMethod: true,
      });

      return this.formatOutputData({ key: 'RETRY_PAYMENT_SUCCESS' }, { data: paymentIntent });
    } catch (error) {
      return this.throwCommonMessage('RETRY_PAYMENT_ERROR', error);
    }
  }

  @Post('setup-payment-method')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Setup payment method for future payments' })
  @ApiResponse({ status: 200, description: 'Payment method setup successfully' })
  async setupPaymentMethod(@Body() body: { organizationId: string }, @Req() req) {
    try {
      const { organizationId } = body;

      // Validate that user belongs to the organization
      const userOrganizationId = req.viewAsUser.organizationId;
      if (organizationId !== userOrganizationId) {
        return this.throwCommonMessage(
          'UNAUTHORIZED_ORGANIZATION_ACCESS',
          'Cannot setup payment method for different organization',
        );
      }

      // Create setup intent for saving payment method
      const setupIntent = await this.paymentService.createSetupIntent(organizationId);

      return this.formatOutputData(
        { key: 'SETUP_PAYMENT_METHOD_SUCCESS' },
        {
          data: {
            clientSecret: setupIntent.client_secret,
            setupIntentId: setupIntent.id,
          },
        },
      );
    } catch (error) {
      return this.throwCommonMessage('SETUP_PAYMENT_METHOD_ERROR', error);
    }
  }

  @Get('cards')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({ summary: 'Get saved payment methods (cards) for organization' })
  @ApiResponse({ status: 200, description: 'Payment methods retrieved successfully' })
  async getCards(@Req() req) {
    try {
      // Get organization ID from authenticated user
      const { organizationId } = req.viewAsUser;

      // Get payment methods with default info from PaymentService
      const paymentMethods = await this.paymentService.getPaymentMethodsWithDefault(organizationId);

      const data = {
        hasPaymentMethods: paymentMethods.length > 0,
        paymentMethods,
        defaultPaymentMethod: paymentMethods.find(pm => pm.isDefault) || null
      };

      return this.formatOutputData(
        { key: 'GET_CARDS_SUCCESS' },
        { data },
      );
    } catch (error) {
      return this.throwCommonMessage('GET_CARDS_ERROR', error);
    }
  }

  @Post('get-payment-methods')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({ summary: 'Get saved payment methods for organization (legacy)' })
  @ApiResponse({ status: 200, description: 'Payment methods retrieved successfully' })
  async getPaymentMethods(@Body() body: { organizationId: string }, @Req() req) {
    try {
      const { organizationId } = body;

      // Validate that user belongs to the organization
      const userOrganizationId = req.viewAsUser.organizationId;
      if (organizationId !== userOrganizationId) {
        return this.throwCommonMessage(
          'UNAUTHORIZED_ORGANIZATION_ACCESS',
          'Cannot access payment methods for different organization',
        );
      }

      // Get payment methods from PaymentService
      const paymentMethods = await this.paymentService.getPaymentMethods(organizationId);

      return this.formatOutputData(
        { key: 'GET_PAYMENT_METHODS_SUCCESS' },
        { data: { paymentMethods } },
      );
    } catch (error) {
      return this.throwCommonMessage('GET_PAYMENT_METHODS_ERROR', error);
    }
  }

  @Post('detach-payment-method')
  @UseGuards(AuthenticationGuard, PermissionGuard)
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Remove saved payment method' })
  @ApiResponse({ status: 200, description: 'Payment method removed successfully' })
  async detachPaymentMethod(@Body() body: { paymentMethodId: string }) {
    try {
      const { paymentMethodId } = body;

      await this.paymentService.detachPaymentMethod(paymentMethodId);

      return this.formatOutputData(
        { key: 'DETACH_PAYMENT_METHOD_SUCCESS' },
        { data: { message: 'Payment method removed successfully' } },
      );
    } catch (error) {
      return this.throwCommonMessage('DETACH_PAYMENT_METHOD_ERROR', error);
    }
  }

  @Get('webhook/events')
  @UseGuards(AuthenticationGuard)
  @ApiOperation({ summary: 'Get recent webhook events from Stripe' })
  @ApiResponse({ status: 200, description: 'Recent webhook events retrieved' })
  async getWebhookEvents(@Req() req) {
    try {
      const { organizationId } = req.viewAsUser;

      // Get recent webhook events for this organization's customer
      const events = await this.paymentService.getRecentWebhookEvents(organizationId);

      return this.formatOutputData({ key: 'GET_WEBHOOK_EVENTS_SUCCESS' }, { data: events });
    } catch (error) {
      return this.throwCommonMessage('GET_WEBHOOK_EVENTS_ERROR', error);
    }
  }
}

import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { LicenseService } from '../services/license.service';
import { PaymentService } from '../services/payment.service';
import { SubscriptionService } from '../services/subscription.service';
import { SubscriptionStatus } from '../entities/subscription.entity';
import { AuthenticationGuard } from '../../auth/guards/auth.guard';
import { PermissionGuard } from '../../../guards/permission.guard';
import { Permission } from '../../../common/decorators/permissions.decorator';
import { PermissionResource } from '../../../common/constants/permission.constant';
import { ResourceEnum } from '../../user/entities/permission.entity';
import { BaseAbstractService } from '../../../base/base.abstract.service';
import { I18nService } from 'nestjs-i18n';
import {
  AssignLicenseDto,
  TransferLicenseDto,
  LicenseQueryDto,
  LicenseResponseDto,
  LicenseListResponseDto,
  AddLicensesToSubscriptionDto,
  AddLicensesResponseDto,
  AddLicensesWithPaymentDto,
  AddLicensesWithPaymentResponseDto,
  PreviewLicenseFeesDto,
  PreviewLicenseFeesResponseDto,
} from '../dto/license.dto';

@ApiTags('Licenses')
@Controller('licenses')
@UseGuards(AuthenticationGuard, PermissionGuard)
export class LicenseController extends BaseAbstractService {
  constructor(
    private readonly licenseService: LicenseService,
    private readonly paymentService: PaymentService,
    private readonly subscriptionService: SubscriptionService,
    readonly i18nService: I18nService,
  ) {
    super(i18nService);
  }

  @Get()
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({ summary: 'Get licenses for organization' })
  @ApiResponse({
    status: 200,
    description: 'Licenses retrieved successfully',
    type: LicenseListResponseDto,
  })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by license status' })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by assigned user ID' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  async getLicenses(@Req() req, @Query() query: LicenseQueryDto) {
    try {
      const { organizationId } = req.viewAsUser;
      const licenses = await this.licenseService.getLicensesForOrganization(organizationId, query);

      return this.formatOutputData({ key: 'GET_LICENSES_SUCCESS' }, { data: licenses });
    } catch (error) {
      return this.throwCommonMessage('GET_LICENSES_ERROR', error);
    }
  }

  @Get(':id')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({ summary: 'Get license details by ID' })
  @ApiResponse({
    status: 200,
    description: 'License retrieved successfully',
    type: LicenseResponseDto,
  })
  @ApiParam({ name: 'id', description: 'License ID' })
  async getLicenseById(@Param('id') id: string, @Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const license = await this.licenseService.getLicenseById(id, organizationId);

      return this.formatOutputData({ key: 'GET_LICENSE_SUCCESS' }, { data: license });
    } catch (error) {
      return this.throwCommonMessage('GET_LICENSE_ERROR', error);
    }
  }

  @Post(':id/assign')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Assign license to user' })
  @ApiResponse({
    status: 200,
    description: 'License assigned successfully',
    type: LicenseResponseDto,
  })
  @ApiParam({ name: 'id', description: 'License ID' })
  async assignLicense(
    @Param('id') id: string,
    @Body() assignLicenseDto: AssignLicenseDto,
    @Req() req,
  ) {
    try {
      const performedBy = req.user.id;
      const { organizationId } = req.viewAsUser;

      // Validate that the user belongs to the same organization
      await this.licenseService.validateUserInOrganization(assignLicenseDto.userId, organizationId);

      const license = await this.licenseService.assignLicenseToUser(
        id,
        assignLicenseDto.userId,
        performedBy,
      );

      return this.formatOutputData({ key: 'ASSIGN_LICENSE_SUCCESS' }, { data: license });
    } catch (error) {
      return this.throwCommonMessage('ASSIGN_LICENSE_ERROR', error);
    }
  }

  @Post(':id/transfer')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Transfer license to another user' })
  @ApiResponse({
    status: 200,
    description: 'License transferred successfully',
    type: LicenseResponseDto,
  })
  @ApiParam({ name: 'id', description: 'License ID' })
  async transferLicense(
    @Param('id') id: string,
    @Body() transferLicenseDto: TransferLicenseDto,
    @Req() req,
  ) {
    try {
      const performedBy = req.user.id;
      const { organizationId } = req.viewAsUser;

      // Validate that the target user belongs to the same organization
      await this.licenseService.validateUserInOrganization(
        transferLicenseDto.toUserId,
        organizationId,
      );

      const license = await this.licenseService.transferLicense(
        id,
        transferLicenseDto.toUserId,
        transferLicenseDto.reason,
        performedBy,
      );

      return this.formatOutputData({ key: 'TRANSFER_LICENSE_SUCCESS' }, { data: license });
    } catch (error) {
      return this.throwCommonMessage('TRANSFER_LICENSE_ERROR', error);
    }
  }

  @Delete(':id/unassign')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Unassign license from user' })
  @ApiResponse({
    status: 200,
    description: 'License unassigned successfully',
    type: LicenseResponseDto,
  })
  @ApiParam({ name: 'id', description: 'License ID' })
  async unassignLicense(@Param('id') id: string, @Req() req) {
    try {
      const performedBy = req.user.id;
      const { organizationId } = req.viewAsUser;

      const license = await this.licenseService.unassignLicense(id, organizationId, performedBy);

      return this.formatOutputData({ key: 'UNASSIGN_LICENSE_SUCCESS' }, { data: license });
    } catch (error) {
      return this.throwCommonMessage('UNASSIGN_LICENSE_ERROR', error);
    }
  }

  @Get('organization/summary')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({ summary: 'Get license summary for organization' })
  @ApiResponse({ status: 200, description: 'License summary retrieved successfully' })
  async getLicenseSummary(@Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const summary = await this.licenseService.getLicenseSummaryForOrganization(organizationId);

      return this.formatOutputData({ key: 'GET_LICENSE_SUMMARY_SUCCESS' }, { data: summary });
    } catch (error) {
      return this.throwCommonMessage('GET_LICENSE_SUMMARY_ERROR', error);
    }
  }

  @Get('user/:userId/history')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({ summary: 'Get license history for user' })
  @ApiResponse({ status: 200, description: 'License history retrieved successfully' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  async getUserLicenseHistory(@Param('userId') userId: string, @Req() req) {
    try {
      const { organizationId } = req.viewAsUser;

      // Validate that the user belongs to the same organization
      await this.licenseService.validateUserInOrganization(userId, organizationId);

      const history = await this.licenseService.getLicenseHistoryForUser(userId);

      return this.formatOutputData({ key: 'GET_LICENSE_HISTORY_SUCCESS' }, { data: history });
    } catch (error) {
      return this.throwCommonMessage('GET_LICENSE_HISTORY_ERROR', error);
    }
  }

  @Get('available/count')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({ summary: 'Get available license count for organization' })
  @ApiResponse({ status: 200, description: 'Available license count retrieved successfully' })
  async getAvailableLicenseCount(@Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const count = await this.licenseService.getAvailableLicenseCount(organizationId);

      return this.formatOutputData(
        { key: 'GET_AVAILABLE_LICENSE_COUNT_SUCCESS' },
        { data: { availableCount: count } },
      );
    } catch (error) {
      return this.throwCommonMessage('GET_AVAILABLE_LICENSE_COUNT_ERROR', error);
    }
  }

  @Post('preview-add-to-subscription')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({
    summary: 'Preview cost of adding additional licenses (no actual license creation)',
  })
  @ApiResponse({ status: 200, description: 'License addition cost preview calculated' })
  async previewAddLicensesToSubscription(@Body() body: AddLicensesToSubscriptionDto, @Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const { subscriptionId, additionalLicenseCount } = body;

      if (!subscriptionId || !additionalLicenseCount || additionalLicenseCount <= 0) {
        return this.throwCommonMessage(
          'PREVIEW_ADD_LICENSES_ERROR',
          'Invalid subscription ID or license count',
        );
      }

      // Get subscription to determine pricing strategy
      const subscription = await this.licenseService.getSubscriptionForPreview(
        subscriptionId,
        organizationId,
      );

      let previewData: any = {
        subscriptionId,
        additionalLicenseCount,
        currentLicenseCount: subscription.licenseCount,
        newTotalLicenseCount: subscription.licenseCount + additionalLicenseCount,
        subscriptionStatus: subscription.status,
      };

      if (subscription.status === SubscriptionStatus.PENDING) {
        // For PENDING: Will need new PaymentIntent with full amount
        const fullAmount = await this.paymentService.calculateFullSubscriptionAmount(
          subscription.licenseCount + additionalLicenseCount,
          subscription.planId,
          subscription.renewInterval,
        );

        previewData = {
          ...previewData,
          paymentType: 'full_subscription',
          amount: fullAmount,
          description: `Full subscription payment for ${subscription.licenseCount + additionalLicenseCount} licenses`,
          willCancelExistingPayment: !!subscription.stripeSubscriptionId,
        };
      } else if (subscription.status === SubscriptionStatus.ACTIVE) {
        // For ACTIVE: Pro-rata payment for additional licenses only
        const proRataCalculation =
          await this.paymentService.calculateProRataAmountForAdditionalLicenses(
            subscriptionId,
            additionalLicenseCount,
          );

        previewData = {
          ...previewData,
          paymentType: 'pro_rata_additional',
          amount: proRataCalculation.amount,
          description: proRataCalculation.description,
          remainingDays: proRataCalculation.remainingDays,
          totalDaysInCycle: proRataCalculation.totalDaysInCycle,
        };
      } else {
        return this.throwCommonMessage(
          'PREVIEW_ADD_LICENSES_ERROR',
          `Cannot add licenses to subscription with status: ${subscription.status}`,
        );
      }

      return this.formatOutputData(
        { key: 'PREVIEW_ADD_LICENSES_SUCCESS' },
        {
          data: {
            preview: previewData,
            message: 'License addition cost preview calculated successfully',
          },
        },
      );
    } catch (error) {
      return this.throwCommonMessage('PREVIEW_ADD_LICENSES_ERROR', error);
    }
  }

  @Post('add-to-subscription')
  // @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({ summary: 'Add additional licenses to existing subscription' })
  @ApiResponse({
    status: 200,
    description: 'Licenses added successfully',
    type: AddLicensesResponseDto,
  })
  async addLicensesToSubscription(@Body() body: AddLicensesToSubscriptionDto, @Req() req) {
    try {
      const { organizationId, id: userId } = req.viewAsUser;
      const { subscriptionId, additionalLicenseCount } = body;

      if (!subscriptionId || !additionalLicenseCount || additionalLicenseCount <= 0) {
        return this.throwCommonMessage(
          'ADD_LICENSES_ERROR',
          'Invalid subscription ID or license count',
        );
      }

      // Get subscription to check status
      const subscription = await this.licenseService.getSubscriptionForPreview(
        subscriptionId,
        organizationId,
      );

      if (subscription.status === SubscriptionStatus.ACTIVE) {
        // For ACTIVE subscriptions, don't create licenses immediately
        // User must pay first via create-payment-intent-for-additional-licenses
        return this.throwCommonMessage(
          'ADD_LICENSES_ERROR',
          'For active subscriptions, please use the payment flow to add licenses. Create payment intent first, then licenses will be added after successful payment.',
        );
      } else if (subscription.status === SubscriptionStatus.PENDING) {
        // For PENDING subscriptions, we can add licenses and cancel old PaymentIntent
        const result = await this.licenseService.addLicensesToSubscription(
          subscriptionId,
          additionalLicenseCount,
          organizationId,
          userId,
        );

        let message = `Successfully added ${additionalLicenseCount} licenses`;
        let paymentIntentCanceled = result.paymentIntentCanceled;

        // If there's an old PaymentIntent to cancel, cancel it now
        if (result.oldPaymentIntentId) {
          try {
            paymentIntentCanceled = await this.paymentService.cancelPaymentIntentIfExists(
              result.oldPaymentIntentId,
            );
            if (paymentIntentCanceled) {
              message +=
                '. Previous PaymentIntent has been canceled - please create a new payment.';
            } else {
              message += '. Please create a new payment with updated license count.';
            }
          } catch (error) {
            // Log error but continue - cancellation failure is not critical
            console.error(
              `Failed to cancel PaymentIntent ${result.oldPaymentIntentId}:`,
              error.message,
            );
            message += '. Please create a new payment with updated license count.';
          }
        } else if (result.needsNewPaymentIntent) {
          message += '. Please create a new payment with updated license count.';
        }

        return this.formatOutputData(
          { key: 'ADD_LICENSES_SUCCESS' },
          {
            data: {
              message,
              newLicenses: result.newLicenses,
              totalLicenseCount: result.totalLicenseCount,
              subscription: result.subscription,
              needsNewPaymentIntent: result.needsNewPaymentIntent,
              paymentIntentCanceled: result.paymentIntentCanceled,
            },
          },
        );
      } else {
        return this.throwCommonMessage(
          'ADD_LICENSES_ERROR',
          `Cannot add licenses to subscription with status: ${subscription.status}`,
        );
      }
    } catch (error) {
      return this.throwCommonMessage('ADD_LICENSES_ERROR', error);
    }
  }

  @Post('preview-fees')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Read)
  @ApiOperation({
    summary: 'Preview fees for adding additional licenses',
    description:
      'Calculate pro-rata pricing and credit allocation without creating payment intent or processing payment',
  })
  @ApiResponse({
    status: 200,
    description: 'License addition fees preview calculated successfully',
    type: PreviewLicenseFeesResponseDto,
  })
  async previewAddLicensesFees(@Body() body: PreviewLicenseFeesDto, @Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const { additionalLicenseCount } = body;

      if (!additionalLicenseCount || additionalLicenseCount <= 0) {
        return this.throwCommonMessage(
          'PREVIEW_FEES_ERROR',
          'Invalid license count. Must be greater than 0.',
        );
      }

      // Get active subscription for the organization
      const subscription =
        await this.subscriptionService.getActiveSubscriptionForOrganization(organizationId);

      if (!subscription) {
        return this.throwCommonMessage(
          'PREVIEW_FEES_ERROR',
          'No active subscription found for organization',
        );
      }

      // Calculate pro-rata amount using preview-only method
      const previewData = await this.paymentService.calculateLicenseAdditionPreview(
        subscription,
        additionalLicenseCount,
      );

      return this.formatOutputData(
        { key: 'PREVIEW_FEES_SUCCESS' },
        {
          data: {
            proRataDetails: previewData.proRataDetails,
            creditAllocation: previewData.creditAllocation,
            subscription: {
              id: subscription.id,
              planId: subscription.planId,
              currentLicenseCount: subscription.licenseCount,
              newLicenseCount: subscription.licenseCount + additionalLicenseCount,
            },
            pricing: {
              subtotal: previewData.proRataDetails.proRataPrice,
              total: previewData.proRataDetails.proRataPrice,
              currency: 'usd',
            },
            message: `Preview: Adding ${additionalLicenseCount} licenses will cost $${previewData.proRataDetails.proRataPrice} (pro-rated for ${previewData.proRataDetails.remainingDays} days).`,
          },
        },
      );
    } catch (error) {
      return this.throwCommonMessage('PREVIEW_FEES_ERROR', error);
    }
  }

  @Post('add-licenses')
  @Permission(PermissionResource[ResourceEnum.SUBSCRIPTION].Write)
  @ApiOperation({
    summary: 'Add licenses to organization subscription with pro-rata payment calculation',
    description:
      'Calculates pro-rata pricing for additional licenses, creates payment intent, and handles credit/feature allocation based on remaining cycle time',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment intent created for additional licenses with pro-rata pricing',
    type: AddLicensesWithPaymentResponseDto,
  })
  async addLicensesWithPayment(@Body() body: AddLicensesWithPaymentDto, @Req() req) {
    try {
      const { organizationId } = req.viewAsUser;
      const {
        additionalLicenseCount,
        autoConfirm = true,
        paymentMethodId,
        savePaymentMethod = false,
      } = body;

      if (!additionalLicenseCount || additionalLicenseCount <= 0) {
        return this.throwCommonMessage(
          'ADD_LICENSES_ERROR',
          'Invalid license count. Must be greater than 0.',
        );
      }

      // Get active subscription for the organization
      const subscription =
        await this.subscriptionService.getActiveSubscriptionForOrganization(organizationId);

      if (!subscription) {
        return this.throwCommonMessage(
          'ADD_LICENSES_ERROR',
          'No active subscription found for organization',
        );
      }

      // Create payment intent with pro-rata calculation and auto-confirm option
      const paymentResult = await this.paymentService.createPaymentIntentForAdditionalLicenses(
        subscription.id,
        additionalLicenseCount,
        organizationId,
        autoConfirm,
        paymentMethodId,
        savePaymentMethod,
      );

      // Handle successful auto-payment
      if (paymentResult.paymentSuccessful) {
        return this.formatOutputData(
          { key: 'ADD_LICENSES_SUCCESS' },
          {
            data: {
              paymentSuccessful: true,
              newLicenses: paymentResult.newLicenses || [],
              totalLicenseCount: paymentResult.totalLicenseCount,
              proRataDetails: paymentResult.proRataDetails,
              creditAllocation: paymentResult.creditAllocation,
              subscription: {
                id: subscription.id,
                planId: subscription.planId,
                currentLicenseCount: subscription.licenseCount,
                newLicenseCount: paymentResult.totalLicenseCount,
              },
              message: `Successfully added ${additionalLicenseCount} licenses and charged $${paymentResult.amount} pro-rata.`,
            },
          },
        );
      }

      // Handle payment error (insufficient funds, etc.)
      if (paymentResult.paymentError) {
        return this.formatOutputData(
          { key: 'ADD_LICENSES_PAYMENT_FAILED' },
          {
            data: {
              paymentError: paymentResult.paymentError,
              proRataDetails: paymentResult.proRataDetails,
              creditAllocation: paymentResult.creditAllocation,
              subscription: {
                id: subscription.id,
                planId: subscription.planId,
                currentLicenseCount: subscription.licenseCount,
                newLicenseCount: subscription.licenseCount + additionalLicenseCount,
              },
              message: `Payment failed: ${paymentResult.paymentError.message}. Please update your payment method.`,
            },
          },
        );
      }

      // Handle manual payment intent (when autoConfirm is false or no saved payment method)
      return this.formatOutputData(
        { key: 'ADD_LICENSES_WITH_PAYMENT_SUCCESS' },
        {
          data: {
            paymentIntent: {
              clientSecret: paymentResult.clientSecret,
              paymentIntentId: paymentResult.paymentIntentId,
              amount: paymentResult.amount,
              currency: paymentResult.currency,
              status: paymentResult.status,
            },
            proRataDetails: paymentResult.proRataDetails,
            creditAllocation: paymentResult.creditAllocation,
            subscription: {
              id: subscription.id,
              planId: subscription.planId,
              currentLicenseCount: subscription.licenseCount,
              newLicenseCount: subscription.licenseCount + additionalLicenseCount,
            },
            message: `Payment intent created for ${additionalLicenseCount} additional licenses. Complete payment to activate licenses.`,
          },
        },
      );
    } catch (error) {
      return this.throwCommonMessage('ADD_LICENSES_WITH_PAYMENT_ERROR', error);
    }
  }
}

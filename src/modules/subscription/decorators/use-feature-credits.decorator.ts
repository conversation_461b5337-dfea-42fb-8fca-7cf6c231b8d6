import { SetMetadata } from '@nestjs/common';
import { FeatureConfig } from '../interfaces/feature-config.interface';
import { FEATURE_CREDITS_KEY } from '../interceptors/feature-credits.interceptor';

/**
 * Decorator to mark a method as requiring feature credits
 * This will be used by the FeatureCreditsInterceptor to check and deduct credits
 *
 * Usage:
 * ```
 * @Get()
 * @UseFeatureCredits({
 *   featureId: 'credits',
 *   purpose: 'List all items',
 *   overrideAmount: 2,
 *   skipInDev: true,
 * })
 * findAll() {
 *   return [];
 * }
 * ```
 *
 * Or with just a feature ID:
 * ```
 * @Get()
 * @UseFeatureCredits('credits')
 * findAll() {
 *   return [];
 * }
 * ```
 */
export const UseFeatureCredits = (featureConfigOrId: FeatureConfig | string): MethodDecorator => {
  let featureConfig: FeatureConfig;

  if (typeof featureConfigOrId === 'string') {
    // If a string is provided, use it as featureId
    featureConfig = {
      featureId: featureConfigOrId,
      purpose: `Using feature: ${featureConfigOrId}`,
    };
  } else {
    // Otherwise, use the provided config
    featureConfig = featureConfigOrId;
  }

  return SetMetadata(FEATURE_CREDITS_KEY, featureConfig);
};

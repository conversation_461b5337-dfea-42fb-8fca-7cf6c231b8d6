import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON>x<PERSON>,
  CallH<PERSON>ler,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { CreditService } from '../services/credit.service';
import { FeatureCostService } from '../services/feature-cost.service';
import { FeatureConfig } from '../interfaces/feature-config.interface';
import { Reflector } from '@nestjs/core';
import { RoleEnum } from 'src/modules/user/entities/role.entity';

export const FEATURE_CREDITS_KEY = 'feature_credits';

@Injectable()
export class FeatureCreditsInterceptor implements NestInterceptor {
  private readonly logger = new Logger(FeatureCreditsInterceptor.name);

  constructor(
    private readonly creditService: CreditService,
    private readonly featureCostService: FeatureCostService,
    private readonly reflector: Reflector,
  ) {}

  async intercept(context: ExecutionContext, next: Call<PERSON><PERSON><PERSON>): Promise<Observable<any>> {
    const featureConfig = this.reflector.get<FeatureConfig>(
      FEATURE_CREDITS_KEY,
      context.getHandler(),
    );

    if (!featureConfig) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // Get user from request
    const user = request.viewAsUser || request.user;

    // Skip if no user in request
    if (!user || !user.id) {
      this.logger.warn('No user found in request, skipping feature check');
      return next.handle();
    }

    // Skip if user is super admin
    if (user.role === RoleEnum.SUPER_ADMIN) {
      this.logger.log(`Skipping feature check for super admin user ${user.id}`);
      return next.handle();
    }

    const userId = user.id;
    const { featureId, purpose: configPurpose, overrideAmount } = featureConfig;

    // Skip in development if configured
    if (featureConfig.skipInDev && process.env.NODE_ENV === 'development') {
      this.logger.log(`Skipping feature check for ${featureId} in development`);
      return next.handle();
    }

    // Calculate the amount based on the user's plan
    let amount: number;

    // If overrideAmount is provided, use it directly
    if (typeof overrideAmount === 'number') {
      amount = overrideAmount;
      this.logger.log(`Override cost for feature ${featureId} for user ${userId}: ${amount}`);
    } else {
      // Otherwise, get the cost from the user's plan
      amount = await this.featureCostService.getFeatureCost(userId, featureId);
      this.logger.log(`Plan-based cost for feature ${featureId} for user ${userId}: ${amount}`);
    }

    // If amount is 0 (unlimited), skip credit check
    if (amount === 0) {
      this.logger.log(
        `Skipping credit check for feature ${featureId} for user ${userId} (unlimited)`,
      );
      return next.handle();
    }

    const purpose = configPurpose || `Using feature: ${featureId}`;

    try {
      // Check if user has enough credits
      const hasEnoughCredits = await this.creditService.hasEnoughCredits(userId, amount, featureId);
      if (!hasEnoughCredits) {
        this.logger.warn(`User ${userId} does not have enough credits for feature ${featureId}`);
        throw new HttpException('Insufficient credits', HttpStatus.PAYMENT_REQUIRED);
      }

      // Use credits
      const usageInfo = await this.creditService.useCredits(userId, amount, purpose, featureId);
      if (!usageInfo) {
        this.logger.error(`Failed to use credits for user ${userId} and feature ${featureId}`);
        throw new HttpException('Failed to use credits', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      // Store usage info in request for later restoration if needed
      request['featureUsageInfo'] = usageInfo;

      return next.handle().pipe(
        tap(() => {
          // Success case - do nothing, credits are already used
        }),
        catchError((error) => {
          // If there's an error, restore credits
          const restorePurpose = `Restore credits after error: ${error.message}`;

          this.creditService
            .restoreCredits(request['featureUsageInfo'], restorePurpose)
            .then((restored) => {
              if (restored) {
                this.logger.log(
                  `Credits restored for user ${userId} and feature ${featureId} after error: ${error.message}`,
                );
              } else {
                this.logger.error(
                  `Failed to restore credits for user ${userId} and feature ${featureId} after error: ${error.message}`,
                );
              }
            })
            .catch((restoreError) => {
              this.logger.error(
                `Error restoring credits: ${restoreError.message}`,
                restoreError.stack,
              );
            });

          return throwError(() => error);
        }),
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error in feature credits processing: ${error.message}`, error.stack);
      throw new HttpException('Error processing feature credits', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}

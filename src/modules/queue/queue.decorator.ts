import { InjectQueue } from '@nestjs/bullmq';
import { BULL_QUEUES } from 'src/configs/configs.constants';

export const InjectSequenceStepTaskQueue = () => InjectQueue(BULL_QUEUES.SEQUENCE_STEP_TASK);

export const InjectWebhookSendgridEventQueue = () => InjectQueue(BULL_QUEUES.WEBHOOK_SENDGRID_EVENT);

export const InjectBullhornEmailNoteEventQueue = () => InjectQueue(BULL_QUEUES.BULLHORN_EMAIL_NOTE_EVENT);

export const InjectInternalQueueEventQueue = () => InjectQueue(BULL_QUEUES.INTERNAL_QUEUE_EVENT);

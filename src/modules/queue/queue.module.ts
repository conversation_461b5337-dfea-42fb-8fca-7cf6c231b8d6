import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { SequenceStepTaskProcessor } from './sequence-step-task.processor';
// import { SequenceStepTaskJob } from './sequence-step-task.producer';
import { BULL_QUEUES } from 'src/configs/configs.constants';
import { MailModule } from '../mail/mail.module';
import { SequenceInstanceRepository } from '../mail/repositories/sequence-instance.repository';
import { SequenceActivityLogRepository } from '../mail/repositories/sequence-activity-log.repository';
import { SequenceRepository } from '../mail/repositories/sequence.repostiory';
import { SequenceStepRepository } from '../mail/repositories/sequence-step.repostiory';
import { JobLeadsRepository } from '../jobs/repository/job-leads.repository';
import { UserRepository } from '../user/repositories/user.repository';
import { SequenceStepTaskRepository } from '../mail/repositories/sequence-step-task.repository';

@Module({
  imports: [
    MailModule,
    BullModule.registerQueue(
      {
        name: BULL_QUEUES.SEQUENCE_STEP_TASK,
        prefix: '{bull_sequence_step_task}',
      },
      {
        name: BULL_QUEUES.WEBHOOK_SENDGRID_EVENT,
        prefix: '{bull_webhook_sendgrid_event}',
      },
      {
        name: BULL_QUEUES.WEBHOOK_NYLAS_EVENT,
        prefix: '{bull_webhook_nylas_event}',
      },
      {
        name: BULL_QUEUES.WEBHOOK_UNIPILE_EVENT,
        prefix: '{bull_webhook_unipile_event}',
      },
      {
        name: BULL_QUEUES.CRAWL_JOB_EVENT,
        prefix: '{bull_crawl_job_event}',
      },
      {
        name: BULL_QUEUES.BULLHORN_EMAIL_NOTE_EVENT,
        prefix: '{bull_bullhorn_email_note_event}',
      },
      {
        name: BULL_QUEUES.INTERNAL_QUEUE_EVENT,
        prefix: '{bull_internal_queue_event}',
      },
    ),
  ],
  providers: [
    SequenceStepTaskProcessor,
    SequenceRepository,
    SequenceStepRepository,
    SequenceInstanceRepository,
    SequenceStepTaskRepository,
    SequenceActivityLogRepository,
    JobLeadsRepository,
    UserRepository,
  ],
  exports: [],
})

export class QueueModule {}

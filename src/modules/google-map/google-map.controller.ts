import { <PERSON>, Get, Query, HttpStatus, <PERSON><PERSON>, UseGuards, Param } from '@nestjs/common';
import { GoogleMapService } from './google-map.service';
import { SkipThrottle } from '@nestjs/throttler';
import { AuthenticationGuard } from '../auth/guards/auth.guard';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('GoogleMap')
@Controller('google-map')
@SkipThrottle()
@UseGuards(AuthenticationGuard)
export class GoogleMapController {
  constructor(private readonly googleMapService: GoogleMapService) {}

  @Get('search-address')
  async searchAddress(@Query('query') query: string) {
    return this.googleMapService.searchAddress(query);
  }

  @Get('get-address-details')
  async searchAddressByPlaceId(@Query('place_id') place_id: string) {
    return this.googleMapService.retrieveAddressDetail(place_id);
  }

  @Get('open-street/search-address')
  async searchAddressByOpenStreet(@Query('location') location: string) {
    return await this.googleMapService.searchAddressByBoxMapService(location);
  }
}

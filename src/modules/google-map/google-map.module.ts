import { Keyv } from 'keyv';
import KeyvRedis from '@keyv/redis';
import { Module } from '@nestjs/common';
import { GoogleMapController } from './google-map.controller';
import { GoogleMapService } from './google-map.service';
import { HttpModule } from '@nestjs/axios';
import { CacheService } from '../cache/cache.service';
import { CacheModule } from '@nestjs/cache-manager';
import * as redisStore from 'cache-manager-redis-store';
import { OpensearchService } from '../opensearch/service/opensearch.service';

@Module({
  imports: [
    HttpModule,
    CacheModule.registerAsync({
      useFactory: async () => {
        return new Keyv({
          store: new KeyvRedis(process.env.REDIS_CONNECTION),
          ttl: 24 * 60 * 60,
        });
      },
    }),
  ],
  controllers: [GoogleMapController],
  providers: [GoogleMapService, CacheService, OpensearchService],
})
export class GoogleMapModule {}

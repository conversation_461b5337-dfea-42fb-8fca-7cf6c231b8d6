import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { map } from 'rxjs';
import axios, { AxiosResponse } from 'axios';
import { CACHE_PREFIX, CACHE_TTL } from '../jobs/constants/job.const';
import { CacheService } from '../cache/cache.service';
import { OpensearchService } from '../opensearch/service/opensearch.service';
import { v4 as uuid } from 'uuid';

const SOURCE_LOCATION = {
  LAMBDA: 'LAMBDA',
  GOOGLE_MAP: 'GOOGLE_MAP',
  BOXMAP: 'BOXMAP',
};

@Injectable()
export class GoogleMapService {
  constructor(
    private readonly httpService: HttpService,
    private readonly cacheService: CacheService,
    private readonly elasticsearchService: OpensearchService,
  ) {}

  async searchAddress(query: string): Promise<any[]> {
    try {
      if (!query) {
        return [];
      }
      const response = await axios.get(
        'https://maps.googleapis.com/maps/api/place/autocomplete/json',
        {
          params: {
            // key: 'AIzaSyAwx7oD_A8Gl4vItEGPuguPwT-t9S8d9Ic',
            // key: 'AIzaSyCG7KCcQYn12qNkrAHgNNHlGyl9xQIjquQ',
            key: process.env.GOOGLE_MAP_API_KEY,
            input: query,
            radius: 50000,
          },
        },
      );

      const placesData = await Promise.all(
        response.data.predictions.map(async (result) => {
          const placeDetails = await this.searchAddressByPlaceId(result.place_id);
          return {
            id: result.place_id,
            address1: result.description,
            details: !placeDetails.photos
              ? null
              : placeDetails.photos.length === 0
                ? null
                : placeDetails.photos[0],
          };
        }),
      );

      return placesData;
    } catch (error) {
      throw new InternalServerErrorException('Error fetching data from Google Maps API');
    }
  }

  async searchAddressByPlaceId(place_id: string): Promise<any> {
    if (!place_id) {
      return {};
    }
    const response = await axios.get('https://maps.googleapis.com/maps/api/place/details/json', {
      params: {
        // key: 'AIzaSyCG7KCcQYn12qNkrAHgNNHlGyl9xQIjquQ',
        key: process.env.GOOGLE_MAP_API_KEY,
        place_id: place_id,
      },
    });
    const {
      formatted_phone_number: phone,
      website,
      photos,
      address_components: addressComponents = [],
    } = response.data.result || {};
    let state: string, county: string, city: string, zip: string, country: string;

    for (const component of addressComponents) {
      const { types, long_name, short_name } = component;

      if (types.includes('country')) {
        country = long_name;
      } else if (types.includes('administrative_area_level_1')) {
        state = long_name;
      } else if (types.includes('administrative_area_level_2')) {
        county = long_name;
      } else if (types.includes('postal_town')) {
        city = long_name;
      } else if (types.includes('postal_code')) {
        zip = short_name;
      }
    }

    const addressInfo = {
      state,
      county,
      city,
      zip,
      country,
      phone,
      website,
      photos,
    };
    return addressInfo;
  }

  async searchAddressByMapBox(query: string) {
    try {
      if (!query) {
        return [];
      }
      const sessionToken = uuid();
      const response = await axios.get('https://api.mapbox.com/search/searchbox/v1/suggest', {
        params: {
          q: query,
          access_token: process.env.MAPBOX_ACCESS_TOKEN,
          session_token: sessionToken,
          language: 'en',
          limit: 10,
          types:
            'district,postcode,locality,place,neighborhood,address,poi,street,category,region,country',
          proximity: '-98,40',
        },
      });

      return response.data?.suggestions;
    } catch (error) {
      throw new InternalServerErrorException('Error fetching data from Box Map API');
    }
  }

  async searchAddressByBoxMapService(location: string) {
    try {
      // const cacheKey = CACHE_PREFIX.OPEN_STREET_SEARCH + encodeURIComponent(location);
      // const cachedLocation = await this.cacheService.get(cacheKey);
      // if (cachedLocation) {
      //   return cachedLocation;
      // }

      const addressData = await this.searchAddressByMapBox(location);
      const sourceKey = SOURCE_LOCATION.BOXMAP;
      const locationData = {
        data: addressData.map((item: any) => ({
          ...item,
          display_name: item?.name || item.full_address,
          place_id: item?.mapbox_id,
          address1: item.address1 || item?.name || item.full_address,
        })),
        sourceKey,
      };

      // await this.cacheService.set(cacheKey, locationData, CACHE_TTL.OPEN_STREET_SEARCH);
      return locationData;
    } catch (error) {
      throw new InternalServerErrorException('Error fetching data from Map Box');
    }
  }

  async retrieveAddressDetailByMapBox(place_id: string): Promise<any> {
    const sessionToken = uuid();

    const response = await axios.get(
      `https://api.mapbox.com/search/searchbox/v1/retrieve/${place_id}`,
      {
        params: {
          access_token: process.env.MAPBOX_ACCESS_TOKEN,
          session_token: sessionToken,
        },
      },
    );

    return response.data.features?.[0];
  }

  async retrieveAddressDetail(place_id: string): Promise<any> {
    if (!place_id) {
      return {};
    }
    const addressData = await this.retrieveAddressDetailByMapBox(place_id);

    const addressInfo = {
      state: addressData?.properties?.context?.region?.name,
      county:
        addressData?.properties?.context?.region?.name ||
        addressData?.properties?.context?.country?.name,
      city: addressData?.properties?.context?.place?.name,
      zip: addressData?.properties?.context?.postcode?.name,
      phone: '',
      website: '',
      photos: '',
      addressData,
    };
    return addressInfo;
  }
}

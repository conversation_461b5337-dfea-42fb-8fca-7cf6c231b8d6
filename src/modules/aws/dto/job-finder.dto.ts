import { ApiPropertyOptional, ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";

export class JobBySearchIdQuery {

    @ApiPropertyOptional()
    @IsOptional()
    page?: number;

    @ApiPropertyOptional()
    @IsOptional()
    searchName?: string;

    @ApiPropertyOptional()
    keywords?: string;

    @ApiPropertyOptional()
    @IsOptional()
    location?: string;

    @ApiPropertyOptional()
    @IsOptional()
    jobBoards?: string;

    @ApiPropertyOptional()
    @IsOptional()
    maxSalary?: number;

    @ApiPropertyOptional()
    @IsOptional()
    minSalary?: number;

    @ApiPropertyOptional()
    @IsOptional()
    postedStartDate?: Date;

    @ApiPropertyOptional()
    @IsOptional()
    postedEndDate?: Date;

    @ApiPropertyOptional()
    @IsOptional()
    newJobOnly?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    notificationId?: string;
}

export class snsSend {
    @IsString()
    subject: string;

    @IsString()
    message: string;

    @IsString()
    topicArn: string;
}

export class createEvent {
    @IsString()
    subject: string;

    @ApiProperty({ example: {type: 'KILL_JOB', jobIds: ['a8651f17-1083-4472-a282-9aa8e7bb9665', 'c36a4fb8-782b-4e26-9eaa-227fcf8eeae5']} })
    message:  { type: string; jobIds: string[] };
}

export class getLinkUrl {
    @IsString()
    key: string;
}

export class snsNotif {
    @ApiPropertyOptional()
    @IsOptional()
    Type?: string;

    @ApiPropertyOptional()
    @IsOptional()
    MessageId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    Token?: string;

    @ApiPropertyOptional()
    @IsOptional()
    TopicArn?: string;

    @IsString()
    Message: string;

    @ApiPropertyOptional()
    @IsOptional()
    SubscribeURL?: string;

    @ApiPropertyOptional()
    @IsOptional()
    Timestamp?: string;
    
    @ApiPropertyOptional()
    @IsOptional()
    SignatureVersion?: string;

    @ApiPropertyOptional()
    @IsOptional()
    Signature?: string;

    @ApiPropertyOptional()
    @IsOptional()
    SigningCertURL?: string;
}
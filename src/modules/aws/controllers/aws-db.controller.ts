// dynamo-db.controller.ts
import { Controller, Get, Post, Query, Param, Body, Req } from '@nestjs/common';
import { SkipThrottle } from "@nestjs/throttler";
import { AwsService } from '../services/aws-db.service';
import { ApiTags } from "@nestjs/swagger";
import { JobBySearchIdQuery } from 'src/modules/jobs/dto/job-by-search-id.dto';
import { snsSend, snsNotif, createEvent, getLinkUrl } from '../dto/job-finder.dto';

@ApiTags("AWS")
@Controller('aws')
@SkipThrottle()
export class AwsDBController {
    constructor(private readonly awsService: AwsService) {}

    @Get("get-link-s3")
    async getLinkUrl(
        @Query() queryParams: getLinkUrl,
    ) {
        return this.awsService.getLinkUrl(queryParams);
    }
}
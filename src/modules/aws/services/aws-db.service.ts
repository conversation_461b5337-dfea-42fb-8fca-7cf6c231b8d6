import { Injectable } from '@nestjs/common';
import { AwsConfig } from '../aws.config';
import { DynamoDB, S3 } from 'aws-sdk';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from '../../../base/base.abstract.service';
import { ISimpleUser } from 'src/modules/auth/payloads/jwt-payload.payload';
import { JobBySearchIdQuery } from 'src/modules/jobs/dto/job-by-search-id.dto';
import { JobSearchRepository } from '../../jobs/repository/job-search.repository';
import { DataSource } from 'typeorm';
import * as AWS from 'aws-sdk';
import { HttpService } from '@nestjs/axios';
import { CrawlingConfig, SnsConfig } from 'src/configs/configs.constants';
@Injectable()
export class AwsService extends BaseAbstractService {
  private readonly dynamoDB: DynamoDB.DocumentClient;
  private readonly sns: AWS.SNS;
  private readonly s3: S3;

  constructor(
    readonly i18nService: I18nService,
    private readonly jobSearchRepository: JobSearchRepository,
    private readonly awsConfig: AwsConfig,
    private readonly dataSource: DataSource,
    private readonly httpService: HttpService
  ) {
    super(i18nService);
    const awsConfiguration = awsConfig.getAWSConfig();
    this.dynamoDB = new DynamoDB.DocumentClient(awsConfiguration);
    AWS.config.update(this.awsConfig.getAWSConfig());
    this.sns = new AWS.SNS();
    this.s3 = new S3();
  }

  async createEvent(reqBody) {
    const { message, subject } = reqBody;
    console.log(reqBody, '============================');
    const params: AWS.SNS.PublishInput = {
      Message: JSON.stringify(message),
      Subject: subject,
      TopicArn: SnsConfig.topicArn,
    };
    await this.sns.publish(params).promise();
    console.log(message, '=========================');
    if (message?.jobIds && message?.jobIds.length > 0) await this.callToKillCrawlProcess(message.jobIds);
    return true;
  }

  async callToKillCrawlProcess(jobIds: string[]) {
    try {
      await this.httpService.axiosRef.post(CrawlingConfig.deleteCrawlingProcess, { jobIds });
      return true;
    } catch (error) {
      console.log('Error in callToKillCrawlProcess', error);
      return null;
    }
  }

  async getLinkUrl(queryParams) {
    try {
      const { key } = queryParams;
      if (key.includes('http')) return key;
      const urlParams = {
        Bucket: process.env.BUCKET_NAME_S3,
        Key: key,
        Expires: 300 * 60,
      };

      const imageUrl = this.s3.getSignedUrl('getObject', urlParams);
      return imageUrl;
    } catch (error) {
      console.error('Error getting image from S3:', error);
      throw new Error('Failed to get image from S3');
    }
  }
}

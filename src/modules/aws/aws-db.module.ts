import { <PERSON>du<PERSON> } from "@nestjs/common";
import { AwsService } from "./services/aws-db.service";
import { AwsDBController } from "./controllers/aws-db.controller";
import { HttpModule } from "@nestjs/axios";
import * as AWS from 'aws-sdk';
import { AwsConfig } from './aws.config'
import { JobSearchRepository } from "../jobs/repository/job-search.repository";
@Module({
  controllers: [AwsDBController],
  imports: [
    HttpModule,
  ],
  providers: [
    AwsConfig,
    AwsService,
    JobSearchRepository
  ],
  exports: [AwsService, JobSearchRepository]
})
export class AwsDBModule { }
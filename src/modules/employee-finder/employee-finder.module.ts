import { Modu<PERSON> } from '@nestjs/common';
import { EmployeeFinderService } from './services/employee-finder.service';
import { EmployeeFinderController } from './controllers/employee-finder.controller';
import { HttpModule } from '@nestjs/axios';
import { MyCacheModule } from '../cache/cache.module';
import { CompanyFinderController } from './controllers/company-finder.controller';
import { ApolloService } from './services/apollo.service';
import { FoundContactService } from './services/found-contact.service';
import { SubscriptionModule } from '../subscription/subscription.module';

@Module({
  controllers: [EmployeeFinderController, CompanyFinderController],
  imports: [
    HttpModule,
    // MyCacheModule,
    SubscriptionModule,
  ],
  providers: [EmployeeFinderService, ApolloService, FoundContactService],
  exports: [ApolloService],
})
export class EmployeeFinderModule {}

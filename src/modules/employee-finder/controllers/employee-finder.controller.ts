import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { EmployeeFinderService } from '../services/employee-finder.service';
import {
  EmployeeFinderQuery,
  GetSimilar<PERSON>erson,
  OrganizationsSnippetDto,
  RecommendedFilterQuery,
  SearchFacets,
  SearchSignalQuery,
  SearchTagQuery,
} from '../dto/employee-finder.dto';
import { SkipThrottle } from '@nestjs/throttler';
import { ApolloService } from '../services/apollo.service';
import { FoundContactService } from '../services/found-contact.service';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';
import { UseFeatureCredits } from 'src/modules/subscription/decorators/use-feature-credits.decorator';

@ApiTags('Employee Finder')
@Controller('employee-finder')
@SkipThrottle()
@UseGuards(AuthenticationGuard, PermissionGuard)
export class EmployeeFinderController {
  constructor(
    private readonly employeeFinderService: EmployeeFinderService,
    private readonly apolloService: ApolloService,
    private readonly foundContactService: FoundContactService
  ) {}

  @Post()
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getEmployees(@Body() queryDto: EmployeeFinderQuery) {
    return this.employeeFinderService.getEmployees(queryDto);
  }

  @Post('organizations-snippet')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getOrganizationsSnippet(@Body() queryDto: OrganizationsSnippetDto) {
    return this.apolloService.getOrganizationsSnippet(queryDto);
  }

  @Post('accounts-snippet')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getAccountSnippet(@Body() queryDto: OrganizationsSnippetDto) {
    return this.apolloService.getAccountsSnippet(queryDto);
  }

  @Get(':employeeId')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  @UseFeatureCredits({
    featureId: 'credits',
    purpose: 'Get detail employee - Access email',
  })
  getDetailEmployee(@Param('employeeId') employeeId: string) {
    return this.apolloService.getDetailEmployee(employeeId);
  }

  @Get('/search/signals')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getSearchSignals(@Query() queryDto: SearchSignalQuery) {
    return this.apolloService.getSearchSignals(queryDto.searchText, queryDto.modality);
  }

  @Get('/search/intent-data-topic/category')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getIntentDataTopicCategory() {
    return this.apolloService.getIntentDataTopicCategory();
  }

  @Get('/search/intent-data-topic/:topicName')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getIntentDataTopicByName(@Param('topicName') topicName: string) {
    return this.apolloService.getIntentDataTopicByName(topicName);
  }

  @Get('/search/intent-data-topic/find-by-category-name/:categoryName')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getIntentDataTopicByCategoryName(@Param('categoryName') categoryName: string) {
    return this.apolloService.getIntentDataTopicByCategoryName(categoryName);
  }

  @Get('/search/tag')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getTag(@Query() queryDto: SearchTagQuery) {
    return this.apolloService.getTag(queryDto.searchText, queryDto.type);
  }

  @Post('/search/facets/')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getFacets(@Body() queryDto: SearchFacets) {
    return this.apolloService.getFacets(queryDto);
  }

  @Post('/search/news/')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getNews(@Body() queryDto: SearchFacets) {
    return this.apolloService.getNewsOfCompany(queryDto);
  }

  @Post('/search/newsfeed_events/')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getNewFeedEvent(@Body() queryDto: SearchFacets) {
    return this.apolloService.getNewFeedEvent(queryDto);
  }

  @Get('/search/recommended-contact-list')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getRecommendedContactList(
    @Query() query: RecommendedFilterQuery
  ) {
    return this.foundContactService.getRecommendedContactList(query);
  }

  @Post("/similar-person-in-company")
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getSimilarPersonInCompany(
    @Body() queryDto: GetSimilarPerson
  ) {
    return this.apolloService.getSimilarPersonInCompany(queryDto);
  }
}

import { Body, Controller, Get, Post, Query, Param, UseGuards } from '@nestjs/common';
import { ApolloService } from '../services/apollo.service';
import { ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { CompanyDetailQuery, CompanyFinderQuery } from '../dto/company-finder.dto';
import { AuthenticationGuard } from 'src/modules/auth/guards/auth.guard';
import { PermissionGuard } from 'src/guards/permission.guard';
import { Permission } from 'src/common/decorators/permissions.decorator';
import { PermissionResource } from 'src/common/constants/permission.constant';
import { ResourceEnum } from 'src/modules/user/entities/permission.entity';

@ApiTags('Company Finder')
@SkipThrottle()
@Controller('company-finder')
@UseGuards(AuthenticationGuard, PermissionGuard)
export class CompanyFinderController {
  constructor(private readonly apolloService: ApolloService) {}

  @Get()
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getCompanies(@Query('searchText') searchText: string) {
    return this.apolloService.getCompanies(searchText);
  }

  @Get(':organizationId')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getCompaniesById(@Param('organizationId') organizationId: string, @Query() queryParams: CompanyDetailQuery, ) {
    return this.apolloService.getCompaniesById(organizationId, queryParams);
  }

  @Post('explore')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getCompaniesWithExploreMode(@Body() companyQuery: CompanyFinderQuery) {
    return this.apolloService.getCompaniesWithExploreMode(companyQuery);
  }

  @Get('mixed-companies/facets')
  @Permission(PermissionResource[ResourceEnum.CONTACT_FINDER].Read)
  getCompaniesFacets() {
    return this.apolloService.getCompaniesFacets();
  }
}

import { Entity, PrimaryGeneratedColumn, Column, BaseEntity, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IApolloContactDto } from '../dto/employee-finder.dto';

@Entity('found_companies')
export class FoundCompanyEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', name: "created_by", length: 100, nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', nullable: true, name: 'apollo_company_id' })
  apolloCompanyId: string;

  @Column({ type: 'jsonb', nullable: true, name: 'raw_information' })
  rawInformation: any;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;
  
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

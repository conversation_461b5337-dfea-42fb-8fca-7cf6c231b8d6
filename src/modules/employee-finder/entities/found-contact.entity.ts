import { Entity, PrimaryGeneratedColumn, Column, BaseEntity, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IApolloContactDto } from '../dto/employee-finder.dto';

@Entity('found_contacts')
export class FoundContactEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  company: string;

  @Column({ type: 'varchar', name: "created_by", length: 100, nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', nullable: true, name: 'apollo_contact_id' })
  apolloContactId: string;

  @Column({ type: 'varchar', nullable: true, name: 'raw_information' })
  rawInformation: IApolloContactDto;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;
  
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;
}

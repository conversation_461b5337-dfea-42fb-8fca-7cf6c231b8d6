import { Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { DataSource } from 'typeorm';
import { FoundContactEntity } from '../entities/found-contact.entity';
import { jsonParse } from 'src/common/utils/helpers.util';
import { RecommendedFilterQuery } from '../dto/employee-finder.dto';

@Injectable()
export class FoundContactService extends BaseAbstractService {
  constructor(private i18nService: I18nService, private readonly dataSource: DataSource) {
    super(i18nService);
  }

  async getRecommendedContactList(recommendedFilterQuery: RecommendedFilterQuery) {
    const query = this.dataSource
      .createQueryBuilder(FoundContactEntity, 'fc')
      .skip((recommendedFilterQuery.page - 1) * recommendedFilterQuery.pageSize)
      .take(recommendedFilterQuery.pageSize);

    if (recommendedFilterQuery.locations && recommendedFilterQuery.locations.length > 0) {
      const locationConditions: string[] = [];
      const locationParams: Record<string, string> = {};

      recommendedFilterQuery.locations.forEach((loc, index) => {
        const paramKey = `location${index}`;
        locationConditions.push(
          `jsonb_extract_path_text(fc.raw_information::jsonb, 'present_raw_address') ILIKE :${paramKey}`
        );
        locationParams[paramKey] = `%${loc}%`;
      });

      query.andWhere(`(${locationConditions.join(' AND ')})`, locationParams);
    }
    
    if (recommendedFilterQuery.personTitles && recommendedFilterQuery.personTitles.length > 0) {
      const locationConditions: string[] = [];
      const locationParams: Record<string, string> = {};

      recommendedFilterQuery.personTitles.forEach((loc, index) => {
        const paramKey = `title${index}`;
        locationConditions.push(`jsonb_extract_path_text(fc.raw_information::jsonb, 'title') ~* :${paramKey}`);
        locationParams[paramKey] = `\\m${loc}\\M`;
      });

      query.andWhere(`(${locationConditions.join(' AND ')})`, locationParams);
    }

    if (recommendedFilterQuery.personName) {
      query.andWhere("jsonb_extract_path_text(fc.raw_information::jsonb, 'name') ILIKE :name", {
        name: `%${recommendedFilterQuery.personName}%`,
      });
    }

    if (recommendedFilterQuery.company) {
      query.andWhere('fc.company = :company', { company: recommendedFilterQuery.company });
    }

    const [data, count] = await query.getManyAndCount();
    const contacts = data.map((item) => jsonParse(item.rawInformation));

    return this.formatOutputData(
      { key: 'GET_RECOMMENDED_CONTACT_LIST' },
      {
        data: {
          items: contacts,
          count,
        },
      }
    );
  }
}

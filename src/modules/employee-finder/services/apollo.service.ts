import { HttpService } from '@nestjs/axios';
import { Injectable, NotFoundException } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { GetSimilar<PERSON>erson, OrganizationsSnippetDto, SearchFacets, TagTypeEnum } from '../dto/employee-finder.dto';
import { EMPLOYEE_RANGE } from '../constants/employee.const';
import { ApolloConfig } from 'src/configs/configs.constants';
import { CompanyDetailQuery, CompanyFinderQuery } from '../dto/company-finder.dto';
const qs = require('querystringify');

@Injectable()
export class ApolloService extends BaseAbstractService {
  constructor(private i18nService: I18nService, private readonly httpService: HttpService) {
    super(i18nService);
  }

  async getCompanies(searchText: string) {
    try {
      const searchCompanyApi = 'https://app.apollo.io/api/v1/organizations/search';
      const response = await this.httpService.axiosRef.post(
        searchCompanyApi,
        {
          q_organization_fuzzy_name: searchText,
          display_mode: 'fuzzy_select_mode',
        },
        {
          headers: {
            'X-Api-Key': ApolloConfig.apiKey,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.log('Error in getCompanies', error);
      throw new NotFoundException('There is no available company');
    }
  }

  async getCompaniesById(organization_id: string, queryParamData: CompanyDetailQuery) {
    try {
      const queryParams = qs.stringify({
        with_split_personas_facets: true
      });
      let searchCompanyApi;
      searchCompanyApi = `${process.env.APOLLO_API_URL}/organizations/show_with_context?organization_id=${organization_id}&${queryParams}`;
      if((queryParamData?.withOutContext).toString() === "true") {
        searchCompanyApi = `${process.env.APOLLO_API_URL}/organizations/${organization_id}?${queryParams}`;
      }
      const response = await this.httpService.axiosRef.get(searchCompanyApi, {
        headers: {
          'X-Api-Key': ApolloConfig.apiKey,
        },
      });
      return response.data;
    } catch (error) {
      console.log('Error in getCompaniesById', error);
      throw new NotFoundException('There is no available company');
    }
  }

  async getPeople(
    {
      organizationId,
      industryTagIds,
      employeeRanges,
      locations,
      personTitles,
      searchText,
      containOneKeywords,
      containAllKeyWords,
      excludeKeyWords,
      contactEmailStatuses,
      searchSignalIds,
      contactEmailOpened,
      contactEmailOpenedAtLeast,
      contactEmailOpenedAtDateRangeMax,
      contactEmailOpenedAtDateRangeMin,
      intentStrengths,
      intentIds,
      recommendationScoresMinTranche,
      currentlyUsingAnyOfTechnologyUids,
      existFields,
      notExistFields,
      organizationTradingStatus,
      organizationLatestFundingStageCd,
      totalFundingRangeMin,
      totalFundingRangeMax,
      personName,
      organizationNumJobsRangeMin,
      organizationNumJobsRangeMax,
      organizationJobPostedAtRangeMin,
      organizationJobPostedAtRangeMax,
      organizationJobLocations,
      qOrganizationJobTitles,
      qPersonPersonaIds,
      mixedAccountIds,
      context,
      metaMode,
      notOrganizationIds,
      personPastOrganizationIds,
    }: {
      organizationId?: string;
      industryTagIds?: string[];
      employeeRanges?: string[];
      locations?: string[];
      personTitles?: string[];
      searchText?: string;
      containOneKeywords?: string[];
      containAllKeyWords?: string[];
      excludeKeyWords?: string[];
      contactEmailStatuses?: string[];
      searchSignalIds?: string[];
      contactEmailOpened?: string;
      contactEmailOpenedAtLeast?: string;
      contactEmailOpenedAtDateRangeMax?: any;
      contactEmailOpenedAtDateRangeMin?: any;
      intentStrengths?: string[];
      intentIds?: string[];
      recommendationScoresMinTranche?: string;
      currentlyUsingAnyOfTechnologyUids?: string[];
      existFields?: string[];
      notExistFields?: string[];
      organizationTradingStatus?: string[];
      organizationLatestFundingStageCd?: string[];
      totalFundingRangeMin?: any;
      totalFundingRangeMax?: any;
      personName?: string;
      organizationNumJobsRangeMin?: string;
      organizationNumJobsRangeMax?: string;
      organizationJobPostedAtRangeMin?: Date;
      organizationJobPostedAtRangeMax?: Date;
      organizationJobLocations?: string[];
      qOrganizationJobTitles?: string[];
      qPersonPersonaIds?: string[];
      mixedAccountIds?: string[];
      context?: string;
      metaMode?: string;
      notOrganizationIds?: string[],
      personPastOrganizationIds?: string[]
    },
    page = 1
  ) {
    try {
      const searchPeopleApi = process.env.APOLLO_API_URL + '/mixed_people/search';
      const bodyRequest: any = {
        // api_key: ApolloConfig.apiKey,
        per_page: 25,
        page,


        finder_table_layout_id: "6674b20eecfedd000184539f",
        finder_view_id: '5b6dfc5a73f47568b2e5f11c',
        display_mode: metaMode ? metaMode :  'explorer_mode',
        // finder_version: 1,
        // open_factor_names: [],
        // num_fetch_result: 6,
        context: context ? context :  'people-index-page',
        // show_suggestions: false,
        // ui_finder_random_seed: 'm9c5pkedl4l',
        prospected_by_current_team: ["yes", "no"],
      };

      if (organizationId) {
        bodyRequest.organization_ids = [organizationId];
      }

      if (industryTagIds && industryTagIds.length) {
        bodyRequest.organization_industry_tag_ids = industryTagIds;
      }

      if (employeeRanges && employeeRanges.length) {
        bodyRequest.organization_num_employees_ranges = employeeRanges;
      }

      if (locations && locations.length) {
        bodyRequest.person_locations = locations;
      }

      if (personTitles && personTitles.length) {
        bodyRequest.person_titles = personTitles;
      }

      if (searchText) {
        bodyRequest.q_keywords = searchText;
      }

      if (containOneKeywords && containOneKeywords.length) {
        bodyRequest.q_organization_keyword_tags = containOneKeywords;
      }

      if (containAllKeyWords && containAllKeyWords.length) {
        bodyRequest.q_anded_organization_keyword_tags = containOneKeywords;
      }

      if (excludeKeyWords && excludeKeyWords.length) {
        bodyRequest.q_not_organization_keyword_tags = containOneKeywords;
      }

      if (contactEmailStatuses && contactEmailStatuses.length) {
        bodyRequest.contact_email_status_v2 = contactEmailStatuses;
      }

      if (searchSignalIds && searchSignalIds.length) {
        bodyRequest.search_signal_ids = searchSignalIds;
      }

      if (contactEmailOpened) {
        bodyRequest.contact_email_open = contactEmailOpened;
      }

      if (contactEmailOpenedAtLeast) {
        bodyRequest.contact_email_opened_at_least = contactEmailOpenedAtLeast;
      }

      if (contactEmailOpenedAtDateRangeMax || contactEmailOpenedAtDateRangeMin) {
        bodyRequest.email_last_opened_at_date_range = {};

        if (contactEmailOpenedAtDateRangeMax) {
          bodyRequest.email_last_opened_at_date_range.max = contactEmailOpenedAtDateRangeMax;
        }

        if (contactEmailOpenedAtDateRangeMin) {
          bodyRequest.email_last_opened_at_date_range.min = contactEmailOpenedAtDateRangeMin;
        }
      }

      if (intentStrengths && intentStrengths.length) {
        bodyRequest.intent_strengths = intentStrengths;
      }

      if (intentIds && intentIds.length) {
        bodyRequest.intent_ids = intentIds;
      }

      if (recommendationScoresMinTranche) {
        bodyRequest.recommendation_scores_min_tranche = recommendationScoresMinTranche;
      }

      if (currentlyUsingAnyOfTechnologyUids && currentlyUsingAnyOfTechnologyUids.length) {
        bodyRequest.currently_using_any_of_technology_uids = currentlyUsingAnyOfTechnologyUids;
      }

      if (existFields) {
        bodyRequest.exist_fields = existFields;
      }

      if (notExistFields) {
        bodyRequest.not_exist_fields = notExistFields;
      }

      if (organizationTradingStatus && organizationTradingStatus.length) {
        bodyRequest.organization_trading_status = organizationTradingStatus;
      }

      if (organizationLatestFundingStageCd && organizationLatestFundingStageCd.length) {
        bodyRequest.organization_latest_funding_stage_cd = organizationLatestFundingStageCd;
      }

      if (totalFundingRangeMin || totalFundingRangeMax) {
        bodyRequest.total_funding_range = {};

        if (totalFundingRangeMin) {
          bodyRequest.total_funding_range.min = totalFundingRangeMin;
        }

        if (totalFundingRangeMax) {
          bodyRequest.total_funding_range.max = totalFundingRangeMax;
        }
      }

      if (organizationNumJobsRangeMin || organizationNumJobsRangeMax) {
        bodyRequest.organization_num_jobs_range = {};

        if (organizationNumJobsRangeMin) {
          bodyRequest.organization_num_jobs_range.min = organizationNumJobsRangeMin;
        }

        if (organizationNumJobsRangeMax) {
          bodyRequest.organization_num_jobs_range.max = organizationNumJobsRangeMax;
        }
      }

      if (organizationJobPostedAtRangeMin || organizationJobPostedAtRangeMax) {
        bodyRequest.organization_job_posted_at_range = {};

        if (organizationJobPostedAtRangeMin) {
          bodyRequest.organization_job_posted_at_range.min = organizationJobPostedAtRangeMin;
        }

        if (organizationJobPostedAtRangeMax) {
          bodyRequest.organization_job_posted_at_range.max = organizationJobPostedAtRangeMax;
        }
      }

      if (organizationJobLocations && organizationJobLocations?.length > 0) {
        bodyRequest.organization_job_locations = organizationJobLocations;
      }

      if (qOrganizationJobTitles && qOrganizationJobTitles?.length > 0) {
        bodyRequest.q_organization_job_titles = qOrganizationJobTitles;
      }

      if(qPersonPersonaIds && qPersonPersonaIds?.length > 0) {
        bodyRequest.q_person_persona_ids = qPersonPersonaIds;
      }

      if(mixedAccountIds && mixedAccountIds?.length > 0) {
        bodyRequest.mixed_account_ids = mixedAccountIds;
      }

      if (notOrganizationIds && notOrganizationIds.length > 0) {
        bodyRequest.not_organization_ids = notOrganizationIds;
      }

      if (personPastOrganizationIds && personPastOrganizationIds.length > 0) {
        bodyRequest.person_past_organization_ids = personPastOrganizationIds;
      }

      if (personName) {
        bodyRequest.q_person_name = personName;
      }

      const searchPaginationPeopleApi = process.env.APOLLO_API_URL + '/mixed_people/search_metadata_mode';
      const headerSetting = {
        headers: {
          'X-Api-Key': ApolloConfig.apiKey,
        },
      }
      const response = await this.httpService.axiosRef.post(searchPeopleApi, bodyRequest, headerSetting);
      return {
        ...response.data,
      };
    } catch (error) {
      console.log('Error in getPeople', error);
      throw new NotFoundException('There is no available employee');
    }
  }

  async getOrganizationsSnippet (body: OrganizationsSnippetDto){
    try {
      const detailEmployeeApi = process.env.APOLLO_API_URL + '/organizations/load_snippets';
      const response = await this.httpService.axiosRef.post(
        detailEmployeeApi,
        {
          ids: body.ids,
          include_logo_info: true,
        },
        {
          headers: {
            'X-Api-Key': ApolloConfig.apiKey,
          },
        }
      );

      return response.data
    } catch (error) {
      console.log('Error in getOrganizationsSnippet', error);
      throw new NotFoundException('There is no available employee');
    }
  }

  async getAccountsSnippet (body: OrganizationsSnippetDto){
    try {
      const detailEmployeeApi = process.env.APOLLO_API_URL + '/accounts/load_snippets';
      const response = await this.httpService.axiosRef.post(
        detailEmployeeApi,
        {
          ids: body.ids
        },
        {
          headers: {
            'X-Api-Key': ApolloConfig.apiKey,
          },
        }
      );

      return response.data
    } catch (error) {
      console.log('Error in getOrganizationsSnippet', error);
      throw new NotFoundException('There is no available employee');
    }
  }

  async getDetailEmployee(employeeId: string) {
    try {
      const detailEmployeeApi = process.env.APOLLO_API_URL + '/mixed_people/add_to_my_prospects';
      const response = await this.httpService.axiosRef.post(
        detailEmployeeApi,
        {
          entity_ids: [employeeId],
          analytics_context: 'Searcher: Individual Add Button',
          skip_fetching_people: true,
          cta_name: 'Access email',
        },
        {
          headers: {
            'X-Api-Key': ApolloConfig.apiKey,
          },
        }
      );

      const contact = response.data?.contacts?.[0];
      if (!contact) {
        throw new NotFoundException('There is no available employee');
      }

      return contact;
    } catch (error) {
      console.log('Error in getDetailEmployee', error);
      throw new NotFoundException('There is no available employee');
    }
  }

  async getTag(searchText: string, type: TagTypeEnum) {
    try {
      if (type === TagTypeEnum.EMPLOYEE) {
        return EMPLOYEE_RANGE;
      }

      const queryParams = qs.stringify({
        q_tag_fuzzy_name: searchText,
        kind: type,
        display_mode: 'fuzzy_select_mode',
      });
      const tagApi = `${process.env.APOLLO_API_URL}/tags/search?${queryParams}`;
      const response = await this.httpService.axiosRef.get(tagApi, {
        headers: {
          'X-Api-Key': ApolloConfig.apiKey,
        },
      });

      return response.data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getFacets(queryDto: SearchFacets) {
    try {
      const detailFacetsApi = process.env.APOLLO_API_URL + '/mixed_people/facets';
      const response = await this.httpService.axiosRef.post(
        detailFacetsApi,
        {
          context: 'people-index-page',
          display_mode: 'explorer_mode',
          open_factor_names: queryDto?.openFactorNames ?? null,
          // api_key: ApolloConfig.apiKey,
          not_exist_fields: queryDto?.notExistFields,
          exist_fields: queryDto?.notExistFields,
          total_funding_range: {
            max: queryDto?.totalFundingRangeMax ?? null,
            min: queryDto?.totalFundingRangeMin ?? null,
          },
        },
        {
          headers: {
            'X-Api-Key': ApolloConfig.apiKey,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getSearchSignals(searchText: string, modality: string) {
    try {
      const body = qs.stringify({
        q_name: searchText,
        // api_key: ApolloConfig.apiKey,
        modality: modality,
      });
      const signalApiUrl = `${process.env.APOLLO_API_URL}/search_signals/search`;
      const response = await this.httpService.axiosRef.post(signalApiUrl, body, {
        headers: {
          'X-Api-Key': ApolloConfig.apiKey,
        },
      });

      return response.data;
    } catch (error) {
      console.log('Error in getSearchSignals:', error);
      throw error;
    }
  }

  async getIntentDataTopicCategory() {
    try {
      const queryParams = qs.stringify({
        api_key: ApolloConfig.apiKey,
      });
      const topicApi = `${process.env.APOLLO_API_URL}/intent_data_topics/get_all_intent_data_categories?${queryParams}`;
      const response = await this.httpService.axiosRef.get(topicApi, {
        headers: {
          'X-Api-Key': ApolloConfig.apiKey,
        },
      });

      return response.data;
    } catch (error) {
      console.log('Error in getIntentDataTopicCategory:', error);
      throw error;
    }
  }

  async getIntentDataTopicByName(topicName: string) {
    try {
      if (topicName?.length < 3) {
        throw new NotFoundException('Please enter at least 3 characters to search');
      }
      const queryParams = qs.stringify({
        // api_key: ApolloConfig.apiKey,
        query: topicName,
      });
      const topicApi = `${process.env.APOLLO_API_URL}/intent_data_topics/search?${queryParams}`;
      const response = await this.httpService.axiosRef.get(topicApi, {
        headers: {
          'X-Api-Key': ApolloConfig.apiKey,
        },
      });

      return response.data;
    } catch (error) {
      console.log('Error in getIntentDataTopicByName:', error);
      throw error;
    }
  }

  async getIntentDataTopicByCategoryName(categoryName: string) {
    try {
      const queryParams = qs.stringify({
        // api_key: ApolloConfig.apiKey,
        category: categoryName,
      });
      const topicApi = `${process.env.APOLLO_API_URL}/intent_data_topics/find_by_category?${queryParams}`;
      const response = await this.httpService.axiosRef.get(topicApi, {
        headers: {
          'X-Api-Key': ApolloConfig.apiKey,
        },
      });

      return response.data;
    } catch (error) {
      console.log('Error in getIntentDataTopicByCategoryName:', error);
      throw error;
    }
  }

  async getCompaniesWithExploreMode(companyQuery: CompanyFinderQuery) {
    try {
      const {
        page = 1,
        searchText,
        organizationId,
        locations,
        employeeRanges,
        industryTagIds,
        containOneKeywords,
        containAllKeyWords,
        excludeKeyWords,
        accountStageIds,
        notAccountStageIds,
        searchSignalIds,
        intentStrengths,
        intentIds,
        recommendationScoresMinTranche,
        organizationJobLocations,
        qOrganizationJobTitles,
        currentlyUsingAnyOfTechnologyUids,
        existFields,
        notExistFields,
        organizationTradingStatus,
        organizationLatestFundingStageCd,
        totalFundingRangeMin,
        totalFundingRangeMax,
        organizationNumJobsRangeMin,
        organizationNumJobsRangeMax,
        organizationJobPostedAtRangeMin,
        organizationJobPostedAtRangeMax,
        notOrganizationIds,
        limit = 25,
      } = companyQuery;
      const searchCompanyApi = process.env.APOLLO_API_URL + '/organizations/search';
      const bodyRequest: any = {
        finder_table_layout_id: "6674b20eecfedd000184539f",
        finder_view_id: '5b6dfc5a73f47568b2e5f11c',
        page,
        display_mode: 'explorer_mode',
        per_page: limit,
        // open_factor_names: [],
        // num_fetch_result: 6,
        context: 'companies-index-page',
        show_suggestions: false,
        // ui_finder_random_seed: 'm9c5pkedl4l',
        // api_key: ApolloConfig.apiKey,
      };

      if (searchText) {
        bodyRequest.q_organization_name = searchText;
      }

      if (organizationId) {
        bodyRequest.organization_ids = [organizationId];
      }
      //x
      if (industryTagIds && industryTagIds.length) {
        bodyRequest.organization_industry_tag_ids = industryTagIds;
      }

      if (employeeRanges && employeeRanges.length) {
        bodyRequest.organization_num_employees_ranges = employeeRanges;
      }

      if (locations && locations.length) {
        bodyRequest.organization_locations = locations;
      }

      if (containOneKeywords && containOneKeywords.length) {
        bodyRequest.q_organization_keyword_tags = containOneKeywords;
      }

      if (containAllKeyWords && containAllKeyWords.length) {
        bodyRequest.q_anded_organization_keyword_tags = containOneKeywords;
      }

      if (excludeKeyWords && excludeKeyWords.length) {
        bodyRequest.q_not_organization_keyword_tags = containOneKeywords;
      }

      if (accountStageIds && accountStageIds.length) {
        bodyRequest.account_stage_ids = accountStageIds;
      }

      if (notAccountStageIds && notAccountStageIds.length) {
        bodyRequest.not_account_stage_ids = notAccountStageIds;
      }

      if (searchSignalIds && searchSignalIds.length) {
        bodyRequest.search_signal_ids = searchSignalIds;
      }

      if (intentStrengths && intentStrengths.length) {
        bodyRequest.intent_strengths = intentStrengths;
      }

      if (intentIds && intentIds.length > 0) {
        bodyRequest.intent_ids = intentIds;
      }

      if (recommendationScoresMinTranche) {
        bodyRequest.recommendation_scores_min_tranche = recommendationScoresMinTranche;
      }

      if (organizationJobLocations) {
        bodyRequest.organization_job_locations = organizationJobLocations;
      }

      if (qOrganizationJobTitles) {
        bodyRequest.q_organization_job_titles = qOrganizationJobTitles;
      }

      if (currentlyUsingAnyOfTechnologyUids && currentlyUsingAnyOfTechnologyUids.length > 0) {
        bodyRequest.currently_using_any_of_technology_uids = currentlyUsingAnyOfTechnologyUids;
      }

      if (existFields && existFields?.length > 0) {
        bodyRequest.exist_fields = existFields;
      }

      if (notExistFields && notExistFields?.length > 0) {
        bodyRequest.not_exist_fields = notExistFields;
      }

      if (organizationTradingStatus && organizationTradingStatus.length) {
        bodyRequest.organization_trading_status = organizationTradingStatus;
      }

      if (organizationLatestFundingStageCd && organizationLatestFundingStageCd.length) {
        bodyRequest.organization_latest_funding_stage_cd = organizationLatestFundingStageCd;
      }

      if (totalFundingRangeMin || totalFundingRangeMax) {
        bodyRequest.total_funding_range = {};

        if (totalFundingRangeMin) {
          bodyRequest.total_funding_range.min = totalFundingRangeMin;
        }

        if (totalFundingRangeMax) {
          bodyRequest.total_funding_range.max = totalFundingRangeMax;
        }
      }

      if (organizationNumJobsRangeMin || organizationNumJobsRangeMax) {
        bodyRequest.organization_num_jobs_range = {};

        if (organizationNumJobsRangeMin) {
          bodyRequest.organization_num_jobs_range.min = organizationNumJobsRangeMin;
        }

        if (organizationNumJobsRangeMax) {
          bodyRequest.organization_num_jobs_range.max = organizationNumJobsRangeMax;
        }
      }

      if (organizationJobPostedAtRangeMin || organizationJobPostedAtRangeMax) {
        bodyRequest.organization_job_posted_at_range = {};

        if (organizationJobPostedAtRangeMin) {
          bodyRequest.organization_job_posted_at_range.min = organizationJobPostedAtRangeMin;
        }

        if (organizationJobPostedAtRangeMax) {
          bodyRequest.organization_job_posted_at_range.max = organizationJobPostedAtRangeMax;
        }
      }

      if (notOrganizationIds && notOrganizationIds.length > 0) {
        bodyRequest.not_organization_ids = notOrganizationIds;
      }


      const response = await this.httpService.axiosRef.post(searchCompanyApi, bodyRequest, {
        headers: {
          'X-Api-Key': ApolloConfig.apiKey,
        },
      });
      return response.data;
    } catch (error) {
      console.log(error);
    }
  }

  async getCompaniesFacets() {
    try {
      const body = {
        context: 'companies-index-page',
        // api_key: ApolloConfig.apiKey,
        display_mode: 'explorer_mode',
        open_factor_names: ['account_stage_ids'],
      };
      const signalApiUrl = `${process.env.APOLLO_API_URL}/mixed_companies/facets`;
      const response = await this.httpService.axiosRef.post(signalApiUrl, body, {
        headers: {
          'X-Api-Key': ApolloConfig.apiKey,
        },
      });

      return response.data;
    } catch (error) {
      console.log('Error in getCompaniesFacets:', error);
      throw error;
    }
  }

  async getNewsOfCompany(queryDto: SearchFacets) {
    try {
      const detailFacetsApi = process.env.APOLLO_API_URL + '/news_articles/search';
      const response = await this.httpService.axiosRef.post(
        detailFacetsApi,
        {
          display_mode: 'explorer_mode',
          finder_version: 1,
          page: queryDto?.page || 1,
          num_fetch_result: 1,
          open_factor_names: queryDto?.openFactorNames ?? null,
          organization_ids: queryDto.organizationIds,
          // api_key: ApolloConfig.apiKey,
          sort_by_field: "published_at",
          per_page: queryDto?.per_page || 10,
          show_suggestions: false,
          categories: queryDto?.categories || [],
          sort_ascending: queryDto?.sortAscending
        },
        {
          headers: {
            'X-Api-Key': ApolloConfig.apiKey,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getNewFeedEvent(queryDto: SearchFacets) {
    try {
      const detailFacetsApi = process.env.APOLLO_API_URL + '/newsfeed_events/search';
      const response = await this.httpService.axiosRef.post(
        detailFacetsApi,
        {
          display_mode: 'explorer_mode',
          finder_version: 1,
          num_fetch_result: 1,
          page: queryDto?.page || 1,
          per_page: queryDto?.per_page || 25,
          organization_ids: queryDto.organizationIds,
          // api_key: ApolloConfig.apiKey,
          sort_by_field: "newsfeed_event_date",
          show_suggestions: false,
          sort_ascending: queryDto?.sortAscending,
          q_newsfeed_event_types: [
            'job_added'
          ]
        },
        {
          headers: {
            'X-Api-Key': ApolloConfig.apiKey,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getSimilarPersonInCompany (body: GetSimilarPerson) {
    try {
      const payload = {
        searchText: body.companyName,
        personTitles: [body.jobTitle]
      }
      const data = await this.getPeople(payload)

      return this.formatOutputData({ key: 'GET_SIMILAR_PERSON_IN_COMPANY' }, { data: data.contacts || data.people });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}

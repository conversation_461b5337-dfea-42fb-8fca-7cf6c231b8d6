import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { BaseAbstractService } from 'src/base/base.abstract.service';
import { ProxyCurlConfig } from 'src/configs/configs.constants';
import { EmployeeFinderQuery } from '../dto/employee-finder.dto';
import { writeFileSync } from 'fs';
// import { CacheService } from 'src/modules/cache/cache.service';
import { ApolloService } from './apollo.service';

@Injectable()
export class EmployeeFinderService extends BaseAbstractService {
  constructor(
    private i18nService: I18nService,
    private readonly httpService: HttpService,
    // private readonly cacheService: CacheService,
    private readonly apolloService: ApolloService
  ) {
    super(i18nService);
  }

  async getEmployees(queryDto: EmployeeFinderQuery) {
    const { companyLinkedUrl, nextPageCode: currentPageCode, page } = queryDto;
    if (!companyLinkedUrl) {
      const peopleOfOrganization = await this.apolloService.getPeople(queryDto, page);
      return this.formatOutputData({ key: 'GET_EMPLOYEES' }, { data: peopleOfOrganization });
    }

    const rawCompanyName = companyLinkedUrl.split('.com/company/')[1].split('/')[0];
    const { url, apiKey } = ProxyCurlConfig;
    const cachedEmployeesKey = `employee-finders.${rawCompanyName}.${currentPageCode}`;
    try {
      // const standardResponseJSON = await this.cacheService.get(cachedEmployeesKey);
      // if (standardResponseJSON) {
      //   return this.formatOutputData({ key: 'GET_EMPLOYEES' }, { data: JSON.parse(standardResponseJSON) });
      // }

      const { data } = await this.httpService.axiosRef.get(url, {
        params: {
          url: companyLinkedUrl,
          employment_status: 'current',
          enrich_profiles: 'enrich',
          after: currentPageCode,
        },
        headers: {
          Authorization: `Bearer ${apiKey}`,
        },
      });

      const { employees, next_page } = data;
      const standardEmployees = employees.map((employee) => ({
        profileUrl: employee.profile_url,
        fullName: employee.profile.full_name,
        jobTitle: employee.profile.experiences.find((item) =>
          item.company_linkedin_profile_url?.includes(rawCompanyName)
        )?.title,
      }));
      const nextPageCode = !next_page ? null : next_page.split('&after=')[1];

      const response = { employees: standardEmployees, nextPageCode };

      // await this.cacheService.set(cachedEmployeesKey, JSON.stringify(response), 1000 * 60 * 60 * 24); //1day

      return this.formatOutputData({ key: 'GET_EMPLOYEES' }, { data: response });
    } catch (error) {
      console.log(error);
      return this.throwCommonMessage('GET_EMPLOYEES', error);
    }
  }
}

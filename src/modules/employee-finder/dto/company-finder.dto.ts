import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsOptional, IsPositive } from 'class-validator';
import { IntentStrengths, RecommendationScores } from './employee-finder.dto';

export class CompanyFinderQuery {
  @ApiPropertyOptional()
  @IsOptional()
  organizationId?: string;

  @ApiPropertyOptional({ description: '[Americas, EMEA]' })
  @IsOptional()
  locations?: string[];

  @ApiPropertyOptional({ description: `['1,50', '50,100']` })
  @IsOptional()
  employeeRanges?: string[];

  @ApiPropertyOptional({ description: `['id1', 'id2']` })
  @IsOptional()
  industryTagIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsPositive()
  page?: number;

  @ApiPropertyOptional()
  @IsOptional()
  searchText?: string;

  @ApiPropertyOptional()
  @IsOptional()
  containOneKeywords?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  containAllKeyWords?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  excludeKeyWords?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  accountStageIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  notAccountStageIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  searchSignalIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsIn(IntentStrengths, { each: true })
  intentStrengths?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  intentIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsIn(RecommendationScores, { each: true })
  recommendationScoresMinTranche?: string;

  @ApiPropertyOptional()
  @IsOptional()
  organizationJobLocations?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  qOrganizationJobTitles?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  currentlyUsingAnyOfTechnologyUids?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  existFields?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  notExistFields?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  organizationTradingStatus?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  organizationLatestFundingStageCd?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  totalFundingRangeMin?: number;

  @ApiPropertyOptional()
  @IsOptional()
  totalFundingRangeMax?: number;

  @ApiPropertyOptional()
  @IsOptional()
  organizationNumJobsRangeMin?: string;

  @ApiPropertyOptional()
  @IsOptional()
  organizationNumJobsRangeMax?: string;

  @ApiPropertyOptional()
  @IsOptional()
  organizationJobPostedAtRangeMin?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  organizationJobPostedAtRangeMax?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  notOrganizationIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsPositive()
  limit?: number;
}


export class CompanyDetailQuery {
  @ApiPropertyOptional()
  @IsOptional()
  withOutContext?: boolean;
}
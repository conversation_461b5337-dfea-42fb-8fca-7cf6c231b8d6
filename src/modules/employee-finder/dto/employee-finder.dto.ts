import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsDate, IsIn, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';

export const ContactStatus = ['likely_to_engage', 'verified', 'unverified', 'update_required', 'unavailable'];

export const IntentStrengths = ['mid', 'none', 'low', 'high'];
export const RecommendationScores = ['good', 'excellent', 'fair', 'poor'];

function transformNullableDate({ value }: TransformFnParams) {
  if (value === null) {
    return null;
  }
  return new Date(value);
}

export class OrganizationsSnippetDto {
  @ApiPropertyOptional()
  @IsOptional()
  ids?: string[];
}

export class EmployeeFinderQuery {
  @ApiPropertyOptional()
  @IsOptional()
  companyLinkedUrl?: string;

  @ApiPropertyOptional()
  @IsOptional()
  nextPageCode?: string;

  @ApiPropertyOptional()
  @IsOptional()
  organizationId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({ description: '[Americas, EMEA]' })
  @IsOptional()
  locations?: string[];

  @ApiPropertyOptional({ description: `['1,50', '50,100']` })
  @IsOptional()
  employeeRanges?: string[];

  @ApiPropertyOptional({ description: `['id1', 'id2']` })
  @IsOptional()
  industryTagIds?: string[];

  @ApiPropertyOptional({ description: `['p1', 'p1']` })
  @IsOptional()
  personTitles?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  searchText?: string;

  @ApiPropertyOptional()
  @IsOptional()
  containOneKeywords?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  containAllKeyWords?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  excludeKeyWords?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsIn(ContactStatus, { each: true })
  contactEmailStatuses?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  searchSignalIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsIn(['yes', 'no'])
  contactEmailOpened?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  contactEmailOpenedAtLeast?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDate()
  @Transform(transformNullableDate)
  contactEmailOpenedAtDateRangeMax?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDate()
  @Transform(transformNullableDate)
  contactEmailOpenedAtDateRangeMin?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsIn(IntentStrengths, { each: true })
  intentStrengths?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  intentIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsIn(RecommendationScores, { each: true })
  recommendationScoresMinTranche?: string;

  @ApiPropertyOptional()
  @IsOptional()
  currentlyUsingAnyOfTechnologyUids?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  existFields?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  notExistFields?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  organizationTradingStatus?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  organizationLatestFundingStageCd?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  totalFundingRangeMin?: number;

  @ApiPropertyOptional()
  @IsOptional()
  totalFundingRangeMax?: number;

  @ApiPropertyOptional()
  @IsOptional()
  personName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  organizationNumJobsRangeMin?: string;

  @ApiPropertyOptional()
  @IsOptional()
  organizationNumJobsRangeMax?: string;

  @ApiPropertyOptional()
  @IsOptional()
  organizationJobPostedAtRangeMin?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  organizationJobPostedAtRangeMax?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  organizationJobLocations?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  qOrganizationJobTitles?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  qPersonPersonaIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  mixedAccountIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  metaMode?: string;

  @ApiPropertyOptional()
  @IsOptional()
  notOrganizationIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  personPastOrganizationIds?: string[];
}

export class RecommendedFilterQuery extends  EmployeeFinderQuery {
  @ApiPropertyOptional()
  @IsOptional()
  company?: string;

  @ApiPropertyOptional()
  @IsOptional()
  pageSize?: number;
}

export enum TagTypeEnum {
  PERSON_TITLE = 'person_title',
  LOCATION = 'location',
  EMPLOYEE = 'employee',
  INDUSTRY = 'linkedin_industry',
  TECHNOLOGY = 'technology',
}

export class SearchTagQuery {
  @ApiPropertyOptional()
  @IsOptional()
  searchText?: string;

  @ApiPropertyOptional()
  @IsOptional()
  type?: TagTypeEnum;
}

export const SignalModality = ['company', 'people'];

export class SearchSignalQuery {
  @ApiPropertyOptional()
  @IsOptional()
  searchText?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsIn(SignalModality)
  modality?: string;
}

export class SearchFacets {
  @ApiPropertyOptional()
  @IsOptional()
  existFields?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  notExistFields?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  openFactorNames?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  totalFundingRangeMax: number;

  @ApiPropertyOptional()
  @IsOptional()
  totalFundingRangeMin: number;

  @ApiPropertyOptional()
  @IsOptional()
  page: number;

  @ApiPropertyOptional()
  @IsOptional()
  per_page: number;

  @ApiPropertyOptional()
  @IsOptional()
  sortAscending: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  organizationIds?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  categories?: string[];
}

export interface IApolloContactDto {
  contact_roles: any;
  id: string;
  first_name: string;
  last_name: string;
  name: string;
  linkedin_url: string;
  title: string;
  contact_stage_id: string;
  owner_id: string;
  creator_id: string;
  person_id: string;
  email_needs_tickling: boolean;
  organization_name: string;
  source: string;
  original_source: string;
  organization_id: string;
  headline: string;
  photo_url: string;
  present_raw_address: string;
  linkedin_uid: string;
  extrapolated_email_confidence: string;
  salesforce_id: string;
  salesforce_lead_id: string;
  salesforce_contact_id: string;
  salesforce_account_id: string;
  crm_owner_id: string;
  created_at: string;
  emailer_campaign_ids: string[];
  direct_dial_status: string;
  direct_dial_enrichment_failed_at: string;
  email_status: string;
  email_source: string;
  account_id: string;
  last_activity_date: string;
  hubspot_vid: string;
  hubspot_company_id: string;
  crm_id: string;
  sanitized_phone: string;
  merged_crm_ids: string;
  updated_at: string;
  queued_for_crm_push: string;
  suggested_from_rule_engine_config_id: string;
  email_unsubscribed: string;
  person_deleted: string;
  label_ids: string[];
  has_pending_email_arcgate_request: boolean;
  has_email_arcgate_request: boolean;
  existence_level: string;
  email: string;
  email_from_customer: string;
  typed_custom_fields: any
  custom_field_errors: any
  crm_record_url: string;
  email_status_unavailable_reason: string;
  email_true_status: string;
  updated_email_true_status: boolean;
  contact_rule_config_statuses: string[];
  source_display_name: string;
  twitter_url: string;
  contact_campaign_statuses: string[];
  contact_emails: string[];
  time_zone: string;
  intent_strength: string;
  show_intent: boolean;
  phone_numbers: [
    {
      raw_number: string;
      sanitized_number: string;
      type: string;
      position: number;
      status: string;
      dnc_status: string;
      dnc_other_info: string;
      dialer_flags: {
        country_name: string;
        country_enabled: boolean;
        high_risk_calling_enabled: boolean;
        potential_high_risk_number: boolean;
      };
    }
  ];
  account_phone_note: string;
  free_domain: boolean;
  is_likely_to_engage: boolean;
  email_domain_catchall: boolean;
}

export class GetSimilarPerson {
  @ApiProperty()
  @IsNotEmpty()
  companyName: string;

  @ApiProperty()
  @IsNotEmpty()
  jobTitle: string;
}

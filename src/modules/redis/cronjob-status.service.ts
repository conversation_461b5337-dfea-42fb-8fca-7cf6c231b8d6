import { Injectable } from '@nestjs/common';
import { RedisService } from './redis.service';
import {
  CronJobInfo,
  CronJobStatus,
  CronJobExecution,
  CronJobMetrics,
} from './interfaces/cronjob-status.interface';
import * as os from 'os';

@Injectable()
export class CronJobStatusService {
  private readonly CRONJOB_PREFIX = 'cronjob:status';
  private readonly CRONJOB_EXECUTION_PREFIX = 'cronjob:execution';
  private readonly CRONJOB_METRICS_KEY = 'cronjob:metrics';
  constructor(private readonly redisService: RedisService) {}

  /**
   * Initialize or update cronjob information
   */
  async initializeCronJob(name: string, schedule: string): Promise<void> {
    const key = `${this.CRONJOB_PREFIX}:${name}`;
    const now = new Date();

    const existingJob = await this.getCronJobInfo(name);

    const jobInfo: CronJobInfo = {
      name,
      schedule,
      status: CronJobStatus.IDLE,
      hostname: os.hostname(),
      instanceId: process.pid.toString(),
      executionCount: existingJob?.executionCount || 0,
      successCount: existingJob?.successCount || 0,
      failureCount: existingJob?.failureCount || 0,
      createdAt: existingJob?.createdAt || now,
      updatedAt: now,
      ...existingJob,
    };

    await this.redisService.hSet(key, 'data', JSON.stringify(jobInfo));
    await this.redisService.expire(key, 86400 * 7); // Expire after 7 days
  }

  /**
   * Start cronjob execution
   */
  async startCronJobExecution(name: string, executionId: string): Promise<void> {
    const key = `${this.CRONJOB_PREFIX}:${name}`;
    const executionKey = `${this.CRONJOB_EXECUTION_PREFIX}:${name}:${executionId}`;
    const now = new Date();

    // Update job status
    const jobInfo = await this.getCronJobInfo(name);
    if (jobInfo) {
      jobInfo.status = CronJobStatus.RUNNING;
      jobInfo.lastStarted = now;
      jobInfo.updatedAt = now;
      jobInfo.executionCount = (jobInfo.executionCount || 0) + 1;

      await this.redisService.hSet(key, 'data', JSON.stringify(jobInfo));
    }

    // Create execution record
    const currentRevision = process.env.BUILD_REVISION || 'dev-local';

    const execution: CronJobExecution = {
      jobName: name,
      executionId,
      startTime: now,
      status: CronJobStatus.RUNNING,
      hostname: os.hostname(),
      revision: currentRevision,
      shortRevision: currentRevision.substring(0, 7),
    };

    await this.redisService.set(executionKey, JSON.stringify(execution), { EX: 86400 }); // Expire after 1 day
  }

  /**
   * Complete cronjob execution successfully
   */
  async completeCronJobExecution(
    name: string,
    executionId: string,
    duration?: number,
  ): Promise<void> {
    const key = `${this.CRONJOB_PREFIX}:${name}`;
    const executionKey = `${this.CRONJOB_EXECUTION_PREFIX}:${name}:${executionId}`;
    const now = new Date();

    // Update job status
    const jobInfo = await this.getCronJobInfo(name);
    if (jobInfo) {
      jobInfo.status = CronJobStatus.COMPLETED;
      jobInfo.lastCompleted = now;
      jobInfo.lastScheduled = now;
      jobInfo.updatedAt = now;
      jobInfo.duration = duration;
      jobInfo.successCount = (jobInfo.successCount || 0) + 1;

      await this.redisService.hSet(key, 'data', JSON.stringify(jobInfo));
    }

    // Update execution record
    const executionData = await this.redisService.get(executionKey);
    if (executionData) {
      const execution: CronJobExecution = JSON.parse(executionData);
      execution.endTime = now;
      execution.status = CronJobStatus.COMPLETED;
      execution.duration = duration;

      await this.redisService.set(executionKey, JSON.stringify(execution), { EX: 86400 });
    }

    // Cleanup old executions - keep only last 10
    await this.cleanupOldExecutionsForJob(name, 10);
  }

  /**
   * Fail cronjob execution
   */
  async failCronJobExecution(
    name: string,
    executionId: string,
    errorMessage: string,
    duration?: number,
  ): Promise<void> {
    const key = `${this.CRONJOB_PREFIX}:${name}`;
    const executionKey = `${this.CRONJOB_EXECUTION_PREFIX}:${name}:${executionId}`;
    const now = new Date();

    // Update job status
    const jobInfo = await this.getCronJobInfo(name);
    if (jobInfo) {
      jobInfo.status = CronJobStatus.FAILED;
      jobInfo.lastFailed = now;
      jobInfo.updatedAt = now;
      jobInfo.duration = duration;
      jobInfo.errorMessage = errorMessage;
      jobInfo.failureCount = (jobInfo.failureCount || 0) + 1;

      await this.redisService.hSet(key, 'data', JSON.stringify(jobInfo));
    }

    // Update execution record
    const executionData = await this.redisService.get(executionKey);
    if (executionData) {
      const execution: CronJobExecution = JSON.parse(executionData);
      execution.endTime = now;
      execution.status = CronJobStatus.FAILED;
      execution.duration = duration;
      execution.errorMessage = errorMessage;

      await this.redisService.set(executionKey, JSON.stringify(execution), { EX: 86400 });
    }

    // Cleanup old executions - keep only last 10
    await this.cleanupOldExecutionsForJob(name, 10);
  }

  /**
   * Set cronjob as locked
   */
  async lockCronJob(name: string): Promise<void> {
    const key = `${this.CRONJOB_PREFIX}:${name}`;
    const jobInfo = await this.getCronJobInfo(name);

    if (jobInfo) {
      jobInfo.status = CronJobStatus.LOCKED;
      jobInfo.updatedAt = new Date();
      await this.redisService.hSet(key, 'data', JSON.stringify(jobInfo));
    }
  }

  /**
   * Get cronjob information
   */
  async getCronJobInfo(name: string): Promise<CronJobInfo | null> {
    const key = `${this.CRONJOB_PREFIX}:${name}`;
    const data = await this.redisService.hGet(key, 'data');

    if (!data) {
      return null;
    }

    try {
      const jobInfo = JSON.parse(data);
      // Convert date strings back to Date objects
      if (jobInfo.createdAt) jobInfo.createdAt = new Date(jobInfo.createdAt);
      if (jobInfo.updatedAt) jobInfo.updatedAt = new Date(jobInfo.updatedAt);
      if (jobInfo.lastScheduled) jobInfo.lastScheduled = new Date(jobInfo.lastScheduled);
      if (jobInfo.lastStarted) jobInfo.lastStarted = new Date(jobInfo.lastStarted);
      if (jobInfo.lastCompleted) jobInfo.lastCompleted = new Date(jobInfo.lastCompleted);
      if (jobInfo.lastFailed) jobInfo.lastFailed = new Date(jobInfo.lastFailed);
      if (jobInfo.nextSchedule) jobInfo.nextSchedule = new Date(jobInfo.nextSchedule);

      return jobInfo;
    } catch (error) {
      console.error(`Error parsing cronjob data for ${name}:`, error);
      return null;
    }
  }

  /**
   * Get all cronjobs
   */
  async getAllCronJobs(): Promise<CronJobInfo[]> {
    const pattern = `${this.CRONJOB_PREFIX}:*`;
    const keys = await this.redisService.keys(pattern);

    const jobs: CronJobInfo[] = [];

    for (const key of keys) {
      const data = await this.redisService.hGet(key, 'data');
      if (data) {
        try {
          const jobInfo = JSON.parse(data);
          // Convert date strings back to Date objects
          if (jobInfo.createdAt) jobInfo.createdAt = new Date(jobInfo.createdAt);
          if (jobInfo.updatedAt) jobInfo.updatedAt = new Date(jobInfo.updatedAt);
          if (jobInfo.lastScheduled) jobInfo.lastScheduled = new Date(jobInfo.lastScheduled);
          if (jobInfo.lastStarted) jobInfo.lastStarted = new Date(jobInfo.lastStarted);
          if (jobInfo.lastCompleted) jobInfo.lastCompleted = new Date(jobInfo.lastCompleted);
          if (jobInfo.lastFailed) jobInfo.lastFailed = new Date(jobInfo.lastFailed);
          if (jobInfo.nextSchedule) jobInfo.nextSchedule = new Date(jobInfo.nextSchedule);

          jobs.push(jobInfo);
        } catch (error) {
          console.error(`Error parsing cronjob data for key ${key}:`, error);
        }
      }
    }

    return jobs.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Get cronjob execution history
   */
  async getCronJobExecutions(name: string, limit: number = 10): Promise<CronJobExecution[]> {
    const pattern = `${this.CRONJOB_EXECUTION_PREFIX}:${name}:*`;
    const keys = await this.redisService.keys(pattern);

    const executions: CronJobExecution[] = [];

    for (const key of keys) {
      const data = await this.redisService.get(key);
      if (data) {
        try {
          const execution = JSON.parse(data);
          // Convert date strings back to Date objects
          if (execution.startTime) execution.startTime = new Date(execution.startTime);
          if (execution.endTime) execution.endTime = new Date(execution.endTime);

          executions.push(execution);
        } catch (error) {
          console.error(`Error parsing execution data for key ${key}:`, error);
        }
      }
    }

    return executions.sort((a, b) => b.startTime.getTime() - a.startTime.getTime()).slice(0, limit);
  }

  /**
   * Get cronjob metrics
   */
  async getCronJobMetrics(): Promise<CronJobMetrics> {
    const jobs = await this.getAllCronJobs();

    const totalJobs = jobs.length;
    const runningJobs = jobs.filter((job) => job.status === CronJobStatus.RUNNING).length;
    const completedJobs = jobs.filter((job) => job.status === CronJobStatus.COMPLETED).length;
    const failedJobs = jobs.filter((job) => job.status === CronJobStatus.FAILED).length;

    const durations = jobs.filter((job) => job.duration).map((job) => job.duration!);
    const averageDuration =
      durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0;

    const lastExecutionTimes = jobs
      .filter((job) => job.lastStarted)
      .map((job) => job.lastStarted!)
      .sort((a, b) => b.getTime() - a.getTime());

    const lastExecutionTime = lastExecutionTimes.length > 0 ? lastExecutionTimes[0] : new Date(0);

    return {
      totalJobs,
      runningJobs,
      completedJobs,
      failedJobs,
      averageDuration,
      lastExecutionTime,
    };
  }

  /**
   * Clean up old execution records for a specific job - keep only the latest N executions
   */
  async cleanupOldExecutionsForJob(jobName: string, keepCount: number = 10): Promise<number> {
    const pattern = `${this.CRONJOB_EXECUTION_PREFIX}:${jobName}:*`;
    const keys = await this.redisService.keys(pattern);

    if (keys.length <= keepCount) {
      return 0; // No cleanup needed
    }

    // Get all executions with their start times
    const executions: { key: string; startTime: Date }[] = [];

    for (const key of keys) {
      const data = await this.redisService.get(key);
      if (data) {
        try {
          const execution = JSON.parse(data);
          const startTime = new Date(execution.startTime);
          executions.push({ key, startTime });
        } catch (error) {
          console.error(`Error parsing execution data for cleanup, key ${key}:`, error);
          // Delete corrupted data
          await this.redisService.del(key);
        }
      }
    }

    // Sort by start time (newest first) and keep only the latest N
    executions.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
    const keysToDelete = executions.slice(keepCount).map((exec) => exec.key);

    // Delete old executions
    let deletedCount = 0;
    for (const key of keysToDelete) {
      await this.redisService.del(key);
      deletedCount++;
    }

    return deletedCount;
  }

  /**
   * Clean up old execution records
   */
  async cleanupOldExecutions(olderThanDays: number = 7): Promise<number> {
    const pattern = `${this.CRONJOB_EXECUTION_PREFIX}:*`;
    const keys = await this.redisService.keys(pattern);

    const cutoffTime = new Date();
    cutoffTime.setDate(cutoffTime.getDate() - olderThanDays);

    let deletedCount = 0;

    for (const key of keys) {
      const data = await this.redisService.get(key);
      if (data) {
        try {
          const execution = JSON.parse(data);
          const startTime = new Date(execution.startTime);

          if (startTime < cutoffTime) {
            await this.redisService.del(key);
            deletedCount++;
          }
        } catch (error) {
          console.error(`Error parsing execution data for cleanup, key ${key}:`, error);
          // Delete corrupted data
          await this.redisService.del(key);
          deletedCount++;
        }
      }
    }

    return deletedCount;
  }


}

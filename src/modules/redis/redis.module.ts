/* istanbul ignore file */
import { Module } from '@nestjs/common';
import { createClient } from 'redis';
import { RedisService } from './redis.service';
import { CronJobStatusService } from './cronjob-status.service';
import { CronDashboardController } from './cron-dashboard.controller';
import { redisConfig } from '../../configs/configs.constants';

@Module({
  controllers: [CronDashboardController],
  providers: [
    RedisService,
    CronJobStatusService,
    {
      provide: 'REDIS_OPTIONS',
      useValue: {
        url: redisConfig.REDIS_CONNECTION,
        ...(redisConfig.IS_USE_ELASTICCACHE === 'ON'
          ? {
              redis: {
                tls: {
                  rejectUnauthorized: false,
                },
              },
            }
          : {}),
      },
    },
    {
      inject: ['REDIS_OPTIONS'],
      provide: 'REDIS_CLIENT',
      useFactory: async (options: { url: string; tls: object }) => {
        const client = createClient(options);
        await client.connect();
        return client;
      },
    },
  ],
  exports: [RedisService, CronJobStatusService],
})
export class RedisModule {}

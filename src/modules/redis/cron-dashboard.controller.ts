import { Controller, Get, UseGuards, Res, UseFilters, Query } from '@nestjs/common';
import { Response } from 'express';
import { CronJobStatusService } from './cronjob-status.service';
import { BasicAuthGuard } from '../../guards/basic-auth.guard';
import { BasicAuthExceptionFilter } from '../../filters/basic-auth-exception.filter';

@Controller('cron-dashboard')
@UseGuards(BasicAuthGuard)
@UseFilters(BasicAuthExceptionFilter)
export class CronDashboardController {
  constructor(private readonly cronJobStatusService: CronJobStatusService) {}

  /**
   * Format duration from milliseconds to human readable format
   */
  private formatDuration(durationMs: number | undefined | null): string {
    if (!durationMs || durationMs <= 0) {
      return 'N/A';
    }

    const ms = Math.round(durationMs);

    // Less than 1 second - show milliseconds
    if (ms < 1000) {
      return `${ms}ms`;
    }

    // Less than 1 minute - show seconds with decimal
    if (ms < 60000) {
      const seconds = (ms / 1000).toFixed(1);
      return `${seconds}s`;
    }

    // Less than 1 hour - show minutes and seconds
    if (ms < 3600000) {
      const minutes = Math.floor(ms / 60000);
      const seconds = Math.floor((ms % 60000) / 1000);
      return seconds > 0 ? `${minutes}m ${seconds}s` : `${minutes}m`;
    }

    // 1 hour or more - show hours, minutes, and seconds
    const hours = Math.floor(ms / 3600000);
    const minutes = Math.floor((ms % 3600000) / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);

    let result = `${hours}h`;
    if (minutes > 0) result += ` ${minutes}m`;
    if (seconds > 0) result += ` ${seconds}s`;

    return result;
  }

  @Get()
  async getDashboard(@Res() res: Response) {
    const jobs = await this.cronJobStatusService.getAllCronJobs();
    const metrics = await this.cronJobStatusService.getCronJobMetrics();

    const html = this.generateDashboardHTML(jobs, metrics);
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  }

  @Get('job-details')
  async getJobDetails(@Query('name') name: string, @Res() res: Response) {
    if (!name) {
      res.status(400).send('Job name is required');
      return;
    }

    const job = await this.cronJobStatusService.getCronJobInfo(name);
    if (!job) {
      res.status(404).send('Job not found');
      return;
    }

    const html = this.generateJobDetailsHTML(job);
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  }

  @Get('job-history')
  async getJobHistory(@Query('name') name: string, @Res() res: Response) {
    if (!name) {
      res.status(400).send('Job name is required');
      return;
    }

    // Always get last 10 executions
    const executions = await this.cronJobStatusService.getCronJobExecutions(name, 10);
    const job = await this.cronJobStatusService.getCronJobInfo(name);

    const html = this.generateJobHistoryHTML(name, executions, job);
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  }

  // API Endpoints (JSON responses)
  @Get('api/jobs')
  async getAllJobs() {
    return await this.cronJobStatusService.getAllCronJobs();
  }

  @Get('api/job')
  async getJob(@Query('name') name: string) {
    if (!name) {
      throw new Error('Job name is required');
    }
    return await this.cronJobStatusService.getCronJobInfo(name);
  }

  @Get('api/executions')
  async getExecutions(@Query('name') name: string, @Query('limit') limit: string) {
    if (!name) {
      throw new Error('Job name is required');
    }
    const limitNum = limit ? parseInt(limit, 10) : 10;
    return await this.cronJobStatusService.getCronJobExecutions(name, limitNum);
  }

  @Get('api/metrics')
  async getMetrics() {
    return await this.cronJobStatusService.getCronJobMetrics();
  }

  @Get('api/cleanup')
  async cleanupOldExecutions(@Query('days') days: string) {
    const daysNum = days ? parseInt(days, 10) : 7;
    const deletedCount = await this.cronJobStatusService.cleanupOldExecutions(daysNum);
    return { deletedCount, message: `Cleaned up ${deletedCount} old execution records` };
  }

  private generateDashboardHTML(jobs: any[], metrics: any): string {
    const jobsHTML = jobs
      .map(
        (job) => `
      <tr class="job-row clickable-row" data-status="${job.status}" data-job-name="${job.name}" onclick="showExecutions('${job.name}')" title="Click to view execution history">
        <td class="job-name">${job.name}</td>
        <td><span class="status-badge status-${job.status}">${job.status}</span></td>
        <td>${job.schedule || 'N/A'}</td>
        <td>${job.lastStarted ? new Date(job.lastStarted).toLocaleString() : 'Never'}</td>
        <td>${job.lastCompleted ? new Date(job.lastCompleted).toLocaleString() : 'Never'}</td>
        <td>${this.formatDuration(job.duration)}</td>
        <td>${job.executionCount || 0}</td>
        <td>${job.successCount || 0}</td>
        <td>${job.failureCount || 0}</td>
        <td>
          <button class="btn-details" onclick="event.stopPropagation(); showJobDetails('${job.name}')">Details</button>
          <span class="click-hint">👆 Click row for history</span>
        </td>
      </tr>
    `,
      )
      .join('');

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CronJob Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .jobs-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .job-row:hover {
            background-color: #f8f9fa;
        }

        .clickable-row {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .clickable-row:hover {
            background-color: #e3f2fd !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .job-name {
            font-weight: 600;
            color: #333;
        }

        .click-hint {
            font-size: 0.7rem;
            color: #666;
            font-style: italic;
            display: block;
            margin-top: 0.2rem;
        }
        
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-idle { background: #e3f2fd; color: #1976d2; }
        .status-running { background: #fff3e0; color: #f57c00; }
        .status-completed { background: #e8f5e8; color: #388e3c; }
        .status-failed { background: #ffebee; color: #d32f2f; }
        .status-locked { background: #f3e5f5; color: #7b1fa2; }
        
        .btn-details, .btn-executions {
            padding: 0.4rem 0.8rem;
            margin: 0 0.2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s;
        }
        
        .btn-details {
            background: #2196f3;
            color: white;
        }
        
        .btn-executions {
            background: #4caf50;
            color: white;
        }
        
        .btn-details:hover, .btn-executions:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s;
        }
        
        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .last-updated {
            text-align: center;
            color: #666;
            margin-top: 2rem;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .metrics {
                grid-template-columns: repeat(2, 1fr);
            }
            
            table {
                font-size: 0.8rem;
            }
            
            th, td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 CronJob Dashboard</h1>
        <p>Real-time monitoring of scheduled tasks</p>
    </div>
    
    <div class="container">
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value">${metrics.totalJobs || 0}</div>
                <div class="metric-label">Total Jobs</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${metrics.runningJobs || 0}</div>
                <div class="metric-label">Running</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${metrics.completedJobs || 0}</div>
                <div class="metric-label">Completed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${metrics.failedJobs || 0}</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${this.formatDuration(metrics.averageDuration)}</div>
                <div class="metric-label">Avg Duration</div>
            </div>
        </div>
        
        <div class="jobs-table">
            <div class="table-header">
                📋 CronJob Status Overview
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Job Name</th>
                        <th>Status</th>
                        <th>Schedule</th>
                        <th>Last Started</th>
                        <th>Last Completed</th>
                        <th>Duration</th>
                        <th>Executions</th>
                        <th>Success</th>
                        <th>Failures</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${jobsHTML}
                </tbody>
            </table>
        </div>

        <div class="last-updated">
            Last updated: ${new Date().toLocaleString()}
        </div>
    </div>
    <button class="refresh-btn" onclick="location.reload()">
        🔄 Refresh
    </button>
    <script>
        function showJobDetails(jobName) {
            console.log('🔍 showJobDetails called with:', jobName);
            const url = '/cron-dashboard/job-details?name=' + encodeURIComponent(jobName);
            console.log('🔗 Opening Details URL:', url);
            window.open(url, '_blank');
        }

        function showExecutions(jobName) {
            console.log('📜 showExecutions called with:', jobName);
            const url = '/cron-dashboard/job-history?name=' + encodeURIComponent(jobName);
            console.log('🔗 Opening History URL:', url);
            window.open(url, '_blank');
        }

        // Debug info on page load
        window.onload = function() {
            console.log('🎯 Dashboard loaded successfully');
            console.log('📊 Jobs count:', document.querySelectorAll('.job-row').length);
            console.log('🖱️ Clickable rows:', document.querySelectorAll('.clickable-row').length);
            console.log('🔘 Details buttons:', document.querySelectorAll('.btn-details').length);
            console.log('💡 Click hints:', document.querySelectorAll('.click-hint').length);

            // Test if functions are available
            console.log('✅ showJobDetails function:', typeof showJobDetails);
            console.log('✅ showExecutions function:', typeof showExecutions);

            // Add visual feedback for clickable rows
            const rows = document.querySelectorAll('.clickable-row');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    console.log('🖱️ Hovering over job:', this.dataset.jobName);
                });
            });
        };

        // Auto refresh every 30 seconds
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
    `;
  }

  private generateJobDetailsHTML(job: any): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Details - ${job.name}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .job-card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .job-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #333;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .status-idle { background: #e3f2fd; color: #1976d2; }
        .status-running { background: #fff3e0; color: #f57c00; }
        .status-completed { background: #e8f5e8; color: #388e3c; }
        .status-failed { background: #ffebee; color: #d32f2f; }
        .status-locked { background: #f3e5f5; color: #7b1fa2; }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .detail-item {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .detail-label {
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.5rem;
        }

        .detail-value {
            font-size: 1.1rem;
            color: #333;
        }

        .back-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin-bottom: 2rem;
            transition: all 0.2s;
        }

        .back-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📋 Job Details</h1>
        <p>${job.name}</p>
    </div>

    <div class="container">
        <button class="back-btn" onclick="window.close()">← Back to Dashboard</button>

        <div class="job-card">
            <div class="job-title">${job.name}</div>
            <span class="status-badge status-${job.status}">${job.status}</span>

            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">Schedule</div>
                    <div class="detail-value">${job.schedule || 'N/A'}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Last Started</div>
                    <div class="detail-value">${job.lastStarted ? new Date(job.lastStarted).toLocaleString() : 'Never'}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Last Completed</div>
                    <div class="detail-value">${job.lastCompleted ? new Date(job.lastCompleted).toLocaleString() : 'Never'}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Last Failed</div>
                    <div class="detail-value">${job.lastFailed ? new Date(job.lastFailed).toLocaleString() : 'Never'}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Duration</div>
                    <div class="detail-value">${this.formatDuration(job.duration)}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Hostname</div>
                    <div class="detail-value">${job.hostname || 'N/A'}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Instance ID</div>
                    <div class="detail-value">${job.instanceId || 'N/A'}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Total Executions</div>
                    <div class="detail-value">${job.executionCount || 0}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Successful</div>
                    <div class="detail-value">${job.successCount || 0}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Failed</div>
                    <div class="detail-value">${job.failureCount || 0}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Success Rate</div>
                    <div class="detail-value">${job.executionCount ? Math.round(((job.successCount || 0) / job.executionCount) * 100) : 0}%</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">Created At</div>
                    <div class="detail-value">${job.createdAt ? new Date(job.createdAt).toLocaleString() : 'N/A'}</div>
                </div>
            </div>

            ${
              job.errorMessage
                ? `
            <div style="margin-top: 2rem; padding: 1rem; background: #ffebee; border-radius: 8px; border-left: 4px solid #d32f2f;">
                <div class="detail-label" style="color: #d32f2f;">Last Error Message</div>
                <div style="color: #d32f2f; font-family: monospace; font-size: 0.9rem; margin-top: 0.5rem;">${job.errorMessage}</div>
            </div>
            `
                : ''
            }
        </div>
    </div>
</body>
</html>
    `;
  }

  private generateJobHistoryHTML(jobName: string, executions: any[], job: any): string {
    const executionsHTML = executions
      .map(
        (exec) => `
      <tr class="execution-row" data-status="${exec.status}">
        <td>${exec.executionId}</td>
        <td><span class="status-badge status-${exec.status}">${exec.status}</span></td>
        <td>${new Date(exec.startTime).toLocaleString()}</td>
        <td>${exec.endTime ? new Date(exec.endTime).toLocaleString() : 'Running...'}</td>
        <td>${this.formatDuration(exec.duration)}</td>
        <td>${exec.hostname || 'N/A'}</td>
        <td class="revision-cell" title="Build revision: ${exec.revision || 'unknown'}">${exec.shortRevision || 'N/A'}</td>
        <td class="error-cell">${exec.errorMessage ? `<span class="error-msg" title="${exec.errorMessage}">❌ Error</span>` : '✅ Success'}</td>
      </tr>
    `,
      )
      .join('');

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Execution History - ${jobName}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .back-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin-bottom: 2rem;
            transition: all 0.2s;
        }

        .back-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .job-info {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .job-info h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .info-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .info-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }

        .info-label {
            font-size: 0.9rem;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .history-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table-header {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .execution-row:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-idle { background: #e3f2fd; color: #1976d2; }
        .status-running { background: #fff3e0; color: #f57c00; }
        .status-completed { background: #e8f5e8; color: #388e3c; }
        .status-failed { background: #ffebee; color: #d32f2f; }
        .status-locked { background: #f3e5f5; color: #7b1fa2; }

        .error-msg {
            color: #d32f2f;
            cursor: help;
        }

        .error-cell {
            text-align: center;
        }

        .revision-cell {
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            color: #667eea;
            font-weight: 600;
            cursor: help;
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #666;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            table {
                font-size: 0.8rem;
            }

            th, td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📜 Execution History</h1>
        <p>${jobName}</p>
    </div>

    <div class="container">
        <button class="back-btn" onclick="window.close()">← Back to Dashboard</button>

        ${
          job
            ? `
        <div class="job-info">
            <h3>Job Overview</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-value">${job.executionCount || 0}</div>
                    <div class="info-label">Total Executions</div>
                </div>
                <div class="info-item">
                    <div class="info-value">${job.successCount || 0}</div>
                    <div class="info-label">Successful</div>
                </div>
                <div class="info-item">
                    <div class="info-value">${job.failureCount || 0}</div>
                    <div class="info-label">Failed</div>
                </div>
                <div class="info-item">
                    <div class="info-value">${job.executionCount ? Math.round(((job.successCount || 0) / job.executionCount) * 100) : 0}%</div>
                    <div class="info-label">Success Rate</div>
                </div>
                <div class="info-item">
                    <div class="info-value">${this.formatDuration(job.duration)}</div>
                    <div class="info-label">Avg Duration</div>
                </div>
            </div>
        </div>
        `
            : ''
        }

        <div class="history-table">
            <div class="table-header">
                📋 Recent Executions (Last 10)
            </div>

            ${
              executions.length > 0
                ? `
            <table>
                <thead>
                    <tr>
                        <th>Execution ID</th>
                        <th>Status</th>
                        <th>Started</th>
                        <th>Completed</th>
                        <th>Duration</th>
                        <th>Hostname</th>
                        <th>Revision</th>
                        <th>Result</th>
                    </tr>
                </thead>
                <tbody>
                    ${executionsHTML}
                </tbody>
            </table>
            `
                : `
            <div class="no-data">
                <h3>No execution history found</h3>
                <p>This job hasn't been executed yet or execution records have been cleaned up.</p>
            </div>
            `
            }
        </div>
    </div>
</body>
</html>
    `;
  }
}

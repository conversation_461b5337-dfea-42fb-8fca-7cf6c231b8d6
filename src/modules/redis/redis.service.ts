import { Inject, Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { RedisClientType } from 'redis';
import { BaseAbstractService } from '../../base/base.abstract.service';

@Injectable()
export class RedisService extends BaseAbstractService {
  constructor(
    @Inject('REDIS_CLIENT') private readonly redis: RedisClientType,
    i18nService: I18nService,
  ) {
    super(i18nService);
  }

  getClient() {
    return this.redis;
  }

  async hSet(key: string, field: string, value: string): Promise<any> {
    return this.redis.hSet(key, field, value);
  }

  async hGet(key: string, field: string): Promise<any> {
    return this.redis.hGet(key, field);
  }

  async hGetAll(key: string): Promise<any> {
    return this.redis.hGetAll(key);
  }

  async hDel(key: string, field: string): Promise<any> {
    return this.redis.hDel(key, field);
  }

  async set(key: string, value: any, options?: any): Promise<any> {
    return this.redis.set(key, value, options);
  }

  async get(key: string): Promise<any> {
    return this.redis.get(key);
  }

  async del(key: string): Promise<any> {
    return this.redis.del(key);
  }

  async keys(pattern: string): Promise<any> {
    return this.redis.keys(pattern);
  }

  async ttl(key: string): Promise<any> {
    return this.redis.ttl(key);
  }

  async exists(key: string): Promise<number> {
    return this.redis.exists(key);
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    return this.redis.expire(key, seconds);
  }
}

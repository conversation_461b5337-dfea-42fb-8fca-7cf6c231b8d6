export enum CronJobStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  LOCKED = 'locked',
}

export interface CronJobInfo {
  name: string;
  status: CronJobStatus;
  schedule: string;
  nextSchedule?: Date;
  lastScheduled?: Date;
  lastStarted?: Date;
  lastCompleted?: Date;
  lastFailed?: Date;
  hostname?: string;
  instanceId?: string;
  duration?: number; // in milliseconds
  errorMessage?: string;
  executionCount?: number;
  successCount?: number;
  failureCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CronJobExecution {
  jobName: string;
  executionId: string;
  startTime: Date;
  endTime?: Date;
  status: CronJobStatus;
  duration?: number;
  errorMessage?: string;
  hostname?: string;
  revision?: string;
  shortRevision?: string;
}

export interface CronJobMetrics {
  totalJobs: number;
  runningJobs: number;
  completedJobs: number;
  failedJobs: number;
  averageDuration: number;
  lastExecutionTime: Date;
}

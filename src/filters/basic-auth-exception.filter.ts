import { ExceptionFilter, Catch, ArgumentsHost, UnauthorizedException } from '@nestjs/common';
import { Response } from 'express';

@Catch(UnauthorizedException)
export class BasicAuthExceptionFilter implements ExceptionFilter {
  catch(exception: UnauthorizedException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    // Check if this is a basic auth request (has WWW-Authenticate header)
    const wwwAuth = response.getHeader('WWW-Authenticate');
    
    if (wwwAuth && wwwAuth.toString().includes('Basic')) {
      // For basic auth, return 401 with proper headers to trigger browser popup
      response
        .status(401)
        .setHeader('WWW-Authenticate', 'Basic realm="CronJob Dashboard"')
        .send('Unauthorized');
    } else {
      // For other requests, return JSON response
      response.status(401).json({
        success: false,
        message: exception.message,
        result: null,
        timestamp: new Date().toISOString(),
        statusCode: 401,
      });
    }
  }
}

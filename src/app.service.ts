import { Injectable } from '@nestjs/common';


export const convertTimezone = (datetimeString, currentOffset) => {
  const date = new Date(datetimeString + 'Z');
  const standardCurrentOffset = currentOffset.replace('(', '').replace(')', '');
  const signCurrent = standardCurrentOffset.startsWith('UTC+') ? 1 : -1;
  const currentOffsetMinutes = Number(standardCurrentOffset.split(':')[1]);
  const currentOffsetHours = Math.abs(Number(standardCurrentOffset.split('UTC')[1].split(':')[0]));

  const currentOffsetTotalMinutes = signCurrent * (currentOffsetHours * 60 + currentOffsetMinutes);
  const targetOffset = -new Date().getTimezoneOffset();
  const offsetDiff = (targetOffset - currentOffsetTotalMinutes) * 60 * 1000;

  const newTime = new Date(date.getTime() + offsetDiff);

  const targetTimezone = Math.floor(targetOffset / 60);
  const targetMinute = (targetOffset / 60 - targetTimezone) * 60;
  const signTargetTimezone = targetTimezone >= 0 ? '+' : '-';
  const formattedDate = newTime
    .toISOString()
    .replace('T', ' ')
    .replace(
      /\.\d{3}Z/,
      ` ${signTargetTimezone}${targetTimezone < 10 ? '0' : ''}${targetTimezone}${targetMinute < 10 ? '0' : ''
      }${targetMinute}`
    );
  return {
    targetOffset,
    offsetDiff,
    newTime,
    targetTimezone,
    targetMinute,
    signTargetTimezone
  };
};

@Injectable()
export class AppService {
  getHello(test: string) {
    console.log('Test dvsdv', test);
    // return convertTimezone('2024-12-09T14:34', '(UTC+07:00)');
    return "David check test 20250609" + new Date().toISOString();

  }
}

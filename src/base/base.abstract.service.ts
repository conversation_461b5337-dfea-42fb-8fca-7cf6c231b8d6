import { I18nService } from "nestjs-i18n";
import { LanguageCode } from "../common/constants/common.constant";
import { IOptionLang, IResponseCommon, IResultDataCommon } from "../common/interfaces/common.interface";
import { getCommonMessageError, getCommonStatusCodeError } from "../common/utils/helpers.util";
import { HttpException } from "@nestjs/common";
import { Brackets } from "typeorm";

export class BaseAbstractService {
  private _i18nService: I18nService;
  protected constructor(_i18nService: I18nService) {
    this._i18nService = _i18nService;
  }
  public translate(key: string, options: { lang?: LanguageCode; args?: any } = {}): string {
    const { lang = LanguageCode.United_States } = options;
    return this._i18nService.translate(key, { ...options, lang });
  }

  /**
   * Format the output data for responding
   */
  public async formatOutputData(
    translateOptions: IOptionLang,
    resultData: IResultDataCommon,
    successFalse = false
  ): Promise<IResponseCommon> {
    const { lang, key, args } = translateOptions;
    const { data, statusCode } = resultData;

    let success = !data ? false : true;
    if (successFalse) {
      success = false;
    }

    return {
      success,
      message: this.translate(key, {
        lang: <LanguageCode>lang,
        args,
      }),
      result: !data ? null : data,
      statusCode,
    };
  }

  public async throwCommonMessage(key: string, error: any, customStatusCode?: number, getMessageFromError = true) {
    const message = getCommonMessageError(error);
    let msg;
    if (Array.isArray(message)) {
      msg = message.find((e) => e.message)?.message;
    }

    if (getMessageFromError) {
      throw new HttpException(msg || message, error.status || error.code || 500);
    }

    const statusCode = getCommonStatusCodeError(error);
    const result = await this.formatOutputData(
      {
        key,
        lang: LanguageCode.United_States,
        args: { message },
      },
      {
        data: null,
        statusCode: statusCode ?? customStatusCode,
      }
    );
    throw new HttpException(result, error.status || 500);
  }
}

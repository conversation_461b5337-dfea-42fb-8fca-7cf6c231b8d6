import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import * as dotenv from 'dotenv';
import * as bodyParser from 'body-parser';
import { json, urlencoded } from 'express';
import { NormalizeStringPipe } from './common/pipe/normalize-string.pipe';
import { HttpExceptionFilter } from './common/interceptors/http-exception.filter';
import { DEFAULT_REQUEST_SIZE_LIMIT, DEFAULT_TIMEOUT } from './common/constants/common.constant';
import * as cookieParser from 'cookie-parser';
import { CorsConfig } from './configs/configs.constants';

dotenv.config();
async function bootstrap() {
  const userApp = await NestFactory.create(AppModule, {
    bodyParser: false,
    logger: ['error', 'warn', 'log'],
    cors: {
      origin: CorsConfig.corsWhiteList,
      credentials: true,
      exposedHeaders: ['set-cookie'],
    },
  });

  const rawBodyBuffer = (req, res, buf, encoding) => {
    if (buf && buf.length) {
      req.rawBody = buf.toString(encoding || 'utf8');
    }
  };
  userApp.use(cookieParser());
  userApp.use(bodyParser.urlencoded({ verify: rawBodyBuffer, extended: true }));
  userApp.use(bodyParser.json({ verify: rawBodyBuffer, limit: '50mb' }));
  userApp.useGlobalFilters(new HttpExceptionFilter());
  const options = new DocumentBuilder()
    .setTitle('Job Finder API')
    .setDescription('The Job Finder API for agent')
    .setVersion('1.0')
    .addBearerAuth({
      type: 'http',
      name: 'Authorization',
      in: 'header',
    })
    .build();

  const userDocument = SwaggerModule.createDocument(userApp, options);
  SwaggerModule.setup('api/docs', userApp, userDocument);
  // Use global validation pipe.
  userApp.useGlobalPipes(new ValidationPipe({ transform: true }));
  userApp.useGlobalPipes(new NormalizeStringPipe());

  //TODO
  userApp.use(json({ limit: DEFAULT_REQUEST_SIZE_LIMIT }));
  userApp.use(urlencoded({ limit: DEFAULT_REQUEST_SIZE_LIMIT, extended: true }));
  // Enable CORS for AWS.
  // userApp.enableCors({ credentials: true, origin: ["http://localhost:3000","http://**********:80"] , methods: "GET,HEAD,PUT,PATCH,POST,DELETE" });
  userApp.enableCors({
    credentials: true,
    origin: ['http://localhost:3000', process.env.CLIENT_URL],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  });
  const userServer = await userApp.listen(process.env.APP_PORT, () => {
    console.log(`Server is running on port ${process.env.APP_PORT}`);
  });
  userServer.setTimeout(DEFAULT_TIMEOUT);
}

bootstrap();

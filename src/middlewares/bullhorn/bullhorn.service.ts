import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { BullHornConfig } from 'src/configs/configs.constants';
import * as urlLib from 'url';
import { stringify } from 'querystring';
import { IBhRestTokenUrlResponse, IGetAccessTokenResponse } from './interface/bullhorn-response.interface';
import { IBullhornConfigDto } from 'src/common/dto/bullhorn-config.dto';

@Injectable()
export class BullHornService {
  private readonly logger = new Logger(BullHornService.name);

  async getDataCenter(username: string) {
    try {
      const { data } = await axios.get(
        `https://rest.bullhornstaffing.com/rest-services/loginInfo?username=${encodeURIComponent(username)}`
      );

      return data;
    } catch {
      return {};
    }
  }

  async getAccessCode({ bhClientId, bhUsername, bhPassword, rootOauthUrl }: IBullhornConfigDto): Promise<string> {
    const oauthBullhornUrl = BullHornConfig.getApiGetAccessCode({ bhClientId, bhUsername, bhPassword, rootOauthUrl });
    console.log({ oauthBullhornUrl, bhClientId, bhUsername, bhPassword });
    try {
      const response = await axios.get(oauthBullhornUrl, {
        maxRedirects: 0, // Prevent Axios from following redirects automatically
        validateStatus: (status) => status >= 200 && status < 400, // Only consider 2xx and 3xx status codes as successful
      });

      if (response.status >= 300 && response.status < 400) {
        const redirectedURL = response.headers.location;
        const parsedUrl = urlLib.parse(redirectedURL, true);
        const queryObject = parsedUrl.query;

        return queryObject.code as string;
      }

      throw new BadRequestException('[Redirect Error] Please contact to Super Admin to get supports');
    } catch (error) {
      console.log('Error in getAccessCode', error);
      this.logger.error(error.message);
      throw error;
    }
  }

  async getAccessToken(
    code: string,
    { bhClientId, bhClientSecret }: IBullhornConfigDto
  ): Promise<IGetAccessTokenResponse> {
    console.log(code, 'code');
    const oauthBaseTokenEndpoint = BullHornConfig.apiGetAccessToken;

    const payload = {
      code,
      grant_type: 'authorization_code',
      client_id: bhClientId,
      client_secret: bhClientSecret,
      redirect_uri: '', // Replace 'YOUR_REDIRECT_URI' with your actual redirect URI
    };
    const queryString = stringify(payload);

    const finalUrl = oauthBaseTokenEndpoint + queryString;
    try {
      const { data } = await axios.post(finalUrl, {});
      return data;
    } catch (error) {
      console.log('Error in getAccessToken', error);
      this.logger.error(error.message);
      throw error;
    }
  }

  async refreshAccessToken(refreshToken: string, bullhornConfig: IBullhornConfigDto): Promise<IGetAccessTokenResponse> {
    const oauthBaseTokenEndpoint = BullHornConfig.apiGetAccessToken;
    const { bhClientId, bhClientSecret } = bullhornConfig;

    const payload = {
      refresh_token: refreshToken,
      grant_type: 'refresh_token',
      client_id: bhClientId,
      client_secret: bhClientSecret,
      redirect_uri: '', // Replace 'YOUR_REDIRECT_URI' with your actual redirect URI
    };
    const queryString = stringify(payload);

    const finalUrl = oauthBaseTokenEndpoint + queryString;
    try {
      const { data } = await axios.post(finalUrl, {});
      return data;
    } catch (error) {
      console.log('Error in refreshAccessToken', error.response?.data || error);
      // this.logger.error(error.message);
      // throw error;
      return this.getAccessTokenFromScratch(null, bullhornConfig);
    }
  }

  async getAccessTokenFromScratch(
    refresh_token: string | null,
    bullhornConfig: IBullhornConfigDto
  ): Promise<IGetAccessTokenResponse> {
    if (!refresh_token) {
      const code = await this.getAccessCode(bullhornConfig);
      return this.getAccessToken(code, bullhornConfig);
    }
    return this.refreshAccessToken(refresh_token, bullhornConfig);
  }

  async getBhRestTokenAndCorporateRestEndpoint(
    accessToken: string,
    rootRestUrl: string
  ): Promise<IBhRestTokenUrlResponse> {
    const restEndpoint: string = BullHornConfig.getBhRestTokenAndCorporateRestEndpoint({ rootRestUrl });

    const payload = {
      access_token: accessToken,
    };
    const queryString = stringify(payload);

    const finalUrl = restEndpoint + queryString;
    try {
      const { data } = await axios.post(finalUrl, {});
      return data;
    } catch (error) {
      console.log('Error in getBhRestTokenAndCorporateRestEndpoint', error);
      this.logger.error(error.message);
      throw error;
    }
  }
}

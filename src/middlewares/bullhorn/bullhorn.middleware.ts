import { BadRequestException, Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { BullHornService } from './bullhorn.service';
import { getValueByPath } from 'src/common/utils/helpers.util';
import { BullHornConfig, CookieConfig } from 'src/configs/configs.constants';
import * as jwt from 'jsonwebtoken';
import { DataSource } from 'typeorm';
import { OrganizationEntity } from 'src/modules/user/entities/organization.entity';
import { UserEntity } from 'src/modules/user/entities/user.entity';

@Injectable()
export class BullHornMiddleware implements NestMiddleware {
  private readonly logger = new Logger(BullHornMiddleware.name);
  constructor(private bullhornService: BullHornService, private dataSource: DataSource) {}

  //if organizationId is falsy -> auto use default BH -> use [0]
  //else-> use organizationId as a key
  async use(req: Request, res: Response, next: NextFunction) {
    // Only routes that use req.app.locals have to check/get bullhorn token
    if (!req.route?.path?.includes('/bullhorn-integration/') && !req.route?.path?.includes('/job-lead/similar-jobs')) {
      return next();
    }

    const accessToken = req.cookies?.[CookieConfig.ACCESS_TOKEN_CODE]?.accessToken;
    const decodedToken = (jwt.decode(accessToken) as any) || {};
    let { organizationId, id: loginUserId } = decodedToken;
    const viewAsUserId = req.headers?.['view-as-user-id'];
    if (viewAsUserId && loginUserId !== viewAsUserId) {
      const viewAsUser = await this.dataSource.createQueryBuilder(UserEntity, 'u').where({ id: viewAsUserId }).getOne();
      organizationId = viewAsUser?.organizationId;
    }

    if (
      req.app.locals?.[organizationId]?.bullhornToken &&
      req.app.locals?.[organizationId]?.bullhornToken.expiresAt > Date.now()
    ) {
      return next();
    }

    try {
      const org = await this.dataSource
        .createQueryBuilder(OrganizationEntity, 'o')
        .where({ id: organizationId })
        .getOne();

      const { bhClientId, bhUsername, bhPassword, bhClientSecret } = org ?? {
        bhClientId: BullHornConfig.clientId,
        bhUsername: BullHornConfig.username,
        bhPassword: BullHornConfig.password,
        bhClientSecret: BullHornConfig.clientSecret,
      };

      if (!bhClientId || !bhUsername || !bhPassword || !bhClientSecret) {
        throw new BadRequestException(
          "Your company's Bullhorn Configuration is not complete. Please contact to your Company Admin to resolve"
        );
      }
      let { refreshToken, rootRestUrl, rootOauthUrl } = req.app.locals?.[organizationId]?.bullhornToken ?? {};
      const currentRefreshToken: string | null = refreshToken || null;

      if (!rootRestUrl || !rootOauthUrl) {
        const { oauthUrl, restUrl } = await this.bullhornService.getDataCenter(bhUsername);
        rootRestUrl = restUrl;
        rootOauthUrl = oauthUrl;
      }

      const { access_token, expires_in, refresh_token } = await this.bullhornService.getAccessTokenFromScratch(
        currentRefreshToken,
        {
          bhClientId,
          bhUsername,
          bhPassword,
          bhClientSecret,
          rootOauthUrl,
        }
      );
      const { BhRestToken: bhRestToken, restUrl: corporateRestUrl } =
        await this.bullhornService.getBhRestTokenAndCorporateRestEndpoint(access_token, rootRestUrl);

      req.app.locals = {
        ...req.app.locals,
        [organizationId || 0]: {
          bullhornToken: {
            rootOauthUrl,
            rootRestUrl,
            bhRestToken,
            corporateRestUrl,
            accessToken: access_token,
            refreshToken: refresh_token,
            expiresAt: Date.now() + expires_in * 1000 - 60 * 1000, // Convert to milliseconds, buffer 1 min
          },
        },
      };
      console.log(req.app.locals, 'req.app.locals');

      return next();
    } catch (error) {
      this.logger.error(error.message);
      return next(error);
    }
  }
}

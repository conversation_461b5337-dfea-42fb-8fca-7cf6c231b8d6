import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { DataSource } from 'typeorm';
import { ResourceEnum } from '../modules/user/entities/permission.entity';
import { PermissionResource } from '../common/constants/permission.constant';
import { UserEntity } from '../modules/user/entities/user.entity';
import { calculateUserFeatures } from '../common/utils/helpers.util';
import * as passport from 'passport';
import { CookieConfig } from 'src/configs/configs.constants';
import { SECURITY_VERIFY } from '../modules/auth/auth.constants';
// We're using 'any' type for viewAsUser to avoid TypeScript errors

// Extend Express Request interface to include viewAsUser
declare global {
  namespace Express {
    interface Request {
      viewAsUser?: any;
    }
  }
}

@Injectable()
export class ViewAsMiddleware implements NestMiddleware {
  constructor(
    private readonly dataSource: DataSource,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const accessToken = req.cookies?.[CookieConfig.ACCESS_TOKEN_CODE]?.accessToken;
      if (!accessToken) {
        return next();
      }

      // Try to get user from JWT token if not already in request
      let user: any = req.user;
      let loginUserId: string;

      if (!user) {
        try {
          // Create a promise-based version of passport.authenticate
          const authenticate = (strategy, options) => {
            return new Promise((resolve, reject) => {
              passport.authenticate(strategy, options, (err, user) => {
                if (err) return reject(err);
                resolve(user);
              })(req, res, () => {});
            });
          };

          // Authenticate using the verify strategy
          const authenticatedUser = await authenticate(SECURITY_VERIFY, { session: false });
          if (authenticatedUser) {
            user = authenticatedUser;
            // Set user in request for future use
            // req.user = user;
          }
        } catch (error) {
          console.error('Error authenticating with Passport:', error);
          return next();
        }
      }

      // Ensure user is treated as any type to access properties
      loginUserId = user?.id;
      if (!loginUserId) {
        return next();
      }

      // Check for view-as-user-id in header
      let viewAsUserId = req.headers?.['view-as-user-id'] as string;

      // If not found in header, check for view-as in URL path
      // TODO: Remove this after client migration is complete
      if (!viewAsUserId) {
        const url = req.url;
        const viewAsMatch = url.match(/\/view-as\/([^\/\?]+)/);
        if (viewAsMatch && viewAsMatch[1]) {
          viewAsUserId = viewAsMatch[1];
        }
      }

      // If not found in header or URL, check for updatedFor in body
      // TODO: Remove this after client migration is complete
      if (!viewAsUserId && req.body?.updatedFor) {
        viewAsUserId = req.body.updatedFor;
      }

      if (viewAsUserId && loginUserId !== viewAsUserId) {
        // Allow if user is viewing themselves
        if (viewAsUserId === loginUserId) {
          req.viewAsUser = user;
          return next();
        }

        // Check if user has VIEW_AS permission
        const viewAsPermission = PermissionResource[ResourceEnum.VIEW_AS].Read;

        // Check explicit permissions first (faster check)
        const hasExplicitViewAsPermission = user.permissions?.includes(viewAsPermission);

        if (!hasExplicitViewAsPermission) {
          // If no explicit permission, check role-based permission (requires DB query)
          const hasViewAsPermission = await this.checkPermissionByRole(user.role as string, viewAsPermission);

          if (!hasViewAsPermission) {
            // Only return 403 if this is a view-as endpoint
            const url = req.url;
            const isViewAsEndpoint = url.includes('/view-as/');

            if (isViewAsEndpoint) {
              return res.status(403).json({
                message: 'You do not have permission to view as another user',
              });
            }
          }
        }

        // Get view-as user information
        const viewAsUser = await this.dataSource
          .getRepository(UserEntity)
          .findOne({
            where: { id: viewAsUserId },
            relations: ['role', 'organization'],
          });

        if (!viewAsUser) {
          return res.status(404).json({
            message: 'View-as user not found',
          });
        }

        // Get permissions for view-as user
        const permissions = await this.getUserPermissions(viewAsUserId);
        // Calculate features based on permissions and role
        const features = calculateUserFeatures(viewAsUser.role?.keyCode, permissions);

        // Add view-as user information to request
        // Add necessary properties to match IJwtPayload interface
        req.viewAsUser = {
          ...viewAsUser,
          permissions,
          features
        };
      } else {
        req.viewAsUser = user;
      }
    } catch (error) {
      console.error('Error in ViewAsMiddleware:', error);
    }

    next();
  }

  /**
   * Get user permissions from database
   */
  private async getUserPermissions(userId: string): Promise<string[]> {
    try {
      // Get permissions from role_permissions table
      const result = await this.dataSource.query(`
        SELECT p."keyCode", rdp."allowRead", rdp."allowWrite"
        FROM users u
        JOIN roles r ON u."roleId" = r.id
        JOIN role_default_permissions rdp ON r.id = rdp."roleId"
        JOIN permissions p ON rdp."permissionId" = p.id
        WHERE u.id = $1
      `, [userId]);

      // Format permissions
      const permissions = [];
      for (const row of result) {
        if (row.allowRead) {
          permissions.push(`${row.keyCode}.Read`);
        }
        if (row.allowWrite) {
          permissions.push(`${row.keyCode}.Write`);
        }
      }

      return permissions;
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }



  private async checkPermissionByRole(role: string, permission: string): Promise<boolean> {
    try {
      const result = await this.dataSource.query(`
        SELECT COUNT(*) as count
        FROM role_default_permissions rdp
        JOIN permissions p ON rdp."permissionId" = p.id
        JOIN roles r ON rdp."roleId" = r.id
        WHERE r."keyCode" = $1 AND
        ((p."keyCode" || '.Read' = $2 AND rdp."allowRead" = true) OR
         (p."keyCode" || '.Write' = $2 AND rdp."allowWrite" = true))
      `, [role, permission]);

      return parseInt(result[0].count) > 0;
    } catch (error) {
      console.error('Error checking permission by role:', error);
      return false;
    }
  }
}

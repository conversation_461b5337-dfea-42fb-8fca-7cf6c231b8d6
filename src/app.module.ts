import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { CommandModule } from 'nestjs-command';
import { I18nModule, HeaderResolver } from 'nestjs-i18n';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import * as path from 'path';
import { BullModule } from '@nestjs/bullmq';
import { AppService } from './app.service';
import { AppController } from './app.controller';
import { UserModule } from './modules/user/user.module';
import { AuthModule } from './modules/auth/auth.module';
import { LanguageCode } from './common/constants/common.constant';
import { CommonConfig, BULL_QUEUES, redisConfig, redisConnection } from './configs/configs.constants';
import { typeOrmConfig } from './configs/database/typeorm.config';
import { FileUploadModule } from './modules/files-upload/file-upload.module';
import { ApiResponseInterceptor } from './common/interceptors/api-response.interceptor';
import { GoogleMapModule } from './modules/google-map/google-map.module';
import { JobsModule } from './modules/jobs/jobs.module';
import { MailModule } from './modules/mail/mail.module';
import { EmailFinderModule } from './modules/email-finder/email-finder.module';
import { UserSettingModule } from './modules/user-setting/user-setting.module';
import { NotificationModule } from './modules/notification/notification.module';
import { CronJobModule } from './modules/system-job/cron-job.module';
import { EmployeeFinderModule } from './modules/employee-finder/employee-finder.module';
import { AwsDBModule } from './modules/aws/aws-db.module';
import { OpensearchModule } from './modules/opensearch/opensearch.module';
import { CommentModule } from './modules/comments/comment.module';
import { OpenaiModule } from './modules/openai/openai.module';
import { SSEModule } from './modules/sse/sse.module';
import { SuggestionsModule } from './modules/suggestions/suggestions.module';
import { QueueModule } from './modules/queue/queue.module';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { LinkedInFinderModule } from './modules/linkedin-finder/linkedin-finder.module';
import { GeminiModule } from './modules/gemini/gemini.module';
import { ZileoCrmModule } from './modules/crm/crm.module';
import { ViewAsMiddleware } from './middlewares/view-as.middleware';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { WebhookModule } from './modules/webhook/webhook.module';


@Module({
  imports: [
    TypeOrmModule.forRoot(typeOrmConfig),
    ScheduleModule.forRoot(),
    I18nModule.forRoot({
      fallbackLanguage: LanguageCode.United_States,
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'),
        watch: true,
      },
      resolvers: [
        new HeaderResolver(['x-lang']),
      ],
    }),
    ThrottlerModule.forRootAsync({
      useFactory: () => ([{
        ttl: CommonConfig.COMMON_API_TTL,
        limit: CommonConfig.COMMON_API_LIMIT,
      }]),
    }),
    BullModule.forRoot({
      connection: redisConnection,
      defaultJobOptions: {
        removeOnComplete: {
          age: 86400,
          count: 1000,
        },
        removeOnFail: {
          age: 86400,
          count: 1000,
        },
      }
    }),
    BullBoardModule.forRoot({
      route: '/bull-board-queues',
      adapter: ExpressAdapter, // Or FastifyAdapter from `@bull-board/fastify`
      middleware: async (req, res, next) => {
        const authHeader = req.get('authorization');

        if (!authHeader || !authHeader.startsWith('Basic ')) {
          res.setHeader('WWW-Authenticate', 'Basic realm="Restricted Area", charset="UTF-8"');
          res.sendStatus(401);
          return;
        }

        const encodedCreds = authHeader.split(' ')[1];
        const decodedCreds = Buffer.from(encodedCreds, 'base64').toString('utf-8');
        const [username, password] = decodedCreds.split(':');

        if (username !== 'ZileoAdmin' || password !== 'Zileopw@12345') {
          res.setHeader('WWW-Authenticate', 'Basic realm="Restricted Area", charset="UTF-8"');
          res.sendStatus(401);
          return;
        }

        next();
      },
    }),
    BullBoardModule.forFeature(
      {
        name: BULL_QUEUES.SEQUENCE_STEP_TASK,
        adapter: BullMQAdapter,
      },
      {
        name: BULL_QUEUES.WEBHOOK_SENDGRID_EVENT,
        adapter: BullMQAdapter,
      },
      {
        name: BULL_QUEUES.WEBHOOK_NYLAS_EVENT,
        adapter: BullMQAdapter,
      },
      {
        name: BULL_QUEUES.WEBHOOK_UNIPILE_EVENT,
        adapter: BullMQAdapter,
      },
      {
        name: BULL_QUEUES.CRAWL_JOB_EVENT,
        adapter: BullMQAdapter,
      },
      {
        name: BULL_QUEUES.BULLHORN_EMAIL_NOTE_EVENT,
        adapter: BullMQAdapter,
      },
      {
        name: BULL_QUEUES.INTERNAL_QUEUE_EVENT,
        adapter: BullMQAdapter,
      },
    ),
    QueueModule,
    CommandModule,
    UserModule,
    AuthModule,
    FileUploadModule,
    EventEmitterModule.forRoot(),
    GoogleMapModule,
    EmailFinderModule,
    JobsModule,
    MailModule,
    UserSettingModule,
    OpensearchModule,
    LinkedInFinderModule,
    // BullModule.forRoot({
    //   redis: {
    //    host: 'localhost',
    //    port: 6379
    //   },
    // }),
    NotificationModule,
    CronJobModule,
    EmployeeFinderModule,
    AwsDBModule,
    CommentModule,
    OpenaiModule,
    SSEModule,
    SuggestionsModule,
    GeminiModule,
    ZileoCrmModule,
    SubscriptionModule,
    WebhookModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ApiResponseInterceptor,
    },
  ],
})
export class AppModule implements NestModule {
  constructor() {}

  configure(consumer: MiddlewareConsumer) {
    // Apply ViewAsMiddleware to all routes
    consumer
      .apply(ViewAsMiddleware)
      .forRoutes('*');
  }

  public async onModuleInit() {
    // const decoratedMethods = await this.discover.methodsAndControllerMethodsWithMetaAtKey<any>(
    //   'permission',
    // );
    // for (const item of decoratedMethods) {
    //   await this.permissionsService.create({
    //     action: item.meta.action,
    //     description: item.meta.description,
    //   });
    // }
    // auto generate permission base on decorator
  }
}

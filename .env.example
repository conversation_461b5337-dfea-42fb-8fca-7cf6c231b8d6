APP_ENV=development
APP_PORT=5001

DATABASE_TYPE=postgres
DATABASE_HOST=
DATABASE_PORT=
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_DATABASE=
DATABASE_SYNCHRONIZE=0


#JWT
COMMON_API_JWT_SECRET=randomtext1
COMMON_API_JWT_EXPIRES_IN=24h

# JWT REFESH TOKEN
COMMON_API_JWT_REFRESH_TOKEN_SECRET=randomtext3
COMMON_API_JWT_REFRESH_TOKEN_EXPIRES_IN=4d

# Rate limit
COMMON_API_TTL=60
COMMON_API_LIMIT=1


##BullHorn
BULLHORN_API_GET_ACCESS_CODE=""
BULLHORN_CLIENT_ID=""
BULLHORN_USERNAME=""
BULLHORN_PASSWORD=
BULLHORN_CLIENT_SECRET=

#Resend Email
RESEND_API_KEY=
INVITATION_LINK=
URL_API=http://localhost:5001

SNS_TOPIC_ARN=

LAMBDA_OPEN_STREET_SEARCH_URL=

REFINE_API=

# Cron Dashboard Basic Auth
CRON_DASHBOARD_USERNAME=ZileoAdmin
CRON_DASHBOARD_PASSWORD=Zileopw@12345
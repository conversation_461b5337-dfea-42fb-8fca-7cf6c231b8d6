#!/bin/bash

git checkout .
git add .
git stash
git reset --hard HEAD~1
git pull origin

# Get current git commit hash
REVISION=$(git rev-parse HEAD)

# Build with revision
docker build --build-arg NODE_ENV=staging --build-arg BUILD_REVISION=$REVISION -t zileo-staging-cron . -f Dockerfile.cron
docker rm -f zileo-staging-cron
docker run -d -it --log-driver=awslogs --log-opt awslogs-region=eu-west-2 --log-opt awslogs-group=zileo-staging-cron --log-opt awslogs-create-group=true --restart=always --name zileo-staging-cron zileo-staging-cron
docker logs zileo-staging-cron --tail 100

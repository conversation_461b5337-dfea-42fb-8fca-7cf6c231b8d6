#!/bin/bash

git checkout .
git add .
git stash
git reset --hard HEAD~1
git pull origin
docker build --build-arg NODE_ENV=staging -t zileo-staging-consumer . -f Dockerfile.consumer
docker rm -f zileo-staging-consumer
docker run -d -it --log-driver=awslogs --log-opt awslogs-region=eu-west-2 --log-opt awslogs-group=zileo-staging-consumer --log-opt awslogs-create-group=true --restart=always --name zileo-staging-consumer zileo-staging-consumer
docker logs zileo-staging-consumer --tail 100

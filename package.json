{"name": "job-finder-api", "version": "0.0.42", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "NODE_ENV=dev nest start", "build:dev": "NODE_ENV=dev npm run build", "build:staging": "NODE_ENV=staging npm run build", "start:dev": "NODE_ENV=dev nest start --watch", "start:consumer": "NODE_ENV=dev npx nodemon --watch src --ext ts --exec \"ts-node -r tsconfig-paths/register src/consumer.ts\"", "start:cron": "NODE_ENV=dev npx nodemon --watch src --ext ts --exec \"ts-node -r tsconfig-paths/register src/cron.ts\"", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:changes": "eslint $(git diff --name-only HEAD | grep -E '\\.(ts|js|jsx)$' | xargs)", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage --detect<PERSON><PERSON>Handles --forceExit", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "format:fix": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "beautiful": "lint-staged", "prepare": "husky", "seed:config": "ts-node ./node_modules/typeorm-seeding/dist/cli.js config", "seed:run": "ts-node ./node_modules/typeorm-seeding/dist/cli.js seed", "seed:run:latest": "nest build && ts-node -r tsconfig-paths/register ./node_modules/typeorm-seeding/dist/cli.js seed", "typeorm": "ts-node ./node_modules/typeorm/cli", "migrate:generate": "npm run build && npm run typeorm -- -d dist/configs/database/typeorm.config.js migration:generate ./src/database/migrations/$npm_config_name", "migrate:create": "npm run typeorm -- migration:create ./src/database/migrations/$npm_config_name", "migrate:run": "npm run build && MIGRATION_MODE=run_migration npm run typeorm migration:run -- -d dist/configs/database/typeorm.config.js", "migrate:revert": "npm run build && npm run typeorm -- -d dist/configs/database/typeorm.config.js migration:revert", "dbs:create": "npm run typeorm -- migration:create ./src/database/seeder/$npm_config_name", "dbs:run": "npm run build && MIGRATION_MODE=run_seeder npm run typeorm migration:run -- -d dist/configs/database/typeorm.config.js"}, "dependencies": {"@bull-board/api": "^6.6.1", "@bull-board/express": "^6.6.1", "@bull-board/nestjs": "^6.6.1", "@google/generative-ai": "^0.21.0", "@huggingface/transformers": "^3.4.0", "@keyv/redis": "^4.2.0", "@nestjs/axios": "^3.1.3", "@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^2.3.0", "@nestjs/cli": "^10.4.9", "@nestjs/common": "^10.4.15", "@nestjs/core": "^10.4.15", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.1", "@nestjs/throttler": "^6.3.0", "@nestjs/typeorm": "^10.0.2", "@opensearch-project/opensearch": "^2.5.0", "@sendgrid/mail": "^8.1.4", "@types/bcryptjs": "^2.4.6", "@types/cache-manager-redis-store": "^2.0.4", "@types/country-list": "^2.1.4", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.14", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "axios-https-proxy-fix": "^0.17.1", "bcryptjs": "^2.4.3", "bluebird": "^3.7.2", "body-parser": "^1.20.2", "bullmq": "^5.41.5", "cache-manager": "^5.5.2", "cache-manager-ioredis": "^2.1.0", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "country-list": "^2.3.0", "csv-parser": "^3.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "fast-csv": "^5.0.2", "ioredis": "^5.5.0", "jsonwebtoken": "^9.0.2", "keyv": "^5.2.3", "libphonenumber-js": "^1.11.17", "lodash": "^4.17.21", "mailcomposer": "^4.0.2", "mailparser": "^3.7.2", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "multer": "^1.4.5-lts.1", "nestjs-command": "^3.1.4", "nestjs-i18n": "^10.5.0", "node-geocoder": "^4.4.1", "nylas": "^7.7.2", "openai": "^4.78.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "pg-query-stream": "^4.7.1", "querystringify": "^2.2.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.33.5", "string-similarity": "^4.0.4", "stripe": "^18.1.1", "tree-kill": "^1.2.2", "typeorm": "^0.3.20", "uuid": "^11.0.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"]}, "devDependencies": {"@nestjs/testing": "^10.4.15", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.10.6", "@types/passport-local": "^1.0.38", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.3.0", "prettier": "^3.4.2", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}}
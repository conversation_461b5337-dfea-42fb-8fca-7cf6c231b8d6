# Credit Quota Assignment for Organization Admins

## Overview

This feature allows organization admins to assign TOPUP credit quotas to users within their organization. The system ensures that only TOPUP credits are used for assignment and provides proper authorization and validation.

## API Endpoints

### 1. Assign Credit Quota (Bulk Assignment)

**Endpoint:** `POST /api/users/credits/assign-quota`

**Description:** Allows organization admins to assign credit quotas to multiple users at once.

**Authorization:** Requires Admin role (ADMIN) or Super Admin (SUPER_ADMIN). Sales role is not allowed.

**Request Body:**
```json
{
  "assignments": [
    {
      "userId": "123e4567-e89b-12d3-a456-426614174000",
      "credits": 100
    },
    {
      "userId": "987fcdeb-51a2-43d1-9f12-345678901234",
      "credits": 50
    }
  ],
  "note": "Monthly credit allocation" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "message": "ASSIGN_CREDIT_QUOTA_SUCCESS",
  "data": {
    "totalCreditsAssigned": 150,
    "usersAssigned": 2,
    "assignments": [
      {
        "userId": "123e4567-e89b-12d3-a456-426614174000",
        "credits": 100,
        "user": {
          "id": "123e4567-e89b-12d3-a456-426614174000",
          "fullName": "John Doe",
          "email": "<EMAIL>",
          "organizationId": "org-123"
        }
      }
    ]
  }
}
```

### 2. Individual Credit Assignment (Legacy)

**Endpoint:** `PUT /api/users/organization-id/{organizationId}/upsert-credit-management`

**Description:** Assigns credits to a single user (enhanced with TOPUP-only logic).

**Authorization:** Requires Admin role (ADMIN) or Super Admin (SUPER_ADMIN) with CREDIT_MANAGEMENT write permission

**Request Body:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "totalCredit": 100
}
```

## Features

### 1. Authorization & Validation
- Only users with Admin or Super Admin roles can assign credits (Sales role is not allowed)
- Admins can only assign credits to users within their organization using TOPUP credits
- Super Admins can assign unlimited credits from system across any organization
- Validates that all target users exist and belong to the correct organization

### 2. Credit Source Management
- **Admin users**: Only uses TOPUP credits from organization quota for assignment
- **Super Admin users**: Uses unlimited system credits (no organization quota deduction)
- Validates sufficient credits are available before assignment (except Super Admin)
- Updates organization TOPUP credit balance after assignment (Admin only)
- Tracks credit source as TOPUP in user quotas

### 3. Logging & Audit Trail
- Logs all credit assignments with detailed information
- Tracks who performed the assignment and when
- Records the purpose and source of credits
- Maintains quota usage logs for audit purposes

### 4. Transaction Safety
- Uses database transactions for bulk assignments
- Ensures data consistency across multiple operations
- Rolls back changes if any part of the assignment fails

## Business Logic

### Credit Flow

#### For Admin Users:
1. Organization has TOPUP credits from purchases/top-ups
2. Admin assigns portions of these TOPUP credits to users
3. Organization's remaining TOPUP credits are reduced
4. Users receive assigned credits in their individual quotas
5. All assignments are logged for audit purposes

#### For Super Admin Users:
1. Super Admin can assign unlimited credits from system
2. No organization quota is checked or reduced
3. Users receive assigned credits in their individual quotas
4. All assignments are logged as "system credits" for audit purposes

### Validation Rules
- Admin must belong to the organization (Super Admin can assign across organizations)
- All target users must belong to the same organization
- Organization must have sufficient TOPUP credits available (Admin only, Super Admin bypasses this)
- Credit amounts must be positive integers
- Users must exist and be active

### Error Handling
- Insufficient permissions: Returns 400 with permission error
- Insufficient credits: Returns 400 with credit shortage details
- Invalid users: Returns 400 with user validation errors
- Organization mismatch: Returns 400 with organization error

## Usage Examples

### Bulk Assignment
```bash
curl -X POST /api/users/credits/assign-quota \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "assignments": [
      {"userId": "user1", "credits": 100},
      {"userId": "user2", "credits": 50}
    ],
    "note": "Q1 2024 allocation"
  }'
```

### Individual Assignment
```bash
curl -X PUT /api/users/organization-id/org-123/upsert-credit-management \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user1",
    "totalCredit": 100
  }'
```

## Database Changes

### Enhanced Fields
- `UserQuotaEntity.source`: Now tracks QuotaSource.TOPUP for assigned credits
- `OrganizationQuotaEntity`: Filtered by source=TOPUP for assignments
- `QuotaUsageLogEntity`: Enhanced logging with assignment details

### New Validation
- `checkLimitCredit()`: Now only checks TOPUP credits availability
- Credit assignment methods: Enhanced with TOPUP-only logic
- Permission guards: Added proper role-based authorization

## Security Considerations

1. **Role-Based Access**: Only admins can assign credits
2. **Organization Isolation**: Admins cannot assign credits outside their org
3. **Credit Source Validation**: Only TOPUP credits can be assigned
4. **Audit Logging**: All assignments are logged with performer details
5. **Transaction Safety**: Database transactions prevent partial assignments
